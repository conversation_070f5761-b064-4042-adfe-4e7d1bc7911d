#! /usr/bin/python3

import os
import sys
import re
from util import dst_dir, real_path, is_code_file

def find_commented_header_files(filename):
    result = set()
    with open(filename, 'r') as f:
        content = f.read()
        matches = re.findall(r'//#include ["](.*?)["]', content)
        for match in matches:
            (src_name, dst_name) = real_path(match)
            if not src_name:
                print("include file not found:", match)
                continue

            if os.path.isfile(dst_name):
                old_str = f"//#include \"{match}\""
                new_str = f"#include \"{match}\""
                result.add(new_str)
                content = content.replace(old_str, new_str)
                with open(filename, 'w', encoding='utf-8') as file:
                    file.write(content)
    if len(result) > 0:
        print(f"file:{filename}")
        for line in result:
            print(f"  {line}")

def traverse_code_files(path):
    for root, dirs, files in os.walk(path):
        for file_name in files:
            if not is_code_file(file_name):
                continue
            find_commented_header_files(os.path.join(root, file_name))

traverse_code_files(dst_dir)
