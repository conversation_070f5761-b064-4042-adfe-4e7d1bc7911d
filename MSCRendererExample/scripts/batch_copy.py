#! /usr/bin/python3

import sys, os
import shutil
from util import src_dir, src_dir2, dst_dir

file = sys.argv[1]
cp = False
# 打开文件
with open(file, 'r') as file:
    # 逐行读取文件内容
    line = file.readline()
    while line:
        src = line.strip()
        det = None
        while (True):
            line = line.strip()
            if len(line) == 0:
                break
            if line.startswith(src_dir2):
                dst = line.replace(src_dir2, dst_dir)
            elif line.startswith(src_dir):
                dst = line.replace(src_dir, dst_dir)
            else:
                print("invalid path:" + line)
                break
            if not os.path.exists(dst):
                cp = True
                os.makedirs(os.path.dirname(dst), exist_ok=True)
                shutil.copy2(src, dst)
            else:
                print("file exists:" + dst)
            break
        line = file.readline()

if cp:
    os.system("./install.sh")
