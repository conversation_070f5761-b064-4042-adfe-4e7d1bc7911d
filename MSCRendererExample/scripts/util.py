import os

src_dir = "/Users/<USER>/Projects/chrome/src"
src_dir2 = "/Users/<USER>/Projects/chrome/src/out/gn/gen"
dst_dir = "/Users/<USER>/Projects/XCode/msc-renderer/Source/chromium"

src_map = [
    ["/partition_alloc/","/base/allocator/partition_allocator/src/partition_alloc/"],
    ["/absl/","/third_party/abseil-cpp/absl/"],
    ["/unicode/","/third_party/icu/source/i18n/unicode/"],
    ["/unicode/","/third_party/icu/source/common/unicode/"],
    ["/perfetto/","/third_party/perfetto/include/perfetto/"],
    ["/protos/perfetto/","/third_party/perfetto/protos/perfetto/"],
    ["/client/","/third_party/crashpad/crashpad/client/"],
    ["/util/","/third_party/crashpad/crashpad/util/"],
    ["/perfetto_build_flags.","/third_party/perfetto/build_config/perfetto_build_flags."],
    ["/string-to-double.","/base/third_party/double_conversion/double-conversion/string-to-double."],
    ["/double-to-string.","/base/third_party/double_conversion/double-conversion/double-to-string."],
    ["/bignum-dtoa.","/base/third_party/double_conversion/double-conversion/bignum-dtoa."],
    ["/fast-dtoa.","/base/third_party/double_conversion/double-conversion/fast-dtoa."],
    ["/fixed-dtoa.","/base/third_party/double_conversion/double-conversion/fixed-dtoa."],
    ["/city.","/base/third_party/cityhash/city."],
    ["/ieee.","/base/third_party/double_conversion/double-conversion/ieee."],
    ["/utils.","/base/third_party/double_conversion/double-conversion/utils."],
    ["/diy-fp.","/base/third_party/double_conversion/double-conversion/diy-fp."],
    ["/strtod.","/base/third_party/double_conversion/double-conversion/strtod."],
    ["/bignum.","/base/third_party/double_conversion/double-conversion/bignum."],
    ["/cached-powers.","/base/third_party/double_conversion/double-conversion/cached-powers."],
    ["/evutil.","/third_party/libevent/evutil."],
    ["/event.","/third_party/libevent/event."],
    ["/event-config.","/third_party/libevent/event-config."],
    ["/event-internal.","/third_party/libevent/event-internal."],
    ["/config.","/third_party/libevent/mac/config."],
    ["/min_heap.","/third_party/libevent/min_heap."],
    ["/evsignal.","/third_party/libevent/evsignal."],
    ["/log.","/third_party/libevent/log."],
    ["/include/", "/third_party/skia/include/"],
    ["/src/", "/third_party/skia/modules/skcms/src/"],
    ["/src/base/sanitizer/", "/v8/src/base/sanitizer/"]
]

def _real_path(name):
    src_name = src_dir + name
    dst_name = dst_dir + name

    if not os.path.isfile(src_name):
        src_name = src_dir2 + name
        if not os.path.isfile(src_name):
            return (None, None)

    return (src_name, dst_name)

def real_path(name):
    if not name.startswith("/"):
        name = "/" + name

    (src_name, dst_name) = _real_path(name)
    if src_name:
        return (src_name, dst_name)

    for (key, value) in src_map:
        if name.startswith(key):
            new_name = name.replace(key, value)
            (src_name, dst_name) = _real_path(new_name)
            if src_name:
                return (src_name, dst_name)

    return (None, None)

def is_header_file(name):
    return name.endswith(".h")

def is_source_file(name):
    return name.endswith(".cc")

def is_code_file(name):
    return is_header_file(name) or is_source_file(name)

def traverse_code_files(path, callback):
    path = path.strip("/")
    fullpath = dst_dir + "/" + path
    for name in os.listdir(fullpath):
        fullname = fullpath + "/" + name
        if os.path.isfile(fullname):
            if is_code_file(fullname):
                callback(path + "/" + name)
        else:
            traverse_code_files(path + "/" + name, callback)
            