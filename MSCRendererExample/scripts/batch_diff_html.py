#! /usr/bin/python3

import os
import sys
import re
import subprocess
import shutil

from util import src_dir, src_dir2, dst_dir, traverse_code_files

diff_path = sys.argv[1]

css = f'''
* {{
    margin: 0;
    padding: 0;
    font-size: 16px;
    line-height:1.5em;
}}
body {{
    padding: 15px;
}}
ul {{
    margin-left: 10px;
}}
'''

def format(path, text):
    body = ""
    for line in text.splitlines():
        color = ""
        if line.startswith("<"):
            color = "red"
        elif line.startswith(">"):
            color = "green"
        elif line.startswith("-"):
            color = "grey"
        else:
            color = "blue"
        body += f'<span style="color:{color}">{line}</span>\n'
    return f'''<!DOCTYPE html>
<html>
    <head>
        <title>{path}</title>
        <style>{css}</style>
    </head>
    <body><pre>{body}</pre></body>
</html>
'''

files = list()

output_path = "./chromium_diff"
if os.path.isdir(output_path):
    shutil.rmtree(output_path)
os.mkdir(output_path)

def diff_file(path):
    src_full_path = src_dir + "/" + path
    dst_full_path = dst_dir + "/" + path
    if not os.path.isfile(src_full_path):
        src_full_path = src_dir2 + "/" + path
    if not os.path.isfile(dst_full_path):
        print("file not exists:" + src_full_path)

    result = subprocess.run(["diff", src_full_path, dst_full_path], capture_output=True, text=True)
    out = result.stdout
    if len(out) > 0:
        out = format(path, out)
        output_full_name = output_path + "/" + path + ".html"
        output_full_dir = os.path.dirname(output_full_name)
        if not os.path.isdir(output_full_dir):
            os.makedirs(output_full_dir)
        with open(output_full_name, 'w', encoding='utf-8') as file:
            file.write(out)
        files.append(path)

traverse_code_files(diff_path, lambda path: diff_file(path))

if len(files) > 0:
    filelist = "<ul>"
    for file in files:
        filelist += f'<li><a href="{file}.html" target="content">{file}</a></li>'
    filelist += "</ul>"
    list_html = f'''<!DOCTYPE html>
<html>
    <head>
        <style>{css}</style>
    </head>
    <body>{filelist}</body>
</html>
'''

    output_list_full_name = output_path + "/list.html"
    with open(output_list_full_name, 'w', encoding='utf-8') as file:
        file.write(list_html)

    index_html = f'''<!DOCTYPE html>
<html>
    <head>
        <title>Diff</title>
        <style>
        * {{
            margin: 0;
            padding: 0;
        }}
        iframe {{
            width: 100%;
            height: 100%;
            border: 0;
        }}
        </style>
    </head>
    <body style="display:flex;flex-direction:row;height:100vh">
        <div style="flex-basis:420px;height:100%">
            <iframe src="list.html" name="list" style="border-right:solid 1px black"></iframe>
        </div>
        <div style="flex-grow:1;height:100%">
            <iframe name="content"></iframe>
        </div>
    </body>
</html>
'''
    output_index_full_name = output_path + "/index.html"
    with open(output_index_full_name, 'w', encoding='utf-8') as file:
        file.write(index_html)
else:
    print("no diff")
