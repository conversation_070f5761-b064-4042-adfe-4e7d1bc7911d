#! /usr/bin/python3

import os
import sys
import re
from util import real_path

blklist = [
    "foo_flags.h",
    "sys/mman.h",
    "my_tracing.h",
    "float.h",
    "DataLog.h",
    "base_win_buildflags.h",
    "wow64apiset.h",
    "foo.gen.h",
    "lib.rs.h",
    "uconfig_local.h",
    "_jni",

    "/partition_alloc/",
    "/threading/",
    "/functional/",
    "base/check.h",
    "base/containers/checked_iterators.h",
    "base/check_op.h",
    "base/sequence_checker.h",
    "base/logging.h",
    "base/feature_list.h",
    "base/strings/string_util_win.h",
    "assertions.h",
    "features.h",
    "threading.h",
    "functional.h",

    "trace_event",
    "win_util.h"
]

deps_lists = list()
walked_files = set()
root = {
    "name": "root",
    "children": []
}

def ayalyze_requires(name, is_entry = True, deps_list = [], parent = ""):
    src_path, dst_path = real_path(name)
    if not src_path:
        # print("invalid:" + name + " [" + parent + "]")
        return

    for blk in blklist:
        if blk in src_path:
            # print("blacklist:" + name)
            return None

    if not is_entry and os.path.isfile(dst_path):
        # print("exists:" + name)
        deps_lists.append(deps_list)
        return

    deps_list = [item for item in deps_list]
    deps_list.append(src_path)

    if src_path in walked_files:
        deps_lists.append(deps_list)
        return
    walked_files.add(src_path)

    leaf = True
    with open(src_path, 'r') as f:
        content = f.read()
        # 使用正则表达式匹配 #include "header.h" 或 #include <header.h> 的语句
        matches = re.findall(r'#include\s+["](.*?)["]', content)
        for match in matches:
            leaf = False
            ayalyze_requires(match, False, deps_list, name)

    if leaf:
        deps_lists.append([item for item in deps_list])

    # imp
    base_name = os.path.splitext(src_path)
    impl_file_path = None
    for ext in (".cc", ".cpp", ".c", ".mm", ".m"):
        if os.path.exists(base_name[0] + ext):
            base_name = os.path.splitext(name)
            impl_file_path = base_name[0] + ext
            ayalyze_requires(impl_file_path, False, deps_list, name)
            return

def tree_insert(node, names):
    for name in names:
        found = False
        for childnode in node["children"]:
            if childnode["name"] == name:
                node = childnode
                found = True
                break

        if not found:
            node["children"].append({"name": name, "children": []})
            node = node["children"][len(node["children"]) - 1]

def tree_print(node, prefix = ""):
    for childnode in node["children"]:
        print(prefix + childnode["name"])
        tree_print(childnode, prefix + "    ")

file = sys.argv[1]
ayalyze_requires(file)
for deps_list in deps_lists:
    tree_insert(root, deps_list)
tree_print(root)
