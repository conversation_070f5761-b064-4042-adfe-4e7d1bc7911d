#! /usr/bin/python3

import subprocess
import sys, os
from util import real_path

file = sys.argv[1]
(src_name, dst_name) = real_path(file)
if src_name != None:
    if not os.path.isfile(dst_name):
        print("fail, destination file not exists")
    else:
        result = subprocess.run(["colordiff", src_name, dst_name], capture_output=True, text=True)
        out = result.stdout
        if len(out) == 0:
            out = "no difference"
        print(out)
else:
    print("fail, source file not exists")