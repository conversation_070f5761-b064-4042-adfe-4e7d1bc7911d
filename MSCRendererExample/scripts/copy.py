#! /usr/bin/python3

import shutil
import sys, os
from util import real_path

def copy_file(source, destination):
    # 创建目标目录（如果不存在）
    os.makedirs(os.path.dirname(destination), exist_ok=True)
    # 拷贝文件
    shutil.copy2(source, destination)

file = sys.argv[1]
(src_name, dst_name) = real_path(file)
if src_name != None:
    if os.path.isfile(dst_name):
        print("fail, file exists")
    else:
        copy_file(src_name, dst_name)
        os.system("./install.sh")
else:
    print("fail, source file not exists")