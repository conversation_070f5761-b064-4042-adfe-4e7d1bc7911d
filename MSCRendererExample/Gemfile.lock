GEM
  remote: http://sakgems.sankuai.com/
  remote: http://ruby.sankuai.com/
  specs:
    CFPropertyList (3.0.5)
      rexml
    activesupport (5.2.6)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 0.7, < 2)
      minitest (~> 5.1)
      tzinfo (~> 1.1)
    addressable (2.8.0)
      public_suffix (>= 2.0.2, < 5.0)
    algoliasearch (1.27.5)
      httpclient (~> 2.8, >= 2.8.3)
      json (>= 1.5.1)
    atomos (0.1.3)
    babosa (1.0.4)
    claide (1.1.0)
    cocoapods (1.10.1)
      addressable (~> 2.6)
      claide (>= 1.0.2, < 2.0)
      cocoapods-core (= 1.10.1)
      cocoapods-deintegrate (>= 1.0.3, < 2.0)
      cocoapods-downloader (>= 1.4.0, < 2.0)
      cocoapods-plugins (>= 1.0.0, < 2.0)
      cocoapods-search (>= 1.0.0, < 2.0)
      cocoapods-trunk (>= 1.4.0, < 2.0)
      cocoapods-try (>= 1.1.0, < 2.0)
      colored2 (~> 3.1)
      escape (~> 0.0.4)
      fourflusher (>= 2.3.0, < 3.0)
      gh_inspector (~> 1.0)
      molinillo (~> 0.6.6)
      nap (~> 1.0)
      ruby-macho (~> 1.4)
      xcodeproj (>= 1.19.0, < 2.0)
    cocoapods-binary (*********)
      colored2
      filesize
      gem-analytics
      ruby-progressbar
    cocoapods-core (1.10.1)
      activesupport (> 5.0, < 6)
      addressable (~> 2.6)
      algoliasearch (~> 1.0)
      concurrent-ruby (~> 1.1)
      fuzzy_match (~> 2.0.4)
      nap (~> 1.0)
      netrc (~> 0.11)
      public_suffix
      typhoeus (~> 1.0)
    cocoapods-deintegrate (1.0.5)
    cocoapods-downloader (1.5.1)
    cocoapods-hmap-prebuilt (1.2.1)
    cocoapods-mtguard-script (0.0.3)
    cocoapods-mtkit (*********)
      cocoapods (= 1.10.1)
      cocoapods-binary (= *********)
      cocoapods-hmap-prebuilt (= 1.2.1)
      cocoapods-parallel-downloader (= 2.0.1)
      cocoapods-xcfilelist-patch (= 1.10.0)
      xcodeproj (= 1.20.0)
    cocoapods-parallel-downloader (2.0.1)
      cocoapods (~> 1.10.0)
      concurrent-ruby (~> 1.1)
    cocoapods-plugins (1.0.0)
      nap
    cocoapods-sakddd-generate-preset-json (0.1.1)
      cocoapods-binary (>= *********)
    cocoapods-search (1.0.1)
    cocoapods-sync-pod-version (0.0.3)
      gem-analytics
    cocoapods-trunk (1.6.0)
      nap (>= 0.8, < 2.0)
      netrc (~> 0.11)
    cocoapods-try (1.2.0)
    cocoapods-xcfilelist-patch (1.10.0)
      cocoapods (~> 1.10.0)
    colored (1.2)
    colored2 (3.1.2)
    commander-fastlane (4.4.6)
      highline (~> 1.7.2)
    concurrent-ruby (1.1.9)
    declarative (0.0.20)
    digest-crc (0.6.4)
      rake (>= 12.0.0, < 14.0.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.7.6)
    emoji_regex (1.0.1)
    escape (0.0.4)
    ethon (0.15.0)
      ffi (>= 1.15.0)
    excon (0.90.0)
    faraday (0.15.4)
      multipart-post (>= 1.2, < 3)
    faraday-cookie_jar (0.0.7)
      faraday (>= 0.8.0)
      http-cookie (~> 1.0.0)
    faraday_middleware (0.14.0)
      faraday (>= 0.7.4, < 1.0)
    fastimage (2.2.6)
    fastlane (2.133.0)
      CFPropertyList (>= 2.3, < 4.0.0)
      addressable (>= 2.3, < 3.0.0)
      babosa (>= 1.0.2, < 2.0.0)
      bundler (>= 1.12.0, < 3.0.0)
      colored
      commander-fastlane (>= 4.4.6, < 5.0.0)
      dotenv (>= 2.1.1, < 3.0.0)
      emoji_regex (>= 0.1, < 2.0)
      excon (>= 0.45.0, < 1.0.0)
      faraday (< 0.16.0)
      faraday-cookie_jar (~> 0.0.6)
      faraday_middleware (< 0.16.0)
      fastimage (>= 2.1.0, < 3.0.0)
      gh_inspector (>= 1.1.2, < 2.0.0)
      google-api-client (>= 0.21.2, < 0.24.0)
      google-cloud-storage (>= 1.15.0, < 2.0.0)
      highline (>= 1.7.2, < 2.0.0)
      json (< 3.0.0)
      jwt (~> 2.1.0)
      mini_magick (>= 4.9.4, < 5.0.0)
      multi_xml (~> 0.5)
      multipart-post (~> 2.0.0)
      plist (>= 3.1.0, < 4.0.0)
      public_suffix (~> 2.0.0)
      rubyzip (>= 1.3.0, < 2.0.0)
      security (= 0.1.3)
      simctl (~> 1.6.3)
      slack-notifier (>= 2.0.0, < 3.0.0)
      terminal-notifier (>= 2.0.0, < 3.0.0)
      terminal-table (>= 1.4.5, < 2.0.0)
      tty-screen (>= 0.6.3, < 1.0.0)
      tty-spinner (>= 0.8.0, < 1.0.0)
      word_wrap (~> 1.0.0)
      xcodeproj (>= 1.8.1, < 2.0.0)
      xcpretty (~> 0.3.0)
      xcpretty-travis-formatter (>= 0.0.3)
    ffi (1.15.5)
    filesize (0.2.0)
    fourflusher (2.3.1)
    fuzzy_match (2.0.4)
    gem-analytics (0.0.1)
    gh_inspector (1.1.3)
    google-api-client (0.23.9)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.5, < 0.7.0)
      httpclient (>= 2.8.1, < 3.0)
      mime-types (~> 3.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.0)
      signet (~> 0.9)
    google-cloud-core (1.6.0)
      google-cloud-env (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-cloud-env (1.3.0)
      faraday (~> 0.11)
    google-cloud-errors (1.2.0)
    google-cloud-storage (1.16.0)
      digest-crc (~> 0.4)
      google-api-client (~> 0.23)
      google-cloud-core (~> 1.2)
      googleauth (>= 0.6.2, < 0.10.0)
    googleauth (0.6.7)
      faraday (~> 0.12)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (~> 0.7)
    highline (1.7.10)
    http-cookie (1.0.4)
      domain_name (~> 0.5)
    httpclient (2.8.3)
    i18n (1.8.11)
      concurrent-ruby (~> 1.0)
    json (2.6.1)
    jwt (2.1.0)
    memoist (0.16.2)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2022.0105)
    mini_magick (4.11.0)
    minitest (5.15.0)
    molinillo (0.6.6)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.0.0)
    nanaimo (0.3.0)
    nap (1.1.0)
    naturally (2.2.1)
    netrc (0.11.0)
    os (1.1.4)
    plist (3.6.0)
    public_suffix (2.0.5)
    rake (13.0.6)
    representable (3.1.1)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    retriable (3.1.2)
    rexml (3.2.5)
    rouge (2.0.7)
    ruby-macho (1.4.0)
    ruby-progressbar (1.11.0)
    rubyzip (1.3.0)
    security (0.1.3)
    signet (0.12.0)
      addressable (~> 2.3)
      faraday (~> 0.9)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simctl (1.6.8)
      CFPropertyList
      naturally
    slack-notifier (2.4.0)
    terminal-notifier (2.0.0)
    terminal-table (1.8.0)
      unicode-display_width (~> 1.1, >= 1.1.1)
    thread_safe (0.3.6)
    trailblazer-option (0.1.2)
    tty-cursor (0.7.1)
    tty-screen (0.8.1)
    tty-spinner (0.9.3)
      tty-cursor (~> 0.7)
    typhoeus (1.4.0)
      ethon (>= 0.9.0)
    tzinfo (1.2.9)
      thread_safe (~> 0.1)
    uber (0.1.0)
    unf (0.1.4)
      unf_ext
    unf_ext (0.0.8)
    unicode-display_width (1.8.0)
    word_wrap (1.0.0)
    xcodeproj (1.20.0)
      CFPropertyList (>= 2.3.3, < 4.0)
      atomos (~> 0.1.3)
      claide (>= 1.0.2, < 2.0)
      colored2 (~> 3.1)
      nanaimo (~> 0.3.0)
      rexml (~> 3.2.4)
    xcpretty (0.3.1)
      rouge (~> 2.0.7)
    xcpretty-travis-formatter (1.0.1)
      xcpretty (~> 0.2, >= 0.0.7)

PLATFORMS
  ruby

DEPENDENCIES
  cocoapods!
  cocoapods-binary!
  cocoapods-hmap-prebuilt!
  cocoapods-mtguard-script (= 0.0.3)!
  cocoapods-mtkit (= *********)!
  cocoapods-sakddd-generate-preset-json (= 0.1.1)!
  cocoapods-sync-pod-version!
  fastlane (~> 2.133.0)!
  xcodeproj!
  xcpretty (= 0.3.1)!

BUNDLED WITH
   2.3.11
