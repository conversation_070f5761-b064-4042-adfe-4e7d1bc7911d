#use_frameworks!
source 'ssh://*******************/ios/specs.git'
platform :ios, '12.0'
install! 'cocoapods', generate_multiple_pod_projects: true

ENV['MSC_DEMO_ENV_KEY'] = 'MSC_DEMO_ENV_VALUE' unless ENV.key?('MSC_DEMO_ENV_KEY')

target 'MSCRendererExample' do
  pod 'MSCRenderer', :path => '../'
  pod "msc-jsi", "0.0.28"
  pod "boost-for-react-native", "~> ********"
  pod "DoubleConversion", "~> *******"
  pod "Folly", "~> 2018.10.22.01"
  pod "GLog", "~> *******"
end


post_install do |installer|
  unless installer.pod_target_subprojects.blank?
    installer.pod_target_subprojects.flat_map { |project| project.targets }.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
        macros = ['$(inherited)']
        if config.name == 'Debug'
          macros << 'CIP_UNIT_TESTS=1'
        end
        config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= macros
        wrapper_extension = config.build_settings['WRAPPER_EXTENSION']
        if !wrapper_extension.nil? && wrapper_extension == 'bundle'
          config.build_settings['CODE_SIGN_IDENTITY'] = ''
          config.build_settings['CODE_SIGN_STYLE'] = 'Manual'
        end
        config.build_settings['GCC_TREAT_INCOMPATIBLE_POINTER_TYPE_WARNINGS_AS_ERRORS'] =  "YES"
      end
    end
  end
end
