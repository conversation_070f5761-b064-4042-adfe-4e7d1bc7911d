// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 55;
	objects = {

/* Begin PBXBuildFile section */
		5721446390B1EA6B5C858FF8 /* libPods-MSCRendererExample.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 1D47F0F371AF1A60A13C05FE /* libPods-MSCRendererExample.a */; };
		84B65CD12C4FC739003E8E46 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 84B65CD02C4FC739003E8E46 /* AppDelegate.m */; };
		84B65CD72C4FC739003E8E46 /* ViewController.mm in Sources */ = {isa = PBXBuildFile; fileRef = 84B65CD62C4FC739003E8E46 /* ViewController.mm */; };
		84B65CDA2C4FC739003E8E46 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = 84B65CD92C4FC739003E8E46 /* Base */; };
		84B65CDC2C4FC73A003E8E46 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 84B65CDB2C4FC73A003E8E46 /* Assets.xcassets */; };
		84B65CDF2C4FC73A003E8E46 /* Base in Resources */ = {isa = PBXBuildFile; fileRef = 84B65CDE2C4FC73A003E8E46 /* Base */; };
		84B65CE22C4FC73A003E8E46 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 84B65CE12C4FC73A003E8E46 /* main.m */; };
		84B65CF32C509F8B003E8E46 /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 84B65CF22C509F8B003E8E46 /* libc++.tbd */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1D47F0F371AF1A60A13C05FE /* libPods-MSCRendererExample.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-MSCRendererExample.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		843C85C12C57C316001621F3 /* libicucore.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libicucore.tbd; path = usr/lib/libicucore.tbd; sourceTree = SDKROOT; };
		843C85CF2C57C73E001621F3 /* libicucore.A.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libicucore.A.tbd; path = usr/lib/libicucore.A.tbd; sourceTree = SDKROOT; };
		84B65CCC2C4FC739003E8E46 /* MSCRendererExample.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MSCRendererExample.app; sourceTree = BUILT_PRODUCTS_DIR; };
		84B65CCF2C4FC739003E8E46 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		84B65CD02C4FC739003E8E46 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		84B65CD52C4FC739003E8E46 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		84B65CD62C4FC739003E8E46 /* ViewController.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = ViewController.mm; sourceTree = "<group>"; };
		84B65CD92C4FC739003E8E46 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		84B65CDB2C4FC73A003E8E46 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		84B65CDE2C4FC73A003E8E46 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		84B65CE02C4FC73A003E8E46 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		84B65CE12C4FC73A003E8E46 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		84B65CF22C509F8B003E8E46 /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		B4545098FB334EAB446DE0DF /* Pods-MSCRendererExample.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MSCRendererExample.debug.xcconfig"; path = "Target Support Files/Pods-MSCRendererExample/Pods-MSCRendererExample.debug.xcconfig"; sourceTree = "<group>"; };
		DC7D2ED258F2424EEF9F6CC8 /* Pods-MSCRendererExample.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MSCRendererExample.release.xcconfig"; path = "Target Support Files/Pods-MSCRendererExample/Pods-MSCRendererExample.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		84B65CC92C4FC739003E8E46 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				84B65CF32C509F8B003E8E46 /* libc++.tbd in Frameworks */,
				5721446390B1EA6B5C858FF8 /* libPods-MSCRendererExample.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		28741D6869384F0F8F2269C8 /* Pods */ = {
			isa = PBXGroup;
			children = (
				B4545098FB334EAB446DE0DF /* Pods-MSCRendererExample.debug.xcconfig */,
				DC7D2ED258F2424EEF9F6CC8 /* Pods-MSCRendererExample.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		713360A8BBCC743A4D726C97 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				843C85CF2C57C73E001621F3 /* libicucore.A.tbd */,
				843C85C12C57C316001621F3 /* libicucore.tbd */,
				84B65CF22C509F8B003E8E46 /* libc++.tbd */,
				1D47F0F371AF1A60A13C05FE /* libPods-MSCRendererExample.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		84B65CC32C4FC739003E8E46 = {
			isa = PBXGroup;
			children = (
				84B65CCE2C4FC739003E8E46 /* MSCRendererExample */,
				84B65CCD2C4FC739003E8E46 /* Products */,
				28741D6869384F0F8F2269C8 /* Pods */,
				713360A8BBCC743A4D726C97 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		84B65CCD2C4FC739003E8E46 /* Products */ = {
			isa = PBXGroup;
			children = (
				84B65CCC2C4FC739003E8E46 /* MSCRendererExample.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		84B65CCE2C4FC739003E8E46 /* MSCRendererExample */ = {
			isa = PBXGroup;
			children = (
				84B65CCF2C4FC739003E8E46 /* AppDelegate.h */,
				84B65CD02C4FC739003E8E46 /* AppDelegate.m */,
				84B65CD52C4FC739003E8E46 /* ViewController.h */,
				84B65CD62C4FC739003E8E46 /* ViewController.mm */,
				84B65CD82C4FC739003E8E46 /* Main.storyboard */,
				84B65CDB2C4FC73A003E8E46 /* Assets.xcassets */,
				84B65CDD2C4FC73A003E8E46 /* LaunchScreen.storyboard */,
				84B65CE02C4FC73A003E8E46 /* Info.plist */,
				84B65CE12C4FC73A003E8E46 /* main.m */,
			);
			path = MSCRendererExample;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		84B65CCB2C4FC739003E8E46 /* MSCRendererExample */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 84B65CE52C4FC73A003E8E46 /* Build configuration list for PBXNativeTarget "MSCRendererExample" */;
			buildPhases = (
				0933B95E6458A63476CD35AE /* [CP] Check Pods Manifest.lock */,
				84B65CC82C4FC739003E8E46 /* Sources */,
				84B65CC92C4FC739003E8E46 /* Frameworks */,
				84B65CCA2C4FC739003E8E46 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MSCRendererExample;
			productName = MSCRenderer;
			productReference = 84B65CCC2C4FC739003E8E46 /* MSCRendererExample.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		84B65CC42C4FC739003E8E46 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					84B65CCB2C4FC739003E8E46 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 84B65CC72C4FC739003E8E46 /* Build configuration list for PBXProject "MSCRenderer" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 84B65CC32C4FC739003E8E46;
			productRefGroup = 84B65CCD2C4FC739003E8E46 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				84B65CCB2C4FC739003E8E46 /* MSCRendererExample */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		84B65CCA2C4FC739003E8E46 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				84B65CDC2C4FC73A003E8E46 /* Assets.xcassets in Resources */,
				84B65CDF2C4FC73A003E8E46 /* Base in Resources */,
				84B65CDA2C4FC739003E8E46 /* Base in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0933B95E6458A63476CD35AE /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MSCRendererExample-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		84B65CC82C4FC739003E8E46 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				84B65CD72C4FC739003E8E46 /* ViewController.mm in Sources */,
				84B65CD12C4FC739003E8E46 /* AppDelegate.m in Sources */,
				84B65CE22C4FC73A003E8E46 /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		84B65CD82C4FC739003E8E46 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				84B65CD92C4FC739003E8E46 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		84B65CDD2C4FC73A003E8E46 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				84B65CDE2C4FC73A003E8E46 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		84B65CE32C4FC73A003E8E46 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = s;
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		84B65CE42C4FC73A003E8E46 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		84B65CE62C4FC73A003E8E46 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B4545098FB334EAB446DE0DF /* Pods-MSCRendererExample.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_CXX_LANGUAGE_STANDARD = "compiler-default";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 72WUXG3TQD;
				GCC_C_LANGUAGE_STANDARD = "compiler-default";
				GCC_OPTIMIZATION_LEVEL = s;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MSCRendererExample/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 12;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_CFLAGS = "";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-ftemplate-backtrace-limit=0",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"MSCRenderer\"",
					"-l\"icucore\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.msc.MSCRenderer1;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		84B65CE72C4FC73A003E8E46 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DC7D2ED258F2424EEF9F6CC8 /* Pods-MSCRendererExample.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_CXX_LANGUAGE_STANDARD = "compiler-default";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 72WUXG3TQD;
				GCC_C_LANGUAGE_STANDARD = "compiler-default";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MSCRendererExample/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 12;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_CFLAGS = "";
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-ftemplate-backtrace-limit=0",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-l\"MSCRenderer\"",
					"-l\"icucore\"",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.msc.MSCRenderer1;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		84B65CC72C4FC739003E8E46 /* Build configuration list for PBXProject "MSCRenderer" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				84B65CE32C4FC73A003E8E46 /* Debug */,
				84B65CE42C4FC73A003E8E46 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		84B65CE52C4FC73A003E8E46 /* Build configuration list for PBXNativeTarget "MSCRendererExample" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				84B65CE62C4FC73A003E8E46 /* Debug */,
				84B65CE72C4FC73A003E8E46 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 84B65CC42C4FC739003E8E46 /* Project object */;
}
