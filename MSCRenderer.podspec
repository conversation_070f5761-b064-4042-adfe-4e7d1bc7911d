#
# Be sure to run `pod lib lint MSCNativeRenderer.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'MSCRenderer'
  s.version          = '0.3.13-nativedom'
  s.summary          = 'NativeRenderer for MSC'

# This description is used to generate tags and improve search results.
#   * Think: What does it do? Why did you write it? What is the focus?
#   * Try to keep it short, snappy and to the point.
#   * Write the description between the DESC delimiters below.
#   * Finally, don't worry about the indent, CocoaPods strips it!

  s.description      = <<-DESC
NativeRenderer for MSC
DESC

  s.homepage         = 'https://dev.sankuai.com/code/repo-detail/msc/msc-renderer/file/list'
  s.license          = { :type => 'All rights reserved.', :file => 'LICENSE' }
  s.author           = { 'msc team' => '<EMAIL>' }
  s.source           = { :git => 'ssh://*******************/msc/msc-renderer.git', :tag => s.version.to_s }
  # s.social_media_url = 'https://twitter.com/<TWITTER_USERNAME>'

  s.ios.deployment_target = '12.0'

  s.dependency "msc-jsi", "0.0.28"

  header_search_paths = [
  ]
  s.compiler_flags = ""
  s.public_header_files = ['Source/Public/**/*.h', 'Source/msc/native_dom/bridge/document_registry.h', 'Source/msc/native_dom/bridge/dom_api_injector.h']
  s.source_files = 'Source/**/*.{h,hpp,c,cc,cpp,m,mm}'
  s.resources = []
  s.resource_bundles = {}
  s.exclude_files = ['Source/msc/native_dom/v8/*.*']
  s.pod_target_xcconfig = {
    'HEADER_SEARCH_PATHS' => ['$(inherited), "$(PODS_TARGET_SRCROOT)/Source/chromium/**"', '"$(PODS_TARGET_SRCROOT)/Source/msc/**"'].join(' '),
    'CLANG_CXX_LANGUAGE_STANDARD' => 'gnu++20',
    'OTHER_CPLUSPLUSFLAGS[config=Debug]' => '$(inherited) -Wno-documentation',
    'OTHER_CPLUSPLUSFLAGS[config=DailyBuild]' => '$(inherited) -Wno-documentation',
    'OTHER_CPLUSPLUSFLAGS[config=Release]' => "$(inherited) -D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D_FORTIFY_SOURCE=2 -D_LIBCPP_HARDENING_MODE=_LIBCPP_HARDENING_MODE_EXTENSIVE -DCR_XCODE_VERSION=1540 -DCR_CLANG_REVISION=\"llvmorg-19-init-9433-g76ea5feb-1\" -D_LIBCPP_DISABLE_VISIBILITY_ANNOTATIONS -D_LIBCXXABI_DISABLE_VISIBILITY_ANNOTATIONS -DCR_LIBCXX_REVISION=e3b94d0e5b86883fd77696bf10dc33ba250ba99b -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DBLINK_CORE_IMPLEMENTATION=1 -DBASE_USE_PERFETTO_CLIENT_LIBRARY=1 -DLIBYUV_DISABLE_LSX -DLIBYUV_DISABLE_LASX -DSK_ENABLE_SKSL -DSK_UNTIL_CRBUG_1187654_IS_FIXED -DSK_WIN_FONTMGR_NO_SIMULATIONS -DSK_DISABLE_LEGACY_INIT_DECODERS -DSK_SLUG_DISABLE_LEGACY_DESERIALIZE -DSK_DISABLE_LEGACY_VULKAN_BACKENDSEMAPHORE -DSK_DISABLE_LEGACY_CREATE_CHARACTERIZATION -DSK_DISABLE_LEGACY_VULKAN_MUTABLE_TEXTURE_STATE -DSK_CODEC_DECODES_JPEG -DSK_ENCODE_JPEG -DSK_ENCODE_PNG -DSK_ENCODE_WEBP -DSK_BUILD_FOR_MAC -DSK_GANESH -DSK_GL -DSK_GRAPHITE -DSK_DAWN -DUSE_EGL -DBLINK_IMPLEMENTATION=1 -DINSIDE_BLINK -DU_USING_ICU_NAMESPACE=0 -DU_ENABLE_DYLOAD=0 -DUSE_CHROMIUM_ICU=1 -DU_ENABLE_TRACING=1 -DU_ENABLE_RESOURCE_TRACING=0 -DU_STATIC_IMPLEMENTATION -DICU_UTIL_DATA_IMPL=ICU_UTIL_DATA_FILE -DGOOGLE_PROTOBUF_NO_RTTI -DGOOGLE_PROTOBUF_NO_STATIC_INITIALIZER -DGOOGLE_PROTOBUF_INTERNAL_DONATE_STEAL_INLINE=0 -DHAVE_PTHREAD -DWEBRTC_ENABLE_AVX2 -DWEBRTC_CHROMIUM_BUILD -DWEBRTC_POSIX -DWEBRTC_MAC -DABSL_ALLOCATOR_NOTHROW=1 -DLOGGING_INSIDE_WEBRTC -DV8_COMPRESS_POINTERS -DV8_COMPRESS_POINTERS_IN_SHARED_CAGE -DV8_31BIT_SMIS_ON_64BIT_ARCH -DV8_ENABLE_SANDBOX -DV8_DEPRECATION_WARNINGS -DV8_USE_PERFETTO -DV8_TARGET_OS_MACOS -DCPPGC_SLIM_WRITE_BARRIER -DLEVELDB_PLATFORM_CHROMIUM=1 -DCRASHPAD_ZLIB_SOURCE_EXTERNAL -DUSE_LIBJPEG_TURBO=1 -DMANGLE_JPEG_NAMES -DWEBP_EXTERN=extern -flto=thin -std=c++20 -g0 -fno-delete-null-pointer-checks -fno-ident -fno-strict-aliasing -fstack-protector -fcolor-diagnostics -fmerge-all-constants -mllvm -instcombine-lower-dbg-declare=0 -ffp-contract=off -fwhole-program-vtables -fcomplete-member-pointers -mno-outline -no-canonical-prefixes -ftrivial-auto-var-init=pattern -fno-omit-frame-pointer -fvisibility=hidden -DUNSAFE_BUFFERS_BUILD -fno-math-errno -DPROTOBUF_ALLOW_DEPRECATED=1 -DLIBXML_STATIC= -fno-rtti -fvisibility-inlines-hidden -ffunction-sections -fdata-sections -O2 -Wno-documentation"  }

  s.prefix_header_contents = <<-PCH
#if defined(__cplusplus)
#include "third_party/blink/renderer/platform/wtf/text/wtf_string.h"
#include "third_party/blink/renderer/core/dom/attribute_collection.h"
#include "third_party/blink/renderer/core/dom/layout_tree_builder_traversal.h"
#include "third_party/blink/renderer/core/style/computed_style.h"
#include "third_party/blink/renderer/core/style/computed_style_constants.h"
#include "third_party/blink/renderer/core/dom/document.h"
#include "third_party/blink/renderer/core/css/style_scope.h"
#include "third_party/blink/renderer/core/css/selector_checker.h"
#include "third_party/blink/renderer/core/css/resolver/scoped_style_resolver.h"
#include "third_party/blink/renderer/core/layout/layout_view.h"
using namespace WTF;
#endif
PCH

end

