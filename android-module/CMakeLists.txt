# Copyright (c) Facebook, Inc. and its affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

cmake_minimum_required(VERSION 3.18.1)
project(mscrenderer)
enable_language(ASM)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

set(CMAKE_CXX_FLAGS "-D__STDC_CONSTANT_MACROS -D__STDC_FORMAT_MACROS -D_FORTIFY_SOURCE=2 -DNDEBUG -DNVALGRIND -DDYNAMIC_ANNOTATIONS_ENABLED=0 -DBLIN<PERSON>_CORE_IMPLEMENTATION=1 -DBLINK_IMPLEMENTATION=1 -DHAVE_PTHREAD -DCPPGC_SLIM_WRITE_BARRIER -flto=thin -std=c++20 -g0 -fno-delete-null-pointer-checks -fno-ident -fno-strict-aliasing -fstack-protector -fcolor-diagnostics -fmerge-all-constants -mllvm -instcombine-lower-dbg-declare=0 -ffp-contract=off -fwhole-program-vtables -fcomplete-member-pointers -mno-outline -no-canonical-prefixes -ftrivial-auto-var-init=pattern -fno-omit-frame-pointer -fvisibility=hidden -DUNSAFE_BUFFERS_BUILD -fno-math-errno -DPROTOBUF_ALLOW_DEPRECATED=1 -fno-rtti -fvisibility-inlines-hidden -ffunction-sections -fdata-sections -O2 -Wno-documentation")

add_definitions(-DANDROID)

add_definitions(-DENABLE_PERF_MONITOR)

# Android侧开启多线程支持
add_definitions(-DMULTI_THREAD)



# v8宏对齐MTV8
if(ANDROID_ABI MATCHES "arm64-v8a" OR ANDROID_ABI MATCHES "x86_64")
    add_definitions(-DV8_COMPRESS_POINTERS)
    add_definitions(-DMTV8_VERSION8_0_ON)
#    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fno-rtti")
#    set(V8_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../libs/include_v8.0)
elseif(ANDROID_ABI MATCHES "armeabi-v7a" OR ANDROID_ABI MATCHES "x86")
#    set(V8_INCLUDE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../libs/include_v7.5)
endif()




file(GLOB SOURCE_SRC
        src/main/cpp/*.cpp src/main/cpp/*.cc
        src/main/cpp/trace/*.cpp src/main/cpp/trace/*.cc
        )

# 设置源代码目录的路径
set(SOURCE_DIR "${CMAKE_SOURCE_DIR}/../Source")

# 使用 GLOB_RECURSE 查找所有源文件
file(GLOB_RECURSE ALL_FILES
        ${SOURCE_DIR}/*.cc ${SOURCE_DIR}/*.cpp
        )

file(GLOB_RECURSE REMOVE_FILES
        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/iOS/*.h"
        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/iOS/*.mm"
#        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/*.h"
        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/jsc/*.h"
        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/jsc/*.cpp"
        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/jsc/*.mm"
#        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/JSI/*.mm"
#        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/bridge/*.h"
#        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/bridge/*.mm"
#        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/**/*.cpp"
#        "${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/**/*.cc"
#        "${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm64/push_registers_asm.cc"
        "${CMAKE_SOURCE_DIR}/../Source/chromium/base/win/*.cc"
        "${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/base/platform/platform-darwin.cc"
)
list(REMOVE_ITEM ALL_FILES ${REMOVE_FILES})

#list(REMOVE_ITEM ALL_FILES
#        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm64/push_registers_asm.cc
#        ${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/
#        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/win/*.cc
#        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/base/platform/platform-darwin.cc
#        )

if ("${ANDROID_ABI}" STREQUAL "armeabi-v7a" OR "${ANDROID_ABI}" STREQUAL "x86")
    list(REMOVE_ITEM ALL_FILES
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/cppgc/caged-heap.cc
            )
elseif ("${ANDROID_ABI}" STREQUAL "arm64-v8a" OR "${ANDROID_ABI}" STREQUAL "x86_64")
#    add_definitions(-DANDROID -DCPPGC_CAGED_HEAP -DCPPGC_YOUNG_GENERATION -DCPPGC_2GB_CAGE)
endif()

if ("${ANDROID_ABI}" STREQUAL "armeabi-v7a")
    list(REMOVE_ITEM ALL_FILES
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/ia32/push_registers_asm.cc
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/x64/push_registers_asm.cc
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm64/push_registers_asm.cc
            )
elseif ("${ANDROID_ABI}" STREQUAL "arm64-v8a")
    list(REMOVE_ITEM ALL_FILES
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/ia32/push_registers_asm.cc
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/x64/push_registers_asm.cc
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm/push_registers_asm.cc
            )
elseif ("${ANDROID_ABI}" STREQUAL "x86")
    list(REMOVE_ITEM ALL_FILES
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm/push_registers_asm.cc
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm64/push_registers_asm.cc
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/x64/push_registers_asm.cc
            )
elseif ("${ANDROID_ABI}" STREQUAL "x86_64")
    list(REMOVE_ITEM ALL_FILES
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/ia32/push_registers_asm.cc
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm/push_registers_asm.cc
            ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm64/push_registers_asm.cc
            )
endif()

# 添加 jsi-android 的 CMake
#add_subdirectory(../jsi-android ${CMAKE_CURRENT_BINARY_DIR}/jsi_android_build)

#include_directories(${FLATBUFFERS_SRC}/include)
#add_subdirectory(flatbuffers)
#FILE(GLOB Generated_SRCS generated/*.h)

set(THIRD_PARTY_DIR ${CMAKE_SOURCE_DIR}/src/main/cpp/third_party)
set(MTV8_LIB_PATH ${CMAKE_SOURCE_DIR}/lib/mtv8/${ANDROID_ABI}/libmtv8.so)
set(V8_LIB_PATH ${CMAKE_SOURCE_DIR}/lib/mtv8/${ANDROID_ABI}/libv8.mt.so)
set(MSC_JSI_LIB_PATH ${CMAKE_SOURCE_DIR}/lib/msc-jsi/${ANDROID_ABI}/libmsc_jsi.so)
set(MSC_EXECUTOR_LIB_PATH ${CMAKE_SOURCE_DIR}/lib/jniLibs/${ANDROID_ABI}/libmscexecutor.so)

# 添加预编译的 cJSON 动态库
set(CJSON_LIB_PATH ${CMAKE_SOURCE_DIR}/lib/cjson/${ANDROID_ABI}/libcjson.so)

add_library(
        mscrenderer
        SHARED
        ${SOURCE_SRC}
        ${ALL_FILES}
#        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm64/push_registers_masm_arm64.S
#        ${Generated_SRCS}
        )

target_link_options(mscrenderer PRIVATE LINKER:--gc-sections)
target_precompile_headers(mscrenderer PRIVATE ${CMAKE_SOURCE_DIR}/src/main/cpp/MSCRenderer_prefix.h)

#target_compile_options(mscrenderer PRIVATE -march=armv8-a)

target_include_directories(mscrenderer PUBLIC
        src/main/cpp
        src/main/cpp/trace
        src/main/cpp/generated
        src/main/cpp/third_party/cjson
        ${FLATBUFFERS_INCLUDE_DIR}
        ${THIRD_PARTY_DIR}
        ${CMAKE_SOURCE_DIR}/../Source/msc
        ${CMAKE_SOURCE_DIR}/../Source/msc/android
        ${CMAKE_SOURCE_DIR}/../Source/msc/wxs
        ${CMAKE_SOURCE_DIR}/../Source/msc/elements
        ${CMAKE_SOURCE_DIR}/../Source/msc/elements/rich_text_element
        ${CMAKE_SOURCE_DIR}/../Source/msc/third_party/tinyxml
        ${CMAKE_SOURCE_DIR}/../Source/msc/components
        ${CMAKE_SOURCE_DIR}/../Source/msc/components/rich_text
        ${CMAKE_SOURCE_DIR}/../Source/Public
        ${CMAKE_SOURCE_DIR}/../Source/chromium
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/allocator
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/allocator/partition_allocator
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/allocator/partition_allocator/src
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/allocator/partition_allocator/src/partition_alloc
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/allocator/partition_allocator/src/partition_alloc/partition_alloc_base
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/allocator/partition_allocator/src/partition_alloc/pointers
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/apple
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/containers
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/debug
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/macros
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/memory
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/numerics
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/ranges
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/strings
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/substring_set_matcher
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/synchronization
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/third_party
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/third_party/double_conversion
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/third_party/double_conversion/double-conversion
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/third_party/icu
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/third_party/nspr
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/threading
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/time
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/types
        ${CMAKE_SOURCE_DIR}/../Source/chromium/base/win
        ${CMAKE_SOURCE_DIR}/../Source/chromium/build
        ${CMAKE_SOURCE_DIR}/../Source/chromium/testing
        ${CMAKE_SOURCE_DIR}/../Source/chromium/testing/gtest
        ${CMAKE_SOURCE_DIR}/../Source/chromium/testing/gtest/include
        ${CMAKE_SOURCE_DIR}/../Source/chromium/testing/gtest/include/gtest
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/abseil-cpp
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/abseil-cpp/absl
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/abseil-cpp/absl/base
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/abseil-cpp/absl/base/internal
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/abseil-cpp/absl/meta
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/abseil-cpp/absl/utility
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/public
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/public/mojom
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/public/mojom/frame
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/public/mojom/use_counter
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/public/mojom/use_counter/metrics
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/css
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/css/invalidation
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/css/parser
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/css/properties
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/css/properties/longhands
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/css/properties/shorthands
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/css/resolver
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/dom
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/execution_context
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/frame
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/html
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/html/canvas
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/html/parser
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/inspector
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/layout
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/layout/exclusions
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/layout/flex
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/layout/geometry
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/layout/inline
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/layout/shapes
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/scroll
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/core/style
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/bindings
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/fonts
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/fonts/shaping
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/geometry
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/graphics
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/heap
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/heap/collection_support
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/text
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/transforms
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/weborigin
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/wtf
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/wtf/allocator
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/blink/renderer/platform/wtf/text
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/googletest
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/googletest/src
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/googletest/src/googletest
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/googletest/src/googletest/include
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/googletest/src/googletest/include/gtest
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/skia
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/skia/include
        ${CMAKE_SOURCE_DIR}/../Source/chromium/third_party/skia/include/core
        ${CMAKE_SOURCE_DIR}/../Source/chromium/ui
        ${CMAKE_SOURCE_DIR}/../Source/chromium/ui/gfx
        ${CMAKE_SOURCE_DIR}/../Source/chromium/ui/gfx/geometry
        ${CMAKE_SOURCE_DIR}/../Source/chromium/url
        ${CMAKE_SOURCE_DIR}/../Source/chromium/url/third_party
        ${CMAKE_SOURCE_DIR}/../Source/chromium/url/third_party/mozilla
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/include
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/include/cppgc
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/include/cppgc/internal
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/include/libplatform
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/base
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/base/utils
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm64
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/ia32
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/arm
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/base/asm/x64
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/heap/cppgc
        ${CMAKE_SOURCE_DIR}/../Source/chromium/v8/src/libplatform
        ${CMAKE_SOURCE_DIR}/../Source/msc/utils
        ${CMAKE_SOURCE_DIR}/../Source/msc/trace
        ${CMAKE_SOURCE_DIR}/../Source/msc/native_dom
        ${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/JSI
        ${CMAKE_SOURCE_DIR}/../Source/msc/native_dom/bridge
        ${CMAKE_SOURCE_DIR}/../libs/include_v8.0
        )

find_library(
        log-lib
        log)

target_link_libraries(mscrenderer ${MTV8_LIB_PATH} ${V8_LIB_PATH}
        ${log-lib} ${CJSON_LIB_PATH} ${MSC_JSI_LIB_PATH} ${MSC_EXECUTOR_LIB_PATH})
