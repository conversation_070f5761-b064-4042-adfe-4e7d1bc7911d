#pragma once

#include <jsi/jsi.h>
#include "mtv8.h"

using namespace msc;

namespace mtv8 {

class MTV8Runtime;
class MTV8PointerValue;

class MTV8Runtime : public jsi::Runtime {
 public:
  //MTV8Runtime();
  MTV8Runtime(std::string newName = nullptr, std::function<void (std::function<void ()>)> callbackFunc = nullptr);
  ~MTV8Runtime();

  //
  // jsi::Runtime implementations
  //
 public:
  jsi::Value evaluateJavaScript(
      const std::shared_ptr<const jsi::Buffer> &buffer,
      const std::string &sourceURL) override;

  // [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
  jsi::Value evaluateJavaScriptWithCodeCache(
      const std::shared_ptr<const jsi::Buffer> &buffer,
      const std::string &sourceURL,
      const std::string &jsCodeCachePath,
      jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback) override;
  // [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
  void createCodeCacheFile(
    const std::shared_ptr<const jsi::Buffer>& script,
    const std::string &sourceURL,
    const std::string& codeCacheFile) override;

  // [MRN60: chendacai] 主动GC，详见：https://km.sankuai.com/page/504934851
  void garbageCollect() override;
  size_t getMemoryUsage() const override;

  void startCPUProfiling(const std::string &profilerName, int interval) override;
  bool stopCPUProfiling(const std::string &profilerName, std::ostream &os) override;

  std::shared_ptr<const jsi::PreparedJavaScript> prepareJavaScript(
      const std::shared_ptr<const jsi::Buffer> &buffer,
      std::string sourceURL) override;
  jsi::Value evaluatePreparedJavaScript(
      const std::shared_ptr<const jsi::PreparedJavaScript> &js) override;

  jsi::Object global() override;
  std::string description() override;
  bool isInspectable() override;

  void changeV8InspectorName (std::string newName) override;

 protected:
  PointerValue *cloneSymbol(const Runtime::PointerValue *pv) override;
  PointerValue *cloneString(const Runtime::PointerValue *pv) override;
  PointerValue *cloneObject(const Runtime::PointerValue *pv) override;
  PointerValue *clonePropNameID(const Runtime::PointerValue *pv) override;

  jsi::PropNameID createPropNameIDFromAscii(const char *str, size_t length)
      override;
  jsi::PropNameID createPropNameIDFromUtf8(const uint8_t *utf8, size_t length)
      override;
  jsi::PropNameID createPropNameIDFromString(const jsi::String &str) override;
  std::string utf8(const jsi::PropNameID &) override;
  bool compare(const jsi::PropNameID &, const jsi::PropNameID &) override;

  std::string symbolToString(const jsi::Symbol &) override;

  jsi::String createStringFromAscii(const char *str, size_t length) override;
  jsi::String createStringFromUtf8(const uint8_t *utf8, size_t length) override;
  std::string utf8(const jsi::String &) override;

  jsi::Object createObject() override;
  jsi::Object createObject(
      std::shared_ptr<jsi::HostObject> hostObject) override;
  std::shared_ptr<jsi::HostObject> getHostObject(const jsi::Object &) override;
  jsi::HostFunctionType &getHostFunction(const jsi::Function &) override;

  jsi::Value getProperty(const jsi::Object &, const jsi::PropNameID &name)
      override;
  jsi::Value getProperty(const jsi::Object &, const jsi::String &name) override;
  bool hasProperty(const jsi::Object &, const jsi::PropNameID &name) override;
  bool hasProperty(const jsi::Object &, const jsi::String &name) override;
  void setPropertyValue(
      jsi::Object &,
      const jsi::PropNameID &name,
      const jsi::Value &value) override;
  void setPropertyValue(
      jsi::Object &,
      const jsi::String &name,
      const jsi::Value &value) override;

  bool isArray(const jsi::Object &) const override;
  bool isArrayBuffer(const jsi::Object &) const override;
  bool isFunction(const jsi::Object &) const override;
  bool isHostObject(const jsi::Object &) const override;
  bool isHostFunction(const jsi::Function &) const override;
  jsi::Array getPropertyNames(const jsi::Object &) override;

  jsi::WeakObject createWeakObject(const jsi::Object &) override;
  jsi::Value lockWeakObject(const jsi::WeakObject &) override;

  jsi::Array createArray(size_t length) override;
  size_t size(const jsi::Array &) override;
  size_t size(const jsi::ArrayBuffer &) override;
  uint8_t *data(const jsi::ArrayBuffer &) override;
  jsi::Value getValueAtIndex(const jsi::Array &, size_t i) override;
  void setValueAtIndexImpl(jsi::Array &, size_t i, const jsi::Value &value)
      override;

  jsi::Function createFunctionFromHostFunction(
      const jsi::PropNameID &name,
      unsigned int paramCount,
      jsi::HostFunctionType func) override;
  jsi::Value call(
      const jsi::Function &,
      const jsi::Value &jsThis,
      const jsi::Value *args,
      size_t count) override;
  jsi::Value callAsConstructor(
      const jsi::Function &,
      const jsi::Value *args,
      size_t count) override;

  bool strictEquals(const jsi::Symbol &a, const jsi::Symbol &b) const override;
  bool strictEquals(const jsi::String &a, const jsi::String &b) const override;
  bool strictEquals(const jsi::Object &a, const jsi::Object &b) const override;

  bool instanceOf(const jsi::Object &o, const jsi::Function &f) override;

 private:
  jsi::Value ExecuteScript(const std::shared_ptr<JSString>& script,
                           const std::shared_ptr<JSString>& sourceURL);
  jsi::Value ExecuteScriptFromCachedData(const std::shared_ptr<JSString>& script,
                           const std::shared_ptr<JSString>& sourceURL,
                           std::unique_ptr<JSCodeCachedData>& cachedData,
                           jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback);
  void ReportException(
      const std::shared_ptr<JSErrorMessage>& jsErrorMessage) const;
  void BindGetRuntimeInfo();

 private:
  friend class MTV8PointerValue;
  friend class JSIMTV8ValueConverter;

 private:
  mtv8::JSRuntime* m_runtime;
};

} // namespace mtv8
