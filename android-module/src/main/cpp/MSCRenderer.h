//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/3.
//

#include <jni.h>
#include <vector>
#include <string>

std::vector<float> measureText(const std::string& key, int tag, double width, double height, uint8_t* attrs, size_t size);

void beginEventNative(long token, const char *event_name, long event_group_id);

void endEventNative(long token, const char *event_name, long event_group_id);
