#include <jni.h>
#include "TraceRecorder.h"
#include "NativeLog.h"
#include "../StringUtil.h"

extern "C" {

// 创建TraceRecorder实例
JNIEXPORT jlong JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeCreateTraceRecorder(
    JNIEnv *env, jclass clazz) {

    trace::TraceRecorder* recorder = new trace::TraceRecorder();
    return reinterpret_cast<jlong>(recorder);
}

// 销毁TraceRecorder实例
JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeDestroyTraceRecorder(
    JNIEnv *env, jclass clazz, jlong recorderPtr) {

    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        delete recorder;
    }
}

// 设置埋点等级开关（位掩码版本）
JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeSetEnabledLevelsMask(
    JNIEnv *env, jobject obj, jlong recorderPtr, jint enabled_mask) {

    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        recorder->setEnabledLevels(static_cast<uint8_t>(enabled_mask));
    }
}

// 获取当前启用的等级掩码
JNIEXPORT jint JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeGetEnabledLevels(
    JNIEnv *env, jobject obj, jlong recorderPtr) {

    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        return static_cast<jint>(recorder->getEnabledLevels());
    }
    return 0;
}

// 关闭所有埋点
JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeDisableAll(
    JNIEnv *env, jobject obj, jlong recorderPtr) {

    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        recorder->disableAll();
}
}

// 开始埋点记录
JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeBeginTrace(
    JNIEnv *env, jobject obj, jlong recorderPtr, jstring key, jint level) {
    
    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        std::string cpp_key = JavaStringToString(env, key);
        trace::TraceLevel cpp_level = static_cast<trace::TraceLevel>(level);
        recorder->beginTrace(cpp_key, cpp_level);
    }
}

// 结束埋点记录
JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeEndTrace(
    JNIEnv *env, jobject obj, jlong recorderPtr, jstring key) {
    
    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        std::string cpp_key = JavaStringToString(env, key);
        recorder->endTrace(cpp_key);
}
}

// 导出二进制数据
JNIEXPORT jbyteArray JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeExportBinaryData(
    JNIEnv *env, jobject obj, jlong recorderPtr) {
    
    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (!recorder) {
        return nullptr;
}
    
    std::vector<uint8_t> binary_data = recorder->exportBinaryData();

    jbyteArray result = env->NewByteArray(static_cast<jsize>(binary_data.size()));
    if (result != nullptr) {
        env->SetByteArrayRegion(result, 0, static_cast<jsize>(binary_data.size()), 
                               reinterpret_cast<const jbyte*>(binary_data.data()));
    }
    
    return result;
}

// 清空所有数据
JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeClear(
    JNIEnv *env, jobject obj, jlong recorderPtr) {

    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        recorder->clear();
}
}

// 添加批次到队列
JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeAddBatch(
    JNIEnv *env, jobject obj, jlong recorderPtr, jint batch) {

    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        recorder->addBatch(static_cast<int32_t>(batch));
}
}

// 清除队列尾部批次
JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeClearTailBatch(
    JNIEnv *env, jobject obj, jlong recorderPtr) {

    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        recorder->clearTailBatch();
    }
}

// 获取当前批次
JNIEXPORT jint JNICALL
Java_com_meituan_android_msc_renderer_trace_OnlineTrace_nativeGetCurrentBatch(
    JNIEnv *env, jobject obj, jlong recorderPtr) {

    trace::TraceRecorder* recorder = reinterpret_cast<trace::TraceRecorder*>(recorderPtr);
    if (recorder) {
        return static_cast<jint>(recorder->getCurrentBatch());
    }
    return 0;
}

} // extern "C"