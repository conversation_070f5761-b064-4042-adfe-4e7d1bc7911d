//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/3.
//

#include "MSCRenderer.h"
#include <jni.h>

#include "third_party/blink/renderer/core/dom/document.h"
#include "StringUtil.h"
#include "mtdocument.h"
#include "NativeLog.h"
#include "types_def.h"
#include <thread>
#include <sys/mman.h>
#include <unistd.h>
#include <fcntl.h>
#include "generated/Person_generated.h"  // 生成的头文件
#include "Command_generated.h"
#include "convert_android.h"
#include "generated/TouchEvent_generated.h"
#include "generated/ComponentEvent_generated.h"
#include "flatbuffers/flatbuffers.h"

#include "msc-jsi/jsi.h"
#include "native_dom/JSI/JSIBinding.h"
#include "native_dom/v8/v8_binding.h"

#include "document_registry.h"
#include "TraceUtils.h"

#include <fstream>
#include <cstring>
#include <charconv>

using namespace com::meituan::android::msc::renderer::generated;

using namespace blink::mt;

using namespace blink::mt;

struct NativeCallBack {
    jobject callback;
    jmethodID layoutMethodId;
    jmethodID measureMethodId;

    bool operator==(std::nullptr_t) const {
        // 定义与 nullptr 的比较逻辑
        return callback == nullptr || layoutMethodId == nullptr || measureMethodId == nullptr;
    }
};

JavaVM *gJvm = nullptr;
pthread_key_t gThreadKey;
std::unordered_map<long, std::shared_ptr<blink::mt::MTDocument>> document_map_;
std::unordered_map<long, NativeCallBack> callback_map_;
std::unordered_map<long, jobject> document_js_callback_refs_;

jclass globalClazz;
jmethodID globalBeginEventMethodId;
jmethodID globalEndEventMethodId;

void destroyJNIEnv(void* env) {
    if (env != nullptr) {
        gJvm->DetachCurrentThread();
    }
}

JNIEnv *GetJNIEnv() {
    JNIEnv *env = static_cast<JNIEnv *>(pthread_getspecific(gThreadKey));
    if (env == nullptr) {
        pthread_key_create(&gThreadKey, destroyJNIEnv);
        if (gJvm->AttachCurrentThread(reinterpret_cast<JNIEnv **>(&env), nullptr) == JNI_OK) {
            pthread_setspecific(gThreadKey, env);
        }
    }
    return env;
}

void newDocumentRegistryInUIThread(JNIEnv *env, jclass obj, jlong jsRuntimePtr) {
#if USE_JSI
    msc::native_dom::JSIBinding::newDocumentRegistryInUIThread(jsRuntimePtr);
#elif USE_V8
    msc::native_dom::V8Binding::newDocumentRegistryInUIThread(jsRuntimePtr);
#endif
}

void newDocumentUnRegistryInUIThread(JNIEnv *env, jclass obj, jlong jsRuntimePtr) {
#if USE_V8
    msc::native_dom::V8Binding::unregisterDocumentRegistryByRuntime(jsRuntimePtr);
#endif
}

// 这里时机为loadPage,运行在主线程,提前在JSEngine实例里注入DOMAPI
void jni_JSEngineRegisterDOMApi(JNIEnv *env, jclass obj, jlong jsRuntimePtr, jstring app_id, jstring pure_path) {
#if USE_JSI
    msc::native_dom::JSIBinding::setupJSContext(jsRuntimePtr);
#elif USE_V8
    msc::native_dom::V8Binding::setupJSContext(jsRuntimePtr, JavaStringToString(env, app_id).c_str(),
                                               JavaStringToString(env, pure_path).c_str());
#endif
}

jlong jni_DocumentNewJNI(JNIEnv *env, jclass obj, jboolean enableTextPreCalculate, jboolean enableJSEngine, jint pageId, jstring pagePath) {
    Config config;
    config.enable_text_pre_calculate = enableTextPreCalculate == JNI_TRUE;
    config.enable_wxs_engine = enableJSEngine == JNI_TRUE;
    config.page_id = pageId;
    config.page_path = JavaStringToString(env, pagePath);
    std::shared_ptr<blink::mt::MTDocument> document_ = blink::mt::MTDocument::Create(std::move(config));
    uintptr_t documentPtr = reinterpret_cast<uintptr_t>(document_.get());
    document_->performBlockOnBlinkThread([documentPtr, document_]() {
        document_map_[documentPtr] = document_;
        GetJNIEnv();
    });
    return documentPtr;
}

void jni_DocumentEnableNativeDom(JNIEnv *env, jclass obj, jobject object, jlong documentPtr, jboolean enableNativeDom) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    document->enableNativeDom(enableNativeDom);
}

// 这里时机为initBlink,运行在Blink线程
void jni_DocumentSetUpJNI(JNIEnv *env, jclass obj, jobject object, jlong documentPtr, jint pageId,
                          jlong jsRuntimePtr, jlong traceRecorderPtr) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
#if USE_JSI
    std::shared_ptr<msc::native_dom::DocumentRegistry> documentRegistry = msc::native_dom::JSIBinding::getDocumentRegistryByRuntime(
            jsRuntimePtr);
#elif USE_V8
    std::shared_ptr<msc::native_dom::DocumentRegistry> documentRegistry = msc::native_dom::V8Binding::getDocumentRegistryByRuntime(
            jsRuntimePtr);
#endif
    JavaVM *jvm = nullptr;
    env->GetJavaVM(&jvm);

    document->setup(documentRegistry, pageId);

    // 将TraceRecorder指针传递给MTDocument
    if (traceRecorderPtr != 0) {
        document->setTraceRecorderPtr(traceRecorderPtr);
    }
}

void
jni_DocumentSetNativeCallBackJNI(JNIEnv *env, jclass obj, jlong documentPtr, jobject callback) {
    jclass callbackClass = env->GetObjectClass(callback);
    jmethodID measureMethodId = env->GetMethodID(callbackClass, "measureText", "(IDDLjava/nio/ByteBuffer;)[F");
    jmethodID layoutMethodId = env->GetMethodID(callbackClass, "onNativeCallback", "(Ljava/nio/ByteBuffer;)V");

    // trace相关
    jclass clazz = env->FindClass("com/meituan/android/msc/renderer/RendererNative");
    globalClazz = (jclass)env->NewGlobalRef(clazz);
    globalBeginEventMethodId = env->GetStaticMethodID(clazz, "beginEvent", "(Ljava/lang/String;)V");
    globalEndEventMethodId = env->GetStaticMethodID(clazz, "endEvent", "(Ljava/lang/String;)V");

    NativeCallBack nativeCallBack = {env->NewGlobalRef(callback), layoutMethodId, measureMethodId};
    auto document_ = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    jobject globalCallback = env->NewGlobalRef(callback);
    document_->performBlockOnBlinkThread([documentPtr, document_, nativeCallBack, globalCallback, layoutMethodId ]() {
        callback_map_[documentPtr] = nativeCallBack;
        auto callback1 = [globalCallback, layoutMethodId, document_](const blink::mt::UICommands &buffer) {
            MSC_RENDERER_LOG_DEBUG("MTDocument callback, command count: %ld", buffer->size());
            flatbuffers::FlatBufferBuilder builder(1024);
            std::pair<uint8_t *, size_t> result = document_->generateResult(builder, buffer);
            JNIEnv *env = GetJNIEnv();
            jobject jResult = env->NewDirectByteBuffer(result.first, result.second);
//            // 调用 Java
            env->CallVoidMethod(globalCallback, layoutMethodId, jResult);
//            // 释放局部引用
            env->DeleteLocalRef(jResult);
        };
        document_->setUICommandBufferCallback(callback1);
    });
}

void jni_DocumentSetJSCallBackJNI(JNIEnv *env, jclass obj, jlong documentPtr, jobject callback) {
    jclass callbackClass = env->GetObjectClass(callback);
    jmethodID doJSCallbackMethodId = env->GetMethodID(callbackClass, "doJSCallback", "(J)Z");
    jobject globalCallback = env->NewGlobalRef(callback);
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    document->performBlockOnBlinkThread([document, documentPtr, globalCallback, doJSCallbackMethodId]() {
      document_js_callback_refs_[documentPtr] = globalCallback;

      document->SetLogicTaskRunner([globalCallback, doJSCallbackMethodId](msc::TaskRunner::Task* task) -> bool {
          JNIEnv *env = GetJNIEnv();
          return env->CallBooleanMethod(globalCallback, doJSCallbackMethodId, reinterpret_cast<long>(task));
      });
    });
}

void jni_DocumentSetSizeJNI(JNIEnv *env, jclass obj, jlong documentPtr, jfloat width, jfloat height, jfloat density) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    document->setSize(blink::mt::Size(width, height, density));
}

void jni_DocumentAddCssJNI(JNIEnv *env, jclass obj, jlong documentPtr, jstring css) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    std::string cssStr = JavaStringToString(env, css);
    document->addCSS(std::move(cssStr));
}

void
jni_DocumentCreateNodeJNI(JNIEnv *env, jclass obj, jlong documentPtr, jlong tag, jstring viewName,
                          jlong rootTag, jobject map) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    std::string viewNameStr = JavaStringToString(env, viewName);
    std::shared_ptr<const blink::mt::Props> props = jobjectToProps(env, map);
    document->createNode(tag, std::move(viewNameStr), rootTag, props);
}

void jni_DocumentSetChildrenJNI(JNIEnv *env, jclass obj, jlong documentPtr, jlong tag,
                                jintArray childTags) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    std::vector<int> childTagList = jObjectToVector(env, childTags);
    document->setChildren(tag, std::make_shared<std::vector<int>>(childTagList));
}

void jni_DocumentManagerChildrenJNI(JNIEnv *env, jclass obj, jlong documentPtr, jlong tag,
                                    jintArray moveFrom, jintArray moveTo, jintArray addChildTags,
                                    jintArray addAtIndices, jintArray removeFrom) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    std::vector<Tag> moveFromList = jObjectToVector(env, moveFrom);
    std::vector<Tag> moveToList = jObjectToVector(env, moveTo);
    std::vector<Tag> addChildTagsList = jObjectToVector(env, addChildTags);
    std::vector<Tag> addAtIndicesList = jObjectToVector(env, addAtIndices);
    std::vector<Tag> removeFromList = jObjectToVector(env, removeFrom);

    MTDocument::ChildrenChanges childrenChanges = {moveFromList, moveToList, addChildTagsList, addAtIndicesList, removeFromList};
    document->manageChildren(tag, std::make_shared<const MTDocument::ChildrenChanges>(childrenChanges));
}

void
jni_DocumentUpdateNodeJNI(JNIEnv *env, jclass obj, jlong documentPtr, jlong tag, jstring viewName,
                          jobject map) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    std::string viewNameStr = JavaStringToString(env, viewName);
    std::shared_ptr<const blink::mt::Props> props = jobjectToProps(env, map);
    document->updateNode(tag, std::move(viewNameStr), props);
}

void jni_DocumentLayoutRootJNI(JNIEnv *env, jclass obj, jlong documentPtr, jboolean isPreBdc) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    blink::mt::LayoutReason reason = isPreBdc ? blink::mt::LayoutReason::PreBatchDidComplete : blink::mt::LayoutReason::BatchDidComplete;
    document->layoutRoot(reason);
}

void jni_SetMscScrollViewContentOffsetJNI(JNIEnv *env, jclass obj, jlong documentPtr,
                                          jlong tag, jfloat x, jfloat y) {
  auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
  document->SetMscScrollViewContentOffset(x, y, tag);
}

void jni_DocumentSetWidthHeightFixJNI(JNIEnv *env, jclass obj, jlong documentPtr, jlong tag,
                                      jboolean widthFix, jboolean heightFix, jfloat width,
                                      jfloat height) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    document->setWidthHeightFix(tag, widthFix, heightFix, width, height);
}

void jni_DocumentOnDestroyJNI(JNIEnv *env, jclass obj, jlong documentPtr) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    document->destroy();
    document->performBlockOnBlinkThread([documentPtr]() {
        document_map_.erase(documentPtr);
        callback_map_.erase(documentPtr);
        auto it = document_js_callback_refs_.find(documentPtr);
        if (it != document_js_callback_refs_.end()) {
            GetJNIEnv()->DeleteGlobalRef(it->second);
            document_js_callback_refs_.erase(it);
        }
    });
}

void jni_performBlockOnBlinkThread(JNIEnv *env, jclass obj, jlong documentPtr, jobject callback) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    jobject globalRunnable = env->NewGlobalRef(callback);
    document->performBlockOnBlinkThread([globalRunnable]() {
        JNIEnv *env = GetJNIEnv();
        // 执行 Runnable
        jclass runnableClass = env->GetObjectClass(globalRunnable);
        jmethodID runMethod = env->GetMethodID(runnableClass, "run", "()V");
        // 调用 Runnable 的 run() 方法
        env->CallVoidMethod(globalRunnable, runMethod);
        // 释放局部引用
        env->DeleteLocalRef(runnableClass);
        // 释放全局引用
        env->DeleteGlobalRef(globalRunnable);
        // 分离线程
    });
}

void jni_runNativeFunction(JNIEnv *env, jclass obj, jlong funcPtr) {
     auto *func = reinterpret_cast<std::function<void()> *>(funcPtr);
     if (func) {
         (*func)();
         delete func;
     }
}

void jni_fireNativeDomEvent(JNIEnv *env, jclass obj, jlong documentPtr, jstring eventName, jbyteArray buffer) {
    jbyte *bytes = env->GetByteArrayElements(buffer, nullptr);
    if (buffer == nullptr) {
        return;
    }
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    const uint8_t *buf = reinterpret_cast<const uint8_t*>(bytes);
    std::string event_name = JavaStringToString(env, eventName);
    std::shared_ptr<msc::native_dom::EventData> event_data = nullptr;
    int ele_id;
    if (event_name == "touch") {
        event_data = constructTouchEventFromBytes(buf, &ele_id);
    } else if(event_name == "component_event") {
        event_data = constructComponentEventFromBytes(buf, &ele_id);
    }
    if (event_data) {
        document->fireEvent(ele_id, event_data);
    }
    env->ReleaseByteArrayElements(buffer, bytes, JNI_ABORT);
}

jobjectArray jni_GetCachedTracesJNI(JNIEnv *env, jclass obj) {
    std::vector<std::string> traceNames;
    getAllEvents(traceNames);

    // 创建Java String数组
    jclass stringClass = env->FindClass("java/lang/String");
    jobjectArray result = env->NewObjectArray(
            traceNames.size(),
            stringClass,
            env->NewStringUTF("") // 初始化元素
    );

    // 检查数组是否创建成功
    if (result == nullptr || env->ExceptionCheck()) {
        env->ExceptionClear();
        return nullptr;
    }

    // 填充数组
    for (jsize i = 0; i < traceNames.size(); ++i) {
        jstring element = env->NewStringUTF(traceNames[i].c_str());

        // 检查字符串创建是否成功
        if (element == nullptr || env->ExceptionCheck()) {
            env->DeleteLocalRef(result);
            env->ExceptionClear();
            return nullptr;
        }

        env->SetObjectArrayElement(result, i, element);
        env->DeleteLocalRef(element); // 及时释放局部引用
    }

    return result;
}

void jni_setupNativeDOMMetricsHandle(JNIEnv *env,
                                                                                 jclass clazz, jlong jsRuntimePtr,
                                                                                 jobject handler) {
    auto document_registery = msc::native_dom::V8Binding::getDocumentRegistryByRuntime(jsRuntimePtr);
    if (document_registery == nullptr) {
        return;
    }
    auto &metrics = document_registery->GetMetrics();
    if (metrics.IsHandleSet()) {
        return;
    }

    jclass handlerClass = env->GetObjectClass(handler);
    jmethodID onMetricesTag = env->GetMethodID(handlerClass, "onMetricesTag", "(Lcom/meituan/android/msc/renderer/NativeDOMMetricsTag;)V");
    jobject globalHandler = env->NewGlobalRef(handler);

    metrics.SetHandle([globalHandler, onMetricesTag](msc::NativeDOMMetrics::Tags &&tags){
        JNIEnv *env = GetJNIEnv();

        jstring nativeStack = env->NewStringUTF(tags.native_stack.c_str());
        jstring message = env->NewStringUTF(tags.message.c_str());

        jclass tagClass = env->FindClass("com/meituan/android/msc/renderer/NativeDOMMetricsTag");
        jmethodID tagConstructor = env->GetMethodID(tagClass, "<init>", "(ILjava/lang/String;Ljava/lang/String;)V");

        jobject tagObject = env->NewObject(tagClass, tagConstructor, static_cast<int>(tags.errcode), nativeStack, message);
        if (tagObject == nullptr) {
            MSC_RENDERER_LOG_ERROR("Failed to create NativeDOMMetricesTag object");
            return;
        }

        env->CallVoidMethod(globalHandler, onMetricesTag, tagObject);
    });
}

// 执行WXS脚本
void jni_evaluateScript(JNIEnv *env, jclass obj, jlong documentPtr, jstring script, jstring source) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    std::string scriptStr = JavaStringToString(env, script);
    std::string sourceStr = JavaStringToString(env, source);
    document->evaluateScript(std::move(scriptStr), std::move(sourceStr));
}

// 设置WXS脚本加载回调
void jni_setWXSLoadScriptCallback(JNIEnv *env, jclass obj, jlong documentPtr, jobject callback) {
    jclass callbackClass = env->GetObjectClass(callback);
    jmethodID callbackMethodId = env->GetMethodID(callbackClass, "onLoadScript", "(Ljava/lang/String;)V");
    jobject globalCallback = env->NewGlobalRef(callback);
    auto document_ = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    auto callback1 = [callbackMethodId, globalCallback](MTDocument* document, const std::string& buffer) {
        JNIEnv *env = GetJNIEnv();
        // 将 C++ std::string 转换为 Java String
        jstring javaString = env->NewStringUTF(buffer.c_str());
        // 调用 Java 回调方法
        env->CallVoidMethod(globalCallback, callbackMethodId, javaString);
        // 释放局部引用
        env->DeleteLocalRef(javaString);
    };
    document_->WXSSetLoadScriptCallback(callback1);
}

// 设置WXS传输回调
void jni_setWXSTransportCallback(JNIEnv *env, jclass obj, jlong documentPtr, jobject callback) {
    jclass callbackClass = env->GetObjectClass(callback);
    jmethodID callbackMethodId = env->GetMethodID(callbackClass, "onSendEvent", "(Ljava/lang/String;)V");
    jobject globalCallback = env->NewGlobalRef(callback);
    auto document_ = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    auto callback1 = [document_, callbackMethodId, globalCallback](const std::string& str) {
        JNIEnv *env = GetJNIEnv();
        jobject jResult = env->NewStringUTF(str.c_str());
        // 调用 Java 回调方法
        env->CallVoidMethod(globalCallback, callbackMethodId, jResult);
       // 释放局部引用
       env->DeleteLocalRef(jResult);
    };
    document_->WXSSetTransportCallback(callback1);
}

// 触发WXS事件
void jni_triggerWXSEvent(JNIEnv *env, jclass obj, jlong documentPtr, jstring name, jlong targetTag, jobject eventMap) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    std::string eventNameStr = JavaStringToString(env, name);
    std::shared_ptr<const blink::mt::Props> props = jobjectToProps(env, eventMap);
    document->WXSTriggerEvent(std::move(eventNameStr), targetTag, props);
}

// 触发WXS属性变更事件
void jni_triggerWXSPropChangeEvent(JNIEnv *env, jclass obj, jlong documentPtr,jstring propName, jlong targetTag, jobject eventMap) {
    auto *document = reinterpret_cast<blink::mt::MTDocument *>(documentPtr);
    std::string propNameStr = JavaStringToString(env, propName);
    std::shared_ptr<const blink::mt::Props> props = jobjectToProps(env, eventMap);
    document->WXSTriggerPropChangeEvent(propNameStr,targetTag, props);
}

static JNINativeMethod methods[] = {
        {"jni_DocumentNewJNI",               "(ZZILjava/lang/String;)J",                              (void *) jni_DocumentNewJNI},
        {"jni_DocumentSetUpJNI",             "(Lcom/meituan/android/msc/renderer/MTDocument;JIJJ)V",   (void *) jni_DocumentSetUpJNI},
        {"jni_DocumentSetNativeCallBackJNI", "(JLcom/meituan/android/msc/renderer/NativeCallback;)V", (void *) jni_DocumentSetNativeCallBackJNI},
        {"jni_DocumentSetSizeJNI",           "(JFFF)V",                                               (void *) jni_DocumentSetSizeJNI},
        {"jni_DocumentAddCssJNI",            "(JLjava/lang/String;)V",                                (void *) jni_DocumentAddCssJNI},
        {"jni_DocumentCreateNodeJNI",        "(JJLjava/lang/String;JLjava/util/Map;)V",               (void *) jni_DocumentCreateNodeJNI},
        {"jni_DocumentSetChildrenJNI",       "(JJ[I)V",                                               (void *) jni_DocumentSetChildrenJNI},
        {"jni_DocumentManagerChildrenJNI",   "(JJ[I[I[I[I[I)V",                                       (void *) jni_DocumentManagerChildrenJNI},
        {"jni_DocumentUpdateNodeJNI",        "(JJLjava/lang/String;Ljava/util/Map;)V",                (void *) jni_DocumentUpdateNodeJNI},
        {"jni_DocumentLayoutRootJNI",        "(JZ)V",                                                 (void *) jni_DocumentLayoutRootJNI},
        {"jni_DocumentSetWidthHeightFixJNI", "(JJZZFF)V",                                             (void *) jni_DocumentSetWidthHeightFixJNI},
        {"jni_DocumentOnDestroyJNI",         "(J)V",                                                  (void *) jni_DocumentOnDestroyJNI},
        {"jni_performBlockOnBlinkThread",    "(JLjava/lang/Runnable;)V",                              (void *) jni_performBlockOnBlinkThread},

        {"jni_evaluateScript",              "(JLjava/lang/String;Ljava/lang/String;)V",               (void *) jni_evaluateScript},
        {"jni_setWXSLoadScriptCallback",    "(JLcom/meituan/android/msc/renderer/WXSLoadScriptCallback;)V", (void *) jni_setWXSLoadScriptCallback},
        {"jni_setWXSTransportCallback",     "(JLcom/meituan/android/msc/renderer/WXSTransportEventCallback;)V", (void *) jni_setWXSTransportCallback},
        {"jni_triggerWXSEvent",             "(JLjava/lang/String;JLjava/util/Map;)V",                 (void *) jni_triggerWXSEvent},
        {"jni_triggerWXSPropChangeEvent",   "(JLjava/lang/String;JLjava/util/Map;)V",                 (void *) jni_triggerWXSPropChangeEvent},

        {"newDocumentRegistryInUIThread",    "(J)V",                                                  (void *) newDocumentRegistryInUIThread},
        {"newDocumentUnRegistryInUIThread",    "(J)V",                                                  (void *) newDocumentUnRegistryInUIThread},
        {"jni_JSEngineRegisterDOMApi",       "(JLjava/lang/String;Ljava/lang/String;)V",                                                  (void *) jni_JSEngineRegisterDOMApi},
        {"jni_DocumentSetJSCallBackJNI", "(JLcom/meituan/android/msc/renderer/JSCallback;)V", (void *) jni_DocumentSetJSCallBackJNI},
        {"jni_DocumentEnableNativeDom",      "(Lcom/meituan/android/msc/renderer/MTDocument;JZ)V",   (void *) jni_DocumentEnableNativeDom},
        {"jni_SetMscScrollViewContentOffsetJNI", "(JJFF)V",                                      (void *) jni_SetMscScrollViewContentOffsetJNI},
        {"jni_fireNativeDomEvent",           "(JLjava/lang/String;[B)V",                              (void *) jni_fireNativeDomEvent},
        {"jni_runNativeFunction",            "(J)V",                                                  (void *) jni_runNativeFunction},
        {"jni_setupNativeDOMMetricsHandle",  "(JLcom/meituan/android/msc/renderer/NativeDOMMetricsTag$Handler;)V",(void *) jni_setupNativeDOMMetricsHandle},
        {"jni_GetCachedTracesJNI",           "()[Ljava/lang/String;",                                 (void *) jni_GetCachedTracesJNI},
};

// 注册本地方法
jint JNI_OnLoad(JavaVM *vm, void *) {
    gJvm = vm;
    JNIEnv *env;
    if (vm->GetEnv((void **) &env, JNI_VERSION_1_6) != JNI_OK) {
        return -1;
    }

    jclass clazz = env->FindClass("com/meituan/android/msc/renderer/RendererNative");
    if (clazz == nullptr) {
        return -1;
    }

    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) < 0) {
        return -1;
    }
    return JNI_VERSION_1_6;
}

std::vector<float> measureText(const std::string& key, int tag, double width, double height, uint8_t* attrs, size_t size) {
    std::vector<float> defaultVec = {0.0f, 0.0f};
    JNIEnv *env = GetJNIEnv();
    long value;
    auto [ptr, ec] = std::from_chars(key.data(), key.data() + key.size(), value);
    if (ec == std::errc::invalid_argument) {
        LOGI("measureText std::from_chars failed %s", key.c_str());
        return defaultVec;
    } else if (ec == std::errc::result_out_of_range) {
        LOGI("measureText std::from_chars failed %s", key.c_str());
        return defaultVec;
    }
    // 使用 env 调用 JNI 方法
    auto globalCallback = callback_map_[value];
    if (globalCallback == nullptr) {
        return defaultVec;
    }

    jobject obj = env->NewDirectByteBuffer(attrs, size);
    jfloatArray result = static_cast<jfloatArray>(env->CallObjectMethod(globalCallback.callback, globalCallback.measureMethodId, tag,
                                                                                  width, height, obj));
    std::vector<float> vec;
    jsize length = env->GetArrayLength(result);
    // 从 jintArray 获取元素
    jfloat *elements = env->GetFloatArrayElements(result, nullptr);
    // 将元素复制到 std::vector 中
    vec.assign(elements, elements + length);
    // 释放元素数组
    env->ReleaseFloatArrayElements(result, elements, JNI_ABORT);
    return vec;
}

void beginEventNative(long token, const char *event_name, long event_group_id) {
    JNIEnv *env = GetJNIEnv();
    if (env != nullptr && globalClazz != nullptr && globalBeginEventMethodId != nullptr) {
        env->CallStaticVoidMethod(globalClazz, globalBeginEventMethodId,
                                  env->NewStringUTF(event_name));
    }
}

void endEventNative(long token, const char *event_name, long event_group_id) {
    JNIEnv *env = GetJNIEnv();
    if (env != nullptr && globalClazz != nullptr && globalEndEventMethodId != nullptr) {
        env->CallStaticVoidMethod(globalClazz, globalEndEventMethodId,
                                  env->NewStringUTF(event_name));
    }
}


