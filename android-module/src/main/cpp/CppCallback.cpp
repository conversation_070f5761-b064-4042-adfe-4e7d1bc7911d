//
// Created by <PERSON> on 2025/6/16.
//

#include <functional>
#include "logger.h"

extern "C" JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_CppCallback_destoryNative(
    JNIEnv *env, jobject thiz, jlong native_ptr) {
  if (native_ptr == 0) {
    return;
  }

  delete reinterpret_cast<std::function<void()> *>(native_ptr);
}
extern "C" JNIEXPORT void JNICALL
Java_com_meituan_android_msc_renderer_CppCallback_runNative(JNIEnv *env,
                                                            jobject thiz,
                                                            jlong native_ptr) {
  if (native_ptr == 0) {
    return;
  }
  auto &callback = *reinterpret_cast<std::function<void()> *>(native_ptr);
  if (callback) {
    callback();
  }
}
