// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PERSON_EXAMPLE_H_
#define FLATBUFFERS_GENERATED_PERSON_EXAMPLE_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 25 &&
              FLATBUFFERS_VERSION_MINOR == 2 &&
              FLATBUFFERS_VERSION_REVISION == 10,
             "Non-compatible flatbuffers version included");

namespace Example {

struct Person;
struct PersonBuilder;

struct PhoneNumber;
struct PhoneNumberBuilder;

struct Person FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PersonBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_NAME = 6,
    VT_EMAIL = 8,
    VT_PHONES = 10
  };
  uint32_t id() const {
    return GetField<uint32_t>(VT_ID, 0);
  }
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::String *email() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EMAIL);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<Example::PhoneNumber>> *phones() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<Example::PhoneNumber>> *>(VT_PHONES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint32_t>(verifier, VT_ID, 4) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_EMAIL) &&
           verifier.VerifyString(email()) &&
           VerifyOffset(verifier, VT_PHONES) &&
           verifier.VerifyVector(phones()) &&
           verifier.VerifyVectorOfTables(phones()) &&
           verifier.EndTable();
  }
};

struct PersonBuilder {
  typedef Person Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(uint32_t id) {
    fbb_.AddElement<uint32_t>(Person::VT_ID, id, 0);
  }
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(Person::VT_NAME, name);
  }
  void add_email(::flatbuffers::Offset<::flatbuffers::String> email) {
    fbb_.AddOffset(Person::VT_EMAIL, email);
  }
  void add_phones(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<Example::PhoneNumber>>> phones) {
    fbb_.AddOffset(Person::VT_PHONES, phones);
  }
  explicit PersonBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Person> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Person>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Person> CreatePerson(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t id = 0,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> email = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<Example::PhoneNumber>>> phones = 0) {
  PersonBuilder builder_(_fbb);
  builder_.add_phones(phones);
  builder_.add_email(email);
  builder_.add_name(name);
  builder_.add_id(id);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Person> CreatePersonDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    uint32_t id = 0,
    const char *name = nullptr,
    const char *email = nullptr,
    const std::vector<::flatbuffers::Offset<Example::PhoneNumber>> *phones = nullptr) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto email__ = email ? _fbb.CreateString(email) : 0;
  auto phones__ = phones ? _fbb.CreateVector<::flatbuffers::Offset<Example::PhoneNumber>>(*phones) : 0;
  return Example::CreatePerson(
      _fbb,
      id,
      name__,
      email__,
      phones__);
}

struct PhoneNumber FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PhoneNumberBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NUMBER = 4,
    VT_TYPE = 6
  };
  const ::flatbuffers::String *number() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NUMBER);
  }
  const ::flatbuffers::String *type() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TYPE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NUMBER) &&
           verifier.VerifyString(number()) &&
           VerifyOffset(verifier, VT_TYPE) &&
           verifier.VerifyString(type()) &&
           verifier.EndTable();
  }
};

struct PhoneNumberBuilder {
  typedef PhoneNumber Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_number(::flatbuffers::Offset<::flatbuffers::String> number) {
    fbb_.AddOffset(PhoneNumber::VT_NUMBER, number);
  }
  void add_type(::flatbuffers::Offset<::flatbuffers::String> type) {
    fbb_.AddOffset(PhoneNumber::VT_TYPE, type);
  }
  explicit PhoneNumberBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<PhoneNumber> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<PhoneNumber>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<PhoneNumber> CreatePhoneNumber(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> number = 0,
    ::flatbuffers::Offset<::flatbuffers::String> type = 0) {
  PhoneNumberBuilder builder_(_fbb);
  builder_.add_type(type);
  builder_.add_number(number);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<PhoneNumber> CreatePhoneNumberDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *number = nullptr,
    const char *type = nullptr) {
  auto number__ = number ? _fbb.CreateString(number) : 0;
  auto type__ = type ? _fbb.CreateString(type) : 0;
  return Example::CreatePhoneNumber(
      _fbb,
      number__,
      type__);
}

inline const Example::Person *GetPerson(const void *buf) {
  return ::flatbuffers::GetRoot<Example::Person>(buf);
}

inline const Example::Person *GetSizePrefixedPerson(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<Example::Person>(buf);
}

inline bool VerifyPersonBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<Example::Person>(nullptr);
}

inline bool VerifySizePrefixedPersonBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<Example::Person>(nullptr);
}

inline void FinishPersonBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<Example::Person> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedPersonBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<Example::Person> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace Example

#endif  // FLATBUFFERS_GENERATED_PERSON_EXAMPLE_H_
