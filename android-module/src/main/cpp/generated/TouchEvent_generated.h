// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TOUCHEVENT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
#define FLATBUFFERS_GENERATED_TOUCHEVENT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 25 &&
              FLATBUFFERS_VERSION_MINOR == 2 &&
              FLATBUFFERS_VERSION_REVISION == 10,
             "Non-compatible flatbuffers version included");

namespace com {
namespace meituan {
namespace android {
namespace msc {
namespace renderer {
namespace generated {

struct FbsTouch;
struct FbsTouchBuilder;

struct FbsTouchEvent;
struct FbsTouchEventBuilder;

struct FbsTouch FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FbsTouchBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PAGEX = 4,
    VT_PAGEY = 6,
    VT_LOCATIONX = 8,
    VT_LOCATIONY = 10,
    VT_TIMESTAMP = 12,
    VT_TARGET = 14,
    VT_TOUCHID = 16,
    VT_CHANGED = 18
  };
  float pageX() const {
    return GetField<float>(VT_PAGEX, 0.0f);
  }
  float pageY() const {
    return GetField<float>(VT_PAGEY, 0.0f);
  }
  float locationX() const {
    return GetField<float>(VT_LOCATIONX, 0.0f);
  }
  float locationY() const {
    return GetField<float>(VT_LOCATIONY, 0.0f);
  }
  int64_t timestamp() const {
    return GetField<int64_t>(VT_TIMESTAMP, 0);
  }
  int32_t target() const {
    return GetField<int32_t>(VT_TARGET, 0);
  }
  int32_t touchId() const {
    return GetField<int32_t>(VT_TOUCHID, 0);
  }
  bool changed() const {
    return GetField<uint8_t>(VT_CHANGED, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_PAGEX, 4) &&
           VerifyField<float>(verifier, VT_PAGEY, 4) &&
           VerifyField<float>(verifier, VT_LOCATIONX, 4) &&
           VerifyField<float>(verifier, VT_LOCATIONY, 4) &&
           VerifyField<int64_t>(verifier, VT_TIMESTAMP, 8) &&
           VerifyField<int32_t>(verifier, VT_TARGET, 4) &&
           VerifyField<int32_t>(verifier, VT_TOUCHID, 4) &&
           VerifyField<uint8_t>(verifier, VT_CHANGED, 1) &&
           verifier.EndTable();
  }
};

struct FbsTouchBuilder {
  typedef FbsTouch Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_pageX(float pageX) {
    fbb_.AddElement<float>(FbsTouch::VT_PAGEX, pageX, 0.0f);
  }
  void add_pageY(float pageY) {
    fbb_.AddElement<float>(FbsTouch::VT_PAGEY, pageY, 0.0f);
  }
  void add_locationX(float locationX) {
    fbb_.AddElement<float>(FbsTouch::VT_LOCATIONX, locationX, 0.0f);
  }
  void add_locationY(float locationY) {
    fbb_.AddElement<float>(FbsTouch::VT_LOCATIONY, locationY, 0.0f);
  }
  void add_timestamp(int64_t timestamp) {
    fbb_.AddElement<int64_t>(FbsTouch::VT_TIMESTAMP, timestamp, 0);
  }
  void add_target(int32_t target) {
    fbb_.AddElement<int32_t>(FbsTouch::VT_TARGET, target, 0);
  }
  void add_touchId(int32_t touchId) {
    fbb_.AddElement<int32_t>(FbsTouch::VT_TOUCHID, touchId, 0);
  }
  void add_changed(bool changed) {
    fbb_.AddElement<uint8_t>(FbsTouch::VT_CHANGED, static_cast<uint8_t>(changed), 0);
  }
  explicit FbsTouchBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FbsTouch> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FbsTouch>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FbsTouch> CreateFbsTouch(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    float pageX = 0.0f,
    float pageY = 0.0f,
    float locationX = 0.0f,
    float locationY = 0.0f,
    int64_t timestamp = 0,
    int32_t target = 0,
    int32_t touchId = 0,
    bool changed = false) {
  FbsTouchBuilder builder_(_fbb);
  builder_.add_timestamp(timestamp);
  builder_.add_touchId(touchId);
  builder_.add_target(target);
  builder_.add_locationY(locationY);
  builder_.add_locationX(locationX);
  builder_.add_pageY(pageY);
  builder_.add_pageX(pageX);
  builder_.add_changed(changed);
  return builder_.Finish();
}

struct FbsTouchEvent FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FbsTouchEventBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EVENTNAME = 4,
    VT_TOUCHES = 6
  };
  const ::flatbuffers::String *eventName() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EVENTNAME);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::FbsTouch>> *touches() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::FbsTouch>> *>(VT_TOUCHES);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_EVENTNAME) &&
           verifier.VerifyString(eventName()) &&
           VerifyOffset(verifier, VT_TOUCHES) &&
           verifier.VerifyVector(touches()) &&
           verifier.VerifyVectorOfTables(touches()) &&
           verifier.EndTable();
  }
};

struct FbsTouchEventBuilder {
  typedef FbsTouchEvent Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_eventName(::flatbuffers::Offset<::flatbuffers::String> eventName) {
    fbb_.AddOffset(FbsTouchEvent::VT_EVENTNAME, eventName);
  }
  void add_touches(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::FbsTouch>>> touches) {
    fbb_.AddOffset(FbsTouchEvent::VT_TOUCHES, touches);
  }
  explicit FbsTouchEventBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FbsTouchEvent> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FbsTouchEvent>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FbsTouchEvent> CreateFbsTouchEvent(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> eventName = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::FbsTouch>>> touches = 0) {
  FbsTouchEventBuilder builder_(_fbb);
  builder_.add_touches(touches);
  builder_.add_eventName(eventName);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<FbsTouchEvent> CreateFbsTouchEventDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *eventName = nullptr,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::FbsTouch>> *touches = nullptr) {
  auto eventName__ = eventName ? _fbb.CreateString(eventName) : 0;
  auto touches__ = touches ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::FbsTouch>>(*touches) : 0;
  return com::meituan::android::msc::renderer::generated::CreateFbsTouchEvent(
      _fbb,
      eventName__,
      touches__);
}

}  // namespace generated
}  // namespace renderer
}  // namespace msc
}  // namespace android
}  // namespace meituan
}  // namespace com

#endif  // FLATBUFFERS_GENERATED_TOUCHEVENT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
