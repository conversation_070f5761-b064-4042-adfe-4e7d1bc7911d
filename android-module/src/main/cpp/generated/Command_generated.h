// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_COMMAND_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
#define FLATBUFFERS_GENERATED_COMMAND_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 25 &&
              FLATBUFFERS_VERSION_MINOR == 2 &&
              FLATBUFFERS_VERSION_REVISION == 10,
             "Non-compatible flatbuffers version included");

#include "Props_generated.h"

namespace com {
namespace meituan {
namespace android {
namespace msc {
namespace renderer {
namespace generated {

struct CreateViewCommand;
struct CreateViewCommandBuilder;

struct UpdateViewCommand;
struct UpdateViewCommandBuilder;

struct DeleteViewsCommand;
struct DeleteViewsCommandBuilder;

struct InsertChildViewsCommand;
struct InsertChildViewsCommandBuilder;

struct RemoveChildViewsCommand;
struct RemoveChildViewsCommandBuilder;

struct UpdateViewFrameCommand;
struct UpdateViewFrameCommandBuilder;

struct UpdateViewStyleCommamnd;
struct UpdateViewStyleCommamndBuilder;

struct UpdateTransformCommand;
struct UpdateTransformCommandBuilder;

struct UpdateTextCommamnd;
struct UpdateTextCommamndBuilder;

struct BatchDidFinishCommand;
struct BatchDidFinishCommandBuilder;

struct CreateKeyframesAnimationCommand;
struct CreateKeyframesAnimationCommandBuilder;

struct ClearKeyframesAnimationCommand;
struct ClearKeyframesAnimationCommandBuilder;

struct CreateAnimationKeyFrame;
struct CreateAnimationKeyFrameBuilder;

struct ClearAnimationOptions;
struct ClearAnimationOptionsBuilder;

struct ViewAtIndex;
struct ViewAtIndexBuilder;

struct Command;
struct CommandBuilder;

struct CommandArray;
struct CommandArrayBuilder;

struct DisplayItem;
struct DisplayItemBuilder;

enum LayoutReason : int8_t {
  LayoutReason_BatchDidComplete = 0,
  LayoutReason_PreBatchDidComplete = 1,
  LayoutReason_WXSSetStyle = 2,
  LayoutReason_MIN = LayoutReason_BatchDidComplete,
  LayoutReason_MAX = LayoutReason_WXSSetStyle
};

inline const LayoutReason (&EnumValuesLayoutReason())[3] {
  static const LayoutReason values[] = {
    LayoutReason_BatchDidComplete,
    LayoutReason_PreBatchDidComplete,
    LayoutReason_WXSSetStyle
  };
  return values;
}

inline const char * const *EnumNamesLayoutReason() {
  static const char * const names[4] = {
    "BatchDidComplete",
    "PreBatchDidComplete",
    "WXSSetStyle",
    nullptr
  };
  return names;
}

inline const char *EnumNameLayoutReason(LayoutReason e) {
  if (::flatbuffers::IsOutRange(e, LayoutReason_BatchDidComplete, LayoutReason_WXSSetStyle)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesLayoutReason()[index];
}

enum CommandValue : uint8_t {
  CommandValue_NONE = 0,
  CommandValue_CreateViewCommand = 1,
  CommandValue_UpdateViewCommand = 2,
  CommandValue_UpdateViewFrameCommand = 3,
  CommandValue_DeleteViewsCommand = 4,
  CommandValue_InsertChildViewsCommand = 5,
  CommandValue_RemoveChildViewsCommand = 6,
  CommandValue_UpdateViewStyleCommamnd = 7,
  CommandValue_UpdateTransformCommand = 8,
  CommandValue_UpdateTextCommamnd = 9,
  CommandValue_BatchDidFinishCommand = 10,
  CommandValue_CreateKeyframesAnimationCommand = 11,
  CommandValue_ClearKeyframesAnimationCommand = 12,
  CommandValue_MIN = CommandValue_NONE,
  CommandValue_MAX = CommandValue_ClearKeyframesAnimationCommand
};

inline const CommandValue (&EnumValuesCommandValue())[13] {
  static const CommandValue values[] = {
    CommandValue_NONE,
    CommandValue_CreateViewCommand,
    CommandValue_UpdateViewCommand,
    CommandValue_UpdateViewFrameCommand,
    CommandValue_DeleteViewsCommand,
    CommandValue_InsertChildViewsCommand,
    CommandValue_RemoveChildViewsCommand,
    CommandValue_UpdateViewStyleCommamnd,
    CommandValue_UpdateTransformCommand,
    CommandValue_UpdateTextCommamnd,
    CommandValue_BatchDidFinishCommand,
    CommandValue_CreateKeyframesAnimationCommand,
    CommandValue_ClearKeyframesAnimationCommand
  };
  return values;
}

inline const char * const *EnumNamesCommandValue() {
  static const char * const names[14] = {
    "NONE",
    "CreateViewCommand",
    "UpdateViewCommand",
    "UpdateViewFrameCommand",
    "DeleteViewsCommand",
    "InsertChildViewsCommand",
    "RemoveChildViewsCommand",
    "UpdateViewStyleCommamnd",
    "UpdateTransformCommand",
    "UpdateTextCommamnd",
    "BatchDidFinishCommand",
    "CreateKeyframesAnimationCommand",
    "ClearKeyframesAnimationCommand",
    nullptr
  };
  return names;
}

inline const char *EnumNameCommandValue(CommandValue e) {
  if (::flatbuffers::IsOutRange(e, CommandValue_NONE, CommandValue_ClearKeyframesAnimationCommand)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesCommandValue()[index];
}

template<typename T> struct CommandValueTraits {
  static const CommandValue enum_value = CommandValue_NONE;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::CreateViewCommand> {
  static const CommandValue enum_value = CommandValue_CreateViewCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::UpdateViewCommand> {
  static const CommandValue enum_value = CommandValue_UpdateViewCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand> {
  static const CommandValue enum_value = CommandValue_UpdateViewFrameCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::DeleteViewsCommand> {
  static const CommandValue enum_value = CommandValue_DeleteViewsCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::InsertChildViewsCommand> {
  static const CommandValue enum_value = CommandValue_InsertChildViewsCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand> {
  static const CommandValue enum_value = CommandValue_RemoveChildViewsCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd> {
  static const CommandValue enum_value = CommandValue_UpdateViewStyleCommamnd;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::UpdateTransformCommand> {
  static const CommandValue enum_value = CommandValue_UpdateTransformCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::UpdateTextCommamnd> {
  static const CommandValue enum_value = CommandValue_UpdateTextCommamnd;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::BatchDidFinishCommand> {
  static const CommandValue enum_value = CommandValue_BatchDidFinishCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand> {
  static const CommandValue enum_value = CommandValue_CreateKeyframesAnimationCommand;
};

template<> struct CommandValueTraits<com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand> {
  static const CommandValue enum_value = CommandValue_ClearKeyframesAnimationCommand;
};

bool VerifyCommandValue(::flatbuffers::Verifier &verifier, const void *obj, CommandValue type);
bool VerifyCommandValueVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct CreateViewCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CreateViewCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_PROPS = 6
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *props() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *>(VT_PROPS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_PROPS) &&
           verifier.VerifyVector(props()) &&
           verifier.VerifyVectorOfTables(props()) &&
           verifier.EndTable();
  }
};

struct CreateViewCommandBuilder {
  typedef CreateViewCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(CreateViewCommand::VT_NAME, name);
  }
  void add_props(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> props) {
    fbb_.AddOffset(CreateViewCommand::VT_PROPS, props);
  }
  explicit CreateViewCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CreateViewCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CreateViewCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CreateViewCommand> CreateCreateViewCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> props = 0) {
  CreateViewCommandBuilder builder_(_fbb);
  builder_.add_props(props);
  builder_.add_name(name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<CreateViewCommand> CreateCreateViewCommandDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *props = nullptr) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto props__ = props ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>(*props) : 0;
  return com::meituan::android::msc::renderer::generated::CreateCreateViewCommand(
      _fbb,
      name__,
      props__);
}

struct UpdateViewCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UpdateViewCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_NAME = 4,
    VT_PROPS = 6
  };
  const ::flatbuffers::String *name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_NAME);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *props() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *>(VT_PROPS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_NAME) &&
           verifier.VerifyString(name()) &&
           VerifyOffset(verifier, VT_PROPS) &&
           verifier.VerifyVector(props()) &&
           verifier.VerifyVectorOfTables(props()) &&
           verifier.EndTable();
  }
};

struct UpdateViewCommandBuilder {
  typedef UpdateViewCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_name(::flatbuffers::Offset<::flatbuffers::String> name) {
    fbb_.AddOffset(UpdateViewCommand::VT_NAME, name);
  }
  void add_props(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> props) {
    fbb_.AddOffset(UpdateViewCommand::VT_PROPS, props);
  }
  explicit UpdateViewCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UpdateViewCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UpdateViewCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UpdateViewCommand> CreateUpdateViewCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> name = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> props = 0) {
  UpdateViewCommandBuilder builder_(_fbb);
  builder_.add_props(props);
  builder_.add_name(name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<UpdateViewCommand> CreateUpdateViewCommandDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *name = nullptr,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *props = nullptr) {
  auto name__ = name ? _fbb.CreateString(name) : 0;
  auto props__ = props ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>(*props) : 0;
  return com::meituan::android::msc::renderer::generated::CreateUpdateViewCommand(
      _fbb,
      name__,
      props__);
}

struct DeleteViewsCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DeleteViewsCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TAGS = 4
  };
  const ::flatbuffers::Vector<int32_t> *tags() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_TAGS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TAGS) &&
           verifier.VerifyVector(tags()) &&
           verifier.EndTable();
  }
};

struct DeleteViewsCommandBuilder {
  typedef DeleteViewsCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tags(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> tags) {
    fbb_.AddOffset(DeleteViewsCommand::VT_TAGS, tags);
  }
  explicit DeleteViewsCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DeleteViewsCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DeleteViewsCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DeleteViewsCommand> CreateDeleteViewsCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> tags = 0) {
  DeleteViewsCommandBuilder builder_(_fbb);
  builder_.add_tags(tags);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DeleteViewsCommand> CreateDeleteViewsCommandDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *tags = nullptr) {
  auto tags__ = tags ? _fbb.CreateVector<int32_t>(*tags) : 0;
  return com::meituan::android::msc::renderer::generated::CreateDeleteViewsCommand(
      _fbb,
      tags__);
}

struct InsertChildViewsCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef InsertChildViewsCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_INSERT = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ViewAtIndex>> *insert() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ViewAtIndex>> *>(VT_INSERT);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_INSERT) &&
           verifier.VerifyVector(insert()) &&
           verifier.VerifyVectorOfTables(insert()) &&
           verifier.EndTable();
  }
};

struct InsertChildViewsCommandBuilder {
  typedef InsertChildViewsCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_insert(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ViewAtIndex>>> insert) {
    fbb_.AddOffset(InsertChildViewsCommand::VT_INSERT, insert);
  }
  explicit InsertChildViewsCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<InsertChildViewsCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<InsertChildViewsCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<InsertChildViewsCommand> CreateInsertChildViewsCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ViewAtIndex>>> insert = 0) {
  InsertChildViewsCommandBuilder builder_(_fbb);
  builder_.add_insert(insert);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<InsertChildViewsCommand> CreateInsertChildViewsCommandDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ViewAtIndex>> *insert = nullptr) {
  auto insert__ = insert ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ViewAtIndex>>(*insert) : 0;
  return com::meituan::android::msc::renderer::generated::CreateInsertChildViewsCommand(
      _fbb,
      insert__);
}

struct RemoveChildViewsCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef RemoveChildViewsCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_REMOVE = 4
  };
  const ::flatbuffers::Vector<int32_t> *remove() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_REMOVE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_REMOVE) &&
           verifier.VerifyVector(remove()) &&
           verifier.EndTable();
  }
};

struct RemoveChildViewsCommandBuilder {
  typedef RemoveChildViewsCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_remove(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> remove) {
    fbb_.AddOffset(RemoveChildViewsCommand::VT_REMOVE, remove);
  }
  explicit RemoveChildViewsCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<RemoveChildViewsCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<RemoveChildViewsCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<RemoveChildViewsCommand> CreateRemoveChildViewsCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> remove = 0) {
  RemoveChildViewsCommandBuilder builder_(_fbb);
  builder_.add_remove(remove);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<RemoveChildViewsCommand> CreateRemoveChildViewsCommandDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *remove = nullptr) {
  auto remove__ = remove ? _fbb.CreateVector<int32_t>(*remove) : 0;
  return com::meituan::android::msc::renderer::generated::CreateRemoveChildViewsCommand(
      _fbb,
      remove__);
}

struct UpdateViewFrameCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UpdateViewFrameCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_PARENTTAG = 4,
    VT_X = 6,
    VT_Y = 8,
    VT_W = 10,
    VT_H = 12
  };
  int32_t parentTag() const {
    return GetField<int32_t>(VT_PARENTTAG, 0);
  }
  float x() const {
    return GetField<float>(VT_X, 0.0f);
  }
  float y() const {
    return GetField<float>(VT_Y, 0.0f);
  }
  float w() const {
    return GetField<float>(VT_W, 0.0f);
  }
  float h() const {
    return GetField<float>(VT_H, 0.0f);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_PARENTTAG, 4) &&
           VerifyField<float>(verifier, VT_X, 4) &&
           VerifyField<float>(verifier, VT_Y, 4) &&
           VerifyField<float>(verifier, VT_W, 4) &&
           VerifyField<float>(verifier, VT_H, 4) &&
           verifier.EndTable();
  }
};

struct UpdateViewFrameCommandBuilder {
  typedef UpdateViewFrameCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_parentTag(int32_t parentTag) {
    fbb_.AddElement<int32_t>(UpdateViewFrameCommand::VT_PARENTTAG, parentTag, 0);
  }
  void add_x(float x) {
    fbb_.AddElement<float>(UpdateViewFrameCommand::VT_X, x, 0.0f);
  }
  void add_y(float y) {
    fbb_.AddElement<float>(UpdateViewFrameCommand::VT_Y, y, 0.0f);
  }
  void add_w(float w) {
    fbb_.AddElement<float>(UpdateViewFrameCommand::VT_W, w, 0.0f);
  }
  void add_h(float h) {
    fbb_.AddElement<float>(UpdateViewFrameCommand::VT_H, h, 0.0f);
  }
  explicit UpdateViewFrameCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UpdateViewFrameCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UpdateViewFrameCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UpdateViewFrameCommand> CreateUpdateViewFrameCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t parentTag = 0,
    float x = 0.0f,
    float y = 0.0f,
    float w = 0.0f,
    float h = 0.0f) {
  UpdateViewFrameCommandBuilder builder_(_fbb);
  builder_.add_h(h);
  builder_.add_w(w);
  builder_.add_y(y);
  builder_.add_x(x);
  builder_.add_parentTag(parentTag);
  return builder_.Finish();
}

struct UpdateViewStyleCommamnd FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UpdateViewStyleCommamndBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VAL = 4
  };
  const com::meituan::android::msc::renderer::generated::DisplayItem *val() const {
    return GetPointer<const com::meituan::android::msc::renderer::generated::DisplayItem *>(VT_VAL);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VAL) &&
           verifier.VerifyTable(val()) &&
           verifier.EndTable();
  }
};

struct UpdateViewStyleCommamndBuilder {
  typedef UpdateViewStyleCommamnd Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_val(::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::DisplayItem> val) {
    fbb_.AddOffset(UpdateViewStyleCommamnd::VT_VAL, val);
  }
  explicit UpdateViewStyleCommamndBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UpdateViewStyleCommamnd> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UpdateViewStyleCommamnd>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UpdateViewStyleCommamnd> CreateUpdateViewStyleCommamnd(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::DisplayItem> val = 0) {
  UpdateViewStyleCommamndBuilder builder_(_fbb);
  builder_.add_val(val);
  return builder_.Finish();
}

struct UpdateTransformCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UpdateTransformCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TRANSFORM = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *transform() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *>(VT_TRANSFORM);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TRANSFORM) &&
           verifier.VerifyVector(transform()) &&
           verifier.VerifyVectorOfTables(transform()) &&
           verifier.EndTable();
  }
};

struct UpdateTransformCommandBuilder {
  typedef UpdateTransformCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_transform(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> transform) {
    fbb_.AddOffset(UpdateTransformCommand::VT_TRANSFORM, transform);
  }
  explicit UpdateTransformCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UpdateTransformCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UpdateTransformCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UpdateTransformCommand> CreateUpdateTransformCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> transform = 0) {
  UpdateTransformCommandBuilder builder_(_fbb);
  builder_.add_transform(transform);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<UpdateTransformCommand> CreateUpdateTransformCommandDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *transform = nullptr) {
  auto transform__ = transform ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>(*transform) : 0;
  return com::meituan::android::msc::renderer::generated::CreateUpdateTransformCommand(
      _fbb,
      transform__);
}

struct UpdateTextCommamnd FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef UpdateTextCommamndBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_X = 4,
    VT_Y = 6,
    VT_W = 8,
    VT_H = 10,
    VT_TEXTPADDINGTOP = 12,
    VT_TEXTPADDINGLEFT = 14,
    VT_TEXTPADDINGRIGHT = 16,
    VT_TEXTPADDINGBOTTOM = 18
  };
  float x() const {
    return GetField<float>(VT_X, 0.0f);
  }
  float y() const {
    return GetField<float>(VT_Y, 0.0f);
  }
  float w() const {
    return GetField<float>(VT_W, 0.0f);
  }
  float h() const {
    return GetField<float>(VT_H, 0.0f);
  }
  float textPaddingTop() const {
    return GetField<float>(VT_TEXTPADDINGTOP, 0.0f);
  }
  float textPaddingLeft() const {
    return GetField<float>(VT_TEXTPADDINGLEFT, 0.0f);
  }
  float textPaddingRight() const {
    return GetField<float>(VT_TEXTPADDINGRIGHT, 0.0f);
  }
  float textPaddingBottom() const {
    return GetField<float>(VT_TEXTPADDINGBOTTOM, 0.0f);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_X, 4) &&
           VerifyField<float>(verifier, VT_Y, 4) &&
           VerifyField<float>(verifier, VT_W, 4) &&
           VerifyField<float>(verifier, VT_H, 4) &&
           VerifyField<float>(verifier, VT_TEXTPADDINGTOP, 4) &&
           VerifyField<float>(verifier, VT_TEXTPADDINGLEFT, 4) &&
           VerifyField<float>(verifier, VT_TEXTPADDINGRIGHT, 4) &&
           VerifyField<float>(verifier, VT_TEXTPADDINGBOTTOM, 4) &&
           verifier.EndTable();
  }
};

struct UpdateTextCommamndBuilder {
  typedef UpdateTextCommamnd Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_x(float x) {
    fbb_.AddElement<float>(UpdateTextCommamnd::VT_X, x, 0.0f);
  }
  void add_y(float y) {
    fbb_.AddElement<float>(UpdateTextCommamnd::VT_Y, y, 0.0f);
  }
  void add_w(float w) {
    fbb_.AddElement<float>(UpdateTextCommamnd::VT_W, w, 0.0f);
  }
  void add_h(float h) {
    fbb_.AddElement<float>(UpdateTextCommamnd::VT_H, h, 0.0f);
  }
  void add_textPaddingTop(float textPaddingTop) {
    fbb_.AddElement<float>(UpdateTextCommamnd::VT_TEXTPADDINGTOP, textPaddingTop, 0.0f);
  }
  void add_textPaddingLeft(float textPaddingLeft) {
    fbb_.AddElement<float>(UpdateTextCommamnd::VT_TEXTPADDINGLEFT, textPaddingLeft, 0.0f);
  }
  void add_textPaddingRight(float textPaddingRight) {
    fbb_.AddElement<float>(UpdateTextCommamnd::VT_TEXTPADDINGRIGHT, textPaddingRight, 0.0f);
  }
  void add_textPaddingBottom(float textPaddingBottom) {
    fbb_.AddElement<float>(UpdateTextCommamnd::VT_TEXTPADDINGBOTTOM, textPaddingBottom, 0.0f);
  }
  explicit UpdateTextCommamndBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<UpdateTextCommamnd> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<UpdateTextCommamnd>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<UpdateTextCommamnd> CreateUpdateTextCommamnd(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    float x = 0.0f,
    float y = 0.0f,
    float w = 0.0f,
    float h = 0.0f,
    float textPaddingTop = 0.0f,
    float textPaddingLeft = 0.0f,
    float textPaddingRight = 0.0f,
    float textPaddingBottom = 0.0f) {
  UpdateTextCommamndBuilder builder_(_fbb);
  builder_.add_textPaddingBottom(textPaddingBottom);
  builder_.add_textPaddingRight(textPaddingRight);
  builder_.add_textPaddingLeft(textPaddingLeft);
  builder_.add_textPaddingTop(textPaddingTop);
  builder_.add_h(h);
  builder_.add_w(w);
  builder_.add_y(y);
  builder_.add_x(x);
  return builder_.Finish();
}

struct BatchDidFinishCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BatchDidFinishCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VAL = 4
  };
  com::meituan::android::msc::renderer::generated::LayoutReason val() const {
    return static_cast<com::meituan::android::msc::renderer::generated::LayoutReason>(GetField<int8_t>(VT_VAL, 0));
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int8_t>(verifier, VT_VAL, 1) &&
           verifier.EndTable();
  }
};

struct BatchDidFinishCommandBuilder {
  typedef BatchDidFinishCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_val(com::meituan::android::msc::renderer::generated::LayoutReason val) {
    fbb_.AddElement<int8_t>(BatchDidFinishCommand::VT_VAL, static_cast<int8_t>(val), 0);
  }
  explicit BatchDidFinishCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BatchDidFinishCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BatchDidFinishCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BatchDidFinishCommand> CreateBatchDidFinishCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    com::meituan::android::msc::renderer::generated::LayoutReason val = com::meituan::android::msc::renderer::generated::LayoutReason_BatchDidComplete) {
  BatchDidFinishCommandBuilder builder_(_fbb);
  builder_.add_val(val);
  return builder_.Finish();
}

struct CreateKeyframesAnimationCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CreateKeyframesAnimationCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TAGS = 4,
    VT_KEYFRAMES = 6,
    VT_DURATION = 8,
    VT_CALLBACK = 10
  };
  const ::flatbuffers::Vector<int32_t> *tags() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_TAGS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::CreateAnimationKeyFrame>> *keyframes() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::CreateAnimationKeyFrame>> *>(VT_KEYFRAMES);
  }
  int32_t duration() const {
    return GetField<int32_t>(VT_DURATION, 0);
  }
  ::flatbuffers::Optional<uint64_t> callback() const {
    return GetOptional<uint64_t, uint64_t>(VT_CALLBACK);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TAGS) &&
           verifier.VerifyVector(tags()) &&
           VerifyOffset(verifier, VT_KEYFRAMES) &&
           verifier.VerifyVector(keyframes()) &&
           verifier.VerifyVectorOfTables(keyframes()) &&
           VerifyField<int32_t>(verifier, VT_DURATION, 4) &&
           VerifyField<uint64_t>(verifier, VT_CALLBACK, 8) &&
           verifier.EndTable();
  }
};

struct CreateKeyframesAnimationCommandBuilder {
  typedef CreateKeyframesAnimationCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tags(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> tags) {
    fbb_.AddOffset(CreateKeyframesAnimationCommand::VT_TAGS, tags);
  }
  void add_keyframes(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::CreateAnimationKeyFrame>>> keyframes) {
    fbb_.AddOffset(CreateKeyframesAnimationCommand::VT_KEYFRAMES, keyframes);
  }
  void add_duration(int32_t duration) {
    fbb_.AddElement<int32_t>(CreateKeyframesAnimationCommand::VT_DURATION, duration, 0);
  }
  void add_callback(uint64_t callback) {
    fbb_.AddElement<uint64_t>(CreateKeyframesAnimationCommand::VT_CALLBACK, callback);
  }
  explicit CreateKeyframesAnimationCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CreateKeyframesAnimationCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CreateKeyframesAnimationCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CreateKeyframesAnimationCommand> CreateCreateKeyframesAnimationCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> tags = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::CreateAnimationKeyFrame>>> keyframes = 0,
    int32_t duration = 0,
    ::flatbuffers::Optional<uint64_t> callback = ::flatbuffers::nullopt) {
  CreateKeyframesAnimationCommandBuilder builder_(_fbb);
  if(callback) { builder_.add_callback(*callback); }
  builder_.add_duration(duration);
  builder_.add_keyframes(keyframes);
  builder_.add_tags(tags);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<CreateKeyframesAnimationCommand> CreateCreateKeyframesAnimationCommandDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *tags = nullptr,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::CreateAnimationKeyFrame>> *keyframes = nullptr,
    int32_t duration = 0,
    ::flatbuffers::Optional<uint64_t> callback = ::flatbuffers::nullopt) {
  auto tags__ = tags ? _fbb.CreateVector<int32_t>(*tags) : 0;
  auto keyframes__ = keyframes ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::CreateAnimationKeyFrame>>(*keyframes) : 0;
  return com::meituan::android::msc::renderer::generated::CreateCreateKeyframesAnimationCommand(
      _fbb,
      tags__,
      keyframes__,
      duration,
      callback);
}

struct ClearKeyframesAnimationCommand FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ClearKeyframesAnimationCommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TAGS = 4,
    VT_OPTIONS = 6,
    VT_CALLBACK = 8
  };
  const ::flatbuffers::Vector<int32_t> *tags() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_TAGS);
  }
  const com::meituan::android::msc::renderer::generated::ClearAnimationOptions *options() const {
    return GetPointer<const com::meituan::android::msc::renderer::generated::ClearAnimationOptions *>(VT_OPTIONS);
  }
  ::flatbuffers::Optional<uint64_t> callback() const {
    return GetOptional<uint64_t, uint64_t>(VT_CALLBACK);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TAGS) &&
           verifier.VerifyVector(tags()) &&
           VerifyOffset(verifier, VT_OPTIONS) &&
           verifier.VerifyTable(options()) &&
           VerifyField<uint64_t>(verifier, VT_CALLBACK, 8) &&
           verifier.EndTable();
  }
};

struct ClearKeyframesAnimationCommandBuilder {
  typedef ClearKeyframesAnimationCommand Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tags(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> tags) {
    fbb_.AddOffset(ClearKeyframesAnimationCommand::VT_TAGS, tags);
  }
  void add_options(::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ClearAnimationOptions> options) {
    fbb_.AddOffset(ClearKeyframesAnimationCommand::VT_OPTIONS, options);
  }
  void add_callback(uint64_t callback) {
    fbb_.AddElement<uint64_t>(ClearKeyframesAnimationCommand::VT_CALLBACK, callback);
  }
  explicit ClearKeyframesAnimationCommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ClearKeyframesAnimationCommand> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ClearKeyframesAnimationCommand>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ClearKeyframesAnimationCommand> CreateClearKeyframesAnimationCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> tags = 0,
    ::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ClearAnimationOptions> options = 0,
    ::flatbuffers::Optional<uint64_t> callback = ::flatbuffers::nullopt) {
  ClearKeyframesAnimationCommandBuilder builder_(_fbb);
  if(callback) { builder_.add_callback(*callback); }
  builder_.add_options(options);
  builder_.add_tags(tags);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<ClearKeyframesAnimationCommand> CreateClearKeyframesAnimationCommandDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *tags = nullptr,
    ::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::ClearAnimationOptions> options = 0,
    ::flatbuffers::Optional<uint64_t> callback = ::flatbuffers::nullopt) {
  auto tags__ = tags ? _fbb.CreateVector<int32_t>(*tags) : 0;
  return com::meituan::android::msc::renderer::generated::CreateClearKeyframesAnimationCommand(
      _fbb,
      tags__,
      options,
      callback);
}

struct CreateAnimationKeyFrame FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CreateAnimationKeyFrameBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OFFSET = 4,
    VT_EASE = 6,
    VT_OPACITY = 8,
    VT_ROTATE = 10,
    VT_SCALE = 12,
    VT_TRANSLATE = 14,
    VT_WIDTH = 16,
    VT_WIDTH_STR = 18
  };
  ::flatbuffers::Optional<float> offset() const {
    return GetOptional<float, float>(VT_OFFSET);
  }
  const ::flatbuffers::String *ease() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EASE);
  }
  ::flatbuffers::Optional<float> opacity() const {
    return GetOptional<float, float>(VT_OPACITY);
  }
  ::flatbuffers::Optional<float> rotate() const {
    return GetOptional<float, float>(VT_ROTATE);
  }
  const ::flatbuffers::Vector<float> *scale() const {
    return GetPointer<const ::flatbuffers::Vector<float> *>(VT_SCALE);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *translate() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_TRANSLATE);
  }
  ::flatbuffers::Optional<float> width() const {
    return GetOptional<float, float>(VT_WIDTH);
  }
  const ::flatbuffers::String *width_str() const {
    return GetPointer<const ::flatbuffers::String *>(VT_WIDTH_STR);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_OFFSET, 4) &&
           VerifyOffset(verifier, VT_EASE) &&
           verifier.VerifyString(ease()) &&
           VerifyField<float>(verifier, VT_OPACITY, 4) &&
           VerifyField<float>(verifier, VT_ROTATE, 4) &&
           VerifyOffset(verifier, VT_SCALE) &&
           verifier.VerifyVector(scale()) &&
           VerifyOffset(verifier, VT_TRANSLATE) &&
           verifier.VerifyVector(translate()) &&
           verifier.VerifyVectorOfStrings(translate()) &&
           VerifyField<float>(verifier, VT_WIDTH, 4) &&
           VerifyOffset(verifier, VT_WIDTH_STR) &&
           verifier.VerifyString(width_str()) &&
           verifier.EndTable();
  }
};

struct CreateAnimationKeyFrameBuilder {
  typedef CreateAnimationKeyFrame Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_offset(float offset) {
    fbb_.AddElement<float>(CreateAnimationKeyFrame::VT_OFFSET, offset);
  }
  void add_ease(::flatbuffers::Offset<::flatbuffers::String> ease) {
    fbb_.AddOffset(CreateAnimationKeyFrame::VT_EASE, ease);
  }
  void add_opacity(float opacity) {
    fbb_.AddElement<float>(CreateAnimationKeyFrame::VT_OPACITY, opacity);
  }
  void add_rotate(float rotate) {
    fbb_.AddElement<float>(CreateAnimationKeyFrame::VT_ROTATE, rotate);
  }
  void add_scale(::flatbuffers::Offset<::flatbuffers::Vector<float>> scale) {
    fbb_.AddOffset(CreateAnimationKeyFrame::VT_SCALE, scale);
  }
  void add_translate(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> translate) {
    fbb_.AddOffset(CreateAnimationKeyFrame::VT_TRANSLATE, translate);
  }
  void add_width(float width) {
    fbb_.AddElement<float>(CreateAnimationKeyFrame::VT_WIDTH, width);
  }
  void add_width_str(::flatbuffers::Offset<::flatbuffers::String> width_str) {
    fbb_.AddOffset(CreateAnimationKeyFrame::VT_WIDTH_STR, width_str);
  }
  explicit CreateAnimationKeyFrameBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CreateAnimationKeyFrame> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CreateAnimationKeyFrame>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CreateAnimationKeyFrame> CreateCreateAnimationKeyFrame(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Optional<float> offset = ::flatbuffers::nullopt,
    ::flatbuffers::Offset<::flatbuffers::String> ease = 0,
    ::flatbuffers::Optional<float> opacity = ::flatbuffers::nullopt,
    ::flatbuffers::Optional<float> rotate = ::flatbuffers::nullopt,
    ::flatbuffers::Offset<::flatbuffers::Vector<float>> scale = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> translate = 0,
    ::flatbuffers::Optional<float> width = ::flatbuffers::nullopt,
    ::flatbuffers::Offset<::flatbuffers::String> width_str = 0) {
  CreateAnimationKeyFrameBuilder builder_(_fbb);
  builder_.add_width_str(width_str);
  if(width) { builder_.add_width(*width); }
  builder_.add_translate(translate);
  builder_.add_scale(scale);
  if(rotate) { builder_.add_rotate(*rotate); }
  if(opacity) { builder_.add_opacity(*opacity); }
  builder_.add_ease(ease);
  if(offset) { builder_.add_offset(*offset); }
  return builder_.Finish();
}

inline ::flatbuffers::Offset<CreateAnimationKeyFrame> CreateCreateAnimationKeyFrameDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Optional<float> offset = ::flatbuffers::nullopt,
    const char *ease = nullptr,
    ::flatbuffers::Optional<float> opacity = ::flatbuffers::nullopt,
    ::flatbuffers::Optional<float> rotate = ::flatbuffers::nullopt,
    const std::vector<float> *scale = nullptr,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *translate = nullptr,
    ::flatbuffers::Optional<float> width = ::flatbuffers::nullopt,
    const char *width_str = nullptr) {
  auto ease__ = ease ? _fbb.CreateString(ease) : 0;
  auto scale__ = scale ? _fbb.CreateVector<float>(*scale) : 0;
  auto translate__ = translate ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*translate) : 0;
  auto width_str__ = width_str ? _fbb.CreateString(width_str) : 0;
  return com::meituan::android::msc::renderer::generated::CreateCreateAnimationKeyFrame(
      _fbb,
      offset,
      ease__,
      opacity,
      rotate,
      scale__,
      translate__,
      width,
      width_str__);
}

struct ClearAnimationOptions FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ClearAnimationOptionsBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_OPACITY = 4,
    VT_SCALE = 6,
    VT_TRANSLATE = 8,
    VT_ROTATE = 10
  };
  ::flatbuffers::Optional<bool> opacity() const {
    return GetOptional<uint8_t, bool>(VT_OPACITY);
  }
  ::flatbuffers::Optional<bool> scale() const {
    return GetOptional<uint8_t, bool>(VT_SCALE);
  }
  ::flatbuffers::Optional<bool> translate() const {
    return GetOptional<uint8_t, bool>(VT_TRANSLATE);
  }
  ::flatbuffers::Optional<bool> rotate() const {
    return GetOptional<uint8_t, bool>(VT_ROTATE);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_OPACITY, 1) &&
           VerifyField<uint8_t>(verifier, VT_SCALE, 1) &&
           VerifyField<uint8_t>(verifier, VT_TRANSLATE, 1) &&
           VerifyField<uint8_t>(verifier, VT_ROTATE, 1) &&
           verifier.EndTable();
  }
};

struct ClearAnimationOptionsBuilder {
  typedef ClearAnimationOptions Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_opacity(bool opacity) {
    fbb_.AddElement<uint8_t>(ClearAnimationOptions::VT_OPACITY, static_cast<uint8_t>(opacity));
  }
  void add_scale(bool scale) {
    fbb_.AddElement<uint8_t>(ClearAnimationOptions::VT_SCALE, static_cast<uint8_t>(scale));
  }
  void add_translate(bool translate) {
    fbb_.AddElement<uint8_t>(ClearAnimationOptions::VT_TRANSLATE, static_cast<uint8_t>(translate));
  }
  void add_rotate(bool rotate) {
    fbb_.AddElement<uint8_t>(ClearAnimationOptions::VT_ROTATE, static_cast<uint8_t>(rotate));
  }
  explicit ClearAnimationOptionsBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ClearAnimationOptions> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ClearAnimationOptions>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ClearAnimationOptions> CreateClearAnimationOptions(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Optional<bool> opacity = ::flatbuffers::nullopt,
    ::flatbuffers::Optional<bool> scale = ::flatbuffers::nullopt,
    ::flatbuffers::Optional<bool> translate = ::flatbuffers::nullopt,
    ::flatbuffers::Optional<bool> rotate = ::flatbuffers::nullopt) {
  ClearAnimationOptionsBuilder builder_(_fbb);
  if(rotate) { builder_.add_rotate(*rotate); }
  if(translate) { builder_.add_translate(*translate); }
  if(scale) { builder_.add_scale(*scale); }
  if(opacity) { builder_.add_opacity(*opacity); }
  return builder_.Finish();
}

struct ViewAtIndex FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ViewAtIndexBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TAG = 4,
    VT_INDEX = 6
  };
  int32_t tag() const {
    return GetField<int32_t>(VT_TAG, 0);
  }
  int32_t index() const {
    return GetField<int32_t>(VT_INDEX, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_TAG, 4) &&
           VerifyField<int32_t>(verifier, VT_INDEX, 4) &&
           verifier.EndTable();
  }
};

struct ViewAtIndexBuilder {
  typedef ViewAtIndex Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tag(int32_t tag) {
    fbb_.AddElement<int32_t>(ViewAtIndex::VT_TAG, tag, 0);
  }
  void add_index(int32_t index) {
    fbb_.AddElement<int32_t>(ViewAtIndex::VT_INDEX, index, 0);
  }
  explicit ViewAtIndexBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ViewAtIndex> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ViewAtIndex>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ViewAtIndex> CreateViewAtIndex(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t tag = 0,
    int32_t index = 0) {
  ViewAtIndexBuilder builder_(_fbb);
  builder_.add_index(index);
  builder_.add_tag(tag);
  return builder_.Finish();
}

struct Command FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CommandBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_ID = 4,
    VT_TAG = 6,
    VT_VALUE_TYPE_TYPE = 8,
    VT_VALUE_TYPE = 10,
    VT_VAL_TYPE = 12,
    VT_VAL = 14
  };
  int32_t id() const {
    return GetField<int32_t>(VT_ID, 0);
  }
  int32_t tag() const {
    return GetField<int32_t>(VT_TAG, 0);
  }
  com::meituan::android::msc::renderer::generated::CommandValue value_type_type() const {
    return static_cast<com::meituan::android::msc::renderer::generated::CommandValue>(GetField<uint8_t>(VT_VALUE_TYPE_TYPE, 0));
  }
  const void *value_type() const {
    return GetPointer<const void *>(VT_VALUE_TYPE);
  }
  template<typename T> const T *value_type_as() const;
  const com::meituan::android::msc::renderer::generated::CreateViewCommand *value_type_as_CreateViewCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_CreateViewCommand ? static_cast<const com::meituan::android::msc::renderer::generated::CreateViewCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateViewCommand *value_type_as_UpdateViewCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateViewCommand ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateViewCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand *value_type_as_UpdateViewFrameCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateViewFrameCommand ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::DeleteViewsCommand *value_type_as_DeleteViewsCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_DeleteViewsCommand ? static_cast<const com::meituan::android::msc::renderer::generated::DeleteViewsCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::InsertChildViewsCommand *value_type_as_InsertChildViewsCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_InsertChildViewsCommand ? static_cast<const com::meituan::android::msc::renderer::generated::InsertChildViewsCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand *value_type_as_RemoveChildViewsCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_RemoveChildViewsCommand ? static_cast<const com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd *value_type_as_UpdateViewStyleCommamnd() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateViewStyleCommamnd ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateTransformCommand *value_type_as_UpdateTransformCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateTransformCommand ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateTransformCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateTextCommamnd *value_type_as_UpdateTextCommamnd() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateTextCommamnd ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateTextCommamnd *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::BatchDidFinishCommand *value_type_as_BatchDidFinishCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_BatchDidFinishCommand ? static_cast<const com::meituan::android::msc::renderer::generated::BatchDidFinishCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand *value_type_as_CreateKeyframesAnimationCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_CreateKeyframesAnimationCommand ? static_cast<const com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand *value_type_as_ClearKeyframesAnimationCommand() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::CommandValue_ClearKeyframesAnimationCommand ? static_cast<const com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand *>(value_type()) : nullptr;
  }
  com::meituan::android::msc::renderer::generated::CommandValue val_type() const {
    return static_cast<com::meituan::android::msc::renderer::generated::CommandValue>(GetField<uint8_t>(VT_VAL_TYPE, 0));
  }
  const void *val() const {
    return GetPointer<const void *>(VT_VAL);
  }
  template<typename T> const T *val_as() const;
  const com::meituan::android::msc::renderer::generated::CreateViewCommand *val_as_CreateViewCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_CreateViewCommand ? static_cast<const com::meituan::android::msc::renderer::generated::CreateViewCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateViewCommand *val_as_UpdateViewCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateViewCommand ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateViewCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand *val_as_UpdateViewFrameCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateViewFrameCommand ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::DeleteViewsCommand *val_as_DeleteViewsCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_DeleteViewsCommand ? static_cast<const com::meituan::android::msc::renderer::generated::DeleteViewsCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::InsertChildViewsCommand *val_as_InsertChildViewsCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_InsertChildViewsCommand ? static_cast<const com::meituan::android::msc::renderer::generated::InsertChildViewsCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand *val_as_RemoveChildViewsCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_RemoveChildViewsCommand ? static_cast<const com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd *val_as_UpdateViewStyleCommamnd() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateViewStyleCommamnd ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateTransformCommand *val_as_UpdateTransformCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateTransformCommand ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateTransformCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::UpdateTextCommamnd *val_as_UpdateTextCommamnd() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_UpdateTextCommamnd ? static_cast<const com::meituan::android::msc::renderer::generated::UpdateTextCommamnd *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::BatchDidFinishCommand *val_as_BatchDidFinishCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_BatchDidFinishCommand ? static_cast<const com::meituan::android::msc::renderer::generated::BatchDidFinishCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand *val_as_CreateKeyframesAnimationCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_CreateKeyframesAnimationCommand ? static_cast<const com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand *val_as_ClearKeyframesAnimationCommand() const {
    return val_type() == com::meituan::android::msc::renderer::generated::CommandValue_ClearKeyframesAnimationCommand ? static_cast<const com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand *>(val()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_ID, 4) &&
           VerifyField<int32_t>(verifier, VT_TAG, 4) &&
           VerifyField<uint8_t>(verifier, VT_VALUE_TYPE_TYPE, 1) &&
           VerifyOffset(verifier, VT_VALUE_TYPE) &&
           VerifyCommandValue(verifier, value_type(), value_type_type()) &&
           VerifyField<uint8_t>(verifier, VT_VAL_TYPE, 1) &&
           VerifyOffset(verifier, VT_VAL) &&
           VerifyCommandValue(verifier, val(), val_type()) &&
           verifier.EndTable();
  }
};

template<> inline const com::meituan::android::msc::renderer::generated::CreateViewCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::CreateViewCommand>() const {
  return value_type_as_CreateViewCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateViewCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::UpdateViewCommand>() const {
  return value_type_as_UpdateViewCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand>() const {
  return value_type_as_UpdateViewFrameCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::DeleteViewsCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::DeleteViewsCommand>() const {
  return value_type_as_DeleteViewsCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::InsertChildViewsCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::InsertChildViewsCommand>() const {
  return value_type_as_InsertChildViewsCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand>() const {
  return value_type_as_RemoveChildViewsCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd *Command::value_type_as<com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd>() const {
  return value_type_as_UpdateViewStyleCommamnd();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateTransformCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::UpdateTransformCommand>() const {
  return value_type_as_UpdateTransformCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateTextCommamnd *Command::value_type_as<com::meituan::android::msc::renderer::generated::UpdateTextCommamnd>() const {
  return value_type_as_UpdateTextCommamnd();
}

template<> inline const com::meituan::android::msc::renderer::generated::BatchDidFinishCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::BatchDidFinishCommand>() const {
  return value_type_as_BatchDidFinishCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand>() const {
  return value_type_as_CreateKeyframesAnimationCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand *Command::value_type_as<com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand>() const {
  return value_type_as_ClearKeyframesAnimationCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::CreateViewCommand *Command::val_as<com::meituan::android::msc::renderer::generated::CreateViewCommand>() const {
  return val_as_CreateViewCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateViewCommand *Command::val_as<com::meituan::android::msc::renderer::generated::UpdateViewCommand>() const {
  return val_as_UpdateViewCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand *Command::val_as<com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand>() const {
  return val_as_UpdateViewFrameCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::DeleteViewsCommand *Command::val_as<com::meituan::android::msc::renderer::generated::DeleteViewsCommand>() const {
  return val_as_DeleteViewsCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::InsertChildViewsCommand *Command::val_as<com::meituan::android::msc::renderer::generated::InsertChildViewsCommand>() const {
  return val_as_InsertChildViewsCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand *Command::val_as<com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand>() const {
  return val_as_RemoveChildViewsCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd *Command::val_as<com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd>() const {
  return val_as_UpdateViewStyleCommamnd();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateTransformCommand *Command::val_as<com::meituan::android::msc::renderer::generated::UpdateTransformCommand>() const {
  return val_as_UpdateTransformCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::UpdateTextCommamnd *Command::val_as<com::meituan::android::msc::renderer::generated::UpdateTextCommamnd>() const {
  return val_as_UpdateTextCommamnd();
}

template<> inline const com::meituan::android::msc::renderer::generated::BatchDidFinishCommand *Command::val_as<com::meituan::android::msc::renderer::generated::BatchDidFinishCommand>() const {
  return val_as_BatchDidFinishCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand *Command::val_as<com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand>() const {
  return val_as_CreateKeyframesAnimationCommand();
}

template<> inline const com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand *Command::val_as<com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand>() const {
  return val_as_ClearKeyframesAnimationCommand();
}

struct CommandBuilder {
  typedef Command Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_id(int32_t id) {
    fbb_.AddElement<int32_t>(Command::VT_ID, id, 0);
  }
  void add_tag(int32_t tag) {
    fbb_.AddElement<int32_t>(Command::VT_TAG, tag, 0);
  }
  void add_value_type_type(com::meituan::android::msc::renderer::generated::CommandValue value_type_type) {
    fbb_.AddElement<uint8_t>(Command::VT_VALUE_TYPE_TYPE, static_cast<uint8_t>(value_type_type), 0);
  }
  void add_value_type(::flatbuffers::Offset<void> value_type) {
    fbb_.AddOffset(Command::VT_VALUE_TYPE, value_type);
  }
  void add_val_type(com::meituan::android::msc::renderer::generated::CommandValue val_type) {
    fbb_.AddElement<uint8_t>(Command::VT_VAL_TYPE, static_cast<uint8_t>(val_type), 0);
  }
  void add_val(::flatbuffers::Offset<void> val) {
    fbb_.AddOffset(Command::VT_VAL, val);
  }
  explicit CommandBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Command> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Command>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Command> CreateCommand(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t id = 0,
    int32_t tag = 0,
    com::meituan::android::msc::renderer::generated::CommandValue value_type_type = com::meituan::android::msc::renderer::generated::CommandValue_NONE,
    ::flatbuffers::Offset<void> value_type = 0,
    com::meituan::android::msc::renderer::generated::CommandValue val_type = com::meituan::android::msc::renderer::generated::CommandValue_NONE,
    ::flatbuffers::Offset<void> val = 0) {
  CommandBuilder builder_(_fbb);
  builder_.add_val(val);
  builder_.add_value_type(value_type);
  builder_.add_tag(tag);
  builder_.add_id(id);
  builder_.add_val_type(val_type);
  builder_.add_value_type_type(value_type_type);
  return builder_.Finish();
}

struct CommandArray FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef CommandArrayBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Command>> *vals() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Command>> *>(VT_VALS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VALS) &&
           verifier.VerifyVector(vals()) &&
           verifier.VerifyVectorOfTables(vals()) &&
           verifier.EndTable();
  }
};

struct CommandArrayBuilder {
  typedef CommandArray Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vals(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Command>>> vals) {
    fbb_.AddOffset(CommandArray::VT_VALS, vals);
  }
  explicit CommandArrayBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<CommandArray> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<CommandArray>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<CommandArray> CreateCommandArray(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Command>>> vals = 0) {
  CommandArrayBuilder builder_(_fbb);
  builder_.add_vals(vals);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<CommandArray> CreateCommandArrayDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Command>> *vals = nullptr) {
  auto vals__ = vals ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Command>>(*vals) : 0;
  return com::meituan::android::msc::renderer::generated::CreateCommandArray(
      _fbb,
      vals__);
}

struct DisplayItem FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef DisplayItemBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TAG = 4,
    VT_CONTAINER_TAG = 6,
    VT_VIEW_NAME = 8,
    VT_OVERFLOW_X = 10,
    VT_OVERFLOW_Y = 12,
    VT_BACKGROUND_COLOR = 14,
    VT_COLOR = 16,
    VT_BACKGROUND_IMAGE = 18,
    VT_BACKGROUND_SIZE = 20,
    VT_BACKGROUND_REPEAT = 22,
    VT_BORDER_TOP_STYLE = 24,
    VT_BORDER_TOP_WIDTH = 26,
    VT_BORDER_TOP_COLOR = 28,
    VT_BORDER_RIGHT_STYLE = 30,
    VT_BORDER_RIGHT_WIDTH = 32,
    VT_BORDER_RIGHT_COLOR = 34,
    VT_BORDER_BOTTOM_STYLE = 36,
    VT_BORDER_BOTTOM_WIDTH = 38,
    VT_BORDER_BOTTOM_COLOR = 40,
    VT_BORDER_LEFT_STYLE = 42,
    VT_BORDER_LEFT_WIDTH = 44,
    VT_BORDER_LEFT_COLOR = 46,
    VT_BORDER_TOP_LEFT_RADIUS = 48,
    VT_BORDER_TOP_RIGHT_RADIUS = 50,
    VT_BORDER_BOTTOM_RIGHT_RADIUS = 52,
    VT_BORDER_BOTTOM_LEFT_RADIUS = 54,
    VT_OPACITY = 56,
    VT_POINTER_EVENTS = 58,
    VT_BOX_SHADOW = 60,
    VT_FONT_SIZE = 62,
    VT_FONT_STYLE = 64,
    VT_TEXT_ALIGN = 66,
    VT_TEXT_PADDING = 68,
    VT_CHANGE_KEYS = 70
  };
  int32_t tag() const {
    return GetField<int32_t>(VT_TAG, 0);
  }
  int32_t container_tag() const {
    return GetField<int32_t>(VT_CONTAINER_TAG, 0);
  }
  const ::flatbuffers::String *view_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VIEW_NAME);
  }
  const ::flatbuffers::String *overflow_x() const {
    return GetPointer<const ::flatbuffers::String *>(VT_OVERFLOW_X);
  }
  const ::flatbuffers::String *overflow_y() const {
    return GetPointer<const ::flatbuffers::String *>(VT_OVERFLOW_Y);
  }
  int32_t background_color() const {
    return GetField<int32_t>(VT_BACKGROUND_COLOR, 0);
  }
  int32_t color() const {
    return GetField<int32_t>(VT_COLOR, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>> *background_image() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>> *>(VT_BACKGROUND_IMAGE);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>> *background_size() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>> *>(VT_BACKGROUND_SIZE);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::StringArray>> *background_repeat() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::StringArray>> *>(VT_BACKGROUND_REPEAT);
  }
  const ::flatbuffers::String *border_top_style() const {
    return GetPointer<const ::flatbuffers::String *>(VT_BORDER_TOP_STYLE);
  }
  float border_top_width() const {
    return GetField<float>(VT_BORDER_TOP_WIDTH, 0.0f);
  }
  int32_t border_top_color() const {
    return GetField<int32_t>(VT_BORDER_TOP_COLOR, 0);
  }
  const ::flatbuffers::String *border_right_style() const {
    return GetPointer<const ::flatbuffers::String *>(VT_BORDER_RIGHT_STYLE);
  }
  float border_right_width() const {
    return GetField<float>(VT_BORDER_RIGHT_WIDTH, 0.0f);
  }
  int32_t border_right_color() const {
    return GetField<int32_t>(VT_BORDER_RIGHT_COLOR, 0);
  }
  const ::flatbuffers::String *border_bottom_style() const {
    return GetPointer<const ::flatbuffers::String *>(VT_BORDER_BOTTOM_STYLE);
  }
  float border_bottom_width() const {
    return GetField<float>(VT_BORDER_BOTTOM_WIDTH, 0.0f);
  }
  int32_t border_bottom_color() const {
    return GetField<int32_t>(VT_BORDER_BOTTOM_COLOR, 0);
  }
  const ::flatbuffers::String *border_left_style() const {
    return GetPointer<const ::flatbuffers::String *>(VT_BORDER_LEFT_STYLE);
  }
  float border_left_width() const {
    return GetField<float>(VT_BORDER_LEFT_WIDTH, 0.0f);
  }
  int32_t border_left_color() const {
    return GetField<int32_t>(VT_BORDER_LEFT_COLOR, 0);
  }
  float border_top_left_radius() const {
    return GetField<float>(VT_BORDER_TOP_LEFT_RADIUS, 0.0f);
  }
  float border_top_right_radius() const {
    return GetField<float>(VT_BORDER_TOP_RIGHT_RADIUS, 0.0f);
  }
  float border_bottom_right_radius() const {
    return GetField<float>(VT_BORDER_BOTTOM_RIGHT_RADIUS, 0.0f);
  }
  float border_bottom_left_radius() const {
    return GetField<float>(VT_BORDER_BOTTOM_LEFT_RADIUS, 0.0f);
  }
  float opacity() const {
    return GetField<float>(VT_OPACITY, 0.0f);
  }
  const ::flatbuffers::String *pointer_events() const {
    return GetPointer<const ::flatbuffers::String *>(VT_POINTER_EVENTS);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *box_shadow() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *>(VT_BOX_SHADOW);
  }
  float font_size() const {
    return GetField<float>(VT_FONT_SIZE, 0.0f);
  }
  const ::flatbuffers::String *font_style() const {
    return GetPointer<const ::flatbuffers::String *>(VT_FONT_STYLE);
  }
  const ::flatbuffers::String *text_align() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TEXT_ALIGN);
  }
  const ::flatbuffers::Vector<float> *text_padding() const {
    return GetPointer<const ::flatbuffers::Vector<float> *>(VT_TEXT_PADDING);
  }
  const ::flatbuffers::Vector<int32_t> *change_keys() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_CHANGE_KEYS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_TAG, 4) &&
           VerifyField<int32_t>(verifier, VT_CONTAINER_TAG, 4) &&
           VerifyOffset(verifier, VT_VIEW_NAME) &&
           verifier.VerifyString(view_name()) &&
           VerifyOffset(verifier, VT_OVERFLOW_X) &&
           verifier.VerifyString(overflow_x()) &&
           VerifyOffset(verifier, VT_OVERFLOW_Y) &&
           verifier.VerifyString(overflow_y()) &&
           VerifyField<int32_t>(verifier, VT_BACKGROUND_COLOR, 4) &&
           VerifyField<int32_t>(verifier, VT_COLOR, 4) &&
           VerifyOffset(verifier, VT_BACKGROUND_IMAGE) &&
           verifier.VerifyVector(background_image()) &&
           verifier.VerifyVectorOfTables(background_image()) &&
           VerifyOffset(verifier, VT_BACKGROUND_SIZE) &&
           verifier.VerifyVector(background_size()) &&
           verifier.VerifyVectorOfTables(background_size()) &&
           VerifyOffset(verifier, VT_BACKGROUND_REPEAT) &&
           verifier.VerifyVector(background_repeat()) &&
           verifier.VerifyVectorOfTables(background_repeat()) &&
           VerifyOffset(verifier, VT_BORDER_TOP_STYLE) &&
           verifier.VerifyString(border_top_style()) &&
           VerifyField<float>(verifier, VT_BORDER_TOP_WIDTH, 4) &&
           VerifyField<int32_t>(verifier, VT_BORDER_TOP_COLOR, 4) &&
           VerifyOffset(verifier, VT_BORDER_RIGHT_STYLE) &&
           verifier.VerifyString(border_right_style()) &&
           VerifyField<float>(verifier, VT_BORDER_RIGHT_WIDTH, 4) &&
           VerifyField<int32_t>(verifier, VT_BORDER_RIGHT_COLOR, 4) &&
           VerifyOffset(verifier, VT_BORDER_BOTTOM_STYLE) &&
           verifier.VerifyString(border_bottom_style()) &&
           VerifyField<float>(verifier, VT_BORDER_BOTTOM_WIDTH, 4) &&
           VerifyField<int32_t>(verifier, VT_BORDER_BOTTOM_COLOR, 4) &&
           VerifyOffset(verifier, VT_BORDER_LEFT_STYLE) &&
           verifier.VerifyString(border_left_style()) &&
           VerifyField<float>(verifier, VT_BORDER_LEFT_WIDTH, 4) &&
           VerifyField<int32_t>(verifier, VT_BORDER_LEFT_COLOR, 4) &&
           VerifyField<float>(verifier, VT_BORDER_TOP_LEFT_RADIUS, 4) &&
           VerifyField<float>(verifier, VT_BORDER_TOP_RIGHT_RADIUS, 4) &&
           VerifyField<float>(verifier, VT_BORDER_BOTTOM_RIGHT_RADIUS, 4) &&
           VerifyField<float>(verifier, VT_BORDER_BOTTOM_LEFT_RADIUS, 4) &&
           VerifyField<float>(verifier, VT_OPACITY, 4) &&
           VerifyOffset(verifier, VT_POINTER_EVENTS) &&
           verifier.VerifyString(pointer_events()) &&
           VerifyOffset(verifier, VT_BOX_SHADOW) &&
           verifier.VerifyVector(box_shadow()) &&
           verifier.VerifyVectorOfTables(box_shadow()) &&
           VerifyField<float>(verifier, VT_FONT_SIZE, 4) &&
           VerifyOffset(verifier, VT_FONT_STYLE) &&
           verifier.VerifyString(font_style()) &&
           VerifyOffset(verifier, VT_TEXT_ALIGN) &&
           verifier.VerifyString(text_align()) &&
           VerifyOffset(verifier, VT_TEXT_PADDING) &&
           verifier.VerifyVector(text_padding()) &&
           VerifyOffset(verifier, VT_CHANGE_KEYS) &&
           verifier.VerifyVector(change_keys()) &&
           verifier.EndTable();
  }
};

struct DisplayItemBuilder {
  typedef DisplayItem Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tag(int32_t tag) {
    fbb_.AddElement<int32_t>(DisplayItem::VT_TAG, tag, 0);
  }
  void add_container_tag(int32_t container_tag) {
    fbb_.AddElement<int32_t>(DisplayItem::VT_CONTAINER_TAG, container_tag, 0);
  }
  void add_view_name(::flatbuffers::Offset<::flatbuffers::String> view_name) {
    fbb_.AddOffset(DisplayItem::VT_VIEW_NAME, view_name);
  }
  void add_overflow_x(::flatbuffers::Offset<::flatbuffers::String> overflow_x) {
    fbb_.AddOffset(DisplayItem::VT_OVERFLOW_X, overflow_x);
  }
  void add_overflow_y(::flatbuffers::Offset<::flatbuffers::String> overflow_y) {
    fbb_.AddOffset(DisplayItem::VT_OVERFLOW_Y, overflow_y);
  }
  void add_background_color(int32_t background_color) {
    fbb_.AddElement<int32_t>(DisplayItem::VT_BACKGROUND_COLOR, background_color, 0);
  }
  void add_color(int32_t color) {
    fbb_.AddElement<int32_t>(DisplayItem::VT_COLOR, color, 0);
  }
  void add_background_image(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>>> background_image) {
    fbb_.AddOffset(DisplayItem::VT_BACKGROUND_IMAGE, background_image);
  }
  void add_background_size(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>>> background_size) {
    fbb_.AddOffset(DisplayItem::VT_BACKGROUND_SIZE, background_size);
  }
  void add_background_repeat(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::StringArray>>> background_repeat) {
    fbb_.AddOffset(DisplayItem::VT_BACKGROUND_REPEAT, background_repeat);
  }
  void add_border_top_style(::flatbuffers::Offset<::flatbuffers::String> border_top_style) {
    fbb_.AddOffset(DisplayItem::VT_BORDER_TOP_STYLE, border_top_style);
  }
  void add_border_top_width(float border_top_width) {
    fbb_.AddElement<float>(DisplayItem::VT_BORDER_TOP_WIDTH, border_top_width, 0.0f);
  }
  void add_border_top_color(int32_t border_top_color) {
    fbb_.AddElement<int32_t>(DisplayItem::VT_BORDER_TOP_COLOR, border_top_color, 0);
  }
  void add_border_right_style(::flatbuffers::Offset<::flatbuffers::String> border_right_style) {
    fbb_.AddOffset(DisplayItem::VT_BORDER_RIGHT_STYLE, border_right_style);
  }
  void add_border_right_width(float border_right_width) {
    fbb_.AddElement<float>(DisplayItem::VT_BORDER_RIGHT_WIDTH, border_right_width, 0.0f);
  }
  void add_border_right_color(int32_t border_right_color) {
    fbb_.AddElement<int32_t>(DisplayItem::VT_BORDER_RIGHT_COLOR, border_right_color, 0);
  }
  void add_border_bottom_style(::flatbuffers::Offset<::flatbuffers::String> border_bottom_style) {
    fbb_.AddOffset(DisplayItem::VT_BORDER_BOTTOM_STYLE, border_bottom_style);
  }
  void add_border_bottom_width(float border_bottom_width) {
    fbb_.AddElement<float>(DisplayItem::VT_BORDER_BOTTOM_WIDTH, border_bottom_width, 0.0f);
  }
  void add_border_bottom_color(int32_t border_bottom_color) {
    fbb_.AddElement<int32_t>(DisplayItem::VT_BORDER_BOTTOM_COLOR, border_bottom_color, 0);
  }
  void add_border_left_style(::flatbuffers::Offset<::flatbuffers::String> border_left_style) {
    fbb_.AddOffset(DisplayItem::VT_BORDER_LEFT_STYLE, border_left_style);
  }
  void add_border_left_width(float border_left_width) {
    fbb_.AddElement<float>(DisplayItem::VT_BORDER_LEFT_WIDTH, border_left_width, 0.0f);
  }
  void add_border_left_color(int32_t border_left_color) {
    fbb_.AddElement<int32_t>(DisplayItem::VT_BORDER_LEFT_COLOR, border_left_color, 0);
  }
  void add_border_top_left_radius(float border_top_left_radius) {
    fbb_.AddElement<float>(DisplayItem::VT_BORDER_TOP_LEFT_RADIUS, border_top_left_radius, 0.0f);
  }
  void add_border_top_right_radius(float border_top_right_radius) {
    fbb_.AddElement<float>(DisplayItem::VT_BORDER_TOP_RIGHT_RADIUS, border_top_right_radius, 0.0f);
  }
  void add_border_bottom_right_radius(float border_bottom_right_radius) {
    fbb_.AddElement<float>(DisplayItem::VT_BORDER_BOTTOM_RIGHT_RADIUS, border_bottom_right_radius, 0.0f);
  }
  void add_border_bottom_left_radius(float border_bottom_left_radius) {
    fbb_.AddElement<float>(DisplayItem::VT_BORDER_BOTTOM_LEFT_RADIUS, border_bottom_left_radius, 0.0f);
  }
  void add_opacity(float opacity) {
    fbb_.AddElement<float>(DisplayItem::VT_OPACITY, opacity, 0.0f);
  }
  void add_pointer_events(::flatbuffers::Offset<::flatbuffers::String> pointer_events) {
    fbb_.AddOffset(DisplayItem::VT_POINTER_EVENTS, pointer_events);
  }
  void add_box_shadow(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> box_shadow) {
    fbb_.AddOffset(DisplayItem::VT_BOX_SHADOW, box_shadow);
  }
  void add_font_size(float font_size) {
    fbb_.AddElement<float>(DisplayItem::VT_FONT_SIZE, font_size, 0.0f);
  }
  void add_font_style(::flatbuffers::Offset<::flatbuffers::String> font_style) {
    fbb_.AddOffset(DisplayItem::VT_FONT_STYLE, font_style);
  }
  void add_text_align(::flatbuffers::Offset<::flatbuffers::String> text_align) {
    fbb_.AddOffset(DisplayItem::VT_TEXT_ALIGN, text_align);
  }
  void add_text_padding(::flatbuffers::Offset<::flatbuffers::Vector<float>> text_padding) {
    fbb_.AddOffset(DisplayItem::VT_TEXT_PADDING, text_padding);
  }
  void add_change_keys(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> change_keys) {
    fbb_.AddOffset(DisplayItem::VT_CHANGE_KEYS, change_keys);
  }
  explicit DisplayItemBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<DisplayItem> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<DisplayItem>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<DisplayItem> CreateDisplayItem(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t tag = 0,
    int32_t container_tag = 0,
    ::flatbuffers::Offset<::flatbuffers::String> view_name = 0,
    ::flatbuffers::Offset<::flatbuffers::String> overflow_x = 0,
    ::flatbuffers::Offset<::flatbuffers::String> overflow_y = 0,
    int32_t background_color = 0,
    int32_t color = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>>> background_image = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>>> background_size = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::StringArray>>> background_repeat = 0,
    ::flatbuffers::Offset<::flatbuffers::String> border_top_style = 0,
    float border_top_width = 0.0f,
    int32_t border_top_color = 0,
    ::flatbuffers::Offset<::flatbuffers::String> border_right_style = 0,
    float border_right_width = 0.0f,
    int32_t border_right_color = 0,
    ::flatbuffers::Offset<::flatbuffers::String> border_bottom_style = 0,
    float border_bottom_width = 0.0f,
    int32_t border_bottom_color = 0,
    ::flatbuffers::Offset<::flatbuffers::String> border_left_style = 0,
    float border_left_width = 0.0f,
    int32_t border_left_color = 0,
    float border_top_left_radius = 0.0f,
    float border_top_right_radius = 0.0f,
    float border_bottom_right_radius = 0.0f,
    float border_bottom_left_radius = 0.0f,
    float opacity = 0.0f,
    ::flatbuffers::Offset<::flatbuffers::String> pointer_events = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> box_shadow = 0,
    float font_size = 0.0f,
    ::flatbuffers::Offset<::flatbuffers::String> font_style = 0,
    ::flatbuffers::Offset<::flatbuffers::String> text_align = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<float>> text_padding = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> change_keys = 0) {
  DisplayItemBuilder builder_(_fbb);
  builder_.add_change_keys(change_keys);
  builder_.add_text_padding(text_padding);
  builder_.add_text_align(text_align);
  builder_.add_font_style(font_style);
  builder_.add_font_size(font_size);
  builder_.add_box_shadow(box_shadow);
  builder_.add_pointer_events(pointer_events);
  builder_.add_opacity(opacity);
  builder_.add_border_bottom_left_radius(border_bottom_left_radius);
  builder_.add_border_bottom_right_radius(border_bottom_right_radius);
  builder_.add_border_top_right_radius(border_top_right_radius);
  builder_.add_border_top_left_radius(border_top_left_radius);
  builder_.add_border_left_color(border_left_color);
  builder_.add_border_left_width(border_left_width);
  builder_.add_border_left_style(border_left_style);
  builder_.add_border_bottom_color(border_bottom_color);
  builder_.add_border_bottom_width(border_bottom_width);
  builder_.add_border_bottom_style(border_bottom_style);
  builder_.add_border_right_color(border_right_color);
  builder_.add_border_right_width(border_right_width);
  builder_.add_border_right_style(border_right_style);
  builder_.add_border_top_color(border_top_color);
  builder_.add_border_top_width(border_top_width);
  builder_.add_border_top_style(border_top_style);
  builder_.add_background_repeat(background_repeat);
  builder_.add_background_size(background_size);
  builder_.add_background_image(background_image);
  builder_.add_color(color);
  builder_.add_background_color(background_color);
  builder_.add_overflow_y(overflow_y);
  builder_.add_overflow_x(overflow_x);
  builder_.add_view_name(view_name);
  builder_.add_container_tag(container_tag);
  builder_.add_tag(tag);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<DisplayItem> CreateDisplayItemDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t tag = 0,
    int32_t container_tag = 0,
    const char *view_name = nullptr,
    const char *overflow_x = nullptr,
    const char *overflow_y = nullptr,
    int32_t background_color = 0,
    int32_t color = 0,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>> *background_image = nullptr,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>> *background_size = nullptr,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::StringArray>> *background_repeat = nullptr,
    const char *border_top_style = nullptr,
    float border_top_width = 0.0f,
    int32_t border_top_color = 0,
    const char *border_right_style = nullptr,
    float border_right_width = 0.0f,
    int32_t border_right_color = 0,
    const char *border_bottom_style = nullptr,
    float border_bottom_width = 0.0f,
    int32_t border_bottom_color = 0,
    const char *border_left_style = nullptr,
    float border_left_width = 0.0f,
    int32_t border_left_color = 0,
    float border_top_left_radius = 0.0f,
    float border_top_right_radius = 0.0f,
    float border_bottom_right_radius = 0.0f,
    float border_bottom_left_radius = 0.0f,
    float opacity = 0.0f,
    const char *pointer_events = nullptr,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *box_shadow = nullptr,
    float font_size = 0.0f,
    const char *font_style = nullptr,
    const char *text_align = nullptr,
    const std::vector<float> *text_padding = nullptr,
    const std::vector<int32_t> *change_keys = nullptr) {
  auto view_name__ = view_name ? _fbb.CreateString(view_name) : 0;
  auto overflow_x__ = overflow_x ? _fbb.CreateString(overflow_x) : 0;
  auto overflow_y__ = overflow_y ? _fbb.CreateString(overflow_y) : 0;
  auto background_image__ = background_image ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>>(*background_image) : 0;
  auto background_size__ = background_size ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::PropArray>>(*background_size) : 0;
  auto background_repeat__ = background_repeat ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::StringArray>>(*background_repeat) : 0;
  auto border_top_style__ = border_top_style ? _fbb.CreateString(border_top_style) : 0;
  auto border_right_style__ = border_right_style ? _fbb.CreateString(border_right_style) : 0;
  auto border_bottom_style__ = border_bottom_style ? _fbb.CreateString(border_bottom_style) : 0;
  auto border_left_style__ = border_left_style ? _fbb.CreateString(border_left_style) : 0;
  auto pointer_events__ = pointer_events ? _fbb.CreateString(pointer_events) : 0;
  auto box_shadow__ = box_shadow ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>(*box_shadow) : 0;
  auto font_style__ = font_style ? _fbb.CreateString(font_style) : 0;
  auto text_align__ = text_align ? _fbb.CreateString(text_align) : 0;
  auto text_padding__ = text_padding ? _fbb.CreateVector<float>(*text_padding) : 0;
  auto change_keys__ = change_keys ? _fbb.CreateVector<int32_t>(*change_keys) : 0;
  return com::meituan::android::msc::renderer::generated::CreateDisplayItem(
      _fbb,
      tag,
      container_tag,
      view_name__,
      overflow_x__,
      overflow_y__,
      background_color,
      color,
      background_image__,
      background_size__,
      background_repeat__,
      border_top_style__,
      border_top_width,
      border_top_color,
      border_right_style__,
      border_right_width,
      border_right_color,
      border_bottom_style__,
      border_bottom_width,
      border_bottom_color,
      border_left_style__,
      border_left_width,
      border_left_color,
      border_top_left_radius,
      border_top_right_radius,
      border_bottom_right_radius,
      border_bottom_left_radius,
      opacity,
      pointer_events__,
      box_shadow__,
      font_size,
      font_style__,
      text_align__,
      text_padding__,
      change_keys__);
}

inline bool VerifyCommandValue(::flatbuffers::Verifier &verifier, const void *obj, CommandValue type) {
  switch (type) {
    case CommandValue_NONE: {
      return true;
    }
    case CommandValue_CreateViewCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::CreateViewCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_UpdateViewCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::UpdateViewCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_UpdateViewFrameCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::UpdateViewFrameCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_DeleteViewsCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::DeleteViewsCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_InsertChildViewsCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::InsertChildViewsCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_RemoveChildViewsCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::RemoveChildViewsCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_UpdateViewStyleCommamnd: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::UpdateViewStyleCommamnd *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_UpdateTransformCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::UpdateTransformCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_UpdateTextCommamnd: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::UpdateTextCommamnd *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_BatchDidFinishCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::BatchDidFinishCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_CreateKeyframesAnimationCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::CreateKeyframesAnimationCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case CommandValue_ClearKeyframesAnimationCommand: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::ClearKeyframesAnimationCommand *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyCommandValueVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyCommandValue(
        verifier,  values->Get(i), types->GetEnum<CommandValue>(i))) {
      return false;
    }
  }
  return true;
}

inline const com::meituan::android::msc::renderer::generated::CommandArray *GetCommandArray(const void *buf) {
  return ::flatbuffers::GetRoot<com::meituan::android::msc::renderer::generated::CommandArray>(buf);
}

inline const com::meituan::android::msc::renderer::generated::CommandArray *GetSizePrefixedCommandArray(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<com::meituan::android::msc::renderer::generated::CommandArray>(buf);
}

inline bool VerifyCommandArrayBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<com::meituan::android::msc::renderer::generated::CommandArray>(nullptr);
}

inline bool VerifySizePrefixedCommandArrayBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<com::meituan::android::msc::renderer::generated::CommandArray>(nullptr);
}

inline void FinishCommandArrayBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::CommandArray> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedCommandArrayBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::CommandArray> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace generated
}  // namespace renderer
}  // namespace msc
}  // namespace android
}  // namespace meituan
}  // namespace com

#endif  // FLATBUFFERS_GENERATED_COMMAND_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
