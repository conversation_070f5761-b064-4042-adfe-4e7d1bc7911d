// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_COMPONENTEVENT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
#define FLATBUFFERS_GENERATED_COMPONENTEVENT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 25 &&
              FLATBUFFERS_VERSION_MINOR == 2 &&
              FLATBUFFERS_VERSION_REVISION == 10,
             "Non-compatible flatbuffers version included");

#include "Props_generated.h"

namespace com {
namespace meituan {
namespace android {
namespace msc {
namespace renderer {
namespace generated {

struct ComponentEvent;
struct ComponentEventBuilder;

struct ComponentEvent FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef ComponentEventBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_EVENT_NAME = 4,
    VT_TARGET = 6,
    VT_PROPS = 8
  };
  const ::flatbuffers::String *event_name() const {
    return GetPointer<const ::flatbuffers::String *>(VT_EVENT_NAME);
  }
  int32_t target() const {
    return GetField<int32_t>(VT_TARGET, 0);
  }
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *props() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *>(VT_PROPS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_EVENT_NAME) &&
           verifier.VerifyString(event_name()) &&
           VerifyField<int32_t>(verifier, VT_TARGET, 4) &&
           VerifyOffset(verifier, VT_PROPS) &&
           verifier.VerifyVector(props()) &&
           verifier.VerifyVectorOfTables(props()) &&
           verifier.EndTable();
  }
};

struct ComponentEventBuilder {
  typedef ComponentEvent Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_event_name(::flatbuffers::Offset<::flatbuffers::String> event_name) {
    fbb_.AddOffset(ComponentEvent::VT_EVENT_NAME, event_name);
  }
  void add_target(int32_t target) {
    fbb_.AddElement<int32_t>(ComponentEvent::VT_TARGET, target, 0);
  }
  void add_props(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> props) {
    fbb_.AddOffset(ComponentEvent::VT_PROPS, props);
  }
  explicit ComponentEventBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<ComponentEvent> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<ComponentEvent>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<ComponentEvent> CreateComponentEvent(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> event_name = 0,
    int32_t target = 0,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> props = 0) {
  ComponentEventBuilder builder_(_fbb);
  builder_.add_props(props);
  builder_.add_target(target);
  builder_.add_event_name(event_name);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<ComponentEvent> CreateComponentEventDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *event_name = nullptr,
    int32_t target = 0,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *props = nullptr) {
  auto event_name__ = event_name ? _fbb.CreateString(event_name) : 0;
  auto props__ = props ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>(*props) : 0;
  return com::meituan::android::msc::renderer::generated::CreateComponentEvent(
      _fbb,
      event_name__,
      target,
      props__);
}

}  // namespace generated
}  // namespace renderer
}  // namespace msc
}  // namespace android
}  // namespace meituan
}  // namespace com

#endif  // FLATBUFFERS_GENERATED_COMPONENTEVENT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
