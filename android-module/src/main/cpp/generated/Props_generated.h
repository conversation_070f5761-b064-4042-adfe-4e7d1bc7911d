// automatically generated by the FlatBuffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_PROPS_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
#define FLATBUFFERS_GENERATED_PROPS_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 25 &&
              FLATBUFFERS_VERSION_MINOR == 2 &&
              FLATBUFFERS_VERSION_REVISION == 10,
             "Non-compatible flatbuffers version included");

namespace com {
namespace meituan {
namespace android {
namespace msc {
namespace renderer {
namespace generated {

struct IntValue;
struct IntValueBuilder;

struct FloatValue;
struct FloatValueBuilder;

struct StringValue;
struct StringValueBuilder;

struct BoolValue;
struct BoolValueBuilder;

struct IntArray;
struct IntArrayBuilder;

struct FloatArray;
struct FloatArrayBuilder;

struct StringArray;
struct StringArrayBuilder;

struct PropArray;
struct PropArrayBuilder;

struct Prop;
struct PropBuilder;

enum Value : uint8_t {
  Value_NONE = 0,
  Value_IntValue = 1,
  Value_FloatValue = 2,
  Value_StringValue = 3,
  Value_BoolValue = 4,
  Value_IntArray = 5,
  Value_FloatArray = 6,
  Value_StringArray = 7,
  Value_PropArray = 8,
  Value_MIN = Value_NONE,
  Value_MAX = Value_PropArray
};

inline const Value (&EnumValuesValue())[9] {
  static const Value values[] = {
    Value_NONE,
    Value_IntValue,
    Value_FloatValue,
    Value_StringValue,
    Value_BoolValue,
    Value_IntArray,
    Value_FloatArray,
    Value_StringArray,
    Value_PropArray
  };
  return values;
}

inline const char * const *EnumNamesValue() {
  static const char * const names[10] = {
    "NONE",
    "IntValue",
    "FloatValue",
    "StringValue",
    "BoolValue",
    "IntArray",
    "FloatArray",
    "StringArray",
    "PropArray",
    nullptr
  };
  return names;
}

inline const char *EnumNameValue(Value e) {
  if (::flatbuffers::IsOutRange(e, Value_NONE, Value_PropArray)) return "";
  const size_t index = static_cast<size_t>(e);
  return EnumNamesValue()[index];
}

template<typename T> struct ValueTraits {
  static const Value enum_value = Value_NONE;
};

template<> struct ValueTraits<com::meituan::android::msc::renderer::generated::IntValue> {
  static const Value enum_value = Value_IntValue;
};

template<> struct ValueTraits<com::meituan::android::msc::renderer::generated::FloatValue> {
  static const Value enum_value = Value_FloatValue;
};

template<> struct ValueTraits<com::meituan::android::msc::renderer::generated::StringValue> {
  static const Value enum_value = Value_StringValue;
};

template<> struct ValueTraits<com::meituan::android::msc::renderer::generated::BoolValue> {
  static const Value enum_value = Value_BoolValue;
};

template<> struct ValueTraits<com::meituan::android::msc::renderer::generated::IntArray> {
  static const Value enum_value = Value_IntArray;
};

template<> struct ValueTraits<com::meituan::android::msc::renderer::generated::FloatArray> {
  static const Value enum_value = Value_FloatArray;
};

template<> struct ValueTraits<com::meituan::android::msc::renderer::generated::StringArray> {
  static const Value enum_value = Value_StringArray;
};

template<> struct ValueTraits<com::meituan::android::msc::renderer::generated::PropArray> {
  static const Value enum_value = Value_PropArray;
};

bool VerifyValue(::flatbuffers::Verifier &verifier, const void *obj, Value type);
bool VerifyValueVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types);

struct IntValue FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef IntValueBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VAL = 4
  };
  int32_t val() const {
    return GetField<int32_t>(VT_VAL, 0);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_VAL, 4) &&
           verifier.EndTable();
  }
};

struct IntValueBuilder {
  typedef IntValue Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_val(int32_t val) {
    fbb_.AddElement<int32_t>(IntValue::VT_VAL, val, 0);
  }
  explicit IntValueBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<IntValue> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<IntValue>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<IntValue> CreateIntValue(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t val = 0) {
  IntValueBuilder builder_(_fbb);
  builder_.add_val(val);
  return builder_.Finish();
}

struct FloatValue FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FloatValueBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VAL = 4
  };
  float val() const {
    return GetField<float>(VT_VAL, 0.0f);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<float>(verifier, VT_VAL, 4) &&
           verifier.EndTable();
  }
};

struct FloatValueBuilder {
  typedef FloatValue Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_val(float val) {
    fbb_.AddElement<float>(FloatValue::VT_VAL, val, 0.0f);
  }
  explicit FloatValueBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FloatValue> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FloatValue>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FloatValue> CreateFloatValue(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    float val = 0.0f) {
  FloatValueBuilder builder_(_fbb);
  builder_.add_val(val);
  return builder_.Finish();
}

struct StringValue FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef StringValueBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VAL = 4
  };
  const ::flatbuffers::String *val() const {
    return GetPointer<const ::flatbuffers::String *>(VT_VAL);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VAL) &&
           verifier.VerifyString(val()) &&
           verifier.EndTable();
  }
};

struct StringValueBuilder {
  typedef StringValue Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_val(::flatbuffers::Offset<::flatbuffers::String> val) {
    fbb_.AddOffset(StringValue::VT_VAL, val);
  }
  explicit StringValueBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<StringValue> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<StringValue>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<StringValue> CreateStringValue(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> val = 0) {
  StringValueBuilder builder_(_fbb);
  builder_.add_val(val);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<StringValue> CreateStringValueDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *val = nullptr) {
  auto val__ = val ? _fbb.CreateString(val) : 0;
  return com::meituan::android::msc::renderer::generated::CreateStringValue(
      _fbb,
      val__);
}

struct BoolValue FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef BoolValueBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VAL = 4
  };
  bool val() const {
    return GetField<uint8_t>(VT_VAL, 0) != 0;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<uint8_t>(verifier, VT_VAL, 1) &&
           verifier.EndTable();
  }
};

struct BoolValueBuilder {
  typedef BoolValue Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_val(bool val) {
    fbb_.AddElement<uint8_t>(BoolValue::VT_VAL, static_cast<uint8_t>(val), 0);
  }
  explicit BoolValueBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<BoolValue> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<BoolValue>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<BoolValue> CreateBoolValue(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    bool val = false) {
  BoolValueBuilder builder_(_fbb);
  builder_.add_val(val);
  return builder_.Finish();
}

struct IntArray FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef IntArrayBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALS = 4
  };
  const ::flatbuffers::Vector<int32_t> *vals() const {
    return GetPointer<const ::flatbuffers::Vector<int32_t> *>(VT_VALS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VALS) &&
           verifier.VerifyVector(vals()) &&
           verifier.EndTable();
  }
};

struct IntArrayBuilder {
  typedef IntArray Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vals(::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> vals) {
    fbb_.AddOffset(IntArray::VT_VALS, vals);
  }
  explicit IntArrayBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<IntArray> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<IntArray>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<IntArray> CreateIntArray(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<int32_t>> vals = 0) {
  IntArrayBuilder builder_(_fbb);
  builder_.add_vals(vals);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<IntArray> CreateIntArrayDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<int32_t> *vals = nullptr) {
  auto vals__ = vals ? _fbb.CreateVector<int32_t>(*vals) : 0;
  return com::meituan::android::msc::renderer::generated::CreateIntArray(
      _fbb,
      vals__);
}

struct FloatArray FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef FloatArrayBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALS = 4
  };
  const ::flatbuffers::Vector<float> *vals() const {
    return GetPointer<const ::flatbuffers::Vector<float> *>(VT_VALS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VALS) &&
           verifier.VerifyVector(vals()) &&
           verifier.EndTable();
  }
};

struct FloatArrayBuilder {
  typedef FloatArray Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vals(::flatbuffers::Offset<::flatbuffers::Vector<float>> vals) {
    fbb_.AddOffset(FloatArray::VT_VALS, vals);
  }
  explicit FloatArrayBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<FloatArray> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<FloatArray>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<FloatArray> CreateFloatArray(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<float>> vals = 0) {
  FloatArrayBuilder builder_(_fbb);
  builder_.add_vals(vals);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<FloatArray> CreateFloatArrayDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<float> *vals = nullptr) {
  auto vals__ = vals ? _fbb.CreateVector<float>(*vals) : 0;
  return com::meituan::android::msc::renderer::generated::CreateFloatArray(
      _fbb,
      vals__);
}

struct StringArray FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef StringArrayBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *vals() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>> *>(VT_VALS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VALS) &&
           verifier.VerifyVector(vals()) &&
           verifier.VerifyVectorOfStrings(vals()) &&
           verifier.EndTable();
  }
};

struct StringArrayBuilder {
  typedef StringArray Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vals(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> vals) {
    fbb_.AddOffset(StringArray::VT_VALS, vals);
  }
  explicit StringArrayBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<StringArray> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<StringArray>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<StringArray> CreateStringArray(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<::flatbuffers::String>>> vals = 0) {
  StringArrayBuilder builder_(_fbb);
  builder_.add_vals(vals);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<StringArray> CreateStringArrayDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<::flatbuffers::String>> *vals = nullptr) {
  auto vals__ = vals ? _fbb.CreateVector<::flatbuffers::Offset<::flatbuffers::String>>(*vals) : 0;
  return com::meituan::android::msc::renderer::generated::CreateStringArray(
      _fbb,
      vals__);
}

struct PropArray FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PropArrayBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_VALS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *vals() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *>(VT_VALS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_VALS) &&
           verifier.VerifyVector(vals()) &&
           verifier.VerifyVectorOfTables(vals()) &&
           verifier.EndTable();
  }
};

struct PropArrayBuilder {
  typedef PropArray Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_vals(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> vals) {
    fbb_.AddOffset(PropArray::VT_VALS, vals);
  }
  explicit PropArrayBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<PropArray> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<PropArray>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<PropArray> CreatePropArray(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>> vals = 0) {
  PropArrayBuilder builder_(_fbb);
  builder_.add_vals(vals);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<PropArray> CreatePropArrayDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>> *vals = nullptr) {
  auto vals__ = vals ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Prop>>(*vals) : 0;
  return com::meituan::android::msc::renderer::generated::CreatePropArray(
      _fbb,
      vals__);
}

struct Prop FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef PropBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_KEY = 4,
    VT_VALUE_TYPE_TYPE = 6,
    VT_VALUE_TYPE = 8,
    VT_VAL_TYPE = 10,
    VT_VAL = 12
  };
  const ::flatbuffers::String *key() const {
    return GetPointer<const ::flatbuffers::String *>(VT_KEY);
  }
  com::meituan::android::msc::renderer::generated::Value value_type_type() const {
    return static_cast<com::meituan::android::msc::renderer::generated::Value>(GetField<uint8_t>(VT_VALUE_TYPE_TYPE, 0));
  }
  const void *value_type() const {
    return GetPointer<const void *>(VT_VALUE_TYPE);
  }
  template<typename T> const T *value_type_as() const;
  const com::meituan::android::msc::renderer::generated::IntValue *value_type_as_IntValue() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::Value_IntValue ? static_cast<const com::meituan::android::msc::renderer::generated::IntValue *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::FloatValue *value_type_as_FloatValue() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::Value_FloatValue ? static_cast<const com::meituan::android::msc::renderer::generated::FloatValue *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::StringValue *value_type_as_StringValue() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::Value_StringValue ? static_cast<const com::meituan::android::msc::renderer::generated::StringValue *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::BoolValue *value_type_as_BoolValue() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::Value_BoolValue ? static_cast<const com::meituan::android::msc::renderer::generated::BoolValue *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::IntArray *value_type_as_IntArray() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::Value_IntArray ? static_cast<const com::meituan::android::msc::renderer::generated::IntArray *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::FloatArray *value_type_as_FloatArray() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::Value_FloatArray ? static_cast<const com::meituan::android::msc::renderer::generated::FloatArray *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::StringArray *value_type_as_StringArray() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::Value_StringArray ? static_cast<const com::meituan::android::msc::renderer::generated::StringArray *>(value_type()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::PropArray *value_type_as_PropArray() const {
    return value_type_type() == com::meituan::android::msc::renderer::generated::Value_PropArray ? static_cast<const com::meituan::android::msc::renderer::generated::PropArray *>(value_type()) : nullptr;
  }
  com::meituan::android::msc::renderer::generated::Value val_type() const {
    return static_cast<com::meituan::android::msc::renderer::generated::Value>(GetField<uint8_t>(VT_VAL_TYPE, 0));
  }
  const void *val() const {
    return GetPointer<const void *>(VT_VAL);
  }
  template<typename T> const T *val_as() const;
  const com::meituan::android::msc::renderer::generated::IntValue *val_as_IntValue() const {
    return val_type() == com::meituan::android::msc::renderer::generated::Value_IntValue ? static_cast<const com::meituan::android::msc::renderer::generated::IntValue *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::FloatValue *val_as_FloatValue() const {
    return val_type() == com::meituan::android::msc::renderer::generated::Value_FloatValue ? static_cast<const com::meituan::android::msc::renderer::generated::FloatValue *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::StringValue *val_as_StringValue() const {
    return val_type() == com::meituan::android::msc::renderer::generated::Value_StringValue ? static_cast<const com::meituan::android::msc::renderer::generated::StringValue *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::BoolValue *val_as_BoolValue() const {
    return val_type() == com::meituan::android::msc::renderer::generated::Value_BoolValue ? static_cast<const com::meituan::android::msc::renderer::generated::BoolValue *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::IntArray *val_as_IntArray() const {
    return val_type() == com::meituan::android::msc::renderer::generated::Value_IntArray ? static_cast<const com::meituan::android::msc::renderer::generated::IntArray *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::FloatArray *val_as_FloatArray() const {
    return val_type() == com::meituan::android::msc::renderer::generated::Value_FloatArray ? static_cast<const com::meituan::android::msc::renderer::generated::FloatArray *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::StringArray *val_as_StringArray() const {
    return val_type() == com::meituan::android::msc::renderer::generated::Value_StringArray ? static_cast<const com::meituan::android::msc::renderer::generated::StringArray *>(val()) : nullptr;
  }
  const com::meituan::android::msc::renderer::generated::PropArray *val_as_PropArray() const {
    return val_type() == com::meituan::android::msc::renderer::generated::Value_PropArray ? static_cast<const com::meituan::android::msc::renderer::generated::PropArray *>(val()) : nullptr;
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_KEY) &&
           verifier.VerifyString(key()) &&
           VerifyField<uint8_t>(verifier, VT_VALUE_TYPE_TYPE, 1) &&
           VerifyOffset(verifier, VT_VALUE_TYPE) &&
           VerifyValue(verifier, value_type(), value_type_type()) &&
           VerifyField<uint8_t>(verifier, VT_VAL_TYPE, 1) &&
           VerifyOffset(verifier, VT_VAL) &&
           VerifyValue(verifier, val(), val_type()) &&
           verifier.EndTable();
  }
};

template<> inline const com::meituan::android::msc::renderer::generated::IntValue *Prop::value_type_as<com::meituan::android::msc::renderer::generated::IntValue>() const {
  return value_type_as_IntValue();
}

template<> inline const com::meituan::android::msc::renderer::generated::FloatValue *Prop::value_type_as<com::meituan::android::msc::renderer::generated::FloatValue>() const {
  return value_type_as_FloatValue();
}

template<> inline const com::meituan::android::msc::renderer::generated::StringValue *Prop::value_type_as<com::meituan::android::msc::renderer::generated::StringValue>() const {
  return value_type_as_StringValue();
}

template<> inline const com::meituan::android::msc::renderer::generated::BoolValue *Prop::value_type_as<com::meituan::android::msc::renderer::generated::BoolValue>() const {
  return value_type_as_BoolValue();
}

template<> inline const com::meituan::android::msc::renderer::generated::IntArray *Prop::value_type_as<com::meituan::android::msc::renderer::generated::IntArray>() const {
  return value_type_as_IntArray();
}

template<> inline const com::meituan::android::msc::renderer::generated::FloatArray *Prop::value_type_as<com::meituan::android::msc::renderer::generated::FloatArray>() const {
  return value_type_as_FloatArray();
}

template<> inline const com::meituan::android::msc::renderer::generated::StringArray *Prop::value_type_as<com::meituan::android::msc::renderer::generated::StringArray>() const {
  return value_type_as_StringArray();
}

template<> inline const com::meituan::android::msc::renderer::generated::PropArray *Prop::value_type_as<com::meituan::android::msc::renderer::generated::PropArray>() const {
  return value_type_as_PropArray();
}

template<> inline const com::meituan::android::msc::renderer::generated::IntValue *Prop::val_as<com::meituan::android::msc::renderer::generated::IntValue>() const {
  return val_as_IntValue();
}

template<> inline const com::meituan::android::msc::renderer::generated::FloatValue *Prop::val_as<com::meituan::android::msc::renderer::generated::FloatValue>() const {
  return val_as_FloatValue();
}

template<> inline const com::meituan::android::msc::renderer::generated::StringValue *Prop::val_as<com::meituan::android::msc::renderer::generated::StringValue>() const {
  return val_as_StringValue();
}

template<> inline const com::meituan::android::msc::renderer::generated::BoolValue *Prop::val_as<com::meituan::android::msc::renderer::generated::BoolValue>() const {
  return val_as_BoolValue();
}

template<> inline const com::meituan::android::msc::renderer::generated::IntArray *Prop::val_as<com::meituan::android::msc::renderer::generated::IntArray>() const {
  return val_as_IntArray();
}

template<> inline const com::meituan::android::msc::renderer::generated::FloatArray *Prop::val_as<com::meituan::android::msc::renderer::generated::FloatArray>() const {
  return val_as_FloatArray();
}

template<> inline const com::meituan::android::msc::renderer::generated::StringArray *Prop::val_as<com::meituan::android::msc::renderer::generated::StringArray>() const {
  return val_as_StringArray();
}

template<> inline const com::meituan::android::msc::renderer::generated::PropArray *Prop::val_as<com::meituan::android::msc::renderer::generated::PropArray>() const {
  return val_as_PropArray();
}

struct PropBuilder {
  typedef Prop Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_key(::flatbuffers::Offset<::flatbuffers::String> key) {
    fbb_.AddOffset(Prop::VT_KEY, key);
  }
  void add_value_type_type(com::meituan::android::msc::renderer::generated::Value value_type_type) {
    fbb_.AddElement<uint8_t>(Prop::VT_VALUE_TYPE_TYPE, static_cast<uint8_t>(value_type_type), 0);
  }
  void add_value_type(::flatbuffers::Offset<void> value_type) {
    fbb_.AddOffset(Prop::VT_VALUE_TYPE, value_type);
  }
  void add_val_type(com::meituan::android::msc::renderer::generated::Value val_type) {
    fbb_.AddElement<uint8_t>(Prop::VT_VAL_TYPE, static_cast<uint8_t>(val_type), 0);
  }
  void add_val(::flatbuffers::Offset<void> val) {
    fbb_.AddOffset(Prop::VT_VAL, val);
  }
  explicit PropBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Prop> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Prop>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Prop> CreateProp(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::String> key = 0,
    com::meituan::android::msc::renderer::generated::Value value_type_type = com::meituan::android::msc::renderer::generated::Value_NONE,
    ::flatbuffers::Offset<void> value_type = 0,
    com::meituan::android::msc::renderer::generated::Value val_type = com::meituan::android::msc::renderer::generated::Value_NONE,
    ::flatbuffers::Offset<void> val = 0) {
  PropBuilder builder_(_fbb);
  builder_.add_val(val);
  builder_.add_value_type(value_type);
  builder_.add_key(key);
  builder_.add_val_type(val_type);
  builder_.add_value_type_type(value_type_type);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Prop> CreatePropDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const char *key = nullptr,
    com::meituan::android::msc::renderer::generated::Value value_type_type = com::meituan::android::msc::renderer::generated::Value_NONE,
    ::flatbuffers::Offset<void> value_type = 0,
    com::meituan::android::msc::renderer::generated::Value val_type = com::meituan::android::msc::renderer::generated::Value_NONE,
    ::flatbuffers::Offset<void> val = 0) {
  auto key__ = key ? _fbb.CreateString(key) : 0;
  return com::meituan::android::msc::renderer::generated::CreateProp(
      _fbb,
      key__,
      value_type_type,
      value_type,
      val_type,
      val);
}

inline bool VerifyValue(::flatbuffers::Verifier &verifier, const void *obj, Value type) {
  switch (type) {
    case Value_NONE: {
      return true;
    }
    case Value_IntValue: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::IntValue *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case Value_FloatValue: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::FloatValue *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case Value_StringValue: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::StringValue *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case Value_BoolValue: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::BoolValue *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case Value_IntArray: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::IntArray *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case Value_FloatArray: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::FloatArray *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case Value_StringArray: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::StringArray *>(obj);
      return verifier.VerifyTable(ptr);
    }
    case Value_PropArray: {
      auto ptr = reinterpret_cast<const com::meituan::android::msc::renderer::generated::PropArray *>(obj);
      return verifier.VerifyTable(ptr);
    }
    default: return true;
  }
}

inline bool VerifyValueVector(::flatbuffers::Verifier &verifier, const ::flatbuffers::Vector<::flatbuffers::Offset<void>> *values, const ::flatbuffers::Vector<uint8_t> *types) {
  if (!values || !types) return !values && !types;
  if (values->size() != types->size()) return false;
  for (::flatbuffers::uoffset_t i = 0; i < values->size(); ++i) {
    if (!VerifyValue(
        verifier,  values->Get(i), types->GetEnum<Value>(i))) {
      return false;
    }
  }
  return true;
}

}  // namespace generated
}  // namespace renderer
}  // namespace msc
}  // namespace android
}  // namespace meituan
}  // namespace com

#endif  // FLATBUFFERS_GENERATED_PROPS_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
