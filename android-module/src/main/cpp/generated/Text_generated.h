// automatically generated by the <PERSON>Buffers compiler, do not modify


#ifndef FLATBUFFERS_GENERATED_TEXT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
#define FLATBUFFERS_GENERATED_TEXT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_

#include "flatbuffers/flatbuffers.h"

// Ensure the included flatbuffers.h is the same version as when this file was
// generated, otherwise it may not be compatible.
static_assert(FLATBUFFERS_VERSION_MAJOR == 25 &&
              FLATBUFFERS_VERSION_MINOR == 2 &&
              FLATBUFFERS_VERSION_REVISION == 10,
             "Non-compatible flatbuffers version included");

namespace com {
namespace meituan {
namespace android {
namespace msc {
namespace renderer {
namespace generated {

struct MeasureTextInfo;
struct MeasureTextInfoBuilder;

struct Text;
struct TextBuilder;

struct MeasureTextInfo FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef MeasureTextInfoBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TEXTS = 4
  };
  const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Text>> *texts() const {
    return GetPointer<const ::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Text>> *>(VT_TEXTS);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyOffset(verifier, VT_TEXTS) &&
           verifier.VerifyVector(texts()) &&
           verifier.VerifyVectorOfTables(texts()) &&
           verifier.EndTable();
  }
};

struct MeasureTextInfoBuilder {
  typedef MeasureTextInfo Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_texts(::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Text>>> texts) {
    fbb_.AddOffset(MeasureTextInfo::VT_TEXTS, texts);
  }
  explicit MeasureTextInfoBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<MeasureTextInfo> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<MeasureTextInfo>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<MeasureTextInfo> CreateMeasureTextInfo(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    ::flatbuffers::Offset<::flatbuffers::Vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Text>>> texts = 0) {
  MeasureTextInfoBuilder builder_(_fbb);
  builder_.add_texts(texts);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<MeasureTextInfo> CreateMeasureTextInfoDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    const std::vector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Text>> *texts = nullptr) {
  auto texts__ = texts ? _fbb.CreateVector<::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::Text>>(*texts) : 0;
  return com::meituan::android::msc::renderer::generated::CreateMeasureTextInfo(
      _fbb,
      texts__);
}

struct Text FLATBUFFERS_FINAL_CLASS : private ::flatbuffers::Table {
  typedef TextBuilder Builder;
  enum FlatBuffersVTableOffset FLATBUFFERS_VTABLE_UNDERLYING_TYPE {
    VT_TAG = 4,
    VT_TYPE = 6,
    VT_PARENT_TAG = 8,
    VT_FONT_FAMILY = 10,
    VT_FONT_WEIGHT = 12,
    VT_FONT_STYLE = 14,
    VT_FONT_SIZE = 16,
    VT_COLOR = 18,
    VT_TEXT_ALIGN = 20,
    VT_LINE_HEIGHT = 22,
    VT_TEXT_OVERFLOW = 24,
    VT_ELLIPSIZE_MODE = 26,
    VT_NUMBER_OF_LINES = 28,
    VT_TEXT = 30
  };
  int32_t tag() const {
    return GetField<int32_t>(VT_TAG, 0);
  }
  const ::flatbuffers::String *type() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TYPE);
  }
  int32_t parent_tag() const {
    return GetField<int32_t>(VT_PARENT_TAG, 0);
  }
  const ::flatbuffers::String *font_family() const {
    return GetPointer<const ::flatbuffers::String *>(VT_FONT_FAMILY);
  }
  float font_weight() const {
    return GetField<float>(VT_FONT_WEIGHT, 0.0f);
  }
  const ::flatbuffers::String *font_style() const {
    return GetPointer<const ::flatbuffers::String *>(VT_FONT_STYLE);
  }
  int32_t font_size() const {
    return GetField<int32_t>(VT_FONT_SIZE, 0);
  }
  int32_t color() const {
    return GetField<int32_t>(VT_COLOR, 0);
  }
  const ::flatbuffers::String *text_align() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TEXT_ALIGN);
  }
  float line_height() const {
    return GetField<float>(VT_LINE_HEIGHT, 0.0f);
  }
  const ::flatbuffers::String *text_overflow() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TEXT_OVERFLOW);
  }
  const ::flatbuffers::String *ellipsize_mode() const {
    return GetPointer<const ::flatbuffers::String *>(VT_ELLIPSIZE_MODE);
  }
  int32_t number_of_lines() const {
    return GetField<int32_t>(VT_NUMBER_OF_LINES, 0);
  }
  const ::flatbuffers::String *text() const {
    return GetPointer<const ::flatbuffers::String *>(VT_TEXT);
  }
  bool Verify(::flatbuffers::Verifier &verifier) const {
    return VerifyTableStart(verifier) &&
           VerifyField<int32_t>(verifier, VT_TAG, 4) &&
           VerifyOffset(verifier, VT_TYPE) &&
           verifier.VerifyString(type()) &&
           VerifyField<int32_t>(verifier, VT_PARENT_TAG, 4) &&
           VerifyOffset(verifier, VT_FONT_FAMILY) &&
           verifier.VerifyString(font_family()) &&
           VerifyField<float>(verifier, VT_FONT_WEIGHT, 4) &&
           VerifyOffset(verifier, VT_FONT_STYLE) &&
           verifier.VerifyString(font_style()) &&
           VerifyField<int32_t>(verifier, VT_FONT_SIZE, 4) &&
           VerifyField<int32_t>(verifier, VT_COLOR, 4) &&
           VerifyOffset(verifier, VT_TEXT_ALIGN) &&
           verifier.VerifyString(text_align()) &&
           VerifyField<float>(verifier, VT_LINE_HEIGHT, 4) &&
           VerifyOffset(verifier, VT_TEXT_OVERFLOW) &&
           verifier.VerifyString(text_overflow()) &&
           VerifyOffset(verifier, VT_ELLIPSIZE_MODE) &&
           verifier.VerifyString(ellipsize_mode()) &&
           VerifyField<int32_t>(verifier, VT_NUMBER_OF_LINES, 4) &&
           VerifyOffset(verifier, VT_TEXT) &&
           verifier.VerifyString(text()) &&
           verifier.EndTable();
  }
};

struct TextBuilder {
  typedef Text Table;
  ::flatbuffers::FlatBufferBuilder &fbb_;
  ::flatbuffers::uoffset_t start_;
  void add_tag(int32_t tag) {
    fbb_.AddElement<int32_t>(Text::VT_TAG, tag, 0);
  }
  void add_type(::flatbuffers::Offset<::flatbuffers::String> type) {
    fbb_.AddOffset(Text::VT_TYPE, type);
  }
  void add_parent_tag(int32_t parent_tag) {
    fbb_.AddElement<int32_t>(Text::VT_PARENT_TAG, parent_tag, 0);
  }
  void add_font_family(::flatbuffers::Offset<::flatbuffers::String> font_family) {
    fbb_.AddOffset(Text::VT_FONT_FAMILY, font_family);
  }
  void add_font_weight(float font_weight) {
    fbb_.AddElement<float>(Text::VT_FONT_WEIGHT, font_weight, 0.0f);
  }
  void add_font_style(::flatbuffers::Offset<::flatbuffers::String> font_style) {
    fbb_.AddOffset(Text::VT_FONT_STYLE, font_style);
  }
  void add_font_size(int32_t font_size) {
    fbb_.AddElement<int32_t>(Text::VT_FONT_SIZE, font_size, 0);
  }
  void add_color(int32_t color) {
    fbb_.AddElement<int32_t>(Text::VT_COLOR, color, 0);
  }
  void add_text_align(::flatbuffers::Offset<::flatbuffers::String> text_align) {
    fbb_.AddOffset(Text::VT_TEXT_ALIGN, text_align);
  }
  void add_line_height(float line_height) {
    fbb_.AddElement<float>(Text::VT_LINE_HEIGHT, line_height, 0.0f);
  }
  void add_text_overflow(::flatbuffers::Offset<::flatbuffers::String> text_overflow) {
    fbb_.AddOffset(Text::VT_TEXT_OVERFLOW, text_overflow);
  }
  void add_ellipsize_mode(::flatbuffers::Offset<::flatbuffers::String> ellipsize_mode) {
    fbb_.AddOffset(Text::VT_ELLIPSIZE_MODE, ellipsize_mode);
  }
  void add_number_of_lines(int32_t number_of_lines) {
    fbb_.AddElement<int32_t>(Text::VT_NUMBER_OF_LINES, number_of_lines, 0);
  }
  void add_text(::flatbuffers::Offset<::flatbuffers::String> text) {
    fbb_.AddOffset(Text::VT_TEXT, text);
  }
  explicit TextBuilder(::flatbuffers::FlatBufferBuilder &_fbb)
        : fbb_(_fbb) {
    start_ = fbb_.StartTable();
  }
  ::flatbuffers::Offset<Text> Finish() {
    const auto end = fbb_.EndTable(start_);
    auto o = ::flatbuffers::Offset<Text>(end);
    return o;
  }
};

inline ::flatbuffers::Offset<Text> CreateText(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t tag = 0,
    ::flatbuffers::Offset<::flatbuffers::String> type = 0,
    int32_t parent_tag = 0,
    ::flatbuffers::Offset<::flatbuffers::String> font_family = 0,
    float font_weight = 0.0f,
    ::flatbuffers::Offset<::flatbuffers::String> font_style = 0,
    int32_t font_size = 0,
    int32_t color = 0,
    ::flatbuffers::Offset<::flatbuffers::String> text_align = 0,
    float line_height = 0.0f,
    ::flatbuffers::Offset<::flatbuffers::String> text_overflow = 0,
    ::flatbuffers::Offset<::flatbuffers::String> ellipsize_mode = 0,
    int32_t number_of_lines = 0,
    ::flatbuffers::Offset<::flatbuffers::String> text = 0) {
  TextBuilder builder_(_fbb);
  builder_.add_text(text);
  builder_.add_number_of_lines(number_of_lines);
  builder_.add_ellipsize_mode(ellipsize_mode);
  builder_.add_text_overflow(text_overflow);
  builder_.add_line_height(line_height);
  builder_.add_text_align(text_align);
  builder_.add_color(color);
  builder_.add_font_size(font_size);
  builder_.add_font_style(font_style);
  builder_.add_font_weight(font_weight);
  builder_.add_font_family(font_family);
  builder_.add_parent_tag(parent_tag);
  builder_.add_type(type);
  builder_.add_tag(tag);
  return builder_.Finish();
}

inline ::flatbuffers::Offset<Text> CreateTextDirect(
    ::flatbuffers::FlatBufferBuilder &_fbb,
    int32_t tag = 0,
    const char *type = nullptr,
    int32_t parent_tag = 0,
    const char *font_family = nullptr,
    float font_weight = 0.0f,
    const char *font_style = nullptr,
    int32_t font_size = 0,
    int32_t color = 0,
    const char *text_align = nullptr,
    float line_height = 0.0f,
    const char *text_overflow = nullptr,
    const char *ellipsize_mode = nullptr,
    int32_t number_of_lines = 0,
    const char *text = nullptr) {
  auto type__ = type ? _fbb.CreateString(type) : 0;
  auto font_family__ = font_family ? _fbb.CreateString(font_family) : 0;
  auto font_style__ = font_style ? _fbb.CreateString(font_style) : 0;
  auto text_align__ = text_align ? _fbb.CreateString(text_align) : 0;
  auto text_overflow__ = text_overflow ? _fbb.CreateString(text_overflow) : 0;
  auto ellipsize_mode__ = ellipsize_mode ? _fbb.CreateString(ellipsize_mode) : 0;
  auto text__ = text ? _fbb.CreateString(text) : 0;
  return com::meituan::android::msc::renderer::generated::CreateText(
      _fbb,
      tag,
      type__,
      parent_tag,
      font_family__,
      font_weight,
      font_style__,
      font_size,
      color,
      text_align__,
      line_height,
      text_overflow__,
      ellipsize_mode__,
      number_of_lines,
      text__);
}

inline const com::meituan::android::msc::renderer::generated::MeasureTextInfo *GetMeasureTextInfo(const void *buf) {
  return ::flatbuffers::GetRoot<com::meituan::android::msc::renderer::generated::MeasureTextInfo>(buf);
}

inline const com::meituan::android::msc::renderer::generated::MeasureTextInfo *GetSizePrefixedMeasureTextInfo(const void *buf) {
  return ::flatbuffers::GetSizePrefixedRoot<com::meituan::android::msc::renderer::generated::MeasureTextInfo>(buf);
}

inline bool VerifyMeasureTextInfoBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifyBuffer<com::meituan::android::msc::renderer::generated::MeasureTextInfo>(nullptr);
}

inline bool VerifySizePrefixedMeasureTextInfoBuffer(
    ::flatbuffers::Verifier &verifier) {
  return verifier.VerifySizePrefixedBuffer<com::meituan::android::msc::renderer::generated::MeasureTextInfo>(nullptr);
}

inline void FinishMeasureTextInfoBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::MeasureTextInfo> root) {
  fbb.Finish(root);
}

inline void FinishSizePrefixedMeasureTextInfoBuffer(
    ::flatbuffers::FlatBufferBuilder &fbb,
    ::flatbuffers::Offset<com::meituan::android::msc::renderer::generated::MeasureTextInfo> root) {
  fbb.FinishSizePrefixed(root);
}

}  // namespace generated
}  // namespace renderer
}  // namespace msc
}  // namespace android
}  // namespace meituan
}  // namespace com

#endif  // FLATBUFFERS_GENERATED_TEXT_COM_MEITUAN_ANDROID_MSC_RENDERER_GENERATED_H_
