#pragma once

#include <jni.h>
#include <android/log.h>
#include <map>
#include <codecvt>
#include "props.h"
#include "types_def.h"

using namespace blink::mt;

static std::string UTF16StringToUTF8String(const char16_t *chars, size_t len) {
    std::u16string u16_string(chars, len);
    return std::wstring_convert<std::codecvt_utf8_utf16<char16_t>, char16_t>{}
            .to_bytes(u16_string);
}

static std::string JavaStringToString(JNIEnv *env, jstring str) {
    if (env == nullptr || str == nullptr) {
        return "";
    }
    const jchar *chars = env->GetStringChars(str, NULL);
    if (chars == nullptr) {
        return "";
    }
    std::string u8_string = UTF16StringToUTF8String(
            reinterpret_cast<const char16_t *>(chars), env->GetStringLength(str));
    env->ReleaseStringChars(str, chars);
    return u8_string;
}

// 辅助函数：将 jobject 转换为 PropValue
static blink::mt::PropValue jobjectToPropValue(JNIEnv *env, jobject obj) {
    if (obj == nullptr) {
        return PropValueType::Null{};
    }

    // 获取对象的类
    jclass objClass = env->GetObjectClass(obj);

    // 获取各种类型的类引用
    jclass stringClass = env->FindClass("java/lang/String");
    jclass numberClass = env->FindClass("java/lang/Number");
    jclass booleanClass = env->FindClass("java/lang/Boolean");
    jclass mapClass = env->FindClass("java/util/Map");
    jclass listClass = env->FindClass("java/util/List");
    jclass collectionClass = env->FindClass("java/util/Collection");

    if (env->IsInstanceOf(obj, stringClass)) {
        // String 类型
        std::string str = JavaStringToString(env, (jstring)obj);
        return PropValueType::String{str};
    } else if (env->IsInstanceOf(obj, booleanClass)) {
        jmethodID booleanValueMethod = env->GetMethodID(booleanClass, "booleanValue", "()Z");
        bool value = env->CallBooleanMethod(obj, booleanValueMethod);
        return value ? PropValueType::Number{1} : PropValueType::Number{0};
    } else if (env->IsInstanceOf(obj, numberClass)) {
        // Number 类型 - 需要根据具体的 Number 子类处理
        jclass integerClass = env->FindClass("java/lang/Integer");
        jclass doubleClass = env->FindClass("java/lang/Double");
        jclass floatClass = env->FindClass("java/lang/Float");
        jclass longClass = env->FindClass("java/lang/Long");

        if (env->IsInstanceOf(obj, integerClass)) {
            jmethodID intValueMethod = env->GetMethodID(integerClass, "intValue", "()I");
            int value = env->CallIntMethod(obj, intValueMethod);
            return PropValueType::Number{static_cast<double>(value)};
        } else if (env->IsInstanceOf(obj, doubleClass)) {
            jmethodID doubleValueMethod = env->GetMethodID(doubleClass, "doubleValue", "()D");
            double value = env->CallDoubleMethod(obj, doubleValueMethod);
            return PropValueType::Number{value};
        } else if (env->IsInstanceOf(obj, floatClass)) {
            jmethodID floatValueMethod = env->GetMethodID(floatClass, "floatValue", "()F");
            float value = env->CallFloatMethod(obj, floatValueMethod);
            return PropValueType::Number{static_cast<double>(value)};
        } else if (env->IsInstanceOf(obj, longClass)) {
            jmethodID longValueMethod = env->GetMethodID(longClass, "longValue", "()J");
            long value = env->CallLongMethod(obj, longValueMethod);
            return PropValueType::Number{static_cast<double>(value)};
        }
    } else if (env->IsInstanceOf(obj, mapClass)) {
        // Map 类型 -> Dictionary
        PropValueType::Dictionary dict;

        // 获取 entrySet 方法
        jmethodID entrySetMethod = env->GetMethodID(mapClass, "entrySet", "()Ljava/util/Set;");
        jobject entrySet = env->CallObjectMethod(obj, entrySetMethod);

        // 获取 iterator
        jclass setClass = env->FindClass("java/util/Set");
        jmethodID iteratorMethod = env->GetMethodID(setClass, "iterator", "()Ljava/util/Iterator;");
        jobject iterator = env->CallObjectMethod(entrySet, iteratorMethod);

        // 遍历处理
        jclass iteratorClass = env->FindClass("java/util/Iterator");
        jmethodID hasNextMethod = env->GetMethodID(iteratorClass, "hasNext", "()Z");
        jmethodID nextMethod = env->GetMethodID(iteratorClass, "next", "()Ljava/lang/Object;");

        jclass entryClass = env->FindClass("java/util/Map$Entry");
        jmethodID getKeyMethod = env->GetMethodID(entryClass, "getKey", "()Ljava/lang/Object;");
        jmethodID getValueMethod = env->GetMethodID(entryClass, "getValue", "()Ljava/lang/Object;");

        while (env->CallBooleanMethod(iterator, hasNextMethod)) {
            jobject entry = env->CallObjectMethod(iterator, nextMethod);
            jobject key = env->CallObjectMethod(entry, getKeyMethod);
            jobject value = env->CallObjectMethod(entry, getValueMethod);

            std::string keyStr = JavaStringToString(env, (jstring)key);
            PropValue propValue = jobjectToPropValue(env, value);
            dict[keyStr] = propValue;

            env->DeleteLocalRef(entry);
            env->DeleteLocalRef(key);
            env->DeleteLocalRef(value);
        }

        env->DeleteLocalRef(entrySet);
        env->DeleteLocalRef(iterator);

        return dict;
    } else if (env->IsInstanceOf(obj, listClass) || env->IsInstanceOf(obj, collectionClass)) {
        // List/Collection 类型 -> Array
        PropValueType::Array array;

        // 获取 iterator
        jmethodID iteratorMethod = env->GetMethodID(collectionClass, "iterator", "()Ljava/util/Iterator;");
        jobject iterator = env->CallObjectMethod(obj, iteratorMethod);

        jclass iteratorClass = env->FindClass("java/util/Iterator");
        jmethodID hasNextMethod = env->GetMethodID(iteratorClass, "hasNext", "()Z");
        jmethodID nextMethod = env->GetMethodID(iteratorClass, "next", "()Ljava/lang/Object;");

        while (env->CallBooleanMethod(iterator, hasNextMethod)) {
            jobject element = env->CallObjectMethod(iterator, nextMethod);
            PropValue propValue = jobjectToPropValue(env, element);
            array.push_back(propValue);

            env->DeleteLocalRef(element);
        }

        env->DeleteLocalRef(iterator);

        return array;
    }

    // 默认返回 Null
    return PropValueType::Null{};
}

static std::shared_ptr<const blink::mt::Props> jobjectToProps(JNIEnv *env, jobject jMap) {
    // 获取 Map 类
    jclass mapClass = env->FindClass("java/util/Map");

    // 获取 entrySet 方法 ID
    jmethodID entrySetMethod = env->GetMethodID(mapClass, "entrySet", "()Ljava/util/Set;");

    // 调用 entrySet 方法
    jobject entrySet = env->CallObjectMethod(jMap, entrySetMethod);

    // 获取 Set 类
    jclass setClass = env->FindClass("java/util/Set");

    // 获取 iterator 方法 ID
    jmethodID iteratorMethod = env->GetMethodID(setClass, "iterator", "()Ljava/util/Iterator;");

    // 调用 iterator 方法
    jobject iterator = env->CallObjectMethod(entrySet, iteratorMethod);

    // 获取 Iterator 类
    jclass iteratorClass = env->FindClass("java/util/Iterator");

    // 获取 hasNext 和 next 方法 ID
    jmethodID hasNextMethod = env->GetMethodID(iteratorClass, "hasNext", "()Z");
    jmethodID nextMethod = env->GetMethodID(iteratorClass, "next", "()Ljava/lang/Object;");

    // 获取 Map.Entry 类
    jclass entryClass = env->FindClass("java/util/Map$Entry");

    // 获取 getKey 和 getValue 方法 ID
    jmethodID getKeyMethod = env->GetMethodID(entryClass, "getKey", "()Ljava/lang/Object;");
    jmethodID getValueMethod = env->GetMethodID(entryClass, "getValue", "()Ljava/lang/Object;");

    blink::mt::PropsBuilder builder;
    // 遍历 Map
    while (env->CallBooleanMethod(iterator, hasNextMethod)) {
        jobject entry = env->CallObjectMethod(iterator, nextMethod);
        jobject key = env->CallObjectMethod(entry, getKeyMethod);
        jobject value = env->CallObjectMethod(entry, getValueMethod);

        // 假设 key 和 value 是 String 类型
        std::string keyStr = JavaStringToString(env, (jstring)key);
//        std::string valueStr = JavaStringToString(env, (jstring)value);
        // 将 value 转换为 PropValue
        blink::mt::PropValue propValue = jobjectToPropValue(env, value);
        // 将键值对添加到 map
        builder.setProp(keyStr, propValue);

        // 删除本地引用
        env->DeleteLocalRef(entry);
        env->DeleteLocalRef(key);
        env->DeleteLocalRef(value);
    }

    // 删除本地引用
    env->DeleteLocalRef(entrySet);
    env->DeleteLocalRef(iterator);

    return builder.getProps();
}

static long jlongToLong(JNIEnv *env, jobject jLong) {
    jclass longClass = env->FindClass("java/lang/Long");
    jmethodID longValueMethod = env->GetMethodID(longClass, "longValue", "()J");
    return env->CallLongMethod(jLong, longValueMethod);
}

static std::vector<Tag> jObjectToVector(JNIEnv *env, jintArray array) {
    jsize length = env->GetArrayLength(array);
    // 获取数组元素
    jint *elements = env->GetIntArrayElements(array, nullptr);
    // 创建 std::vector<long> 并复制数据
    std::vector<Tag> result;
    result.reserve(length); // 预留空间以提高效率
    for (jsize i = 0; i < length; ++i) {
        result.push_back(elements[i]);
    }
    // 释放 jlongArray 元素
    env->ReleaseIntArrayElements(array, elements, 0);
    return result;
}

static std::vector<std::string> convertJObjectArrayToVector(JNIEnv *env, jobjectArray stringArray) {
    std::vector<std::string> result;
    // 获取数组的长度
    jsize arrayLength = env->GetArrayLength(stringArray);
    // 遍历数组
    for (jsize i = 0; i < arrayLength; i++) {
        // 获取每个字符串元素，注意这里传递的是索引 i
        jstring jstr = (jstring) env->GetObjectArrayElement(stringArray, i);
        // 将 jstring 转换为 C 字符串
        const char *cstr = env->GetStringUTFChars(jstr, nullptr);
        // 将 C 字符串转换为 std::string 并存储到 vector 中
        result.push_back(std::string(cstr));
        // 释放 C 字符串
        env->ReleaseStringUTFChars(jstr, cstr);
        // 释放局部引用
        env->DeleteLocalRef(jstr);
    }
    return result;
}

static jstring stoJstring(JNIEnv *env, const char *pat) {
    jclass strClass = (env)->FindClass("java/lang/String");
    jmethodID ctorID = (env)->GetMethodID(strClass, "<init>", "([BLjava/lang/String;)V");
    jbyteArray bytes = (env)->NewByteArray(strlen(pat));
    (env)->SetByteArrayRegion(bytes, 0, strlen(pat), (jbyte *) pat);
    jstring encoding = (env)->NewStringUTF("GB2312");
    return (jstring) (env)->NewObject(strClass, ctorID, bytes, encoding);
}
