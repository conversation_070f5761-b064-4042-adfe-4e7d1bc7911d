
#ifndef __APPLE__

#include "props.h"
#include <sstream>
#include <iomanip>  // 添加这个头文件
#include <cctype>   // 添加这个头文件用于std::isspace, std::isdigit
#include "cJSON.h"

namespace blink::mt {

namespace {
// 将 PropValue 转换为 cJSON 对象
cJSON* propValueToJson(const PropValue& value) {
  return std::visit([](auto&& arg) -> cJSON* {
    using T = std::decay_t<decltype(arg)>;

    if constexpr (std::is_same_v<T, PropValueType::Null>) {
      return cJSON_CreateNull();
    } else if constexpr (std::is_same_v<T, PropValueType::Number>) {
      return cJSON_CreateNumber(arg);
    } else if constexpr (std::is_same_v<T, PropValueType::String>) {
      return cJSON_CreateString(arg.c_str());
    } else if constexpr (std::is_same_v<T, PropValueType::Array>) {
      cJSON* array = cJSON_CreateArray();
      for (const auto& item : arg) {
        cJSON* jsonItem = propValueToJson(item);
        if (jsonItem) {
          cJSON_AddItemToArray(array, jsonItem);
        }
      }
      return array;
    } else if constexpr (std::is_same_v<T, PropValueType::Dictionary>) {
      cJSON* object = cJSON_CreateObject();
      for (const auto& pair : arg) {
        cJSON* jsonValue = propValueToJson(pair.second);
        if (jsonValue) {
          cJSON_AddItemToObject(object, pair.first.c_str(), jsonValue);
        }
      }
      return object;
    }
    return nullptr;
  }, value);
}

// 将 cJSON 对象转换为 PropValue
PropValue jsonToPropValue(const cJSON* json) {
  if (!json) {
    return PropValue();
  }

  if (cJSON_IsNull(json)) {
    return PropValue();
  } else if (cJSON_IsNumber(json)) {
    // 使用直接访问成员的方式
    return PropValue(json->valuedouble);
  } else if (cJSON_IsString(json)) {
    // 使用直接访问成员的方式
    return PropValue(std::string(json->valuestring ? json->valuestring : ""));
  } else if (cJSON_IsArray(json)) {
    PropValueType::Array array;
    const cJSON* item = nullptr;
    cJSON_ArrayForEach(item, json) {
      array.push_back(jsonToPropValue(item));
    }
    return PropValue(array);
  } else if (cJSON_IsObject(json)) {
    PropValueType::Dictionary dict;
    const cJSON* item = nullptr;
    cJSON_ArrayForEach(item, json) {
      if (item->string) {
        dict[std::string(item->string)] = jsonToPropValue(item);
      }
    }
    return PropValue(dict);
  } else if (cJSON_IsBool(json)) {
    // 使用直接访问成员的方式
    return PropValue(cJSON_IsTrue(json) ? 1.0 : 0.0);
  }

  return PropValue();
}

}

std::string propsToJson(const shared_ptr<const blink::mt::Props>& props){
  if (!props) {
    return "{}";
  }

  cJSON* root = cJSON_CreateObject();
  if (!root) {
    return "{}";
  }

  props->forEach([&](const std::string& key, const PropValue& value) {
    cJSON* jsonValue = propValueToJson(value);
    if (jsonValue) {
      cJSON_AddItemToObject(root, key.c_str(), jsonValue);
    }
  });

  char* jsonString = cJSON_Print(root);
  std::string result;
  if (jsonString) {
    result = std::string(jsonString);
    free(jsonString);
  } else {
    result = "{}";
  }

  cJSON_Delete(root);
  return result;
}

const std::shared_ptr<const blink::mt::Props> jsonToProps(const std::string& json_string){
  if (json_string.empty()) {
    return std::make_shared<const Props>();
  }

  cJSON* root = cJSON_Parse(json_string.c_str());
  if (!root) {
    return std::make_shared<const Props>();
  }

  if (!cJSON_IsObject(root)) {
    cJSON_Delete(root);
    return std::make_shared<const Props>();
  }

  PropsBuilder builder;
  const cJSON* item = nullptr;
  cJSON_ArrayForEach(item, root) {
    if (item->string) {
      PropValue value = jsonToPropValue(item);
      builder.setProp(std::string(item->string), value);
    }
  }

  cJSON_Delete(root);
  return builder.getProps();
}

} // namespace blink::mt
#endif
