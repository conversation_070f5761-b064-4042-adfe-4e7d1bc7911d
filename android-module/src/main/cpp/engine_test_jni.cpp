////
//// Created by 司雨寒 on 2025/6/10.
////
//
////#include "engine_test_jni.h"
//
//#include <jni.h>
//#include <string>
//#include <memory>
//#include <iostream>
//#include <wxs/js_engine.h>
//
//extern "C" JNIEXPORT jstring JNICALL
//Java_com_meituan_android_msc_renderer_JSEngineTest_TestEngine(JNIEnv* env, jclass) {
//    blink::mt::JSEngine engine(nullptr);
//    std::string script_str = "NativeBridge.importScripts([\"111\",\"222\",\"333\"]);\
//        NativeBridge.invoke(\"1\",\"2\",\"3\");\
//        NativeBridge.invoke(\"4\",\"5\",\"6\");\
//        NativeBridge.nativeCallSyncHook(\"1\",\"2\",\"3\",\"4\");\
//        NativeTiming.createTimer(1, 2, 3, 4);\
//        NativeTiming.deleteTimer(1);\
//        nativeLoggingHook(\"hello\", 1);\
//        WxsJSBridge.invoke = (a, b) => {\
//        let ret = `args: ${a}, ${b}`;\
//        return ret;\
//        }";
//    auto script_buffer = std::make_shared<facebook::jsi::StringBuffer>(std::move(script_str));
//    auto ret = engine.EvaluateJavaScript(script_buffer, "test.js");
//    facebook::jsi::Value args[2] = {
//            facebook::jsi::Value(42),
//            facebook::jsi::String::createFromUtf8(*engine.runtime_, "lalala")
//    };
//    auto ret2 = engine.WxsJSBridge_invoke(args, 2);
//    //std::cout << "WxsJSBridge.invoke return: " << ret2.asString(*engine.runtime_).utf8(*engine.runtime_) << "\n";
//
//    return env->NewStringUTF(ret2.asString(*engine.runtime_).utf8(*engine.runtime_).c_str());
//}
