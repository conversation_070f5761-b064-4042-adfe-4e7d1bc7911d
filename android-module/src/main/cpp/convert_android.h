//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/11.
//

#ifndef RENDERER_CONVERT_ANDROID_H
#define RENDERER_CONVERT_ANDROID_H

#include <flatbuffers/flatbuffers.h>
#include "Command_generated.h"
#include "event_data.h"
#include "logger.h"

namespace blink::mt {

    std::pair<uint8_t *, size_t> GetResult(flatbuffers::FlatBufferBuilder &builder,
                                           const blink::mt::UICommands &buffer, float density,
                                           function<std::shared_ptr<const NativeStyle>(int)> oldDisplayInfoGetter,
                                           function<void(int, std::shared_ptr<const NativeStyle>)> oldDisplayInfoSetter);

  std::shared_ptr<msc::native_dom::EventData> constructTouchEventFromBytes(const uint8_t *buf, int* ele_id);
  std::shared_ptr<msc::native_dom::EventData> constructComponentEventFromBytes(const uint8_t *buf, int* ele_id);
}

#endif //RENDERER_CONVERT_ANDROID_H
