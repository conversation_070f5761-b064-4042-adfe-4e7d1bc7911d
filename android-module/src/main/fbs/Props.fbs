namespace com.meituan.android.msc.renderer.generated;

table IntValue    { val: int; }
table FloatValue  { val: float; }
table StringValue { val: string; }
table BoolValue   { val: bool; }
table IntArray    { vals:[int]; }
table FloatArray  { vals:[float]; }
table StringArray { vals:[string]; }
table PropArray   { vals:[Prop]; }

union Value {
  IntValue,    // int
  FloatValue,  // float
  StringValue, // string
  BoolValue,    // bool
  IntArray,     // [int]
  FloatArray,   // [float]
  StringArray,  // [string]
  PropArray,    // [Prop]
}

table Prop {
  key: string;
  value_type: Value;
  val: Value;
}
