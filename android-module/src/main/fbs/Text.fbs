namespace com.meituan.android.msc.renderer.generated;

table MeasureTextInfo {
  texts: [Text];
}

table Text {
  tag: int;
  type: string;
  parent_tag: int;
  font_family: string;
  font_weight: float;
  font_style: string;
  font_size: int;
  color: int;
  text_align: string;
  line_height: float;
  text_overflow: string;
  ellipsize_mode: string;
  number_of_lines: int;
  text: string;
}

root_type MeasureTextInfo;  // 声明根类型