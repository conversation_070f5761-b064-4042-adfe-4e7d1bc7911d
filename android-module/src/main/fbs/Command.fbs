include "Props.fbs";

namespace com.meituan.android.msc.renderer.generated;

enum LayoutReason : byte {
  BatchDidComplete = 0,
  PreBatchDidComplete = 1,
  WXSSetStyle = 2
}

table CreateViewCommand { name: string; props: [Prop]; }
table UpdateViewCommand { name: string; props: [Prop]; }
table DeleteViewsCommand  { tags : [int]; }
table InsertChildViewsCommand { insert: [ViewAtIndex]; }
table RemoveChildViewsCommand { remove: [int]; }
table UpdateViewFrameCommand { parentTag:int;  x: float;  y: float;   w: float;   h: float;}
table UpdateViewStyleCommamnd { val: DisplayItem; }
table UpdateTransformCommand { transform: [Prop]; }
table UpdateTextCommamnd  {x: float; y: float; w: float; h: float; textPaddingTop :float; textPaddingLeft: float; textPaddingRight: float; textPaddingBottom: float;}
table BatchDidFinishCommand { val: LayoutReason; }
table CreateKeyframesAnimationCommand{ tags: [int]; keyframes: [CreateAnimationKeyFrame]; duration: int; callback: ulong = null; }
table ClearKeyframesAnimationCommand{ tags: [int]; options: ClearAnimationOptions; callback: ulong = null; }

union CommandValue {
  CreateViewCommand,
  UpdateViewCommand,
  UpdateViewFrameCommand,
  DeleteViewsCommand,
  InsertChildViewsCommand,
  RemoveChildViewsCommand,
  UpdateViewStyleCommamnd,
  UpdateTransformCommand,
  UpdateTextCommamnd,   // [float]
  BatchDidFinishCommand,  // [string]
  CreateKeyframesAnimationCommand,
  ClearKeyframesAnimationCommand
}

table CreateAnimationKeyFrame {
    offset: float = null;
    ease: string;
    opacity: float = null;
    rotate: float = null;
    scale: [float];
    translate: [string];
    width: float = null;
    width_str: string;
}

table ClearAnimationOptions {
    opacity: bool = null;
    scale: bool = null;
    translate: bool = null;
    rotate: bool = null;
}

table ViewAtIndex {
    tag: int;
    index: int;
}

table Command {
  id: int;
  tag: int;
  value_type: CommandValue;
  val: CommandValue;
}

table CommandArray {
    vals:[Command];
}

table DisplayItem {
  tag:int;
  container_tag:int;
  view_name:string;
  overflow_x: string;
  overflow_y: string;
  background_color: int;
  color: int;
  background_image: [PropArray];
  background_size: [PropArray];
  background_repeat: [StringArray];
  border_top_style: string;
  border_top_width: float;
  border_top_color: int;
  border_right_style: string;
  border_right_width: float;
  border_right_color: int;
  border_bottom_style: string;
  border_bottom_width: float;
  border_bottom_color: int;
  border_left_style: string;
  border_left_width: float;
  border_left_color: int;
  border_top_left_radius: float;
  border_top_right_radius: float;
  border_bottom_right_radius: float;
  border_bottom_left_radius: float;
  opacity: float;
  pointer_events: string;
  box_shadow: [Prop];
  font_size: float;
  font_style: string;
  text_align: string;
  text_padding: [float];
  change_keys: [int];
}

// 5. 根类型声明（必须有分号）
root_type CommandArray;