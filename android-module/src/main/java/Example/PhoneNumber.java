// automatically generated by the <PERSON><PERSON>uffers compiler, do not modify

package Example;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class PhoneNumber extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static PhoneNumber getRootAsPhoneNumber(ByteBuffer _bb) { return getRootAsPhoneNumber(_bb, new PhoneNumber()); }
  public static PhoneNumber getRootAsPhoneNumber(ByteBuffer _bb, PhoneNumber obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public PhoneNumber __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public String number() { int o = __offset(4); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer numberAsByteBuffer() { return __vector_as_bytebuffer(4, 1); }
  public ByteBuffer numberInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 4, 1); }
  public String type() { int o = __offset(6); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer typeAsByteBuffer() { return __vector_as_bytebuffer(6, 1); }
  public ByteBuffer typeInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 6, 1); }

  public static int createPhoneNumber(FlatBufferBuilder builder,
      int numberOffset,
      int typeOffset) {
    builder.startTable(2);
    PhoneNumber.addType(builder, typeOffset);
    PhoneNumber.addNumber(builder, numberOffset);
    return PhoneNumber.endPhoneNumber(builder);
  }

  public static void startPhoneNumber(FlatBufferBuilder builder) { builder.startTable(2); }
  public static void addNumber(FlatBufferBuilder builder, int numberOffset) { builder.addOffset(0, numberOffset, 0); }
  public static void addType(FlatBufferBuilder builder, int typeOffset) { builder.addOffset(1, typeOffset, 0); }
  public static int endPhoneNumber(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public PhoneNumber get(int j) { return get(new PhoneNumber(), j); }
    public PhoneNumber get(PhoneNumber obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

