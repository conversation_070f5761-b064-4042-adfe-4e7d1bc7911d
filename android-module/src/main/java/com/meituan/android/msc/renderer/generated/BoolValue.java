// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class BoolValue extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static BoolValue getRootAsBoolValue(ByteBuffer _bb) { return getRootAsBoolValue(_bb, new BoolValue()); }
  public static BoolValue getRootAsBoolValue(ByteBuffer _bb, BoolValue obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public BoolValue __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public boolean val() { int o = __offset(4); return o != 0 ? 0!=bb.get(o + bb_pos) : false; }

  public static int createBoolValue(FlatBufferBuilder builder,
      boolean val) {
    builder.startTable(1);
    BoolValue.addVal(builder, val);
    return BoolValue.endBoolValue(builder);
  }

  public static void startBoolValue(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addVal(FlatBufferBuilder builder, boolean val) { builder.addBoolean(0, val, false); }
  public static int endBoolValue(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public BoolValue get(int j) { return get(new BoolValue(), j); }
    public BoolValue get(BoolValue obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

