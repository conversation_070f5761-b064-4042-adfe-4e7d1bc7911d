// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class FbsTouch extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static FbsTouch getRootAsFbsTouch(ByteBuffer _bb) { return getRootAsFbsTouch(_bb, new FbsTouch()); }
  public static FbsTouch getRootAsFbsTouch(ByteBuffer _bb, FbsTouch obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public FbsTouch __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float pageX() { int o = __offset(4); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float pageY() { int o = __offset(6); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float locationX() { int o = __offset(8); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float locationY() { int o = __offset(10); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public long timestamp() { int o = __offset(12); return o != 0 ? bb.getLong(o + bb_pos) : 0L; }
  public int target() { int o = __offset(14); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public int touchId() { int o = __offset(16); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public boolean changed() { int o = __offset(18); return o != 0 ? 0!=bb.get(o + bb_pos) : false; }

  public static int createFbsTouch(FlatBufferBuilder builder,
      float pageX,
      float pageY,
      float locationX,
      float locationY,
      long timestamp,
      int target,
      int touchId,
      boolean changed) {
    builder.startTable(8);
    FbsTouch.addTimestamp(builder, timestamp);
    FbsTouch.addTouchId(builder, touchId);
    FbsTouch.addTarget(builder, target);
    FbsTouch.addLocationY(builder, locationY);
    FbsTouch.addLocationX(builder, locationX);
    FbsTouch.addPageY(builder, pageY);
    FbsTouch.addPageX(builder, pageX);
    FbsTouch.addChanged(builder, changed);
    return FbsTouch.endFbsTouch(builder);
  }

  public static void startFbsTouch(FlatBufferBuilder builder) { builder.startTable(8); }
  public static void addPageX(FlatBufferBuilder builder, float pageX) { builder.addFloat(0, pageX, 0.0f); }
  public static void addPageY(FlatBufferBuilder builder, float pageY) { builder.addFloat(1, pageY, 0.0f); }
  public static void addLocationX(FlatBufferBuilder builder, float locationX) { builder.addFloat(2, locationX, 0.0f); }
  public static void addLocationY(FlatBufferBuilder builder, float locationY) { builder.addFloat(3, locationY, 0.0f); }
  public static void addTimestamp(FlatBufferBuilder builder, long timestamp) { builder.addLong(4, timestamp, 0L); }
  public static void addTarget(FlatBufferBuilder builder, int target) { builder.addInt(5, target, 0); }
  public static void addTouchId(FlatBufferBuilder builder, int touchId) { builder.addInt(6, touchId, 0); }
  public static void addChanged(FlatBufferBuilder builder, boolean changed) { builder.addBoolean(7, changed, false); }
  public static int endFbsTouch(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public FbsTouch get(int j) { return get(new FbsTouch(), j); }
    public FbsTouch get(FbsTouch obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

