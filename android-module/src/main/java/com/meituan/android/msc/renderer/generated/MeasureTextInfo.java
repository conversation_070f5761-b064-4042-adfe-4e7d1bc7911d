// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class MeasureTextInfo extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static MeasureTextInfo getRootAsMeasureTextInfo(ByteBuffer _bb) { return getRootAsMeasureTextInfo(_bb, new MeasureTextInfo()); }
  public static MeasureTextInfo getRootAsMeasureTextInfo(ByteBuffer _bb, MeasureTextInfo obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public MeasureTextInfo __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public com.meituan.android.msc.renderer.generated.Text texts(int j) { return texts(new com.meituan.android.msc.renderer.generated.Text(), j); }
  public com.meituan.android.msc.renderer.generated.Text texts(com.meituan.android.msc.renderer.generated.Text obj, int j) { int o = __offset(4); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int textsLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.Text.Vector textsVector() { return textsVector(new com.meituan.android.msc.renderer.generated.Text.Vector()); }
  public com.meituan.android.msc.renderer.generated.Text.Vector textsVector(com.meituan.android.msc.renderer.generated.Text.Vector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }

  public static int createMeasureTextInfo(FlatBufferBuilder builder,
      int textsOffset) {
    builder.startTable(1);
    MeasureTextInfo.addTexts(builder, textsOffset);
    return MeasureTextInfo.endMeasureTextInfo(builder);
  }

  public static void startMeasureTextInfo(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addTexts(FlatBufferBuilder builder, int textsOffset) { builder.addOffset(0, textsOffset, 0); }
  public static int createTextsVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startTextsVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endMeasureTextInfo(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }
  public static void finishMeasureTextInfoBuffer(FlatBufferBuilder builder, int offset) { builder.finish(offset); }
  public static void finishSizePrefixedMeasureTextInfoBuffer(FlatBufferBuilder builder, int offset) { builder.finishSizePrefixed(offset); }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public MeasureTextInfo get(int j) { return get(new MeasureTextInfo(), j); }
    public MeasureTextInfo get(MeasureTextInfo obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

