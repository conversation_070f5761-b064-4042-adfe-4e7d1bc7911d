// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class ClearAnimationOptions extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static ClearAnimationOptions getRootAsClearAnimationOptions(ByteBuffer _bb) { return getRootAsClearAnimationOptions(_bb, new ClearAnimationOptions()); }
  public static ClearAnimationOptions getRootAsClearAnimationOptions(ByteBuffer _bb, ClearAnimationOptions obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public ClearAnimationOptions __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public boolean hasOpacity() { return 0 != __offset(4); }
  public boolean opacity() { int o = __offset(4); return o != 0 ? 0!=bb.get(o + bb_pos) : false; }
  public boolean hasScale() { return 0 != __offset(6); }
  public boolean scale() { int o = __offset(6); return o != 0 ? 0!=bb.get(o + bb_pos) : false; }
  public boolean hasTranslate() { return 0 != __offset(8); }
  public boolean translate() { int o = __offset(8); return o != 0 ? 0!=bb.get(o + bb_pos) : false; }
  public boolean hasRotate() { return 0 != __offset(10); }
  public boolean rotate() { int o = __offset(10); return o != 0 ? 0!=bb.get(o + bb_pos) : false; }

  public static int createClearAnimationOptions(FlatBufferBuilder builder,
      boolean opacity,
      boolean scale,
      boolean translate,
      boolean rotate) {
    builder.startTable(4);
    ClearAnimationOptions.addRotate(builder, rotate);
    ClearAnimationOptions.addTranslate(builder, translate);
    ClearAnimationOptions.addScale(builder, scale);
    ClearAnimationOptions.addOpacity(builder, opacity);
    return ClearAnimationOptions.endClearAnimationOptions(builder);
  }

  public static void startClearAnimationOptions(FlatBufferBuilder builder) { builder.startTable(4); }
  public static void addOpacity(FlatBufferBuilder builder, boolean opacity) { builder.addBoolean(0, opacity, false); }
  public static void addScale(FlatBufferBuilder builder, boolean scale) { builder.addBoolean(1, scale, false); }
  public static void addTranslate(FlatBufferBuilder builder, boolean translate) { builder.addBoolean(2, translate, false); }
  public static void addRotate(FlatBufferBuilder builder, boolean rotate) { builder.addBoolean(3, rotate, false); }
  public static int endClearAnimationOptions(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public ClearAnimationOptions get(int j) { return get(new ClearAnimationOptions(), j); }
    public ClearAnimationOptions get(ClearAnimationOptions obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

