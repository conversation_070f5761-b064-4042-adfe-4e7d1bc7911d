// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class Command extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static Command getRootAsCommand(ByteBuffer _bb) { return getRootAsCommand(_bb, new Command()); }
  public static Command getRootAsCommand(ByteBuffer _bb, Command obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public Command __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int id() { int o = __offset(4); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public int tag() { int o = __offset(6); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public byte valueTypeType() { int o = __offset(8); return o != 0 ? bb.get(o + bb_pos) : 0; }
  public Table valueType(Table obj) { int o = __offset(10); return o != 0 ? __union(obj, o + bb_pos) : null; }
  public byte valType() { int o = __offset(12); return o != 0 ? bb.get(o + bb_pos) : 0; }
  public Table val(Table obj) { int o = __offset(14); return o != 0 ? __union(obj, o + bb_pos) : null; }

  public static int createCommand(FlatBufferBuilder builder,
      int id,
      int tag,
      byte valueTypeType,
      int valueTypeOffset,
      byte valType,
      int valOffset) {
    builder.startTable(6);
    Command.addVal(builder, valOffset);
    Command.addValueType(builder, valueTypeOffset);
    Command.addTag(builder, tag);
    Command.addId(builder, id);
    Command.addValType(builder, valType);
    Command.addValueTypeType(builder, valueTypeType);
    return Command.endCommand(builder);
  }

  public static void startCommand(FlatBufferBuilder builder) { builder.startTable(6); }
  public static void addId(FlatBufferBuilder builder, int id) { builder.addInt(0, id, 0); }
  public static void addTag(FlatBufferBuilder builder, int tag) { builder.addInt(1, tag, 0); }
  public static void addValueTypeType(FlatBufferBuilder builder, byte valueTypeType) { builder.addByte(2, valueTypeType, 0); }
  public static void addValueType(FlatBufferBuilder builder, int valueTypeOffset) { builder.addOffset(3, valueTypeOffset, 0); }
  public static void addValType(FlatBufferBuilder builder, byte valType) { builder.addByte(4, valType, 0); }
  public static void addVal(FlatBufferBuilder builder, int valOffset) { builder.addOffset(5, valOffset, 0); }
  public static int endCommand(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public Command get(int j) { return get(new Command(), j); }
    public Command get(Command obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

