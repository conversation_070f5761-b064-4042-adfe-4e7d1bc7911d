// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class UpdateTransformCommand extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static UpdateTransformCommand getRootAsUpdateTransformCommand(ByteBuffer _bb) { return getRootAsUpdateTransformCommand(_bb, new UpdateTransformCommand()); }
  public static UpdateTransformCommand getRootAsUpdateTransformCommand(ByteBuffer _bb, UpdateTransformCommand obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public UpdateTransformCommand __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public com.meituan.android.msc.renderer.generated.Prop transform(int j) { return transform(new com.meituan.android.msc.renderer.generated.Prop(), j); }
  public com.meituan.android.msc.renderer.generated.Prop transform(com.meituan.android.msc.renderer.generated.Prop obj, int j) { int o = __offset(4); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int transformLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.Prop.Vector transformVector() { return transformVector(new com.meituan.android.msc.renderer.generated.Prop.Vector()); }
  public com.meituan.android.msc.renderer.generated.Prop.Vector transformVector(com.meituan.android.msc.renderer.generated.Prop.Vector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }

  public static int createUpdateTransformCommand(FlatBufferBuilder builder,
      int transformOffset) {
    builder.startTable(1);
    UpdateTransformCommand.addTransform(builder, transformOffset);
    return UpdateTransformCommand.endUpdateTransformCommand(builder);
  }

  public static void startUpdateTransformCommand(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addTransform(FlatBufferBuilder builder, int transformOffset) { builder.addOffset(0, transformOffset, 0); }
  public static int createTransformVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startTransformVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endUpdateTransformCommand(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public UpdateTransformCommand get(int j) { return get(new UpdateTransformCommand(), j); }
    public UpdateTransformCommand get(UpdateTransformCommand obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

