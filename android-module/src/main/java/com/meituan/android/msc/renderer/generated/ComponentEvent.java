// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class ComponentEvent extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static ComponentEvent getRootAsComponentEvent(ByteBuffer _bb) { return getRootAsComponentEvent(_bb, new ComponentEvent()); }
  public static ComponentEvent getRootAsComponentEvent(ByteBuffer _bb, ComponentEvent obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public ComponentEvent __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public String eventName() { int o = __offset(4); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer eventNameAsByteBuffer() { return __vector_as_bytebuffer(4, 1); }
  public ByteBuffer eventNameInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 4, 1); }
  public int target() { int o = __offset(6); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public com.meituan.android.msc.renderer.generated.Prop props(int j) { return props(new com.meituan.android.msc.renderer.generated.Prop(), j); }
  public com.meituan.android.msc.renderer.generated.Prop props(com.meituan.android.msc.renderer.generated.Prop obj, int j) { int o = __offset(8); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int propsLength() { int o = __offset(8); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.Prop.Vector propsVector() { return propsVector(new com.meituan.android.msc.renderer.generated.Prop.Vector()); }
  public com.meituan.android.msc.renderer.generated.Prop.Vector propsVector(com.meituan.android.msc.renderer.generated.Prop.Vector obj) { int o = __offset(8); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }

  public static int createComponentEvent(FlatBufferBuilder builder,
      int eventNameOffset,
      int target,
      int propsOffset) {
    builder.startTable(3);
    ComponentEvent.addProps(builder, propsOffset);
    ComponentEvent.addTarget(builder, target);
    ComponentEvent.addEventName(builder, eventNameOffset);
    return ComponentEvent.endComponentEvent(builder);
  }

  public static void startComponentEvent(FlatBufferBuilder builder) { builder.startTable(3); }
  public static void addEventName(FlatBufferBuilder builder, int eventNameOffset) { builder.addOffset(0, eventNameOffset, 0); }
  public static void addTarget(FlatBufferBuilder builder, int target) { builder.addInt(1, target, 0); }
  public static void addProps(FlatBufferBuilder builder, int propsOffset) { builder.addOffset(2, propsOffset, 0); }
  public static int createPropsVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startPropsVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endComponentEvent(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public ComponentEvent get(int j) { return get(new ComponentEvent(), j); }
    public ComponentEvent get(ComponentEvent obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

