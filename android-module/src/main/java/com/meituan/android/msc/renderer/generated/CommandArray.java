// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class CommandArray extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static CommandArray getRootAsCommandArray(ByteBuffer _bb) { return getRootAsCommandArray(_bb, new CommandArray()); }
  public static CommandArray getRootAsCommandArray(ByteBuffer _bb, CommandArray obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public CommandArray __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public com.meituan.android.msc.renderer.generated.Command vals(int j) { return vals(new com.meituan.android.msc.renderer.generated.Command(), j); }
  public com.meituan.android.msc.renderer.generated.Command vals(com.meituan.android.msc.renderer.generated.Command obj, int j) { int o = __offset(4); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int valsLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.Command.Vector valsVector() { return valsVector(new com.meituan.android.msc.renderer.generated.Command.Vector()); }
  public com.meituan.android.msc.renderer.generated.Command.Vector valsVector(com.meituan.android.msc.renderer.generated.Command.Vector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }

  public static int createCommandArray(FlatBufferBuilder builder,
      int valsOffset) {
    builder.startTable(1);
    CommandArray.addVals(builder, valsOffset);
    return CommandArray.endCommandArray(builder);
  }

  public static void startCommandArray(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addVals(FlatBufferBuilder builder, int valsOffset) { builder.addOffset(0, valsOffset, 0); }
  public static int createValsVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startValsVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endCommandArray(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }
  public static void finishCommandArrayBuffer(FlatBufferBuilder builder, int offset) { builder.finish(offset); }
  public static void finishSizePrefixedCommandArrayBuffer(FlatBufferBuilder builder, int offset) { builder.finishSizePrefixed(offset); }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public CommandArray get(int j) { return get(new CommandArray(), j); }
    public CommandArray get(CommandArray obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

