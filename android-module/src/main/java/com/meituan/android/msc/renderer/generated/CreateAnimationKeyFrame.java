// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class CreateAnimationKeyFrame extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static CreateAnimationKeyFrame getRootAsCreateAnimationKeyFrame(ByteBuffer _bb) { return getRootAsCreateAnimationKeyFrame(_bb, new CreateAnimationKeyFrame()); }
  public static CreateAnimationKeyFrame getRootAsCreateAnimationKeyFrame(ByteBuffer _bb, CreateAnimationKeyFrame obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public CreateAnimationKeyFrame __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public boolean hasOffset() { return 0 != __offset(4); }
  public float offset() { int o = __offset(4); return o != 0 ? bb.getFloat(o + bb_pos) : 0f; }
  public String ease() { int o = __offset(6); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer easeAsByteBuffer() { return __vector_as_bytebuffer(6, 1); }
  public ByteBuffer easeInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 6, 1); }
  public boolean hasOpacity() { return 0 != __offset(8); }
  public float opacity() { int o = __offset(8); return o != 0 ? bb.getFloat(o + bb_pos) : 0f; }
  public boolean hasRotate() { return 0 != __offset(10); }
  public float rotate() { int o = __offset(10); return o != 0 ? bb.getFloat(o + bb_pos) : 0f; }
  public float scale(int j) { int o = __offset(12); return o != 0 ? bb.getFloat(__vector(o) + j * 4) : 0; }
  public int scaleLength() { int o = __offset(12); return o != 0 ? __vector_len(o) : 0; }
  public FloatVector scaleVector() { return scaleVector(new FloatVector()); }
  public FloatVector scaleVector(FloatVector obj) { int o = __offset(12); return o != 0 ? obj.__assign(__vector(o), bb) : null; }
  public ByteBuffer scaleAsByteBuffer() { return __vector_as_bytebuffer(12, 4); }
  public ByteBuffer scaleInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 12, 4); }
  public String translate(int j) { int o = __offset(14); return o != 0 ? __string(__vector(o) + j * 4) : null; }
  public int translateLength() { int o = __offset(14); return o != 0 ? __vector_len(o) : 0; }
  public StringVector translateVector() { return translateVector(new StringVector()); }
  public StringVector translateVector(StringVector obj) { int o = __offset(14); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }
  public boolean hasWidth() { return 0 != __offset(16); }
  public float width() { int o = __offset(16); return o != 0 ? bb.getFloat(o + bb_pos) : 0f; }
  public String widthStr() { int o = __offset(18); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer widthStrAsByteBuffer() { return __vector_as_bytebuffer(18, 1); }
  public ByteBuffer widthStrInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 18, 1); }

  public static int createCreateAnimationKeyFrame(FlatBufferBuilder builder,
      float offset,
      int easeOffset,
      float opacity,
      float rotate,
      int scaleOffset,
      int translateOffset,
      float width,
      int widthStrOffset) {
    builder.startTable(8);
    CreateAnimationKeyFrame.addWidthStr(builder, widthStrOffset);
    CreateAnimationKeyFrame.addWidth(builder, width);
    CreateAnimationKeyFrame.addTranslate(builder, translateOffset);
    CreateAnimationKeyFrame.addScale(builder, scaleOffset);
    CreateAnimationKeyFrame.addRotate(builder, rotate);
    CreateAnimationKeyFrame.addOpacity(builder, opacity);
    CreateAnimationKeyFrame.addEase(builder, easeOffset);
    CreateAnimationKeyFrame.addOffset(builder, offset);
    return CreateAnimationKeyFrame.endCreateAnimationKeyFrame(builder);
  }

  public static void startCreateAnimationKeyFrame(FlatBufferBuilder builder) { builder.startTable(8); }
  public static void addOffset(FlatBufferBuilder builder, float offset) { builder.addFloat(0, offset, 0f); }
  public static void addEase(FlatBufferBuilder builder, int easeOffset) { builder.addOffset(1, easeOffset, 0); }
  public static void addOpacity(FlatBufferBuilder builder, float opacity) { builder.addFloat(2, opacity, 0f); }
  public static void addRotate(FlatBufferBuilder builder, float rotate) { builder.addFloat(3, rotate, 0f); }
  public static void addScale(FlatBufferBuilder builder, int scaleOffset) { builder.addOffset(4, scaleOffset, 0); }
  public static int createScaleVector(FlatBufferBuilder builder, float[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addFloat(data[i]); return builder.endVector(); }
  public static void startScaleVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addTranslate(FlatBufferBuilder builder, int translateOffset) { builder.addOffset(5, translateOffset, 0); }
  public static int createTranslateVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startTranslateVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addWidth(FlatBufferBuilder builder, float width) { builder.addFloat(6, width, 0f); }
  public static void addWidthStr(FlatBufferBuilder builder, int widthStrOffset) { builder.addOffset(7, widthStrOffset, 0); }
  public static int endCreateAnimationKeyFrame(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public CreateAnimationKeyFrame get(int j) { return get(new CreateAnimationKeyFrame(), j); }
    public CreateAnimationKeyFrame get(CreateAnimationKeyFrame obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

