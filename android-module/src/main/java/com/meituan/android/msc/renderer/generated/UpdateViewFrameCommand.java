// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class UpdateViewFrameCommand extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static UpdateViewFrameCommand getRootAsUpdateViewFrameCommand(ByteBuffer _bb) { return getRootAsUpdateViewFrameCommand(_bb, new UpdateViewFrameCommand()); }
  public static UpdateViewFrameCommand getRootAsUpdateViewFrameCommand(ByteBuffer _bb, UpdateViewFrameCommand obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public UpdateViewFrameCommand __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int parentTag() { int o = __offset(4); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public float x() { int o = __offset(6); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float y() { int o = __offset(8); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float w() { int o = __offset(10); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float h() { int o = __offset(12); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }

  public static int createUpdateViewFrameCommand(FlatBufferBuilder builder,
      int parentTag,
      float x,
      float y,
      float w,
      float h) {
    builder.startTable(5);
    UpdateViewFrameCommand.addH(builder, h);
    UpdateViewFrameCommand.addW(builder, w);
    UpdateViewFrameCommand.addY(builder, y);
    UpdateViewFrameCommand.addX(builder, x);
    UpdateViewFrameCommand.addParentTag(builder, parentTag);
    return UpdateViewFrameCommand.endUpdateViewFrameCommand(builder);
  }

  public static void startUpdateViewFrameCommand(FlatBufferBuilder builder) { builder.startTable(5); }
  public static void addParentTag(FlatBufferBuilder builder, int parentTag) { builder.addInt(0, parentTag, 0); }
  public static void addX(FlatBufferBuilder builder, float x) { builder.addFloat(1, x, 0.0f); }
  public static void addY(FlatBufferBuilder builder, float y) { builder.addFloat(2, y, 0.0f); }
  public static void addW(FlatBufferBuilder builder, float w) { builder.addFloat(3, w, 0.0f); }
  public static void addH(FlatBufferBuilder builder, float h) { builder.addFloat(4, h, 0.0f); }
  public static int endUpdateViewFrameCommand(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public UpdateViewFrameCommand get(int j) { return get(new UpdateViewFrameCommand(), j); }
    public UpdateViewFrameCommand get(UpdateViewFrameCommand obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

