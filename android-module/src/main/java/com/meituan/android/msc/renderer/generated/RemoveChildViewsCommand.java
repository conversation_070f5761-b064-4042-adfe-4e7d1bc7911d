// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class RemoveChildViewsCommand extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static RemoveChildViewsCommand getRootAsRemoveChildViewsCommand(ByteBuffer _bb) { return getRootAsRemoveChildViewsCommand(_bb, new RemoveChildViewsCommand()); }
  public static RemoveChildViewsCommand getRootAsRemoveChildViewsCommand(ByteBuffer _bb, RemoveChildViewsCommand obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public RemoveChildViewsCommand __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int remove(int j) { int o = __offset(4); return o != 0 ? bb.getInt(__vector(o) + j * 4) : 0; }
  public int removeLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public IntVector removeVector() { return removeVector(new IntVector()); }
  public IntVector removeVector(IntVector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), bb) : null; }
  public ByteBuffer removeAsByteBuffer() { return __vector_as_bytebuffer(4, 4); }
  public ByteBuffer removeInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 4, 4); }

  public static int createRemoveChildViewsCommand(FlatBufferBuilder builder,
      int removeOffset) {
    builder.startTable(1);
    RemoveChildViewsCommand.addRemove(builder, removeOffset);
    return RemoveChildViewsCommand.endRemoveChildViewsCommand(builder);
  }

  public static void startRemoveChildViewsCommand(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addRemove(FlatBufferBuilder builder, int removeOffset) { builder.addOffset(0, removeOffset, 0); }
  public static int createRemoveVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addInt(data[i]); return builder.endVector(); }
  public static void startRemoveVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endRemoveChildViewsCommand(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public RemoveChildViewsCommand get(int j) { return get(new RemoveChildViewsCommand(), j); }
    public RemoveChildViewsCommand get(RemoveChildViewsCommand obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

