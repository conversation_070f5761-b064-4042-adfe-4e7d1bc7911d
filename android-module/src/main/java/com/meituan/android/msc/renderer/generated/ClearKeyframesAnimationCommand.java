// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class ClearKeyframesAnimationCommand extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static ClearKeyframesAnimationCommand getRootAsClearKeyframesAnimationCommand(ByteBuffer _bb) { return getRootAsClearKeyframesAnimationCommand(_bb, new ClearKeyframesAnimationCommand()); }
  public static ClearKeyframesAnimationCommand getRootAsClearKeyframesAnimationCommand(ByteBuffer _bb, ClearKeyframesAnimationCommand obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public ClearKeyframesAnimationCommand __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int tags(int j) { int o = __offset(4); return o != 0 ? bb.getInt(__vector(o) + j * 4) : 0; }
  public int tagsLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public IntVector tagsVector() { return tagsVector(new IntVector()); }
  public IntVector tagsVector(IntVector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), bb) : null; }
  public ByteBuffer tagsAsByteBuffer() { return __vector_as_bytebuffer(4, 4); }
  public ByteBuffer tagsInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 4, 4); }
  public com.meituan.android.msc.renderer.generated.ClearAnimationOptions options() { return options(new com.meituan.android.msc.renderer.generated.ClearAnimationOptions()); }
  public com.meituan.android.msc.renderer.generated.ClearAnimationOptions options(com.meituan.android.msc.renderer.generated.ClearAnimationOptions obj) { int o = __offset(6); return o != 0 ? obj.__assign(__indirect(o + bb_pos), bb) : null; }
  public boolean hasCallback() { return 0 != __offset(8); }
  public long callback() { int o = __offset(8); return o != 0 ? bb.getLong(o + bb_pos) : 0L; }

  public static int createClearKeyframesAnimationCommand(FlatBufferBuilder builder,
      int tagsOffset,
      int optionsOffset,
      long callback) {
    builder.startTable(3);
    ClearKeyframesAnimationCommand.addCallback(builder, callback);
    ClearKeyframesAnimationCommand.addOptions(builder, optionsOffset);
    ClearKeyframesAnimationCommand.addTags(builder, tagsOffset);
    return ClearKeyframesAnimationCommand.endClearKeyframesAnimationCommand(builder);
  }

  public static void startClearKeyframesAnimationCommand(FlatBufferBuilder builder) { builder.startTable(3); }
  public static void addTags(FlatBufferBuilder builder, int tagsOffset) { builder.addOffset(0, tagsOffset, 0); }
  public static int createTagsVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addInt(data[i]); return builder.endVector(); }
  public static void startTagsVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addOptions(FlatBufferBuilder builder, int optionsOffset) { builder.addOffset(1, optionsOffset, 0); }
  public static void addCallback(FlatBufferBuilder builder, long callback) { builder.addLong(2, callback, 0L); }
  public static int endClearKeyframesAnimationCommand(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public ClearKeyframesAnimationCommand get(int j) { return get(new ClearKeyframesAnimationCommand(), j); }
    public ClearKeyframesAnimationCommand get(ClearKeyframesAnimationCommand obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

