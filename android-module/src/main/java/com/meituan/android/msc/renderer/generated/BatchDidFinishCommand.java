// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class BatchDidFinishCommand extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static BatchDidFinishCommand getRootAsBatchDidFinishCommand(ByteBuffer _bb) { return getRootAsBatchDidFinishCommand(_bb, new BatchDidFinishCommand()); }
  public static BatchDidFinishCommand getRootAsBatchDidFinishCommand(ByteBuffer _bb, BatchDidFinishCommand obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public BatchDidFinishCommand __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public byte val() { int o = __offset(4); return o != 0 ? bb.get(o + bb_pos) : 0; }

  public static int createBatchDidFinishCommand(FlatBufferBuilder builder,
      byte val) {
    builder.startTable(1);
    BatchDidFinishCommand.addVal(builder, val);
    return BatchDidFinishCommand.endBatchDidFinishCommand(builder);
  }

  public static void startBatchDidFinishCommand(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addVal(FlatBufferBuilder builder, byte val) { builder.addByte(0, val, 0); }
  public static int endBatchDidFinishCommand(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public BatchDidFinishCommand get(int j) { return get(new BatchDidFinishCommand(), j); }
    public BatchDidFinishCommand get(BatchDidFinishCommand obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

