package com.meituan.android.msc.renderer;

public class CppCallback implements AutoCloseable{
    private long nativePtr = 0;
    public CppCallback(long nativePtr) { this.nativePtr = nativePtr; }

    private native void destoryNative(long nativePtr);

    private native void runNative(long nativePtr);

    public void run() {
        runNative(nativePtr);
    }

    @Override
    public void close() throws Exception {
        long ptr = nativePtr;
        nativePtr = 0;
        if (ptr != 0) {
            destoryNative(ptr);
        }
    }
}
