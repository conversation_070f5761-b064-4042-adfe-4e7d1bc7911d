// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class FloatValue extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static FloatValue getRootAsFloatValue(ByteBuffer _bb) { return getRootAsFloatValue(_bb, new FloatValue()); }
  public static FloatValue getRootAsFloatValue(ByteBuffer _bb, FloatValue obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public FloatValue __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float val() { int o = __offset(4); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }

  public static int createFloatValue(FlatBufferBuilder builder,
      float val) {
    builder.startTable(1);
    FloatValue.addVal(builder, val);
    return FloatValue.endFloatValue(builder);
  }

  public static void startFloatValue(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addVal(FlatBufferBuilder builder, float val) { builder.addFloat(0, val, 0.0f); }
  public static int endFloatValue(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public FloatValue get(int j) { return get(new FloatValue(), j); }
    public FloatValue get(FloatValue obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

