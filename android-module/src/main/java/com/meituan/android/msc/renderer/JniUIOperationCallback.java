package com.meituan.android.msc.renderer;

import java.util.ArrayList;
import java.util.HashMap;

public interface JniUIOperationCallback {
    void jniCreateNode(int tag,
                    String viewName,
                    int root_tag,
                    HashMap<String, String> props);

    void jniUpdateNode(int tag, String className, HashMap<String, String> props);

    void jniSetChildrenUI(int parent_tag, int[] child_tags);

    void jniOnBatchDidCompleteLayoutFinished(String result);
}
