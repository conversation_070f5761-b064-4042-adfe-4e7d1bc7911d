package com.meituan.android.msc.renderer;

import android.support.annotation.Keep;
import android.text.Layout;
import android.text.Spanned;
import com.meituan.android.msc.renderer.trace.OnlineTrace;

import java.util.HashMap;
import java.util.Map;

@Keep
public class MTDocument {
    protected long mNativePointer;
    private volatile boolean hasAddCss = false;
    private OnlineTrace mOnlineTrace;

    public MTDocument(long nativePointer, OnlineTrace onlineTrace) {
        if (nativePointer == 0) {
            throw new IllegalStateException("native pointer is null");
        }
        mNativePointer = nativePointer;
        // 从外部传入OnlineTrace实例
        mOnlineTrace = onlineTrace;
    }

    public long getNativePointerId() {
        return mNativePointer;
    }

    public void enableNativeDom(boolean enableNativeDom) {
        RendererNative.jni_DocumentEnableNativeDom(this, mNativePointer, enableNativeDom);
    }

    /**
     * 获取OnlineTrace实例
     * @return OnlineTrace实例
     */
    public OnlineTrace getOnlineTrace() {
        return mOnlineTrace;
    }

    /**
     * 获取TraceRecorder的C++指针地址
     * @return C++层TraceRecorder实例的指针地址
     */
    public long getTraceRecorderPtr() {
        return mOnlineTrace != null ? mOnlineTrace.getTraceRecorderPtr() : 0;
    }

    public void setUp(int pageId, long jsRuntimePtr) {
        RendererNative.jni_DocumentSetUpJNI(this, mNativePointer, pageId, jsRuntimePtr, getTraceRecorderPtr());
    }

    public void setNativeCallBack(NativeCallback callback) {
        RendererNative.jni_DocumentSetNativeCallBackJNI(mNativePointer, callback);
    }

    public void setJSCallBack(JSCallback callback) {
        RendererNative.jni_DocumentSetJSCallBackJNI(mNativePointer, callback);
    }

    // msi组件需要将文本属性返回
    public void setSize(float width, float height, float density) {
        RendererNative.jni_DocumentSetSizeJNI(mNativePointer, width, height, density);
    }

    public void addCss(String cssString) {
//        if (!hasAddCss) {
        RendererNative.jni_DocumentAddCssJNI(mNativePointer, cssString);
//            hasAddCss = true;
//        }
    }

    public void createNode(long tag, String viewName, long rootTag, Map<String, Object> props) {
        RendererNative.jni_DocumentCreateNodeJNI(mNativePointer, tag, viewName, rootTag, props);
    }

    public void setChildren(long tag, int[] childTags) {
        RendererNative.jni_DocumentSetChildrenJNI(mNativePointer, tag, childTags);
    }

    public void managerChildren(long viewTag, int[] moveFrom, int[] moveTo, int[] addChildTags, int[] addAtIndices, int[] removeFrom) {
        RendererNative.jni_DocumentManagerChildrenJNI(mNativePointer, viewTag, moveFrom, moveTo, addChildTags, addAtIndices, removeFrom);
    }

    public void updateNode(long tag, String viewName, Map<String, Object> props) {
        RendererNative.jni_DocumentUpdateNodeJNI(mNativePointer, tag, viewName, props);
    }

    public void fireNativeDOMEvent(String eventName, byte[] buffer) {
        RendererNative.jni_fireNativeDomEvent(mNativePointer, eventName, buffer);
    }

    public void layoutRoot(boolean isPreBdc) {
        RendererNative.jni_DocumentLayoutRootJNI(mNativePointer, isPreBdc);
    }

    public void setWidthHeightFix(long tag, boolean widthFix, boolean heightFix, float width, float height) {
        RendererNative.jni_DocumentSetWidthHeightFixJNI(mNativePointer, tag, widthFix, heightFix, width, height);
    }

    public void onDestroy() {
        RendererNative.jni_DocumentOnDestroyJNI(mNativePointer);
        // 注意：不再在这里销毁OnlineTrace，因为它是从外部传入的
        // 外部调用者负责管理OnlineTrace的生命周期
        mOnlineTrace = null;
    }

    public void performBlockOnBlinkThread(Runnable runnable) {
        RendererNative.jni_performBlockOnBlinkThread(mNativePointer, new Runnable() {
            @Override
            public void run() {
                 try {
                     runnable.run();
                 } catch (Throwable t) {
                     t.printStackTrace();
                 }
            }
        });
    }

    public void setMscScrollViewContentOffset(long tag, float x, float y) {
        RendererNative.jni_SetMscScrollViewContentOffsetJNI(mNativePointer, tag, x, y);
    }

    public void setWXSLoadScriptCallback(WXSLoadScriptCallback callback) {
        RendererNative.jni_setWXSLoadScriptCallback(mNativePointer, callback);
    }

    public void evaluateScript(String script, String source) {
        RendererNative.jni_evaluateScript(mNativePointer, script, source);
    }

    public void setWXSTransportCallback(WXSTransportEventCallback callback) {
        RendererNative.jni_setWXSTransportCallback(mNativePointer, callback);
    }

    public void triggerWXSEvent(String name, long targetTag, Map<String, Object> event) {
        RendererNative.jni_triggerWXSEvent(mNativePointer, name, targetTag, event);
    }

    public void triggerWXSPropChangeEvent(String propName, long targetTag, Map<String, Object> event) {
        RendererNative.jni_triggerWXSPropChangeEvent(mNativePointer, propName, targetTag, event);
    }
}
