package com.meituan.android.msc.renderer;

import android.support.annotation.Keep;
import android.util.Log;

import com.meituan.android.soloader.SoLoader;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

@Keep
public class RendererNative {
    private static final AtomicBoolean isInited = new AtomicBoolean(false);

    private static TraceListener mTraceListener;

    private static final AtomicBoolean isTraceSet = new AtomicBoolean(false);

    private static final AtomicBoolean isNativeDOMMetricsHandleInited = new AtomicBoolean(false);


    public static void initIfNeed(boolean enable) {
        if (!enable) {
            return;
        }
        if (isInited.getAndSet(true)) {
            return;
        }
        List<String> dependentSoFiles = new ArrayList<>();
        dependentSoFiles.add("c++_shared");

        try {
            SoLoader.loadLibrary("cjson");
            SoLoader.loadLibrary("mscrenderer");
        } catch (UnsatisfiedLinkError e) {
            Log.e("NativeDom", "NativeDom loadLibrary failed:" + e);
        }
    }

    public static void setupNativeDOMMetricsHandleIfNeeded(long jsRuntimePtr, NativeDOMMetricsTag.Handler handler) {
        if (!isInited.get()) {
            return;
        }

        if (isNativeDOMMetricsHandleInited.getAndSet(true)) {
            return;
        }

        jni_setupNativeDOMMetricsHandle(jsRuntimePtr, handler);
    }

    public static boolean isNativeDOMMetricsHandleInited() {
        return isNativeDOMMetricsHandleInited.get();
    }

    public static native void jni_setupNativeDOMMetricsHandle(long jsRuntimePtr, NativeDOMMetricsTag.Handler handler);

    public static native void newDocumentRegistryInUIThread(long jsRuntimePtr);

    public static native void newDocumentUnRegistryInUIThread(long jsRuntimePtr);

    public static native void jni_JSEngineRegisterDOMApi(long jsRuntimePtr, String appId, String purePath);

    public static boolean isTraceSet() {
        return isTraceSet.get();
    }

    public static void setTraceListener(TraceListener traceListener) {
        if (isTraceSet.getAndSet(true)) {
            return;
        }
        mTraceListener = traceListener;
    }

    public static native long jni_DocumentNewJNI(boolean enableTextPreCalculate, boolean enableJSEngine, int pageId, String pagePath);

    public static native void jni_DocumentEnableNativeDom(MTDocument document, long nativeDocument, boolean enableNativeDom);

    public static native void jni_DocumentSetUpJNI(MTDocument document, long nativeDocument, int pageId, long jsRuntimePtr, long traceRecorderPtr);

    public static native void jni_DocumentSetNativeCallBackJNI(long nativeDocument, NativeCallback callback);

    public static native void jni_DocumentSetJSCallBackJNI(long nativeDocument, JSCallback callback);

    public static native void jni_DocumentSetSizeJNI(long nativeDocument, float width, float height, float density);

    public static native void jni_DocumentAddCssJNI(long nativeDocument, String pageId);

    public static native void jni_DocumentCreateNodeJNI(long nativeDocument, long tag, String viewName, long rootTag, Map<String, Object> props);

    public static native void jni_DocumentSetChildrenJNI(long nativeDocument, long tag, int[] childTags);

    public static native void jni_DocumentManagerChildrenJNI(long nativeDocument, long viewTag, int[] moveFrom, int[] moveTo, int[] addChildTags, int[] addAtIndices, int[] removeFrom);

    public static native void jni_DocumentUpdateNodeJNI(long nativeDocument, long tag, String viewName, Map<String, Object> props);

    public static native void jni_DocumentLayoutRootJNI(long nativeDocument, boolean isPreBdc);

    public static native void jni_SetMscScrollViewContentOffsetJNI(long nativeDocument, long tag, float x, float y);

    public static native void jni_DocumentSetWidthHeightFixJNI(long nativeDocument, long tag, boolean widthFix, boolean heightFix, float width, float height);

    public static native void jni_DocumentOnDestroyJNI(long nativeDocument);

    public static native void jni_performBlockOnBlinkThread(long nativeDocument, Runnable runnable);

    public static native void jni_fireNativeDomEvent(long nativeDocument, String eventName, byte[] buffer);

    public static native void jni_runNativeFunction(long funcPtr);

    public static native String[] jni_GetCachedTracesJNI();

    public static native void jni_evaluateScript(long nativeDocument, String script, String source);

    public static native void jni_setWXSLoadScriptCallback(long nativeDocument, WXSLoadScriptCallback callback);

    public static native void jni_setWXSTransportCallback(long nativeDocument, WXSTransportEventCallback callback);

    public static native void jni_triggerWXSEvent(long nativeDocument, String name, long targetTag, Map<String,Object> event);

    public static native void jni_triggerWXSPropChangeEvent(long nativeDocument,String propName, long targetTag, Map<String,Object> event);

    public static void beginEvent(String name) {
        if (mTraceListener != null) {
            mTraceListener.begin(name);
        }
    }

    public static void endEvent(String name) {
        if (mTraceListener != null) {
            mTraceListener.end(name);
        }
    }

    public interface TraceListener {
        public void begin(String name);

        public void end(String name);
    }
}
