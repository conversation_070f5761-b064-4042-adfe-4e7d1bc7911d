// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class UpdateTextCommamnd extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static UpdateTextCommamnd getRootAsUpdateTextCommamnd(ByteBuffer _bb) { return getRootAsUpdateTextCommamnd(_bb, new UpdateTextCommamnd()); }
  public static UpdateTextCommamnd getRootAsUpdateTextCommamnd(ByteBuffer _bb, UpdateTextCommamnd obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public UpdateTextCommamnd __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float x() { int o = __offset(4); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float y() { int o = __offset(6); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float w() { int o = __offset(8); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float h() { int o = __offset(10); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float textPaddingTop() { int o = __offset(12); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float textPaddingLeft() { int o = __offset(14); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float textPaddingRight() { int o = __offset(16); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float textPaddingBottom() { int o = __offset(18); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }

  public static int createUpdateTextCommamnd(FlatBufferBuilder builder,
      float x,
      float y,
      float w,
      float h,
      float textPaddingTop,
      float textPaddingLeft,
      float textPaddingRight,
      float textPaddingBottom) {
    builder.startTable(8);
    UpdateTextCommamnd.addTextPaddingBottom(builder, textPaddingBottom);
    UpdateTextCommamnd.addTextPaddingRight(builder, textPaddingRight);
    UpdateTextCommamnd.addTextPaddingLeft(builder, textPaddingLeft);
    UpdateTextCommamnd.addTextPaddingTop(builder, textPaddingTop);
    UpdateTextCommamnd.addH(builder, h);
    UpdateTextCommamnd.addW(builder, w);
    UpdateTextCommamnd.addY(builder, y);
    UpdateTextCommamnd.addX(builder, x);
    return UpdateTextCommamnd.endUpdateTextCommamnd(builder);
  }

  public static void startUpdateTextCommamnd(FlatBufferBuilder builder) { builder.startTable(8); }
  public static void addX(FlatBufferBuilder builder, float x) { builder.addFloat(0, x, 0.0f); }
  public static void addY(FlatBufferBuilder builder, float y) { builder.addFloat(1, y, 0.0f); }
  public static void addW(FlatBufferBuilder builder, float w) { builder.addFloat(2, w, 0.0f); }
  public static void addH(FlatBufferBuilder builder, float h) { builder.addFloat(3, h, 0.0f); }
  public static void addTextPaddingTop(FlatBufferBuilder builder, float textPaddingTop) { builder.addFloat(4, textPaddingTop, 0.0f); }
  public static void addTextPaddingLeft(FlatBufferBuilder builder, float textPaddingLeft) { builder.addFloat(5, textPaddingLeft, 0.0f); }
  public static void addTextPaddingRight(FlatBufferBuilder builder, float textPaddingRight) { builder.addFloat(6, textPaddingRight, 0.0f); }
  public static void addTextPaddingBottom(FlatBufferBuilder builder, float textPaddingBottom) { builder.addFloat(7, textPaddingBottom, 0.0f); }
  public static int endUpdateTextCommamnd(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public UpdateTextCommamnd get(int j) { return get(new UpdateTextCommamnd(), j); }
    public UpdateTextCommamnd get(UpdateTextCommamnd obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

