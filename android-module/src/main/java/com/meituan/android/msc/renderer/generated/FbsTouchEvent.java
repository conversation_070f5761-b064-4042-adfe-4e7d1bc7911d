// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class FbsTouchEvent extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static FbsTouchEvent getRootAsFbsTouchEvent(ByteBuffer _bb) { return getRootAsFbsTouchEvent(_bb, new FbsTouchEvent()); }
  public static FbsTouchEvent getRootAsFbsTouchEvent(ByteBuffer _bb, FbsTouchEvent obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public FbsTouchEvent __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public String eventName() { int o = __offset(4); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer eventNameAsByteBuffer() { return __vector_as_bytebuffer(4, 1); }
  public ByteBuffer eventNameInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 4, 1); }
  public com.meituan.android.msc.renderer.generated.FbsTouch touches(int j) { return touches(new com.meituan.android.msc.renderer.generated.FbsTouch(), j); }
  public com.meituan.android.msc.renderer.generated.FbsTouch touches(com.meituan.android.msc.renderer.generated.FbsTouch obj, int j) { int o = __offset(6); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int touchesLength() { int o = __offset(6); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.FbsTouch.Vector touchesVector() { return touchesVector(new com.meituan.android.msc.renderer.generated.FbsTouch.Vector()); }
  public com.meituan.android.msc.renderer.generated.FbsTouch.Vector touchesVector(com.meituan.android.msc.renderer.generated.FbsTouch.Vector obj) { int o = __offset(6); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }

  public static int createFbsTouchEvent(FlatBufferBuilder builder,
      int eventNameOffset,
      int touchesOffset) {
    builder.startTable(2);
    FbsTouchEvent.addTouches(builder, touchesOffset);
    FbsTouchEvent.addEventName(builder, eventNameOffset);
    return FbsTouchEvent.endFbsTouchEvent(builder);
  }

  public static void startFbsTouchEvent(FlatBufferBuilder builder) { builder.startTable(2); }
  public static void addEventName(FlatBufferBuilder builder, int eventNameOffset) { builder.addOffset(0, eventNameOffset, 0); }
  public static void addTouches(FlatBufferBuilder builder, int touchesOffset) { builder.addOffset(1, touchesOffset, 0); }
  public static int createTouchesVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startTouchesVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endFbsTouchEvent(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public FbsTouchEvent get(int j) { return get(new FbsTouchEvent(), j); }
    public FbsTouchEvent get(FbsTouchEvent obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

