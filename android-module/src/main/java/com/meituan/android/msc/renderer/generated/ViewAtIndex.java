// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class ViewAtIndex extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static ViewAtIndex getRootAsViewAtIndex(ByteBuffer _bb) { return getRootAsViewAtIndex(_bb, new ViewAtIndex()); }
  public static ViewAtIndex getRootAsViewAtIndex(ByteBuffer _bb, ViewAtIndex obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public ViewAtIndex __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int tag() { int o = __offset(4); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public int index() { int o = __offset(6); return o != 0 ? bb.getInt(o + bb_pos) : 0; }

  public static int createViewAtIndex(FlatBufferBuilder builder,
      int tag,
      int index) {
    builder.startTable(2);
    ViewAtIndex.addIndex(builder, index);
    ViewAtIndex.addTag(builder, tag);
    return ViewAtIndex.endViewAtIndex(builder);
  }

  public static void startViewAtIndex(FlatBufferBuilder builder) { builder.startTable(2); }
  public static void addTag(FlatBufferBuilder builder, int tag) { builder.addInt(0, tag, 0); }
  public static void addIndex(FlatBufferBuilder builder, int index) { builder.addInt(1, index, 0); }
  public static int endViewAtIndex(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public ViewAtIndex get(int j) { return get(new ViewAtIndex(), j); }
    public ViewAtIndex get(ViewAtIndex obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

