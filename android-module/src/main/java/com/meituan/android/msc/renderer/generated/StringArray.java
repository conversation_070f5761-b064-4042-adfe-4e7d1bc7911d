// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class StringArray extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static StringArray getRootAsStringArray(ByteBuffer _bb) { return getRootAsStringArray(_bb, new StringArray()); }
  public static StringArray getRootAsStringArray(ByteBuffer _bb, StringArray obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public StringArray __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public String vals(int j) { int o = __offset(4); return o != 0 ? __string(__vector(o) + j * 4) : null; }
  public int valsLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public StringVector valsVector() { return valsVector(new StringVector()); }
  public StringVector valsVector(StringVector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }

  public static int createStringArray(FlatBufferBuilder builder,
      int valsOffset) {
    builder.startTable(1);
    StringArray.addVals(builder, valsOffset);
    return StringArray.endStringArray(builder);
  }

  public static void startStringArray(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addVals(FlatBufferBuilder builder, int valsOffset) { builder.addOffset(0, valsOffset, 0); }
  public static int createValsVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startValsVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endStringArray(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public StringArray get(int j) { return get(new StringArray(), j); }
    public StringArray get(StringArray obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

