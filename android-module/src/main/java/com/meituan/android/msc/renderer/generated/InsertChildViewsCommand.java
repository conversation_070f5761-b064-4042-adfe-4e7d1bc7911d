// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class InsertChildViewsCommand extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static InsertChildViewsCommand getRootAsInsertChildViewsCommand(ByteBuffer _bb) { return getRootAsInsertChildViewsCommand(_bb, new InsertChildViewsCommand()); }
  public static InsertChildViewsCommand getRootAsInsertChildViewsCommand(ByteBuffer _bb, InsertChildViewsCommand obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public InsertChildViewsCommand __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public com.meituan.android.msc.renderer.generated.ViewAtIndex insert(int j) { return insert(new com.meituan.android.msc.renderer.generated.ViewAtIndex(), j); }
  public com.meituan.android.msc.renderer.generated.ViewAtIndex insert(com.meituan.android.msc.renderer.generated.ViewAtIndex obj, int j) { int o = __offset(4); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int insertLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.ViewAtIndex.Vector insertVector() { return insertVector(new com.meituan.android.msc.renderer.generated.ViewAtIndex.Vector()); }
  public com.meituan.android.msc.renderer.generated.ViewAtIndex.Vector insertVector(com.meituan.android.msc.renderer.generated.ViewAtIndex.Vector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }

  public static int createInsertChildViewsCommand(FlatBufferBuilder builder,
      int insertOffset) {
    builder.startTable(1);
    InsertChildViewsCommand.addInsert(builder, insertOffset);
    return InsertChildViewsCommand.endInsertChildViewsCommand(builder);
  }

  public static void startInsertChildViewsCommand(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addInsert(FlatBufferBuilder builder, int insertOffset) { builder.addOffset(0, insertOffset, 0); }
  public static int createInsertVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startInsertVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endInsertChildViewsCommand(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public InsertChildViewsCommand get(int j) { return get(new InsertChildViewsCommand(), j); }
    public InsertChildViewsCommand get(InsertChildViewsCommand obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

