// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class Prop extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static Prop getRootAsProp(ByteBuffer _bb) { return getRootAsProp(_bb, new Prop()); }
  public static Prop getRootAsProp(ByteBuffer _bb, Prop obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public Prop __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public String key() { int o = __offset(4); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer keyAsByteBuffer() { return __vector_as_bytebuffer(4, 1); }
  public ByteBuffer keyInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 4, 1); }
  public byte valueTypeType() { int o = __offset(6); return o != 0 ? bb.get(o + bb_pos) : 0; }
  public Table valueType(Table obj) { int o = __offset(8); return o != 0 ? __union(obj, o + bb_pos) : null; }
  public byte valType() { int o = __offset(10); return o != 0 ? bb.get(o + bb_pos) : 0; }
  public Table val(Table obj) { int o = __offset(12); return o != 0 ? __union(obj, o + bb_pos) : null; }

  public static int createProp(FlatBufferBuilder builder,
      int keyOffset,
      byte valueTypeType,
      int valueTypeOffset,
      byte valType,
      int valOffset) {
    builder.startTable(5);
    Prop.addVal(builder, valOffset);
    Prop.addValueType(builder, valueTypeOffset);
    Prop.addKey(builder, keyOffset);
    Prop.addValType(builder, valType);
    Prop.addValueTypeType(builder, valueTypeType);
    return Prop.endProp(builder);
  }

  public static void startProp(FlatBufferBuilder builder) { builder.startTable(5); }
  public static void addKey(FlatBufferBuilder builder, int keyOffset) { builder.addOffset(0, keyOffset, 0); }
  public static void addValueTypeType(FlatBufferBuilder builder, byte valueTypeType) { builder.addByte(1, valueTypeType, 0); }
  public static void addValueType(FlatBufferBuilder builder, int valueTypeOffset) { builder.addOffset(2, valueTypeOffset, 0); }
  public static void addValType(FlatBufferBuilder builder, byte valType) { builder.addByte(3, valType, 0); }
  public static void addVal(FlatBufferBuilder builder, int valOffset) { builder.addOffset(4, valOffset, 0); }
  public static int endProp(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public Prop get(int j) { return get(new Prop(), j); }
    public Prop get(Prop obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

