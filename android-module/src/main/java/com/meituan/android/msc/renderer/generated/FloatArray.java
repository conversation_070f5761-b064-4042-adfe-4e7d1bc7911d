// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class FloatArray extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static FloatArray getRootAsFloatArray(ByteBuffer _bb) { return getRootAsFloatArray(_bb, new FloatArray()); }
  public static FloatArray getRootAsFloatArray(ByteBuffer _bb, FloatArray obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public FloatArray __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public float vals(int j) { int o = __offset(4); return o != 0 ? bb.getFloat(__vector(o) + j * 4) : 0; }
  public int valsLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public FloatVector valsVector() { return valsVector(new FloatVector()); }
  public FloatVector valsVector(FloatVector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), bb) : null; }
  public ByteBuffer valsAsByteBuffer() { return __vector_as_bytebuffer(4, 4); }
  public ByteBuffer valsInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 4, 4); }

  public static int createFloatArray(FlatBufferBuilder builder,
      int valsOffset) {
    builder.startTable(1);
    FloatArray.addVals(builder, valsOffset);
    return FloatArray.endFloatArray(builder);
  }

  public static void startFloatArray(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addVals(FlatBufferBuilder builder, int valsOffset) { builder.addOffset(0, valsOffset, 0); }
  public static int createValsVector(FlatBufferBuilder builder, float[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addFloat(data[i]); return builder.endVector(); }
  public static void startValsVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endFloatArray(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public FloatArray get(int j) { return get(new FloatArray(), j); }
    public FloatArray get(FloatArray obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

