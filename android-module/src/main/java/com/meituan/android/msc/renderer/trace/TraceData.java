package com.meituan.android.msc.renderer.trace;

/**
 * 埋点数据类
 */
public class TraceData {
    
    /**
     * 埋点等级枚举
     */
    public enum TraceLevel {
        P0(0),  // 最高优先级
        P1(1),  // 中等优先级
        P2(2);  // 低优先级
        
        private final int value;
        
        TraceLevel(int value) {
            this.value = value;
        }
        
        public int getValue() {
            return value;
        }
        
        public static TraceLevel fromValue(int value) {
            for (TraceLevel level : values()) {
                if (level.value == value) {
                    return level;
                }
            }
            return P0; // 默认返回P0
        }
    }
    
    private String key;
    private TraceLevel level;
    private long totalTimeNs;    // 总耗时（纳秒）
    private int count;           // 执行次数
    private int batch;           // 批次字段
    
    public TraceData() {
        this.key = "";
        this.level = TraceLevel.P0;
        this.totalTimeNs = 0;
        this.count = 0;
        this.batch = 0;
    }
    
    public TraceData(String key, int level, long totalTimeNs, int count) {
        this.key = key;
        this.level = TraceLevel.fromValue(level);
        this.totalTimeNs = totalTimeNs;
        this.count = count;
        this.batch = 0;
    }
    
    public TraceData(String key, int level, long totalTimeNs, int count, int batch) {
        this.key = key;
        this.level = TraceLevel.fromValue(level);
        this.totalTimeNs = totalTimeNs;
        this.count = count;
        this.batch = batch;
    }

    public TraceData(String key, TraceLevel level, long totalTimeNs, int count) {
        this.key = key;
        this.level = level;
        this.totalTimeNs = totalTimeNs;
        this.count = count;
        this.batch = 0;
    }
    
    public TraceData(String key, TraceLevel level, long totalTimeNs, int count, int batch) {
        this.key = key;
        this.level = level;
        this.totalTimeNs = totalTimeNs;
        this.count = count;
        this.batch = batch;
    }

    // Getters
    public String getKey() {
        return key;
    }
    
    public TraceLevel getLevel() {
        return level;
    }
    
    public long getTotalTimeNs() {
        return totalTimeNs;
    }
    
    public int getCount() {
        return count;
    }
    
    public int getBatch() {
        return batch;
    }

    // 获取平均耗时（纳秒）
    public long getAverageTimeNs() {
        return count > 0 ? totalTimeNs / count : 0;
    }
    
    // 获取总耗时（毫秒）
    public double getTotalTimeMs() {
        return totalTimeNs / 1_000_000.0;
    }
    
    // 获取平均耗时（毫秒）
    public double getAverageTimeMs() {
        return getAverageTimeNs() / 1_000_000.0;
    }
    
    // Setters
    public void setKey(String key) {
        this.key = key;
    }
    
    public void setLevel(TraceLevel level) {
        this.level = level;
    }
    
    public void setTotalTimeNs(long totalTimeNs) {
        this.totalTimeNs = totalTimeNs;
    }
    
    public void setCount(int count) {
        this.count = count;
    }
    
    public void setBatch(int batch) {
        this.batch = batch;
    }

    @Override
    public String toString() {
        return "TraceData{" +
                "key='" + key + '\'' +
                ", level=" + level +
                ", totalTimeMs=" + getTotalTimeMs() +
                ", averageTimeMs=" + getAverageTimeMs() +
                ", count=" + count +
                ", batch=" + batch +
                '}';
    }
}