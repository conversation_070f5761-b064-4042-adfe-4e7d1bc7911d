// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class UpdateViewStyleCommamnd extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static UpdateViewStyleCommamnd getRootAsUpdateViewStyleCommamnd(ByteBuffer _bb) { return getRootAsUpdateViewStyleCommamnd(_bb, new UpdateViewStyleCommamnd()); }
  public static UpdateViewStyleCommamnd getRootAsUpdateViewStyleCommamnd(ByteBuffer _bb, UpdateViewStyleCommamnd obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public UpdateViewStyleCommamnd __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public com.meituan.android.msc.renderer.generated.DisplayItem val() { return val(new com.meituan.android.msc.renderer.generated.DisplayItem()); }
  public com.meituan.android.msc.renderer.generated.DisplayItem val(com.meituan.android.msc.renderer.generated.DisplayItem obj) { int o = __offset(4); return o != 0 ? obj.__assign(__indirect(o + bb_pos), bb) : null; }

  public static int createUpdateViewStyleCommamnd(FlatBufferBuilder builder,
      int valOffset) {
    builder.startTable(1);
    UpdateViewStyleCommamnd.addVal(builder, valOffset);
    return UpdateViewStyleCommamnd.endUpdateViewStyleCommamnd(builder);
  }

  public static void startUpdateViewStyleCommamnd(FlatBufferBuilder builder) { builder.startTable(1); }
  public static void addVal(FlatBufferBuilder builder, int valOffset) { builder.addOffset(0, valOffset, 0); }
  public static int endUpdateViewStyleCommamnd(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public UpdateViewStyleCommamnd get(int j) { return get(new UpdateViewStyleCommamnd(), j); }
    public UpdateViewStyleCommamnd get(UpdateViewStyleCommamnd obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

