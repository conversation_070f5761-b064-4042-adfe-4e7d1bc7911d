// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class Text extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static Text getRootAsText(ByteBuffer _bb) { return getRootAsText(_bb, new Text()); }
  public static Text getRootAsText(ByteBuffer _bb, Text obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public Text __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int tag() { int o = __offset(4); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public String type() { int o = __offset(6); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer typeAsByteBuffer() { return __vector_as_bytebuffer(6, 1); }
  public ByteBuffer typeInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 6, 1); }
  public int parentTag() { int o = __offset(8); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public String fontFamily() { int o = __offset(10); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer fontFamilyAsByteBuffer() { return __vector_as_bytebuffer(10, 1); }
  public ByteBuffer fontFamilyInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 10, 1); }
  public float fontWeight() { int o = __offset(12); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public String fontStyle() { int o = __offset(14); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer fontStyleAsByteBuffer() { return __vector_as_bytebuffer(14, 1); }
  public ByteBuffer fontStyleInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 14, 1); }
  public int fontSize() { int o = __offset(16); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public int color() { int o = __offset(18); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public String textAlign() { int o = __offset(20); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer textAlignAsByteBuffer() { return __vector_as_bytebuffer(20, 1); }
  public ByteBuffer textAlignInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 20, 1); }
  public float lineHeight() { int o = __offset(22); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public String textOverflow() { int o = __offset(24); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer textOverflowAsByteBuffer() { return __vector_as_bytebuffer(24, 1); }
  public ByteBuffer textOverflowInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 24, 1); }
  public String ellipsizeMode() { int o = __offset(26); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer ellipsizeModeAsByteBuffer() { return __vector_as_bytebuffer(26, 1); }
  public ByteBuffer ellipsizeModeInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 26, 1); }
  public int numberOfLines() { int o = __offset(28); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public String text() { int o = __offset(30); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer textAsByteBuffer() { return __vector_as_bytebuffer(30, 1); }
  public ByteBuffer textInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 30, 1); }

  public static int createText(FlatBufferBuilder builder,
      int tag,
      int typeOffset,
      int parentTag,
      int fontFamilyOffset,
      float fontWeight,
      int fontStyleOffset,
      int fontSize,
      int color,
      int textAlignOffset,
      float lineHeight,
      int textOverflowOffset,
      int ellipsizeModeOffset,
      int numberOfLines,
      int textOffset) {
    builder.startTable(14);
    Text.addText(builder, textOffset);
    Text.addNumberOfLines(builder, numberOfLines);
    Text.addEllipsizeMode(builder, ellipsizeModeOffset);
    Text.addTextOverflow(builder, textOverflowOffset);
    Text.addLineHeight(builder, lineHeight);
    Text.addTextAlign(builder, textAlignOffset);
    Text.addColor(builder, color);
    Text.addFontSize(builder, fontSize);
    Text.addFontStyle(builder, fontStyleOffset);
    Text.addFontWeight(builder, fontWeight);
    Text.addFontFamily(builder, fontFamilyOffset);
    Text.addParentTag(builder, parentTag);
    Text.addType(builder, typeOffset);
    Text.addTag(builder, tag);
    return Text.endText(builder);
  }

  public static void startText(FlatBufferBuilder builder) { builder.startTable(14); }
  public static void addTag(FlatBufferBuilder builder, int tag) { builder.addInt(0, tag, 0); }
  public static void addType(FlatBufferBuilder builder, int typeOffset) { builder.addOffset(1, typeOffset, 0); }
  public static void addParentTag(FlatBufferBuilder builder, int parentTag) { builder.addInt(2, parentTag, 0); }
  public static void addFontFamily(FlatBufferBuilder builder, int fontFamilyOffset) { builder.addOffset(3, fontFamilyOffset, 0); }
  public static void addFontWeight(FlatBufferBuilder builder, float fontWeight) { builder.addFloat(4, fontWeight, 0.0f); }
  public static void addFontStyle(FlatBufferBuilder builder, int fontStyleOffset) { builder.addOffset(5, fontStyleOffset, 0); }
  public static void addFontSize(FlatBufferBuilder builder, int fontSize) { builder.addInt(6, fontSize, 0); }
  public static void addColor(FlatBufferBuilder builder, int color) { builder.addInt(7, color, 0); }
  public static void addTextAlign(FlatBufferBuilder builder, int textAlignOffset) { builder.addOffset(8, textAlignOffset, 0); }
  public static void addLineHeight(FlatBufferBuilder builder, float lineHeight) { builder.addFloat(9, lineHeight, 0.0f); }
  public static void addTextOverflow(FlatBufferBuilder builder, int textOverflowOffset) { builder.addOffset(10, textOverflowOffset, 0); }
  public static void addEllipsizeMode(FlatBufferBuilder builder, int ellipsizeModeOffset) { builder.addOffset(11, ellipsizeModeOffset, 0); }
  public static void addNumberOfLines(FlatBufferBuilder builder, int numberOfLines) { builder.addInt(12, numberOfLines, 0); }
  public static void addText(FlatBufferBuilder builder, int textOffset) { builder.addOffset(13, textOffset, 0); }
  public static int endText(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public Text get(int j) { return get(new Text(), j); }
    public Text get(Text obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

