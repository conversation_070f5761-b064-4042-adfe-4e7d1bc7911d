// automatically generated by the FlatBuffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

@SuppressWarnings("unused")
public final class CommandValue {
  private CommandValue() { }
  public static final byte NONE = 0;
  public static final byte CreateViewCommand = 1;
  public static final byte UpdateViewCommand = 2;
  public static final byte UpdateViewFrameCommand = 3;
  public static final byte DeleteViewsCommand = 4;
  public static final byte InsertChildViewsCommand = 5;
  public static final byte RemoveChildViewsCommand = 6;
  public static final byte UpdateViewStyleCommamnd = 7;
  public static final byte UpdateTransformCommand = 8;
  public static final byte UpdateTextCommamnd = 9;
  public static final byte BatchDidFinishCommand = 10;
  public static final byte CreateKeyframesAnimationCommand = 11;
  public static final byte ClearKeyframesAnimationCommand = 12;

  public static final String[] names = { "NONE", "CreateViewCommand", "UpdateViewCommand", "UpdateViewFrameCommand", "DeleteViewsCommand", "InsertChildViewsCommand", "RemoveChildViewsCommand", "UpdateViewStyleCommamnd", "UpdateTransformCommand", "UpdateTextCommamnd", "BatchDidFinishCommand", "CreateKeyframesAnimationCommand", "ClearKeyframesAnimationCommand", };

  public static String name(int e) { return names[e]; }
}

