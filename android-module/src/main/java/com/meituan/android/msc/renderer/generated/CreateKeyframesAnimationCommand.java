// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class CreateKeyframesAnimationCommand extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static CreateKeyframesAnimationCommand getRootAsCreateKeyframesAnimationCommand(ByteBuffer _bb) { return getRootAsCreateKeyframesAnimationCommand(_bb, new CreateKeyframesAnimationCommand()); }
  public static CreateKeyframesAnimationCommand getRootAsCreateKeyframesAnimationCommand(ByteBuffer _bb, CreateKeyframesAnimationCommand obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public CreateKeyframesAnimationCommand __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int tags(int j) { int o = __offset(4); return o != 0 ? bb.getInt(__vector(o) + j * 4) : 0; }
  public int tagsLength() { int o = __offset(4); return o != 0 ? __vector_len(o) : 0; }
  public IntVector tagsVector() { return tagsVector(new IntVector()); }
  public IntVector tagsVector(IntVector obj) { int o = __offset(4); return o != 0 ? obj.__assign(__vector(o), bb) : null; }
  public ByteBuffer tagsAsByteBuffer() { return __vector_as_bytebuffer(4, 4); }
  public ByteBuffer tagsInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 4, 4); }
  public com.meituan.android.msc.renderer.generated.CreateAnimationKeyFrame keyframes(int j) { return keyframes(new com.meituan.android.msc.renderer.generated.CreateAnimationKeyFrame(), j); }
  public com.meituan.android.msc.renderer.generated.CreateAnimationKeyFrame keyframes(com.meituan.android.msc.renderer.generated.CreateAnimationKeyFrame obj, int j) { int o = __offset(6); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int keyframesLength() { int o = __offset(6); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.CreateAnimationKeyFrame.Vector keyframesVector() { return keyframesVector(new com.meituan.android.msc.renderer.generated.CreateAnimationKeyFrame.Vector()); }
  public com.meituan.android.msc.renderer.generated.CreateAnimationKeyFrame.Vector keyframesVector(com.meituan.android.msc.renderer.generated.CreateAnimationKeyFrame.Vector obj) { int o = __offset(6); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }
  public int duration() { int o = __offset(8); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public boolean hasCallback() { return 0 != __offset(10); }
  public long callback() { int o = __offset(10); return o != 0 ? bb.getLong(o + bb_pos) : 0L; }

  public static int createCreateKeyframesAnimationCommand(FlatBufferBuilder builder,
      int tagsOffset,
      int keyframesOffset,
      int duration,
      long callback) {
    builder.startTable(4);
    CreateKeyframesAnimationCommand.addCallback(builder, callback);
    CreateKeyframesAnimationCommand.addDuration(builder, duration);
    CreateKeyframesAnimationCommand.addKeyframes(builder, keyframesOffset);
    CreateKeyframesAnimationCommand.addTags(builder, tagsOffset);
    return CreateKeyframesAnimationCommand.endCreateKeyframesAnimationCommand(builder);
  }

  public static void startCreateKeyframesAnimationCommand(FlatBufferBuilder builder) { builder.startTable(4); }
  public static void addTags(FlatBufferBuilder builder, int tagsOffset) { builder.addOffset(0, tagsOffset, 0); }
  public static int createTagsVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addInt(data[i]); return builder.endVector(); }
  public static void startTagsVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addKeyframes(FlatBufferBuilder builder, int keyframesOffset) { builder.addOffset(1, keyframesOffset, 0); }
  public static int createKeyframesVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startKeyframesVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addDuration(FlatBufferBuilder builder, int duration) { builder.addInt(2, duration, 0); }
  public static void addCallback(FlatBufferBuilder builder, long callback) { builder.addLong(3, callback, 0L); }
  public static int endCreateKeyframesAnimationCommand(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public CreateKeyframesAnimationCommand get(int j) { return get(new CreateKeyframesAnimationCommand(), j); }
    public CreateKeyframesAnimationCommand get(CreateKeyframesAnimationCommand obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

