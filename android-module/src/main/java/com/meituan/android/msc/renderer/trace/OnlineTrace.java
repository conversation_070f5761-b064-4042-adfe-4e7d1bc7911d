package com.meituan.android.msc.renderer.trace;

import android.os.Looper;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.util.Log;

import com.meituan.android.msc.renderer.BuildConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 在线埋点记录器
 * 提供Java端控制C++埋点框架的接口
 */
public class OnlineTrace {

    public static final String TAG = "OnlineTrace";

    // 埋点等级位掩码常量
    public static final int TRACE_LEVEL_P0 = 1 << 0;  // 0x01
    public static final int TRACE_LEVEL_P1 = 1 << 1;  // 0x02
    public static final int TRACE_LEVEL_P2 = 1 << 2;  // 0x04
    public static final int TRACE_LEVEL_ALL = TRACE_LEVEL_P0 | TRACE_LEVEL_P1 | TRACE_LEVEL_P2;  // 0x07
    public static final int TRACE_LEVEL_NONE = 0;     // 0x00
    private static final long SCHEDULED_STOP_AND_CLEAR_DELAY_TIME = 10000L; //10s

    // TraceRecorder实例指针
    private long mTraceRecorderPtr;
    private AtomicBoolean isScheduledStopAndClear = new AtomicBoolean(false);
    private boolean started = true;

    // Native方法声明
    private static native long nativeCreateTraceRecorder();
    private static native void nativeDestroyTraceRecorder(long recorderPtr);
    private static native void nativeSetEnabledLevelsMask(long recorderPtr, int enabledMask);
    private static native int nativeGetEnabledLevels(long recorderPtr);
    private static native void nativeDisableAll(long recorderPtr);
    private static native void nativeBeginTrace(long recorderPtr, String key, int level);
    private static native void nativeEndTrace(long recorderPtr, String key);
    private static native byte[] nativeExportBinaryData(long recorderPtr);
    private static native void nativeClear(long recorderPtr);
    private static native TraceData nativeGetTraceData(long recorderPtr, String key);
    private static native void nativeAddBatch(long recorderPtr, int batch);
    private static native void nativeClearTailBatch(long recorderPtr);
    private static native int nativeGetCurrentBatch(long recorderPtr);

    private final List<RenderBatch>[] periodList = new ArrayList[ThreadType.values().length];
    /**
     * 构造函数，创建C++层的TraceRecorder实例
     */
    public OnlineTrace(boolean blinkEnable) {
        if (blinkEnable) {
            mTraceRecorderPtr = nativeCreateTraceRecorder();
            if (mTraceRecorderPtr == 0) {
                throw new RuntimeException("Failed to create native TraceRecorder");
            }
        } else {
            mTraceRecorderPtr = 0L;
        }
        for (int i = 0; i < ThreadType.values().length; i++) {
            periodList[i] = new ArrayList<>(32);
        }
    }

    /**
     * 销毁OnlineTrace，释放C++层资源
     */
    public void destroy() {
        if (mTraceRecorderPtr != 0) {
            nativeDestroyTraceRecorder(mTraceRecorderPtr);
            mTraceRecorderPtr = 0;
        }
    }

    public void cleanPeriodData() {
        for (int i = 0; i < ThreadType.values().length; i++) {
            periodList[i].clear();
        }
    }

    /**
     * 获取TraceRecorder的C++指针地址
     * @return C++层TraceRecorder实例的指针地址
     */
    public long getTraceRecorderPtr() {
        return mTraceRecorderPtr;
    }

    /**
     * 设置启用的埋点等级（使用位掩码）
     * @param enabledMask 启用的等级位掩码，可以使用 TRACE_LEVEL_* 常量组合
     *
     * 示例：
     * - 只启用P0: setEnabledLevels(TRACE_LEVEL_P0)
     * - 启用P0和P1: setEnabledLevels(TRACE_LEVEL_P0 | TRACE_LEVEL_P1)
     * - 启用所有: setEnabledLevels(TRACE_LEVEL_ALL)
     * - 禁用所有: setEnabledLevels(TRACE_LEVEL_NONE)
     */
    public void setEnabledLevels(int enabledMask) {
        if (mTraceRecorderPtr != 0) {
            nativeSetEnabledLevelsMask(mTraceRecorderPtr, enabledMask);
        }
    }

    /**
     * 获取当前启用的等级掩码
     * @return 当前启用的等级位掩码
     */
    public int getEnabledLevels() {
        if (mTraceRecorderPtr != 0) {
            return nativeGetEnabledLevels(mTraceRecorderPtr);
        }
        return 0;
    }

    /**
     * 检查指定等级是否启用
     * @param level 要检查的等级掩码
     * @return 是否启用
     */
    public boolean isLevelEnabled(int level) {
        return (getEnabledLevels() & level) != 0;
    }

    /**
     * 关闭所有埋点
     */
    public void disableAll() {
        if (mTraceRecorderPtr != 0) {
            nativeDisableAll(mTraceRecorderPtr);
        }
    }
    
    /**
     * 开始埋点记录
     * @param key 埋点key
     * @param level 埋点等级
     */
    public void beginTrace(String key, TraceData.TraceLevel level) {
        if (mTraceRecorderPtr != 0) {
            nativeBeginTrace(mTraceRecorderPtr, key, level.getValue());
        }
    }
    
    /**
     * 结束埋点记录
     * @param key 埋点key
     */
    public void endTrace(String key) {
        if (mTraceRecorderPtr != 0) {
            nativeEndTrace(mTraceRecorderPtr, key);
        }
    }
    
    /**
     * 获取指定key的埋点数据
     * @param key 埋点key
     * @return 埋点数据，如果不存在返回空数据
     */
    public TraceData getTraceData(String key) {
        if (mTraceRecorderPtr != 0) {
            return nativeGetTraceData(mTraceRecorderPtr, key);
        }
        return null;
    }
    
    /**
     * 导出所有埋点数据的二进制流
     * @return 二进制数据
     */
    public byte[] exportBinaryData() {
        if (mTraceRecorderPtr != 0) {
            return nativeExportBinaryData(mTraceRecorderPtr);
        }
        return new byte[0];
    }
    
    /**
     * 清空所有埋点数据
     */
    public void clear() {
        if (mTraceRecorderPtr != 0) {
            nativeClear(mTraceRecorderPtr);
        }
    }
    
    /**
     * 添加批次到队列头部
     * @param batch 批次号
     */
    public void addBatch(int batch) {
        if (mTraceRecorderPtr != 0) {
            nativeAddBatch(mTraceRecorderPtr, batch);
        }
    }

    /**
     * 清除队列尾部的批次
     */
    public void clearTailBatch() {
        if (mTraceRecorderPtr != 0) {
            nativeClearTailBatch(mTraceRecorderPtr);
        }
    }

    /**
     * 获取当前使用的批次（队列尾部）
     * @return 当前批次号
     */
    public int getCurrentBatch() {
        if (mTraceRecorderPtr != 0) {
            return nativeGetCurrentBatch(mTraceRecorderPtr);
        }
        return 0;
    }

    public void stop() {
        started = false;
    }

    public enum ThreadType {
        JS {
            @Override
            boolean checkThread() {
                return "msc_js".equals(Thread.currentThread().getName());
            }
            @NonNull
            @Override
            public String toString() {
                return super.toString() + "\t";
            }
        },
        SHADOW {
            @Override
            boolean checkThread() {
                //Blink 会命中后一种情况（线程名称一般是 Thread-xxx），简单处理下，不是 JS 和 UI 线程就行
                return "msc_shadow".equals(Thread.currentThread().getName()) || (!JS.checkThread() && !UI.checkThread());
            }

            @NonNull
            @Override
            public String toString() {
                return super.toString();
            }
        },
        UI {
            @Override
            boolean checkThread() {
                return Looper.getMainLooper().getThread() == Thread.currentThread();
            }

            @NonNull
            @Override
            public String toString() {
                return super.toString() + "\t";
            }
        };

        abstract boolean checkThread();
    }

    public static class RenderBatch {
        public final long start = System.currentTimeMillis();
        public final long startCPU = SystemClock.currentThreadTimeMillis();
        public final long startInNano = System.nanoTime();
        public long end = -1L;
        public long endInNano = -1L;
        public long endCpu = -1L;
        public final int index;
        private final int batchId;
        public boolean hasLayoutAction = false;

        public RenderBatch(int batchId, int index) {
            this.batchId = batchId;
            this.index = index;
        }

        public void markDone() {
            if (end == -1) {
                end = System.currentTimeMillis();
                endInNano = System.nanoTime();
                endCpu = SystemClock.currentThreadTimeMillis();
            }
        }

        public void markHasLayoutOpt() {
            hasLayoutAction = true;
        }

        public void putToJson(String tag, JSONObject object, long ffpStart) throws JSONException {
            putToJson(tag, object, ffpStart, true);
        }

        public void putToJson(String tag, JSONObject object, long ffpStart, boolean addToTrace) throws JSONException {
            if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY && addToTrace) {
                PerfTrace.duration(tag, PerfTrace.convertSystemNanoTime(startInNano), endInNano - startInNano)
                        .arg("index", index)
                        .arg("batchId", batchId);
                object.put(tag, toString());
                object.put(tag + "_ffp_sw", start - ffpStart);
                object.put(tag + "_dur", end - start);
            }
            object.put(tag + "_sw", start); // sw: start_in_wall
            object.put(tag + "_ew", end); // ew: end_in_wall
            object.put(tag + "_sc", startCPU); // sc: start_in_cpu
            object.put(tag + "_ec", endCpu); // ec: end_in_cpu
            object.put(tag + "_la", hasLayoutAction); // la: has_layout_action
            object.put(tag + "_i", index); // i: index
        }

        @Override
        public String toString() {
            return batchId + "@" + start + "~" + end + ":(" + (end - start) + "ms)";
        }
    }

    public void markHasLayoutOpt() {
        List<RenderBatch> list = periodList[ThreadType.UI.ordinal()];
        if (!started || list.isEmpty()) {
            return;
        }
        RenderBatch last = list.get(list.size() - 1);
        if (last.end > 0) {
            return;
        }
        last.markHasLayoutOpt();
    }

    public void markRenderBatch(ThreadType threadType, boolean isBegin, int batchId) {
        if (!threadType.checkThread() || !started) {
            if (started) {
                Log.e(TAG, "illegal thread type, now=" + Thread.currentThread().getName() + ", but type=" + threadType);
            } else {
                Log.e(TAG, "started == false");
            }
            return;
        }
        List<RenderBatch> list = periodList[threadType.ordinal()];

        if (threadType == ThreadType.UI && list.isEmpty()
                && (periodList[ThreadType.SHADOW.ordinal()].isEmpty() || periodList[ThreadType.JS.ordinal()].isEmpty())) {
            return;
        }

        if (isBegin) {
            if (list.isEmpty() || list.get(list.size() - 1).end != -1L /*上一段没有闭合，说明 begin 多次调用，可以忽略*/ ) {
                //上一段已经闭合，添加新的,或者是空列表
                list.add(new RenderBatch(batchId, list.size()));
                //Log.d(TAG, "markRenderBatch() called with: threadType = [" + threadType + "], size = [" + list.size() + "], batchId = [" + batchId + "], >>>>>>");
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                    PerfTrace.begin(list.size() + "@Trace#" + threadType.toString().trim())
                            .arg("batchId-start", batchId);
                }

            }
        } else if (!list.isEmpty()) {
            RenderBatch batch = list.get(list.size() - 1);
            boolean hasRecorded = batch.end > 0;
            batch.markDone();
            //Log.println(hasRecorded ? Log.WARN : Log.DEBUG, TAG, "markRenderBatch() called with: threadType = [" + threadType + "], size = [" + list.size() + "], batchId = [" + batchId + "], <<<");
            PerfTrace.end(list.size() +  "@Trace#" + threadType.toString().trim())
                    .arg("batchId-end", batchId);
        }
    }

    public RenderBatch getFirstJs() {
        List<RenderBatch> list = periodList[ThreadType.JS.ordinal()];
        if (list.isEmpty()) {
            return null;
        }
        return list.get(0);
    }

    public RenderBatch getLastUI(long ffpTime) {
        List<RenderBatch> uiList = periodList[ThreadType.UI.ordinal()];
        if (uiList.isEmpty()) {
            return null;
        }
        for (int i = uiList.size() - 1; i >= 0 ; i--) {
            RenderBatch batch = uiList.get(i);
            if(batch.end > 0 && (batch.end <= ffpTime || (batch.start <= ffpTime && batch.hasLayoutAction))) {
                return batch;
            }
        }
        return null;
    }

    public RenderBatch getCurUI(long ffpTime) {
        List<RenderBatch> uiList = periodList[ThreadType.UI.ordinal()];
        if (uiList.isEmpty()) {
            return null;
        }
        for (int i = uiList.size() - 1; i >= 0 ; i--) {
            RenderBatch batch = uiList.get(i);
            if(batch.end > 0 && batch.start <= ffpTime && ffpTime <= batch.end) {
                return batch;
            }
        }
        return null;
    }

    public RenderBatch getValue(ThreadType threadType, int index) {
        List<RenderBatch> targetList = periodList[threadType.ordinal()];

        if (targetList.isEmpty() || index >= targetList.size() || index < 0) {
            Log.w(TAG, "getLastFFPValue() called with: threadType = [" + threadType + "], at = [" + index + "], list.size = [" + targetList.size() + "] return null;");
            return null;
        } else {
            return targetList.get(index);
        }
    }

    /**
     * 解析二进制数据，获取所有埋点记录
     * @param binaryData 二进制数据
     * @return 埋点数据列表
     */
    public static List<TraceData> parseBinaryData(byte[] binaryData) {
        List<TraceData> result = new ArrayList<>();
        
        if (binaryData == null || binaryData.length < 8) {
            return result;
        }
        
        ByteBuffer buffer = ByteBuffer.wrap(binaryData);
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        
        try {  
            // 读取数据条目数量
            int count = buffer.getInt();
            
            // 读取每个埋点数据
            for (int i = 0; i < count; i++) {
                // 读取key长度
                int keyLength = buffer.getInt();
                
                // 读取key内容
                byte[] keyBytes = new byte[keyLength];
                buffer.get(keyBytes);
                String key = new String(keyBytes);
                
                // 读取等级
                int level = buffer.getInt();
                
                // 读取总耗时
                long totalTimeNs = buffer.getLong();
                
                // 读取执行次数
                int executeCount = buffer.getInt();
                
                // 读取批次字段
                int batch = buffer.getInt();

                TraceData data = new TraceData(key, level, totalTimeNs, executeCount, batch);
                result.add(data);
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return result;
    }
    
    /**
     * 解析二进制数据，获取指定keys的埋点记录
     * @param binaryData 二进制数据
     * @param keys 指定的key列表
     * @return key到埋点数据的映射
     */
    public static Map<String, TraceData> parseBinaryData(byte[] binaryData, List<String> keys) {
        Map<String, TraceData> result = new HashMap<>();
        List<TraceData> allData = parseBinaryData(binaryData);
        
        for (TraceData data : allData) {
            if (keys.contains(data.getKey())) {
                result.put(data.getKey(), data);
            }
        }
        
        return result;
    }
    
    /**
     * 解析二进制数据，获取指定key的埋点记录
     * @param binaryData 二进制数据
     * @param key 指定的key
     * @return 埋点数据，如果不存在返回null
     */
    public static TraceData parseBinaryData(byte[] binaryData, String key) {
        List<TraceData> allData = parseBinaryData(binaryData);
        
        for (TraceData data : allData) {
            if (key.equals(data.getKey())) {
                return data;
            }
        }
        
        return null;
    }

    public void scheduleStopAndClear(long pageStartTime) {
        if (!isScheduledStopAndClear.compareAndSet(false, true) || !started) {
            return;
        }
        if (pageStartTime < 0) {
            pageStartTime = System.currentTimeMillis();
        }
        long pageLoadDurationTime = System.currentTimeMillis() - pageStartTime;
        if (pageLoadDurationTime < 0) {
            pageLoadDurationTime = 0;
        }
        long delayTimeInMs = SCHEDULED_STOP_AND_CLEAR_DELAY_TIME - pageLoadDurationTime;

        Runnable stopAndClear = new Runnable() {
            @Override
            public void run() {
                stop();
                cleanPeriodData();
            }
        };

        if (delayTimeInMs <= 0) {
            stopAndClear.run();
        } else {
            MSCExecutors.postDelayOnUiThread(stopAndClear, delayTimeInMs);
        }
    }

    public String queueSizeDesc() {
        StringBuilder sb = new StringBuilder();
        for (ThreadType threadType : ThreadType.values()) {
            sb.append(threadType.name())
                    .append(":")
                    .append(periodList[threadType.ordinal()].size())
                    .append(",");
        }
        return sb.toString();
    }
}