// automatically generated by the <PERSON>Buffers compiler, do not modify

package com.meituan.android.msc.renderer.generated;

import com.google.flatbuffers.BaseVector;
import com.google.flatbuffers.BooleanVector;
import com.google.flatbuffers.ByteVector;
import com.google.flatbuffers.Constants;
import com.google.flatbuffers.DoubleVector;
import com.google.flatbuffers.FlatBufferBuilder;
import com.google.flatbuffers.FloatVector;
import com.google.flatbuffers.IntVector;
import com.google.flatbuffers.LongVector;
import com.google.flatbuffers.ShortVector;
import com.google.flatbuffers.StringVector;
import com.google.flatbuffers.Struct;
import com.google.flatbuffers.Table;
import com.google.flatbuffers.UnionVector;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

@SuppressWarnings("unused")
public final class DisplayItem extends Table {
  public static void ValidateVersion() { Constants.FLATBUFFERS_25_2_10(); }
  public static DisplayItem getRootAsDisplayItem(ByteBuffer _bb) { return getRootAsDisplayItem(_bb, new DisplayItem()); }
  public static DisplayItem getRootAsDisplayItem(ByteBuffer _bb, DisplayItem obj) { _bb.order(ByteOrder.LITTLE_ENDIAN); return (obj.__assign(_bb.getInt(_bb.position()) + _bb.position(), _bb)); }
  public void __init(int _i, ByteBuffer _bb) { __reset(_i, _bb); }
  public DisplayItem __assign(int _i, ByteBuffer _bb) { __init(_i, _bb); return this; }

  public int tag() { int o = __offset(4); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public int containerTag() { int o = __offset(6); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public String viewName() { int o = __offset(8); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer viewNameAsByteBuffer() { return __vector_as_bytebuffer(8, 1); }
  public ByteBuffer viewNameInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 8, 1); }
  public String overflowX() { int o = __offset(10); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer overflowXAsByteBuffer() { return __vector_as_bytebuffer(10, 1); }
  public ByteBuffer overflowXInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 10, 1); }
  public String overflowY() { int o = __offset(12); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer overflowYAsByteBuffer() { return __vector_as_bytebuffer(12, 1); }
  public ByteBuffer overflowYInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 12, 1); }
  public int backgroundColor() { int o = __offset(14); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public int color() { int o = __offset(16); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public com.meituan.android.msc.renderer.generated.PropArray backgroundImage(int j) { return backgroundImage(new com.meituan.android.msc.renderer.generated.PropArray(), j); }
  public com.meituan.android.msc.renderer.generated.PropArray backgroundImage(com.meituan.android.msc.renderer.generated.PropArray obj, int j) { int o = __offset(18); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int backgroundImageLength() { int o = __offset(18); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.PropArray.Vector backgroundImageVector() { return backgroundImageVector(new com.meituan.android.msc.renderer.generated.PropArray.Vector()); }
  public com.meituan.android.msc.renderer.generated.PropArray.Vector backgroundImageVector(com.meituan.android.msc.renderer.generated.PropArray.Vector obj) { int o = __offset(18); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }
  public com.meituan.android.msc.renderer.generated.PropArray backgroundSize(int j) { return backgroundSize(new com.meituan.android.msc.renderer.generated.PropArray(), j); }
  public com.meituan.android.msc.renderer.generated.PropArray backgroundSize(com.meituan.android.msc.renderer.generated.PropArray obj, int j) { int o = __offset(20); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int backgroundSizeLength() { int o = __offset(20); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.PropArray.Vector backgroundSizeVector() { return backgroundSizeVector(new com.meituan.android.msc.renderer.generated.PropArray.Vector()); }
  public com.meituan.android.msc.renderer.generated.PropArray.Vector backgroundSizeVector(com.meituan.android.msc.renderer.generated.PropArray.Vector obj) { int o = __offset(20); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }
  public com.meituan.android.msc.renderer.generated.StringArray backgroundRepeat(int j) { return backgroundRepeat(new com.meituan.android.msc.renderer.generated.StringArray(), j); }
  public com.meituan.android.msc.renderer.generated.StringArray backgroundRepeat(com.meituan.android.msc.renderer.generated.StringArray obj, int j) { int o = __offset(22); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int backgroundRepeatLength() { int o = __offset(22); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.StringArray.Vector backgroundRepeatVector() { return backgroundRepeatVector(new com.meituan.android.msc.renderer.generated.StringArray.Vector()); }
  public com.meituan.android.msc.renderer.generated.StringArray.Vector backgroundRepeatVector(com.meituan.android.msc.renderer.generated.StringArray.Vector obj) { int o = __offset(22); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }
  public String borderTopStyle() { int o = __offset(24); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer borderTopStyleAsByteBuffer() { return __vector_as_bytebuffer(24, 1); }
  public ByteBuffer borderTopStyleInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 24, 1); }
  public float borderTopWidth() { int o = __offset(26); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public int borderTopColor() { int o = __offset(28); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public String borderRightStyle() { int o = __offset(30); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer borderRightStyleAsByteBuffer() { return __vector_as_bytebuffer(30, 1); }
  public ByteBuffer borderRightStyleInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 30, 1); }
  public float borderRightWidth() { int o = __offset(32); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public int borderRightColor() { int o = __offset(34); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public String borderBottomStyle() { int o = __offset(36); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer borderBottomStyleAsByteBuffer() { return __vector_as_bytebuffer(36, 1); }
  public ByteBuffer borderBottomStyleInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 36, 1); }
  public float borderBottomWidth() { int o = __offset(38); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public int borderBottomColor() { int o = __offset(40); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public String borderLeftStyle() { int o = __offset(42); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer borderLeftStyleAsByteBuffer() { return __vector_as_bytebuffer(42, 1); }
  public ByteBuffer borderLeftStyleInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 42, 1); }
  public float borderLeftWidth() { int o = __offset(44); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public int borderLeftColor() { int o = __offset(46); return o != 0 ? bb.getInt(o + bb_pos) : 0; }
  public float borderTopLeftRadius() { int o = __offset(48); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float borderTopRightRadius() { int o = __offset(50); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float borderBottomRightRadius() { int o = __offset(52); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float borderBottomLeftRadius() { int o = __offset(54); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public float opacity() { int o = __offset(56); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public String pointerEvents() { int o = __offset(58); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer pointerEventsAsByteBuffer() { return __vector_as_bytebuffer(58, 1); }
  public ByteBuffer pointerEventsInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 58, 1); }
  public com.meituan.android.msc.renderer.generated.Prop boxShadow(int j) { return boxShadow(new com.meituan.android.msc.renderer.generated.Prop(), j); }
  public com.meituan.android.msc.renderer.generated.Prop boxShadow(com.meituan.android.msc.renderer.generated.Prop obj, int j) { int o = __offset(60); return o != 0 ? obj.__assign(__indirect(__vector(o) + j * 4), bb) : null; }
  public int boxShadowLength() { int o = __offset(60); return o != 0 ? __vector_len(o) : 0; }
  public com.meituan.android.msc.renderer.generated.Prop.Vector boxShadowVector() { return boxShadowVector(new com.meituan.android.msc.renderer.generated.Prop.Vector()); }
  public com.meituan.android.msc.renderer.generated.Prop.Vector boxShadowVector(com.meituan.android.msc.renderer.generated.Prop.Vector obj) { int o = __offset(60); return o != 0 ? obj.__assign(__vector(o), 4, bb) : null; }
  public float fontSize() { int o = __offset(62); return o != 0 ? bb.getFloat(o + bb_pos) : 0.0f; }
  public String fontStyle() { int o = __offset(64); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer fontStyleAsByteBuffer() { return __vector_as_bytebuffer(64, 1); }
  public ByteBuffer fontStyleInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 64, 1); }
  public String textAlign() { int o = __offset(66); return o != 0 ? __string(o + bb_pos) : null; }
  public ByteBuffer textAlignAsByteBuffer() { return __vector_as_bytebuffer(66, 1); }
  public ByteBuffer textAlignInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 66, 1); }
  public float textPadding(int j) { int o = __offset(68); return o != 0 ? bb.getFloat(__vector(o) + j * 4) : 0; }
  public int textPaddingLength() { int o = __offset(68); return o != 0 ? __vector_len(o) : 0; }
  public FloatVector textPaddingVector() { return textPaddingVector(new FloatVector()); }
  public FloatVector textPaddingVector(FloatVector obj) { int o = __offset(68); return o != 0 ? obj.__assign(__vector(o), bb) : null; }
  public ByteBuffer textPaddingAsByteBuffer() { return __vector_as_bytebuffer(68, 4); }
  public ByteBuffer textPaddingInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 68, 4); }
  public int changeKeys(int j) { int o = __offset(70); return o != 0 ? bb.getInt(__vector(o) + j * 4) : 0; }
  public int changeKeysLength() { int o = __offset(70); return o != 0 ? __vector_len(o) : 0; }
  public IntVector changeKeysVector() { return changeKeysVector(new IntVector()); }
  public IntVector changeKeysVector(IntVector obj) { int o = __offset(70); return o != 0 ? obj.__assign(__vector(o), bb) : null; }
  public ByteBuffer changeKeysAsByteBuffer() { return __vector_as_bytebuffer(70, 4); }
  public ByteBuffer changeKeysInByteBuffer(ByteBuffer _bb) { return __vector_in_bytebuffer(_bb, 70, 4); }

  public static int createDisplayItem(FlatBufferBuilder builder,
      int tag,
      int containerTag,
      int viewNameOffset,
      int overflowXOffset,
      int overflowYOffset,
      int backgroundColor,
      int color,
      int backgroundImageOffset,
      int backgroundSizeOffset,
      int backgroundRepeatOffset,
      int borderTopStyleOffset,
      float borderTopWidth,
      int borderTopColor,
      int borderRightStyleOffset,
      float borderRightWidth,
      int borderRightColor,
      int borderBottomStyleOffset,
      float borderBottomWidth,
      int borderBottomColor,
      int borderLeftStyleOffset,
      float borderLeftWidth,
      int borderLeftColor,
      float borderTopLeftRadius,
      float borderTopRightRadius,
      float borderBottomRightRadius,
      float borderBottomLeftRadius,
      float opacity,
      int pointerEventsOffset,
      int boxShadowOffset,
      float fontSize,
      int fontStyleOffset,
      int textAlignOffset,
      int textPaddingOffset,
      int changeKeysOffset) {
    builder.startTable(34);
    DisplayItem.addChangeKeys(builder, changeKeysOffset);
    DisplayItem.addTextPadding(builder, textPaddingOffset);
    DisplayItem.addTextAlign(builder, textAlignOffset);
    DisplayItem.addFontStyle(builder, fontStyleOffset);
    DisplayItem.addFontSize(builder, fontSize);
    DisplayItem.addBoxShadow(builder, boxShadowOffset);
    DisplayItem.addPointerEvents(builder, pointerEventsOffset);
    DisplayItem.addOpacity(builder, opacity);
    DisplayItem.addBorderBottomLeftRadius(builder, borderBottomLeftRadius);
    DisplayItem.addBorderBottomRightRadius(builder, borderBottomRightRadius);
    DisplayItem.addBorderTopRightRadius(builder, borderTopRightRadius);
    DisplayItem.addBorderTopLeftRadius(builder, borderTopLeftRadius);
    DisplayItem.addBorderLeftColor(builder, borderLeftColor);
    DisplayItem.addBorderLeftWidth(builder, borderLeftWidth);
    DisplayItem.addBorderLeftStyle(builder, borderLeftStyleOffset);
    DisplayItem.addBorderBottomColor(builder, borderBottomColor);
    DisplayItem.addBorderBottomWidth(builder, borderBottomWidth);
    DisplayItem.addBorderBottomStyle(builder, borderBottomStyleOffset);
    DisplayItem.addBorderRightColor(builder, borderRightColor);
    DisplayItem.addBorderRightWidth(builder, borderRightWidth);
    DisplayItem.addBorderRightStyle(builder, borderRightStyleOffset);
    DisplayItem.addBorderTopColor(builder, borderTopColor);
    DisplayItem.addBorderTopWidth(builder, borderTopWidth);
    DisplayItem.addBorderTopStyle(builder, borderTopStyleOffset);
    DisplayItem.addBackgroundRepeat(builder, backgroundRepeatOffset);
    DisplayItem.addBackgroundSize(builder, backgroundSizeOffset);
    DisplayItem.addBackgroundImage(builder, backgroundImageOffset);
    DisplayItem.addColor(builder, color);
    DisplayItem.addBackgroundColor(builder, backgroundColor);
    DisplayItem.addOverflowY(builder, overflowYOffset);
    DisplayItem.addOverflowX(builder, overflowXOffset);
    DisplayItem.addViewName(builder, viewNameOffset);
    DisplayItem.addContainerTag(builder, containerTag);
    DisplayItem.addTag(builder, tag);
    return DisplayItem.endDisplayItem(builder);
  }

  public static void startDisplayItem(FlatBufferBuilder builder) { builder.startTable(34); }
  public static void addTag(FlatBufferBuilder builder, int tag) { builder.addInt(0, tag, 0); }
  public static void addContainerTag(FlatBufferBuilder builder, int containerTag) { builder.addInt(1, containerTag, 0); }
  public static void addViewName(FlatBufferBuilder builder, int viewNameOffset) { builder.addOffset(2, viewNameOffset, 0); }
  public static void addOverflowX(FlatBufferBuilder builder, int overflowXOffset) { builder.addOffset(3, overflowXOffset, 0); }
  public static void addOverflowY(FlatBufferBuilder builder, int overflowYOffset) { builder.addOffset(4, overflowYOffset, 0); }
  public static void addBackgroundColor(FlatBufferBuilder builder, int backgroundColor) { builder.addInt(5, backgroundColor, 0); }
  public static void addColor(FlatBufferBuilder builder, int color) { builder.addInt(6, color, 0); }
  public static void addBackgroundImage(FlatBufferBuilder builder, int backgroundImageOffset) { builder.addOffset(7, backgroundImageOffset, 0); }
  public static int createBackgroundImageVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startBackgroundImageVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addBackgroundSize(FlatBufferBuilder builder, int backgroundSizeOffset) { builder.addOffset(8, backgroundSizeOffset, 0); }
  public static int createBackgroundSizeVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startBackgroundSizeVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addBackgroundRepeat(FlatBufferBuilder builder, int backgroundRepeatOffset) { builder.addOffset(9, backgroundRepeatOffset, 0); }
  public static int createBackgroundRepeatVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startBackgroundRepeatVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addBorderTopStyle(FlatBufferBuilder builder, int borderTopStyleOffset) { builder.addOffset(10, borderTopStyleOffset, 0); }
  public static void addBorderTopWidth(FlatBufferBuilder builder, float borderTopWidth) { builder.addFloat(11, borderTopWidth, 0.0f); }
  public static void addBorderTopColor(FlatBufferBuilder builder, int borderTopColor) { builder.addInt(12, borderTopColor, 0); }
  public static void addBorderRightStyle(FlatBufferBuilder builder, int borderRightStyleOffset) { builder.addOffset(13, borderRightStyleOffset, 0); }
  public static void addBorderRightWidth(FlatBufferBuilder builder, float borderRightWidth) { builder.addFloat(14, borderRightWidth, 0.0f); }
  public static void addBorderRightColor(FlatBufferBuilder builder, int borderRightColor) { builder.addInt(15, borderRightColor, 0); }
  public static void addBorderBottomStyle(FlatBufferBuilder builder, int borderBottomStyleOffset) { builder.addOffset(16, borderBottomStyleOffset, 0); }
  public static void addBorderBottomWidth(FlatBufferBuilder builder, float borderBottomWidth) { builder.addFloat(17, borderBottomWidth, 0.0f); }
  public static void addBorderBottomColor(FlatBufferBuilder builder, int borderBottomColor) { builder.addInt(18, borderBottomColor, 0); }
  public static void addBorderLeftStyle(FlatBufferBuilder builder, int borderLeftStyleOffset) { builder.addOffset(19, borderLeftStyleOffset, 0); }
  public static void addBorderLeftWidth(FlatBufferBuilder builder, float borderLeftWidth) { builder.addFloat(20, borderLeftWidth, 0.0f); }
  public static void addBorderLeftColor(FlatBufferBuilder builder, int borderLeftColor) { builder.addInt(21, borderLeftColor, 0); }
  public static void addBorderTopLeftRadius(FlatBufferBuilder builder, float borderTopLeftRadius) { builder.addFloat(22, borderTopLeftRadius, 0.0f); }
  public static void addBorderTopRightRadius(FlatBufferBuilder builder, float borderTopRightRadius) { builder.addFloat(23, borderTopRightRadius, 0.0f); }
  public static void addBorderBottomRightRadius(FlatBufferBuilder builder, float borderBottomRightRadius) { builder.addFloat(24, borderBottomRightRadius, 0.0f); }
  public static void addBorderBottomLeftRadius(FlatBufferBuilder builder, float borderBottomLeftRadius) { builder.addFloat(25, borderBottomLeftRadius, 0.0f); }
  public static void addOpacity(FlatBufferBuilder builder, float opacity) { builder.addFloat(26, opacity, 0.0f); }
  public static void addPointerEvents(FlatBufferBuilder builder, int pointerEventsOffset) { builder.addOffset(27, pointerEventsOffset, 0); }
  public static void addBoxShadow(FlatBufferBuilder builder, int boxShadowOffset) { builder.addOffset(28, boxShadowOffset, 0); }
  public static int createBoxShadowVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addOffset(data[i]); return builder.endVector(); }
  public static void startBoxShadowVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addFontSize(FlatBufferBuilder builder, float fontSize) { builder.addFloat(29, fontSize, 0.0f); }
  public static void addFontStyle(FlatBufferBuilder builder, int fontStyleOffset) { builder.addOffset(30, fontStyleOffset, 0); }
  public static void addTextAlign(FlatBufferBuilder builder, int textAlignOffset) { builder.addOffset(31, textAlignOffset, 0); }
  public static void addTextPadding(FlatBufferBuilder builder, int textPaddingOffset) { builder.addOffset(32, textPaddingOffset, 0); }
  public static int createTextPaddingVector(FlatBufferBuilder builder, float[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addFloat(data[i]); return builder.endVector(); }
  public static void startTextPaddingVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static void addChangeKeys(FlatBufferBuilder builder, int changeKeysOffset) { builder.addOffset(33, changeKeysOffset, 0); }
  public static int createChangeKeysVector(FlatBufferBuilder builder, int[] data) { builder.startVector(4, data.length, 4); for (int i = data.length - 1; i >= 0; i--) builder.addInt(data[i]); return builder.endVector(); }
  public static void startChangeKeysVector(FlatBufferBuilder builder, int numElems) { builder.startVector(4, numElems, 4); }
  public static int endDisplayItem(FlatBufferBuilder builder) {
    int o = builder.endTable();
    return o;
  }

  public static final class Vector extends BaseVector {
    public Vector __assign(int _vector, int _element_size, ByteBuffer _bb) { __reset(_vector, _element_size, _bb); return this; }

    public DisplayItem get(int j) { return get(new DisplayItem(), j); }
    public DisplayItem get(DisplayItem obj, int j) {  return obj.__assign(__indirect(__element(j), bb), bb); }
  }
}

