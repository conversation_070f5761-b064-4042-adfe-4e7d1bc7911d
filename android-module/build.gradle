apply plugin: 'com.android.library'

android {
    namespace 'com.meituan.android.msc.renderer'

    compileSdkVersion 31

    defaultConfig {
        minSdkVersion 21

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"

        ndk {
            abiFilters 'arm64-v8a', 'armeabi-v7a'
        }
        ndkVersion '26.0.10792818'
    }

    packagingOptions{
        // 裁减掉lib,使用其他组件内的
//        exclude '**/libc++_shared.so'
        exclude("**/libc++_shared.so")
        exclude("**/libmscexecutor.so")
        exclude("**/libmsc_jsi.so")
        exclude("**/libmtv8.so")
        exclude("**/libv8.mt.so")
        exclude("**/libcjson.so")
    }

    buildTypes {
        release {
            externalNativeBuild {
                cmake {
                    arguments.add('-DANDROID_TOOLCHAIN=clang')
                    arguments.add('-DANDROID_STL=c++_shared')
                    arguments.add('-DCMAKE_BUILD_TYPE=Release')
                    arguments "-DFLATBUFFERS_INCLUDE_DIR=${project.projectDir.absolutePath}/include"
                }
            }
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            externalNativeBuild {
                cmake {
                    arguments.add('-DANDROID_TOOLCHAIN=clang')
                    arguments.add('-DANDROID_STL=c++_shared')
                    arguments.add('-DCMAKE_BUILD_TYPE=Debug')
                    arguments "-DFLATBUFFERS_INCLUDE_DIR=${project.projectDir.absolutePath}/include"
                }
            }
            jniDebuggable true
            renderscriptDebuggable true
        }
    }

    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
            version "3.18.1"
        }
    }

    task generateFbsCpp(type: Exec) {
        def inputDir = file("$projectDir/src/main/fbs")
        def outputCppDir = file("$projectDir/src/main/cpp/generated/")
        def fbsFiles = layout.files { file(inputDir).listFiles() }.filter { File f -> f.name.endsWith(".fbs") }.toList()
        ignoreExitValue(true)

        standardOutput = new ByteArrayOutputStream()
        errorOutput = new ByteArrayOutputStream()
        def commandLineArgs = ['flatc', '-o', outputCppDir, '--cpp']
        fbsFiles.forEach{
            commandLineArgs.add(it.path)
        }

        commandLine commandLineArgs

        doFirst {
            delete "$outputCppDir/"
            mkdir "$outputCppDir/"
        }

        doLast {
            if (executionResult.get().exitValue != 0) {
//                throw new GradleException("flatc failed with: ${executionResult.get().toString()}")
            }
        }
    }

    task generateFbsJava(type: Exec) {
        def inputDir = file("$projectDir/src/main/fbs")
        def outputJavaDir = file("$projectDir/src/main/java")
        def fbsFiles = layout.files { file(inputDir).listFiles() }.filter { File f -> f.name.endsWith(".fbs") }.toList()
        ignoreExitValue(true)

        standardOutput = new ByteArrayOutputStream()
        errorOutput = new ByteArrayOutputStream()

        setErrorOutput(errorOutput)
        setStandardOutput(standardOutput)

        def commandLineArgs = ['flatc', '-o', outputJavaDir, '--java']
        fbsFiles.forEach{
            commandLineArgs.add(it.path)
        }
        commandLine commandLineArgs

        doFirst {
            delete "$outputJavaDir/com/meituan/android/msc/renderer/generated"
            //会根据fbs文件的namespace自动放到输出目录下对应的子目录中
            mkdir "$outputJavaDir/com/meituan/android/msc/renderer/generated"
        }
        doLast {
            if (executionResult.get().exitValue != 0) {
//                throw new GradleException("flatc failed with: ${executionResult.get().toString()}")
            }
        }
    }

    afterEvaluate {
        tasks.named("preBuild") {
//            dependsOn(generateFbsJava)
//            dependsOn(generateFbsCpp)
        }
    }

    sourceSets {
        main {
            java.srcDirs = [
                    'src/main/java',
            ]
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    ndkVersion '26.0.10792818'
}

dependencies {

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'com.android.support.test:runner:1.0.2'
    androidTestImplementation 'com.android.support.test.espresso:espresso-core:3.0.2'
    implementation 'com.meituan.android.loader:dynloader-interface:1.0.8'
    implementation 'com.google.flatbuffers:flatbuffers-java:25.2.10'
    implementation("com.meituan.android.msc:msc-util:1.67.14.1-grey1")
}

/**
 * 获取当前分支名
 * @return
 */
def getGitBranchName() {
    return 'git symbolic-ref --short -q HEAD'.execute([], project.rootDir).text.trim()
}

/**
 * 获取最近一次提交的 Commit Id
 * @return
 */
def getGitLastShorCommitId() {
    return 'git rev-parse --short HEAD'.execute([], project.rootDir).text.trim()
}

if (hasProperty("VERSION_NAME")) {
    group = GROUP

    def enableSnapshotBuild = hasProperty("enableSnapshotBuild") ? enableSnapshotBuild.toBoolean() : false
    if (enableSnapshotBuild) {
        // 打快照包，会在版本号中间自动加上标识符，以区分不同功能的快照包
        // 使用分支名和最后一个commit的 commit id的hashCode作为版本标识
        def identifier = Math.abs((getGitBranchName() + getGitLastShorCommitId()).hashCode())
        VERSION_NAME += "-${identifier}-SNAPSHOT"
    }

    version = VERSION_NAME
}

afterEvaluate {
    ['release', 'debug'].each { buildType ->
        tasks.named("strip${buildType.capitalize()}DebugSymbols").configure {
            doLast {
                copy {
                    from "$buildDir/intermediates/stripped_native_libs/$buildType/out/lib/armeabi-v7a"
                    into "$buildDir/intermediates/stripped_native_libs/$buildType/out/lib/armeabi"
                }
            }
        }
    }
}

//task copyArmLibs(type: Copy) {
//
//    description = "Copy ARM libs and rename armeabi-v7a to armeabi"
//
//    // 定义输入和输出文件夹
//    def inputDir = file("$buildDir/intermediates/merged_native_libs/debug/out")
//    def outputDir = projectDir
//
//    from(inputDir)
//    into(outputDir)
//
//    doLast {
//        def v7dir = new File(outputDir, "lib/armeabi-v7a")
//        if (v7dir.exists()) {
//            def armeabiDir = new File(outputDir, "lib/armeabi")
//            armeabiDir.mkdirs()
//            v7dir.eachFile { file ->
//                file.copyTo(new File(armeabiDir, file.name))
//            }
//        }
//    }
//}
//
//tasks.whenTaskAdded { task ->
//    if (task.name == 'mergeDebugNativeLibs') {
//        task.finalizedBy(copyArmLibs)
//    }
//}
