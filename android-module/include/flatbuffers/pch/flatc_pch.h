/*
 * Copyright 2017 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef FLATBUFFERS_FLATC_PCH_H_
#define FLATBUFFERS_FLATC_PCH_H_

// stl
#include <cmath>
#include <sstream>
#include <cassert>
#include <unordered_set>
#include <unordered_map>
#include <iostream>
#include <functional>
#include <set>
#include <iterator>
#include <tuple>

// flatbuffers
#include "flatbuffers/pch/pch.h"
#include "flatbuffers/code_generators.h"
#include "flatbuffers/flatbuffers.h"
#include "flatbuffers/flexbuffers.h"
#include "flatbuffers/idl.h"

#endif // FLATBUFFERS_FLATC_PCH_H_
