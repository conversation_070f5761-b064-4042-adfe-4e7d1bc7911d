//
//  memory_monitor.h
//  MS<PERSON>enderer
//
//  Created by <PERSON><PERSON> on 2025/2/6.
//

#ifndef memory_monitor_h
#define memory_monitor_h

//#define ENABLE_MEMORY_MONITOR

#ifdef ENABLE_MEMORY_MONITOR

#define MEMORY_MALLOC(TYPE, SIZE) \
  blink::mt::MemoryMonitor::monitor().malloc(blink::mt::MemoryMonitor::MemoryType::TYPE, SIZE)
#define MEMORY_REALLOC(TYPE, OLD_SIZE, NEW_SIZE) \
  blink::mt::MemoryMonitor::monitor().realloc(blink::mt::MemoryMonitor::MemoryType::TYPE, OLD_SIZE, NEW_SIZE)
#define MEMORY_FREE(TYPE, SIZE) \
  blink::mt::MemoryMonitor::monitor().free(blink::mt::MemoryMonitor::MemoryType::TYPE, SIZ<PERSON>)
#define MEMORY_GET_USAGE(TYPE) \
  blink::mt::MemoryMonitor::monitor().getUsage(blink::mt::MemoryMonitor::MemoryType::TYPE)
#define MEMORY_GET_TOTAL_USAGE() \
  blink::mt::MemoryMonitor::monitor().getTotalUsage()
#define MEMORY_TYPES_COUNT 4

namespace blink::mt {

class MemoryMonitor {
public:
  enum class MemoryType {
    WTFMalloc = 0,
    V8Malloc,
    V8Heap,
    Other
  };

  static MemoryMonitor& monitor() {
    static MemoryMonitor instance;
    return instance;
  }

  void malloc(MemoryType type, long size) {
    if (UNLIKELY(size < 0)) {
      size = 0;
    }
    mem_usage_[(int)type] += size;
  }
  
  void realloc(MemoryType type, long old_size, long new_size) {
    auto cur_size = mem_usage_[(int)type] + (new_size - old_size);
    if (UNLIKELY(cur_size < 0)) {
      cur_size = 0;
    }
    mem_usage_[(int)type] = cur_size;
  }

  void free(MemoryType type, long size) {
    auto cur_size = mem_usage_[(int)type] - size;
    if (UNLIKELY(cur_size < 0)) {
      cur_size = 0;
    }
    mem_usage_[(int)type] = cur_size;
  }

  long getUsage(MemoryType type) const {
    return mem_usage_[(int)type];
  }

  long getTotalUsage() const {
    long total = 0;
    for (int i = 0; i < MEMORY_TYPES_COUNT; i++) {
      total += mem_usage_[i];
    }
    return total;
  }

private:
  long mem_usage_[MEMORY_TYPES_COUNT];
};

} // namespace blink::mt

#else

#define MEMORY_MALLOC(TYPE, AMOUNT)
#define MEMORY_FREE(TYPE, AMOUNT)
#define MEMORY_GET_USAGE(TYPE) 0
#define MEMORY_GET_TOTAL_USAGE() 0

#endif

#endif /* memory_monitor_hpp */
