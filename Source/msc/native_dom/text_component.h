//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/4/7.
//

#ifndef MSC_ANDROID_TEXT_COMPONENT_H
#define MSC_ANDROID_TEXT_COMPONENT_H

#include "view_component.h"

namespace msc {
namespace native_dom {

class TextComponent : public ViewComponent {
public:
    TextComponent(ContainerNode *root, Tag tag = Tag::MSC_TEXT) : ViewComponent(root, tag) {};
    virtual ~TextComponent() override {};

    bool IsContainerElement() const override {
        return true;
    }
};

class VirtualTextComponent : public ViewComponent {
public:
    VirtualTextComponent(ContainerNode *root, Tag tag = Tag::MSC_VIRTUAL_TEXT) : ViewComponent(root, tag) {};
    virtual ~VirtualTextComponent() override {};

    bool IsContainerElement() const override {
        return true;
    }
};

} // msc
} // native_dom

#endif //MSC_ANDROID_TEXT_COMPONENT_H
