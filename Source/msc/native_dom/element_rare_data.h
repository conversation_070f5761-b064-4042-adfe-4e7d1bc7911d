//
//  element_rare_data.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef ElementRareData_h
#define ElementRareData_h

#include "dom_attr.h"
#include "callback_function.h"
#include "event_target.h"
#include "dom_named_node_map.h"

#include <map>

namespace msc {
namespace native_dom {

using DOMAttrMap = std::map<MSCString, std::unique_ptr<DOMAttr>>;
using DOMCallbackMap = std::map<uint32_t, std::unique_ptr<CallbackFunction>>;

class ElementRareData {
public:
  ElementRareData() = default;
  virtual ~ElementRareData() = default;
  
  const MSCString &idAttribute() const { return id_attribute_; }
  void SetIdAttribute(const MSCString& value) { id_attribute_ = value; }

  const MSCString& inlineStyle() const { return inline_style_; }
  void SetInlineStyle(const MSCString& value) { inline_style_ = value; }

  EventTargetDataMap* eventTargetData() const { return event_target_data_.get(); }
  EventTargetDataMap& EnsureEventTargetData()
  {
      if (!event_target_data_)
          event_target_data_.reset(new EventTargetDataMap);
      return *event_target_data_;
  }

//  AnimatableComponentData* animatableComponentData() const { return m_animatableComponentData.get(); }
//  AnimatableComponentData& ensureAnimatableComponentData()
//  {
//      if (!m_animatableComponentData)
//          m_animatableComponentData.reset(new AnimatableComponentData);
//      return *m_animatableComponentData;
//  }
//
//  HoverableComponentData* hoverableComponentData() const { return m_hoverableComponentData.get(); }
//  HoverableComponentData& ensureHoverableComponentData()
//  {
//      if (!m_hoverableComponentData)
//          m_hoverableComponentData.reset(new HoverableComponentData);
//      return *m_hoverableComponentData;
//  }

  DOMAttr* domAttrIfExist(const MSCString& name)
  {
      if (!dom_attr_map_)
          return nullptr;
      auto value = dom_attr_map_->find(name);
      if (value == dom_attr_map_->end())
          return nullptr;
      DOMAttr* attr = value->second.get();
      if (attr && !attr->IsDetached())
          return attr;
      return attr;
  }

  void AddDOMAttr(const MSCString& name, std::unique_ptr<DOMAttr> attr)
  {
      EnsureDOMAttrMap()[name] = std::move(attr);
  }

  DOMNamedNodeMap* attributesNamedNodeMap()
  {
      return attribute_named_node_map_.get();
  }

  void SetAttributesNamedNodeMap(std::unique_ptr<DOMNamedNodeMap> nodeMap)
  {
      attribute_named_node_map_ = std::move(nodeMap);
  }

  std::unordered_map<void*, bool>* intersectionObserverData() const
  {
      return intersection_observers_.get();
  }

  std::unordered_map<void*, bool>& ensureIntersectionObserverData()
  {
      if (!intersection_observers_)
          intersection_observers_.reset(new std::unordered_map<void*, bool>);
      return *intersection_observers_;
  }

  DOMCallbackMap* repeatCallbacks() const { return repeat_callbacks_.get(); }

  DOMCallbackMap* onceCallbacks() const { return once_callbacks_.get(); }

  DOMCallbackMap& EnsureRepeatCallbacks()
  {
      if (!repeat_callbacks_)
          repeat_callbacks_.reset(new DOMCallbackMap);
      return *repeat_callbacks_;
  }

  DOMCallbackMap& EnsureOnceCallbacks()
  {
      if (!once_callbacks_)
          once_callbacks_.reset(new DOMCallbackMap);
      return *once_callbacks_;
  }

private:
  DOMAttrMap& EnsureDOMAttrMap()
  {
      if (!dom_attr_map_)
          dom_attr_map_.reset(new DOMAttrMap);
      return *dom_attr_map_;
  }

  MSCString id_attribute_;
  MSCString inline_style_;
  std::unique_ptr<DOMAttrMap> dom_attr_map_;
  std::unique_ptr<DOMNamedNodeMap> attribute_named_node_map_;
  std::unique_ptr<EventTargetDataMap> event_target_data_;
//  std::unique_ptr<AnimatableComponentData> m_animatableComponentData;
//  std::unique_ptr<HoverableComponentData> m_hoverableComponentData;
  std::unique_ptr<std::unordered_map<void*, bool>> intersection_observers_;
  std::unique_ptr<DOMCallbackMap> repeat_callbacks_;
  std::unique_ptr<DOMCallbackMap> once_callbacks_;
  
};

}  // namespace native_dom
}  // namespace msc
#endif /* ElementRareData_h */
