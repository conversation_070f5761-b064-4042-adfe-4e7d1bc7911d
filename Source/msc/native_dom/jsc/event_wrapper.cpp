//
//  event_wrapper.cpp
//  MSCRenderer
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/30.
//

#include "event_wrapper.h"
#include "../event.h"
#include "../event_target.h"
#include "jsc_binding_utils.h"

#if USE_JSC

namespace msc {
namespace native_dom {

#define JSC_NATIVE_DOM_FUNCTION_DEF(V)                                                \
  V(EventWrapper, Event, Event::kStopPropagation.c_str(), StopPropagation)

#define JSC_NATIVE_DOM_PROP_GETTER_DEF(V)  \
    V(EventWrapper, Event, "target", Target)  \
    V(EventWrapper, Event, "currentTarget", CurrentTarget)  \
    V(EventWrapper, Event, "timeStamp", TimeStamp)  \
    V(EventWrapper, Event, "type", Type)  \
    V(EventWrapper, Event, "eventPhase", EventPhase)  \
    V(EventWrapper, Event, "detail", Detail)

#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V) \
    JSC_NATIVE_DOM_FUNCTION_DEF(V)             \
    JSC_STATIC_FUNCTION_END

#define JSC_NATIVE_DOM_PROP_INITIALIZER(V)   \
    JSC_NATIVE_DOM_PROP_GETTER_DEF(V) \
    JSC_STATIC_VALUE_END

JSC_NATIVE_DOM_FUNCTION_DEF(JSC_OBJ_FUNCTION_IMPL);
JSC_NATIVE_DOM_PROP_GETTER_DEF(JSC_BINDING_PROP_GET)

JSClassRef EventWrapper::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
       JSC_NATIVE_DOM_FUNCTION_INITIALIZER(JSC_STATIC_FUNCTION)
   };

   static JSStaticValue static_values[] = {
       JSC_NATIVE_DOM_PROP_INITIALIZER(JSC_STATIC_VALUE_READ_ONLY)
   };
   return registerJSClass("Event", static_functions, static_values, parent_class);
}

JSValueRef EventWrapper::CreateJSObject(JSContextRef context) {
  JSObjectRef ret = tryCreateJSObject(context, "Event", this);
  return ret;
}

JSValueRef EventWrapper::StopPropagation(JSContextRef context,
                                         JSObjectRef thisObject, size_t argc,
                                         const JSValueRef* argv,
                                         JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  auto currentEvent = getJSCInterface(thisObject);
  if (currentEvent) {
    GetInstance<Event>()->SetStopPropagation();
  }
  return JSValueMakeNull(context);
}

JSValueRef EventWrapper::getEventPhase(JSContextRef ctx,
                                       JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto eventPhase = GetInstance<Event>()->eventPhase();
  return JSValueMakeNumber(ctx, eventPhase);
}

JSValueRef EventWrapper::getTimeStamp(JSContextRef ctx, JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  long long ts = pointToMillisecondsFromEpoch(GetInstance<Event>()->timestamp());
  return JSValueMakeNumber(ctx, ts);
}

JSValueRef EventWrapper::getType(JSContextRef ctx, JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto typeName = GetInstance<Event>()->typeName();
  JSStringRef str = JSStringCreateWithUTF8CString(typeName.c_str());
  auto jsType = JSValueMakeString(ctx, str);
  JSStringRelease(str);
  return jsType;
}

JSValueRef EventWrapper::getTarget(JSContextRef ctx, JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto target = GetInstance<Event>()->target();
  if (target) {
     return GET_JSOBJECT_FROM(target);
  }
  return JSValueMakeNull(ctx);
}

JSValueRef EventWrapper::getCurrentTarget(JSContextRef ctx,
                                          JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto target = GetInstance<Event>()->currentTarget();
  if (target) {
     return GET_JSOBJECT_FROM(target);
  }
  return JSValueMakeNull(ctx);
}

JSValueRef EventWrapper::getDetail(JSContextRef ctx, JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto detail = GetInstance<Event>()->detail();
  if (!detail) {
    return JSObjectMake(ctx, nullptr, nullptr);
  }
  return JSObjectFromProps(ctx, *detail, "target");
}

}  // namespace native_dom
}  // namespace msc

#endif
