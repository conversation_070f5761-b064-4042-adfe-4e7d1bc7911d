//
//  dom_implementation_wrapper.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/13.
//

#ifndef DOMImplementationWrapper_hpp
#define DOMImplementationWrapper_hpp

#include "native_dom_object_wrapper.h"
#include "dom_implementation.h"

#if USE_JSC

namespace msc {
namespace native_dom {

class DOMImplementationWrapper : public NativeDOMObjectWrapper {
public:
  DOMImplementationWrapper(std::shared_ptr<DOMImplementation> impl) : NativeDOMObjectWrapper(impl) {}
  
  static JSClassRef SetupJSClass(JSClassRef parent_class);
  JSValueRef CreateJSObject(JSContextRef context);
  
  DECLARE_JSC_FUNCTION(CreateDocument);
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* DOMImplementationWrapper_hpp */
