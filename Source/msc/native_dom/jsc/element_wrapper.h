//
//  NodeWrapper.hpp
//  MSC
//
//  Created by ji<PERSON><PERSON> zhang on 2025/2/13.
//

#ifndef ElementWrapper_hpp
#define ElementWrapper_hpp

#include "node_wrapper.h"
#include "css_style_declaration_wrapper.h"
#include "../element.h"

#if USE_JSC

namespace msc {
namespace native_dom {


class ElementWrapper : public NodeWrapper {
public:
  ElementWrapper(std::shared_ptr<Element> element) : NodeWrapper(element) {}
  virtual ~ElementWrapper();
  
  static JSClassRef SetupJSClass(JSClassRef parent_class);
  JSValueRef CreateJSObject(JSContextRef context);
  
  DECLARE_JSC_FUNCTION(GetAttribute);
  DECLARE_JSC_FUNCTION(SetAttribute);
  DECLARE_JSC_FUNCTION(RemoveAttribute);
  
  DECLARE_JSC_PROP_GETTER_AND_SETTER(Id);
  DECLARE_JSC_PROP_GETTER_AND_SETTER(ClassName);
  DECLARE_JSC_PROP_GETTER_AND_SETTER(Class);
  DECLARE_JSC_PROP_GETTER(Style);
  DECLARE_JSC_PROP_GETTER(ClassList);
  DECLARE_JSC_PROP_GETTER(TagName);
};


}  // namespace native_dom
}  // namespace msc

#endif

#endif /* NodeWrapper_hpp */
