//
//  DOMImplementationWrapper.cpp
//  MSC
//
//  Created by ji<PERSON><PERSON> zhang on 2025/2/13.
//

#include "dom_implementation_wrapper.h"
#include "../document.h"
#include "document_wrapper.h"
#include "../bridge/message_proxy.h"

#include <chrono>

#if USE_JSC

#include <os/log.h>

namespace msc {
namespace native_dom {

#define JSC_NATIVE_DOM_FUNCTION_DEF(V)                                                \
V(DOMImplementationWrapper, DOMImplementation, DOMImplementation::kCreateDocument.c_str(), CreateDocument)

#define JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V)



#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V) \
JSC_NATIVE_DOM_FUNCTION_DEF(V)             \
JSC_STATIC_FUNCTION_END

#define JSC_NATIVE_DOM_PROP_INITIALIZER(V)   \
JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V) \
JSC_STATIC_VALUE_END

JSC_NATIVE_DOM_FUNCTION_DEF(JSC_OBJ_FUNCTION_IMPL);

JSClassRef DOMImplementationWrapper::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
    JSC_NATIVE_DOM_FUNCTION_INITIALIZER(JSC_STATIC_FUNCTION)
  };
  
  static JSStaticValue static_values[] = {
    JSC_NATIVE_DOM_PROP_INITIALIZER(JSC_STATIC_VALUE_READ_WRITE)
  };
  return registerJSClass("DOMImplementation", static_functions, static_values, parent_class);
}

JSValueRef DOMImplementationWrapper::CreateJSObject(JSContextRef context) {
  JSObjectRef ret = tryCreateJSObject(context, "DOMImplementation", this);
  return ret;
}

JSValueRef DOMImplementationWrapper::CreateDocument(JSContextRef context,
                                                    JSObjectRef thisObject,
                                                    size_t argc,
                                                    const JSValueRef argv[],
                                                    JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'createDocument' on 'DOMImplementation':1 argument "
      "required");

  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  auto pageId = JSValueToNumber(context, argv[0], nullptr);
  auto domImpl = GetInstance<DOMImplementation>();
  auto document = domImpl->CreateDocument(pageId);
  auto wrapper = new DocumentWrapper(document);
  return wrapper->CreateJSObject(context);
}

}  // namespace native_dom
}  // namespace msc

#endif
