//
//  jsc_js_callback.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/6/3.
//  Changed by q<PERSON><PERSON> on 2025/6/23.
//

#include "../../../Public/task_runner.h"

#ifndef JSC_JS_CALLBACK_H
#define JSC_JS_CALLBACK_H

namespace msc {
namespace native_dom {

struct JSCJSCallback : public JSCallback {
 public:
  JSCJSCallback(std::unique_ptr<JSCallable> callable, JSContextRef context,
                JSObjectRef callback)
      : JSCallback(std::move(callable)),
        context_(context),
        callback_(callback) {
    JSValueProtect(context_, callback_);
  }
  virtual ~JSCJSCallback() override { JSValueUnprotect(context_, callback_); };

 protected:
  JSContextRef context_ = nullptr;
  JSObjectRef callback_ = nullptr;
};

}  // namespace native_dom
}  // namespace msc

#endif /* JSC_JS_CALLBACK_H */
