//
//  dom_token_list_wrapper.h
//  MSCRenderer
//
//  Created by <PERSON> on 2025/6/2.
//

#ifndef MSC_NATIVE_DOM_JSC_DOM_TOKEN_LIST_WRAPPER_H
#define MSC_NATIVE_DOM_JSC_DOM_TOKEN_LIST_WRAPPER_H

#include "native_dom/dom_token_list.h"
#include "native_dom_object_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

template <typename Wrapper>
class DOMTokenLIstIteratorWrapper : public NativeDOMObjectWrapper {
public:
  DOMTokenLIstIteratorWrapper(std::shared_ptr<DOMTokenList::Iterator> node)
      : NativeDOMObjectWrapper(node) {}

  static JSClassRef SetupJSClass(JSClassRef parent_class);
  JSValueRef CreateJSObject(JSContextRef context);

  DECLARE_JSC_FUNCTION(GetNext);

  static JSValueRef SymbolIterator(JSContextRef ctx, JSObjectRef function,
                                   JSObjectRef thisObject, size_t argumentCount,
                                   const JSValueRef arguments[],
                                   J<PERSON><PERSON>ue<PERSON>ef *exception);
};

template <typename Wrapper>
static JSValueRef onCallDOMTokenLIstIteratorWrapperGetNext(
    JSContextRef context, JSObjectRef function, JSObjectRef thisObject,
    size_t argc, const JSValueRef argv[], JSValueRef *exception) {
  DOMTokenLIstIteratorWrapper<Wrapper> *element =
      static_cast<DOMTokenLIstIteratorWrapper<Wrapper> *>(
          getJSCInterface(thisObject));
  if (element == nullptr)
    return JSValueMakeNull(context);
  return element->GetNext(context, thisObject, argc, argv, exception);
}

template <typename Wrapper>
JSClassRef
DOMTokenLIstIteratorWrapper<Wrapper>::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
      {"next", onCallDOMTokenLIstIteratorWrapperGetNext<Wrapper>,
       kJSPropertyAttributeDontDelete | kJSPropertyAttributeReadOnly},
      JSC_STATIC_FUNCTION_END};

  static JSStaticValue static_values[] = {JSC_STATIC_VALUE_END};
  return registerJSClass(Wrapper::kInterfaceName, static_functions,
                         static_values, parent_class);
}

template <typename Wrapper>
JSValueRef
DOMTokenLIstIteratorWrapper<Wrapper>::CreateJSObject(JSContextRef context) {
  JSObjectRef ret = tryCreateJSObject(context, Wrapper::kInterfaceName, this, false);
  return ret;
}

template <typename Wrapper>
JSValueRef DOMTokenLIstIteratorWrapper<Wrapper>::GetNext(
    JSContextRef context, JSObjectRef thisObject, size_t argc,
    const JSValueRef *argv, JSValueRef *exception) {
  auto iterator = GetInstance<DOMTokenList::Iterator>();

  JSObjectRef res = JSObjectMake(context, nullptr, nullptr);
  if (iterator && iterator->HasNext()) {
    auto next_tuple = iterator->Next();
    auto value = Wrapper::GetNextValue(context, std::get<0>(next_tuple),
                                       std::get<1>(next_tuple));
    JSStringRef valueKey = JSStringCreateWithUTF8CString("value");
    JSObjectSetProperty(context, res, valueKey, value, kJSPropertyAttributeNone,
                        nullptr);
    JSStringRelease(valueKey);

    // 添加 done 属性
    JSStringRef doneKey = JSStringCreateWithUTF8CString("done");
    JSValueRef done = JSValueMakeBoolean(context, false);
    JSObjectSetProperty(context, res, doneKey, done, kJSPropertyAttributeNone,
                        nullptr);
    JSStringRelease(doneKey);
  } else {
    // 添加 done 属性
    JSStringRef doneKey = JSStringCreateWithUTF8CString("done");
    JSValueRef done = JSValueMakeBoolean(context, true);
    JSObjectSetProperty(context, res, doneKey, done, kJSPropertyAttributeNone,
                        nullptr);
    JSStringRelease(doneKey);
  }
  return res;
}

template <typename Wrapper>
JSValueRef DOMTokenLIstIteratorWrapper<Wrapper>::SymbolIterator(
    JSContextRef ctx, JSObjectRef function, JSObjectRef thisObject,
    size_t argumentCount, const JSValueRef arguments[], JSValueRef *exception) {
  return thisObject;
}

class DOMTokenListEntryIteratorWrapper
    : public DOMTokenLIstIteratorWrapper<DOMTokenListEntryIteratorWrapper> {
  using Base = DOMTokenLIstIteratorWrapper<DOMTokenListEntryIteratorWrapper>;

public:
  DOMTokenListEntryIteratorWrapper(std::shared_ptr<DOMTokenList::Iterator> node)
      : Base(node) {}
  static constexpr char kInterfaceName[] = "DOMTokenListEntryIterator";
  static JSValueRef GetNextValue(JSContextRef context, size_t key,
                                 const std::string &value);
};

class DOMTokenListKeyIteratorWrapper
    : public DOMTokenLIstIteratorWrapper<DOMTokenListKeyIteratorWrapper> {
  using Base = DOMTokenLIstIteratorWrapper<DOMTokenListKeyIteratorWrapper>;

public:
  DOMTokenListKeyIteratorWrapper(std::shared_ptr<DOMTokenList::Iterator> node)
      : Base(node) {}
  static constexpr char kInterfaceName[] = "DOMTokenListKeyIterator";
  static JSValueRef GetNextValue(JSContextRef context, size_t key,
                                 const std::string &value);
};

class DOMTokenListValueIteratorWrapper
    : public DOMTokenLIstIteratorWrapper<DOMTokenListValueIteratorWrapper> {
  using Base = DOMTokenLIstIteratorWrapper<DOMTokenListValueIteratorWrapper>;

public:
  DOMTokenListValueIteratorWrapper(std::shared_ptr<DOMTokenList::Iterator> node)
      : Base(node) {}
  static constexpr char kInterfaceName[] = "DOMTokenListValueIterator";
  static JSValueRef GetNextValue(JSContextRef context, size_t key,
                                 const std::string &value);
};

class DOMTokenListWrapper : public NativeDOMObjectWrapper {
public:
  DOMTokenListWrapper(std::shared_ptr<DOMTokenList> node)
      : NativeDOMObjectWrapper(node) {}

  static JSClassRef SetupJSClass(JSClassRef parent_class);
  JSValueRef CreateJSObject(JSContextRef context);

  template <typename IteratorWrapper>
  JSValueRef GetIterator(JSContextRef context);

  DECLARE_JSC_FUNCTION(GetItem);
  DECLARE_JSC_FUNCTION(Contains);
  DECLARE_JSC_FUNCTION(Add);
  DECLARE_JSC_FUNCTION(Remove);
  DECLARE_JSC_FUNCTION(Replace);
  DECLARE_JSC_FUNCTION(Supports);
  DECLARE_JSC_FUNCTION(Toggle);
  DECLARE_JSC_FUNCTION(ToString);
  DECLARE_JSC_FUNCTION(ForEach);
  DECLARE_JSC_FUNCTION(Entries);
  DECLARE_JSC_FUNCTION(Values);
  DECLARE_JSC_FUNCTION(Keys);

  DECLARE_JSC_PROP_GETTER(Length);
  DECLARE_JSC_PROP_GETTER(Value);
};

// 设置 obj[Symbol.{symbolName}] = value
void SetSymbolProperty(JSContextRef ctx, JSObjectRef obj,
                       const char *symbolName, JSValueRef value);

template <typename IteratorWrapper>
JSValueRef DOMTokenListWrapper::GetIterator(JSContextRef ctx) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  auto iterator =
      std::make_shared<DOMTokenList::Iterator>(*GetInstance<DOMTokenList>());
  auto wrapper = new IteratorWrapper(iterator);
  auto obj = JSValueToObject(ctx, wrapper->CreateJSObject(ctx), nullptr);

  // 添加 Symbol.iterator 方法
  // 创建迭代器函数
  JSObjectRef iteratorFunc = JSObjectMakeFunctionWithCallback(
      ctx, nullptr, IteratorWrapper::SymbolIterator);

  // 设置 Symbol.iterator 属性
  SetSymbolProperty(ctx, obj, "iterator", iteratorFunc);
  return obj;
}

} // namespace native_dom
} // namespace msc

#endif

#endif // MSC_NATIVE_DOM_JSC_DOM_TOKEN_LIST_WRAPPER_H
