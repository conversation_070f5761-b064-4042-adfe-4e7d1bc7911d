//
//  touch_event_wrapper_hpp.cpp
//  MSCRenderer
//
//  Created by ji<PERSON><PERSON> <PERSON><PERSON> on 2025/5/30.
//

#include "touch_event_wrapper.h"
#include "../event.h"
#include "../event_target.h"
#include "jsc_binding_utils.h"

#if USE_JSC

namespace msc {
namespace native_dom {

#define JSC_NATIVE_DOM_PROP_GETTER_DEF(V)  \
    V(TouchEventWrapper, TouchEvent, "touches", Touches)  \
    V(TouchEventWrapper, TouchEvent, "changedTouches", ChangedTouches)

#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V) \
    JSC_NATIVE_DOM_FUNCTION_DEF(V)             \
    JSC_STATIC_FUNCTION_END

#define JSC_NATIVE_DOM_PROP_INITIALIZER(V)   \
    JSC_NATIVE_DOM_PROP_GETTER_DEF(V) \
    JSC_STATIC_VALUE_END

JSC_NATIVE_DOM_PROP_GETTER_DEF(JSC_BINDING_PROP_GET)

JSClassRef TouchEventWrapper::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
   };

   static JSStaticValue static_values[] = {
       JSC_NATIVE_DOM_PROP_INITIALIZER(JSC_STATIC_VALUE_READ_ONLY)
   };
   return registerJSClass("TouchEvent", static_functions, static_values, parent_class);
}

JSValueRef TouchEventWrapper::CreateJSObject(JSContextRef context) {
  JSObjectRef ret = tryCreateJSObject(context, "TouchEvent", this);
  return ret;
}

JSValueRef createTouchArray(JSContextRef ctx, std::vector<msc::native_dom::Touch> &touches) {
    JSStringRef propClientX = JSStringCreateWithUTF8CString("clientX");
    JSStringRef propClientY = JSStringCreateWithUTF8CString("clientY");
    JSStringRef propPageX = JSStringCreateWithUTF8CString("pageX");
    JSStringRef propPageY = JSStringCreateWithUTF8CString("pageY");
    JSStringRef propIdentifier = JSStringCreateWithUTF8CString("identifier");
    JSStringRef propForce = JSStringCreateWithUTF8CString("force");

    const size_t arraySize = touches.size();
    std::vector<JSValueRef> entries;
    entries.reserve(arraySize);

    for (size_t i = 0; i < arraySize; ++i) {
      Touch &touch = touches[i];
      
      JSObjectRef obj = JSObjectMake(ctx, NULL, NULL);
        
      JSValueRef clientX = JSValueMakeNumber(ctx, touch.GetClientX());
      JSValueRef clientY = JSValueMakeNumber(ctx, touch.GetClientY());
      JSValueRef pageX = JSValueMakeNumber(ctx, touch.GetPageX());
      JSValueRef pageY = JSValueMakeNumber(ctx, touch.GetPageY());
      JSValueRef identifier = JSValueMakeNumber(ctx, touch.GetIdentifier());
      JSValueRef force = JSValueMakeNumber(ctx, touch.GetForce());
        
      JSObjectSetProperty(ctx, obj, propClientX, clientX, kJSPropertyAttributeNone, NULL);
      JSObjectSetProperty(ctx, obj, propClientY, clientY, kJSPropertyAttributeNone, NULL);
      JSObjectSetProperty(ctx, obj, propPageX, pageX, kJSPropertyAttributeNone, NULL);
      JSObjectSetProperty(ctx, obj, propPageY, pageY, kJSPropertyAttributeNone, NULL);
      JSObjectSetProperty(ctx, obj, propIdentifier, identifier, kJSPropertyAttributeNone, NULL);
      JSObjectSetProperty(ctx, obj, propForce, force, kJSPropertyAttributeNone, NULL);
        
      entries.push_back(obj);
    }

    JSObjectRef jsArray = JSObjectMakeArray(ctx, arraySize, entries.data(), NULL);

    JSStringRelease(propClientX);
    JSStringRelease(propClientY);
    JSStringRelease(propPageX);
    JSStringRelease(propPageY);
    JSStringRelease(propIdentifier);
    JSStringRelease(propForce);

    return jsArray;
}

JSValueRef TouchEventWrapper::getTouches(JSContextRef ctx,
                                         JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto touches = GetInstance<TouchEvent>()->touches();
  return createTouchArray(ctx, touches);
}

JSValueRef TouchEventWrapper::getChangedTouches(JSContextRef ctx,
                                                JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto touches = GetInstance<TouchEvent>()->changedTouches();
  return createTouchArray(ctx, touches);
}

//#pragma mark - TapEventWrapper
//
//#define JSC_NATIVE_DOM_PROP_GETTER_DEF(V)  \
//    V(TapEventWrapper, TapEvent, "detail", Detail)
//
//#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V) \
//    JSC_NATIVE_DOM_FUNCTION_DEF(V)             \
//    JSC_STATIC_FUNCTION_END
//
//#define JSC_NATIVE_DOM_PROP_INITIALIZER(V)   \
//    JSC_NATIVE_DOM_PROP_GETTER_DEF(V) \
//    JSC_STATIC_VALUE_END
//
//JSC_NATIVE_DOM_PROP_GETTER_DEF(JSC_BINDING_PROP_GET)
//
//JSClassRef TapEventWrapper::SetupJSClass(JSClassRef parent_class) {
//  static JSStaticFunction static_functions[] = {
//   };
//
//   static JSStaticValue static_values[] = {
//       JSC_NATIVE_DOM_PROP_INITIALIZER(JSC_STATIC_VALUE_READ_ONLY)
//   };
//   return registerJSClass("TapEvent", static_functions, static_values, parent_class);
//}
//
//JSValueRef TapEventWrapper::CreateJSObject(JSContextRef context) {
//  JSObjectRef ret = tryCreateJSObject(context, "TapEvent", this);
//  return ret;
//}
//
//JSValueRef TapEventWrapper::getDetail(JSContextRef ctx) {
//  auto detailX = GetInstance<TapEvent>()->DetailX();
//  auto detailY = GetInstance<TapEvent>()->DetailY();
//  
//  JSStringRef propX = JSStringCreateWithUTF8CString("x");
//  JSStringRef propY = JSStringCreateWithUTF8CString("y");
//  
//  JSObjectRef detail = JSObjectMake(ctx, NULL, NULL);
//    
//  JSValueRef x = JSValueMakeNumber(ctx, detailX);
//  JSValueRef y = JSValueMakeNumber(ctx, detailY);
//    
//  JSObjectSetProperty(ctx, detail, propX, x, kJSPropertyAttributeNone, NULL);
//  JSObjectSetProperty(ctx, detail, propY, y, kJSPropertyAttributeNone, NULL);
//  
//  JSStringRelease(propX);
//  JSStringRelease(propY);
//  
//  return detail;
//}


}  // namespace native_dom
}  // namespace msc

#endif
