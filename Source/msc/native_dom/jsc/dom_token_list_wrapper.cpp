//
//  dom_token_list_wrapper.cpp
//  MSCRenderer
//
//  Created by <PERSON> on 2025/6/2.
//

#include "dom_token_list_wrapper.h"

#if USE_JSC

namespace msc {

namespace native_dom {

#define JSC_NATIVE_DOM_FUNCTION_DEF(V)                                         \
  V(DOMTokenListWrapper, DOMTokenList, "item", GetItem)                        \
  V(DOMTokenListWrapper, DOMTokenList, "contains", Contains)                   \
  V(DOMTokenListWrapper, DOMTokenList, "add", Add)                             \
  V(DOMTokenListWrapper, DOMTokenList, "remove", Remove)                       \
  V(DOMTokenListWrapper, DOMTokenList, "replace", Replace)                     \
  V(DOMTokenListWrapper, DOMTokenList, "supports", Supports)                   \
  V(DOMTokenListWrapper, DOMTokenList, "toggle", Toggle)                       \
  V(DOMTokenListWrapper, DOMTokenList, "toString", ToString)                   \
  V(DOMTokenListWrapper, DOMTokenList, "forEach", ForEach)                     \
  V(DOMTokenListWrapper, DOMTokenList, "entries", Entries)                     \
  V(DOMTokenListWrapper, DOMTokenList, "values", Values)                       \
  V(DOMTokenListWrapper, DOMTokenList, "keys", Keys)

#define JSC_NATIVE_DOM_PROP_GETTER_DEF(V)                                      \
  V(DOMTokenListWrapper, DOMTokenList, "length", Length)                       \
  V(DOMTokenListWrapper, DOMTokenList, "value", Value)

#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V)                                 \
  JSC_NATIVE_DOM_FUNCTION_DEF(V)                                               \
  JSC_STATIC_FUNCTION_END

#define JSC_NATIVE_DOM_PROP_INITIALIZER(V)                                     \
  JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V)

#define JSC_NATIVE_DOM_PROP_READ_ONLY_INITIALIZER(V)                           \
  JSC_NATIVE_DOM_PROP_GETTER_DEF(V)

JSC_NATIVE_DOM_FUNCTION_DEF(JSC_OBJ_FUNCTION_IMPL);
JSC_NATIVE_DOM_PROP_GETTER_DEF(JSC_BINDING_PROP_GET);

JSClassRef DOMTokenListWrapper::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
      JSC_NATIVE_DOM_FUNCTION_INITIALIZER(JSC_STATIC_FUNCTION)};

  static JSStaticValue static_values[] = {
      JSC_NATIVE_DOM_PROP_READ_ONLY_INITIALIZER(JSC_STATIC_VALUE_READ_ONLY)
          JSC_STATIC_VALUE_END};
  return registerJSClass("DOMTokenList", static_functions, static_values,
                         parent_class);
}

JSValueRef DOMTokenListWrapper::CreateJSObject(JSContextRef context) {
  JSObjectRef ret = tryCreateJSObject(context, "DOMTokenList", this);
  return ret;
}

JSValueRef DOMTokenListWrapper::getLength(JSContextRef context,
                                          JSValueRef *exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  auto length = GetInstance<DOMTokenList>()->GetLength();
  return JSValueMakeNumber(context, length);
}

JSValueRef DOMTokenListWrapper::getValue(JSContextRef context,
                                         JSValueRef *exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  MSCString value = GetInstance<DOMTokenList>()->GetValue();
  auto jsString = JSStringCreateWithUTF8CString(value.c_str());
  return JSValueMakeString(context, jsString);
}

JSValueRef DOMTokenListWrapper::GetItem(JSContextRef context,
                                        JSObjectRef thisObject, size_t argc,
                                        const JSValueRef *argv,
                                        JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'getItem' on 'DOMTokenList':1 argument required");
  JSC_API_CHECK_NUMBER_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'getItem' on 'DOMTokenList':1st argument must be "
      "number");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));

  auto value = JSValueToNumber(context, argv[0], exception);
  if (exception != nullptr && *exception != nullptr) {
    return JSValueMakeUndefined(context);
  }
  auto index = static_cast<int>(value);

  auto item = GetInstance<DOMTokenList>()->GetItem(index);
  auto jsString = JSStringCreateWithUTF8CString(item.c_str());
  return JSValueMakeString(context, jsString);
}

JSValueRef DOMTokenListWrapper::Contains(JSContextRef context,
                                         JSObjectRef thisObject, size_t argc,
                                         const JSValueRef *argv,
                                         JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'contains' on 'DOMTokenList':1 argument required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'contains' on 'DOMTokenList':1st argument must be "
      "string");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));

  auto tokenRef = JSValueToStringCopy(context, argv[0], nullptr);
  auto token = JSStringToMSCString(tokenRef);
  JSStringRelease(tokenRef);

  return JSValueMakeBoolean(context,
                            GetInstance<DOMTokenList>()->Contains(token));
}

JSValueRef DOMTokenListWrapper::Add(JSContextRef context,
                                    JSObjectRef thisObject, size_t argc,
                                    const JSValueRef *argv,
                                    JSValueRef *exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeUndefined(context));
  
  std::vector<std::string> tokens;
  tokens.reserve(argc);

  for (int i = 0; i < argc; i++) {
    JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
        context, exception, argv[i],
        "Failed to execute 'add' on 'DOMTokenList':argument must be string");
    auto tokenRef = JSValueToStringCopy(context, argv[i], nullptr);
    auto token = JSStringToMSCString(tokenRef);
    JSStringRelease(tokenRef);

    tokens.push_back(token);
  }

  GetInstance<DOMTokenList>()->Add(tokens);

  return JSValueMakeUndefined(context);
}

JSValueRef DOMTokenListWrapper::Remove(JSContextRef context,
                                       JSObjectRef thisObject, size_t argc,
                                       const JSValueRef *argv,
                                       JSValueRef *exception) {
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeUndefined(context));
  
  std::vector<std::string> tokens;
  tokens.reserve(argc);

  for (int i = 0; i < argc; i++) {
    JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
        context, exception, argv[i],
        "Failed to execute 'remove' on 'DOMTokenList':argument must be string");
    auto tokenRef = JSValueToStringCopy(context, argv[i], nullptr);
    auto token = JSStringToMSCString(tokenRef);
    JSStringRelease(tokenRef);

    tokens.push_back(token);
  }

  GetInstance<DOMTokenList>()->Remove(tokens);

  return JSValueMakeUndefined(context);
}

JSValueRef DOMTokenListWrapper::Replace(JSContextRef context,
                                        JSObjectRef thisObject, size_t argc,
                                        const JSValueRef *argv,
                                        JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 2,
      "Failed to execute 'replace' on 'DOMTokenList':2 arguments required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'replace' on 'DOMTokenList':1st argument must be "
      "string");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[1],
      "Failed to execute 'replace' on 'DOMTokenList':2nd argument must be "
      "string");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeUndefined(context));

  auto old_token_ref = JSValueToStringCopy(context, argv[0], nullptr);
  auto old_token = JSStringToMSCString(old_token_ref);
  JSStringRelease(old_token_ref);

  auto new_token_ref = JSValueToStringCopy(context, argv[1], nullptr);
  auto new_token = JSStringToMSCString(new_token_ref);
  JSStringRelease(new_token_ref);

  GetInstance<DOMTokenList>()->Replace(old_token, new_token);

  return JSValueMakeUndefined(context);
}

JSValueRef DOMTokenListWrapper::Supports(JSContextRef context,
                                         JSObjectRef thisObject, size_t argc,
                                         const JSValueRef *argv,
                                         JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'supports' on 'DOMTokenList':1 argument required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'supports' on 'DOMTokenList':1st argument must be "
      "string");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));

  auto tokenRef = JSValueToStringCopy(context, argv[0], nullptr);
  auto token = JSStringToMSCString(tokenRef);
  JSStringRelease(tokenRef);

  return JSValueMakeBoolean(context,
                            GetInstance<DOMTokenList>()->Supports(token));
}

JSValueRef DOMTokenListWrapper::Toggle(JSContextRef context,
                                       JSObjectRef thisObject, size_t argc,
                                       const JSValueRef *argv,
                                       JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'toggle' on 'DOMTokenList':1 argument required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'toggle' on 'DOMTokenList':1st argument must be "
      "string");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));

  bool force{false}, has_force{false};
  auto tokenRef = JSValueToStringCopy(context, argv[0], nullptr);
  auto token = JSStringToMSCString(tokenRef);
  JSStringRelease(tokenRef);

  if (argc == 2) {
    has_force = true;
    force = JSValueToBoolean(context, argv[1]);
  }

  return JSValueMakeBoolean(
      context, GetInstance<DOMTokenList>()->Toggle(token, force, has_force));
}

JSValueRef DOMTokenListWrapper::ToString(JSContextRef context,
                                         JSObjectRef thisObject, size_t argc,
                                         const JSValueRef *argv,
                                         JSValueRef *exception) {
  return getValue(context, exception);
}

JSValueRef DOMTokenListWrapper::ForEach(JSContextRef context,
                                        JSObjectRef thisObject, size_t argc,
                                        const JSValueRef *argv,
                                        JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'forEach' on 'DOMTokenList':1 argument required");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'forEach' on 'DOMTokenList':1st argument must be "
      "callable object");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeUndefined(context));

  JSObjectRef callback = JSValueToObject(context, argv[0], nullptr);
  JSObjectRef this_arg = nullptr;
  if (argc == 2) {
    this_arg = JSValueToObject(context, argv[1], nullptr);
  }

  GetInstance<DOMTokenList>()->ForEach([context, callback, this_arg,
                                        thisObject](const std::string &token,
                                                    int index) {
    auto jsString = JSStringCreateWithUTF8CString(token.c_str());
    JSValueRef arguments[] = {JSValueMakeString(context, jsString),
                              JSValueMakeNumber(context, index), thisObject};
    JSObjectCallAsFunction(context, callback, this_arg, 3, arguments, nullptr);
  });

  return JSValueMakeUndefined(context);
}

JSValueRef DOMTokenListWrapper::Entries(JSContextRef context,
                                        JSObjectRef thisObject, size_t argc,
                                        const JSValueRef *argv,
                                        JSValueRef *exception) {
  return GetIterator<DOMTokenListEntryIteratorWrapper>(context);
}

JSValueRef DOMTokenListWrapper::Keys(JSContextRef context,
                                     JSObjectRef thisObject, size_t argc,
                                     const JSValueRef *argv,
                                     JSValueRef *exception) {
  return GetIterator<DOMTokenListKeyIteratorWrapper>(context);
}

JSValueRef DOMTokenListWrapper::Values(JSContextRef context,
                                       JSObjectRef thisObject, size_t argc,
                                       const JSValueRef *argv,
                                       JSValueRef *exception) {
  return GetIterator<DOMTokenListValueIteratorWrapper>(context);
}

JSValueRef
DOMTokenListEntryIteratorWrapper::GetNextValue(JSContextRef context, size_t key,
                                               const std::string &value) {
  JSStringRef jsValue = JSStringCreateWithUTF8CString(value.c_str());

  JSValueRef entries[] = {JSValueMakeNumber(context, key),
                          JSValueMakeString(context, jsValue)};
  JSStringRelease(jsValue);

  return JSObjectMakeArray(context, 2, entries, nullptr);
}

JSValueRef
DOMTokenListKeyIteratorWrapper::GetNextValue(JSContextRef context, size_t key,
                                             const std::string &value) {
  return JSValueMakeNumber(context, key);
}

JSValueRef
DOMTokenListValueIteratorWrapper::GetNextValue(JSContextRef context, size_t key,
                                               const std::string &value) {
  JSStringRef jsValue = JSStringCreateWithUTF8CString(value.c_str());
  auto res = JSValueMakeString(context, jsValue);
  JSStringRelease(jsValue);
  return res;
}

// 设置 obj[Symbol.{symbolName}] = value
void SetSymbolProperty(JSContextRef ctx, JSObjectRef obj,
                       const char *symbolName, JSValueRef value) {
  // 构造 JS 脚本：function(obj, value) { obj[Symbol.<symbolName>] = value; }
  std::string script = "(function(obj, val) { obj[Symbol." +
                       std::string(symbolName) + "] = val; })";

  // 编译并执行这段 JS 脚本，得到函数对象
  JSStringRef scriptStr = JSStringCreateWithUTF8CString(script.c_str());
  JSValueRef funcVal =
      JSEvaluateScript(ctx, scriptStr, nullptr, nullptr, 1, nullptr);
  JSStringRelease(scriptStr);

  if (!funcVal || !JSValueIsObject(ctx, funcVal)) {
    // 可能 JS 脚本语法错误
    fprintf(stderr, "Failed to create symbol setter function.\n");
    return;
  }

  JSObjectRef setterFunc = (JSObjectRef)funcVal;

  // 准备参数并调用 function(obj, val)
  JSValueRef args[] = {obj, value};
  JSObjectCallAsFunction(ctx, setterFunc, nullptr, 2, args, nullptr);
}

} // namespace native_dom
} // namespace msc

#endif // USE_JSC
