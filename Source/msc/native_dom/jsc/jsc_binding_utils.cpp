//
//  JSCBindingUtils.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/13.
//
#include "jsc_binding_utils.h"

#if USE_JSC

namespace msc {
namespace native_dom {

MSCString JSStringToMSCString(JSStringRef str) {
  // Small string optimization: Avoid one heap allocation for strings that fit
  // in stackBuffer.size() bytes of UTF-8 (including the null terminator).
  std::array<char, 20> stackBuffer;
  std::unique_ptr<char[]> heapBuffer;
  char *buffer;
  // NOTE: By definition, maxBytes >= 1 since the null terminator is included.
  size_t maxBytes = JSStringGetMaximumUTF8CStringSize(str);
  if (maxBytes <= stackBuffer.size()) {
    buffer = stackBuffer.data();
  } else {
    heapBuffer = std::make_unique<char[]>(maxBytes);
    buffer = heapBuffer.get();
  }
  size_t actualBytes = JSStringGetUTF8CString(str, buffer, maxBytes);
  if (!actualBytes) {
    // Happens if maxBytes == 0 (never the case here) or if str contains
    // invalid UTF-16 data, since J<PERSON>tringGetUTF8CString attempts a strict
    // conversion.
    // When converting an invalid string, JSStringGetUTF8CString writes a null
    // terminator before returning. So we can reliably treat our buffer as a C
    // string and return the truncated data to our caller. This is slightly
    // slower than if we knew the length (like below) but better than crashing.
    // TODO(T62295565): Perform a non-strict, best effort conversion of the
    // full string instead, like we did before the JSI migration.
    return std::string(buffer);
  }
  return std::string(buffer, actualBytes - 1);
}

blink::mt::PropValue JSValueToPropValue(JSContextRef ctx, const JSValueRef &jsValue) {
  if (JSValueIsString(ctx, jsValue)) {
    JSStringRef jsStr = JSValueToStringCopy(ctx, jsValue, nullptr);
    std::string str = JSStringToMSCString(jsStr);
    JSStringRelease(jsStr);
    return blink::mt::PropValue(str);
  }
  
  if (JSValueIsNull(ctx, jsValue)) {
    return blink::mt::PropValue(blink::mt::PropValueType::Null{});
  }
  
  if (JSValueIsUndefined(ctx, jsValue)) {
    return blink::mt::PropValue(blink::mt::PropValueType::Null{}); // 将 undefined 视为 null
  }
  
  if (JSValueIsBoolean(ctx, jsValue)) {
    bool value = JSValueToBoolean(ctx, jsValue);
    return blink::mt::PropValue(static_cast<bool>(value));
  }
  
  if (JSValueIsNumber(ctx, jsValue)) {
    double value = JSValueToNumber(ctx, jsValue, nullptr);
    return blink::mt::PropValue(value);
  }
  
  if (JSValueIsArray(ctx, jsValue)) {
    JSObjectRef arrayObj = JSValueToObject(ctx, jsValue, nullptr);
    JSValueRef lengthVal = JSObjectGetProperty(ctx, arrayObj, JSStringCreateWithUTF8CString("length"), nullptr);
    uint32_t length = static_cast<uint32_t>(JSValueToNumber(ctx, lengthVal, nullptr));
    
    blink::mt::PropValueType::Array arr;
    arr.reserve(length);
    for (uint32_t i = 0; i < length; ++i) {
      JSValueRef element = JSObjectGetPropertyAtIndex(ctx, arrayObj, i, nullptr);
      arr.push_back(JSValueToPropValue(ctx, element));
    }
    return blink::mt::PropValue(std::move(arr));
  }
  
  if (JSValueIsObject(ctx, jsValue)) {
    JSObjectRef obj = JSValueToObject(ctx, jsValue, nullptr);
    
    //如果是个特殊对象（如NativeDOM的Element），则直接取其string，否则可能会有无穷递归
    if (JSObjectGetPrivate(obj)) {
      JSStringRef jsStr = JSValueToStringCopy(ctx, jsValue, nullptr);
      std::string str = JSStringToMSCString(jsStr);
      JSStringRelease(jsStr);
      return blink::mt::PropValue(str);
    }
    
    JSPropertyNameArrayRef names = JSObjectCopyPropertyNames(ctx, obj);
    size_t count = JSPropertyNameArrayGetCount(names);
    
    blink::mt::PropValueType::Dictionary dict;
    for (size_t i = 0; i < count; ++i) {
      JSStringRef jsKey = JSPropertyNameArrayGetNameAtIndex(names, i);
      std::string key = JSStringToMSCString(jsKey);
      JSValueRef jsVal = JSObjectGetProperty(ctx, obj, jsKey, nullptr);
      dict.emplace(std::move(key), JSValueToPropValue(ctx, jsVal));
    }
    JSPropertyNameArrayRelease(names);
    return blink::mt::PropValue(std::move(dict));
  }
  
  // 未知类型返回 Null
  return blink::mt::PropValue(blink::mt::PropValueType::Null{});
}

JSValueRef PropValueToJS(JSContextRef ctx, const blink::mt::PropValue& prop) {
  if (prop.isNull()) {
    return JSValueMakeNull(ctx);
  }
  
  return std::visit([ctx](auto&& arg) -> JSValueRef {
    using T = std::decay_t<decltype(arg)>;
    
    // Null类型处理
    if constexpr (std::is_same_v<T, blink::mt::PropValueType::Null>) {
      return JSValueMakeNull(ctx);
    }
    // 布尔类型处理
    else if constexpr (std::is_same_v<T, blink::mt::PropValueType::Boolean>) {
      return JSValueMakeBoolean(ctx, arg);
    }
    // 数值类型处理
    else if constexpr (std::is_same_v<T, blink::mt::PropValueType::Number>) {
      return JSValueMakeNumber(ctx, arg);
    }
    // 字符串类型处理
    else if constexpr (std::is_same_v<T, blink::mt::PropValueType::String>) {
      JSStringRef jsStr = JSStringCreateWithUTF8CString(arg.c_str());
      JSValueRef ret = JSValueMakeString(ctx, jsStr);
      JSStringRelease(jsStr);
      return ret;
    }
    // 数组类型递归处理
    else if constexpr (std::is_same_v<T, blink::mt::PropValueType::Array>) {
      JSObjectRef array = JSObjectMakeArray(ctx, 0, nullptr, nullptr);
      
      for (int i = 0; i < arg.size(); ++i) {
        JSValueRef element = PropValueToJS(ctx, arg[i]);
        JSObjectSetPropertyAtIndex(ctx, array, i, element, nullptr);
      }
      return array;
    }
    // 字典类型递归处理
    else if constexpr (std::is_same_v<T,
                                      blink::mt::PropValueType::Dictionary>) {
      JSObjectRef obj = JSObjectMake(ctx, nullptr, nullptr);
      
      for (const auto& [key, value] : arg) {
        JSStringRef propName = JSStringCreateWithUTF8CString(key.c_str());
        JSValueRef jsValue = PropValueToJS(ctx, value);
        
        JSObjectSetProperty(ctx, obj, propName, jsValue,
                            kJSPropertyAttributeNone, nullptr);
        JSStringRelease(propName);
      }
      return obj;
    }
    // 未知类型保护
    else {
      return JSValueMakeUndefined(ctx);
    }
  }, static_cast<const blink::mt::PropValueBase&>(prop));
}

JSObjectRef JSObjectFromProps(JSContextRef ctx, const blink::mt::Props &props, const std::string &exclude_key) {
  JSObjectRef obj = JSObjectMake(ctx, nullptr, nullptr);
  props.forEach([&](const std::string &key, const blink::mt::PropValue &prop){
    if (key == exclude_key) {
      return;
    }
    JSStringRef key_js = JSStringCreateWithUTF8CString(key.c_str());
    JSObjectSetProperty(ctx, obj, key_js, PropValueToJS(ctx, prop), kJSPropertyAttributeNone, nullptr);
    JSStringRelease(key_js);
  });
  return obj;
}

void ThrowError(JSContextRef ctx, JSValueRef* exception, const char* message, const char* error_type) {
  if (exception == nullptr) {
    return;
  }
  // 1. 创建错误消息字符串
  JSStringRef errorMsg =
      JSStringCreateWithUTF8CString(message ? message : "Invalid type");

  // 2. 获取全局对象
  JSObjectRef globalObject = JSContextGetGlobalObject(ctx);

  // 3. 获取 TypeError 构造函数
  JSStringRef ctorName = JSStringCreateWithUTF8CString(error_type);
  JSValueRef ctorVal =
      JSObjectGetProperty(ctx, globalObject, ctorName, nullptr);
  JSStringRelease(ctorName);

  // 检查是否成功获取构造函数
  if (!JSValueIsObject(ctx, ctorVal)) {
    // 回退方案：创建通用 Error 对象
    JSValueRef args[] = {JSValueMakeString(ctx, errorMsg)};
    *exception = JSObjectMakeError(ctx, 1, args, nullptr);
    JSStringRelease(errorMsg);
    return;
  }

  // 4. 调用构造函数创建 TypeError 对象
  JSValueRef args[] = {JSValueMakeString(ctx, errorMsg)};
  *exception =
      JSObjectCallAsConstructor(ctx, (JSObjectRef)ctorVal, 1, args, nullptr);
  JSStringRelease(errorMsg);
}

}
}

#endif
