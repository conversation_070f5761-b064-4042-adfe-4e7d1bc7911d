//
//  touch_event_wrapper_hpp.hpp
//  MSCRenderer
//
//  Created by ji<PERSON><PERSON> zhang on 2025/5/30.
//

#ifndef touch_event_wrapper_hpp
#define touch_event_wrapper_hpp

#include "native_dom_object_wrapper.h"
#include "../event.h"
#include "event_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

class TouchEventWrapper : public EventWrapper {
public:
  TouchEventWrapper(std::shared_ptr<TouchEvent> event) : EventWrapper(event) {}
  
  static JSClassRef SetupJSClass(JSClassRef parent_class);
  JSValueRef CreateJSObject(JSContextRef context);
  
  DECLARE_JSC_PROP_GETTER(Touches);
  DECLARE_JSC_PROP_GETTER(ChangedTouches);
};

//class TapEventWrapper : public TouchEventWrapper {
//public:
//  TapEventWrapper(std::shared_ptr<TapEvent> event) : TouchEventWrapper(event) {}
//  
//  static JSClassRef SetupJSClass(JSClassRef parent_class);
//  JSValueRef CreateJSObject(JSContextRef context);
//};

}
}

#endif

#endif /* event_wrapper_hpp */
