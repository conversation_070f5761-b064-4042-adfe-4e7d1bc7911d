//
//  node_wrapper.h
//  MSC
//
//  Created by ji<PERSON><PERSON> zhang on 2025/2/13.
//

#ifndef NODE_WRAPPER_H
#define NODE_WRAPPER_H

#include "native_dom_object_wrapper.h"
#include "../node.h"

#if USE_JSC

namespace msc {
namespace native_dom {

class NodeWrapper : public NativeDOMObjectWrapper {
public:
  NodeWrapper(std::shared_ptr<Node> node) : NativeDOMObjectWrapper(node) {}
  
  static JSClassRef SetupJSClass(JSClassRef parent_class);
  JSValueRef CreateJSObject(JSContextRef context);
  
  DECLARE_JSC_FUNCTION(AppendChild);
  DECLARE_JSC_FUNCTION(InsertBefore);
  DECLARE_JSC_FUNCTION(Remove);
  DECLARE_JSC_FUNCTION(RemoveChild);
  DECLARE_JSC_FUNCTION(AddEventListener);
  DECLARE_JSC_FUNCTION(RemoveEventListener);
  DECLARE_JSC_FUNCTION(HasChildNodes);

  DECLARE_JSC_PROP_GETTER(FirstChild);
  DECLARE_JSC_PROP_GETTER(LastChild);
  DECLARE_JSC_PROP_GETTER(PreviousSibling);
  DECLARE_JSC_PROP_GETTER(NextSibling);
  DECLARE_JSC_PROP_GETTER(ParentNode);
  DECLARE_JSC_PROP_GETTER(ParentElement);
  DECLARE_JSC_PROP_GETTER_AND_SETTER(TextContent);
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* NODE_WRAPPER_H */
