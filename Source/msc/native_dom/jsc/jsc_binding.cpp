//
//  JSCBinding.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/18.
//


#include "jsc_binding.h"
//JSC binding
#include "dom_implementation_wrapper.h"
#include "node_wrapper.h"
#include "element_wrapper.h"
#include "document_wrapper.h"
#include "css_style_declaration_wrapper.h"
#include "intersection_observer_wrapper.h"
#include "dom_token_list_wrapper.h"
#include "event_wrapper.h"
#include "touch_event_wrapper.h"

#include "../bridge/message_proxy.h"
#include "../bridge/document_registry.h"
 
namespace msc {
namespace native_dom {

JSObjectRef JSCBinding::InjectAPIs(JSGlobalContextRef jsContext,
                                   JSObjectRef globalObject,
                                   JSStringRef nativePropertyName,
                                   const std::shared_ptr<DocumentRegistry> &document_registry) {
#if USE_JSC
  auto domImplementationClass = DOMImplementationWrapper::SetupJSClass(nullptr);
  auto cssStyleDeclarationClass = CSSStyleDeclarationWrapper::SetupJSClass(nullptr);
  auto intersectionObserverClass = IntersectionObserverWrapper::SetupJSClass(nullptr);
  auto nodeClass = NodeWrapper::SetupJSClass(nullptr);
  auto elementClass = ElementWrapper::SetupJSClass(nodeClass);
  auto documentClass = DocumentWrapper::SetupJSClass(elementClass);
  auto text_node_class = TextNodeWrapper::SetupJSClass(elementClass);

  auto dom_token_list_class = DOMTokenListWrapper::SetupJSClass(nullptr);
  auto dom_token_list_entry_iterator_class =
      DOMTokenListEntryIteratorWrapper::SetupJSClass(nullptr);
  auto dom_token_list_key_iterator_class =
      DOMTokenListKeyIteratorWrapper::SetupJSClass(nullptr);
  auto dom_token_list_value_iterator_class =
      DOMTokenListValueIteratorWrapper::SetupJSClass(nullptr);

  auto event_class =  EventWrapper::SetupJSClass(nullptr);
  auto touch_event_class =  TouchEventWrapper::SetupJSClass(event_class);
  
  auto nativeObj = JSObjectMake(jsContext, NULL, NULL);
  JSObjectSetProperty(jsContext, globalObject, nativePropertyName, nativeObj, kJSPropertyAttributeNone, NULL);

  auto messageProxy = std::make_shared<MessageProxy>(document_registry);
  auto domImplObj = std::make_shared<DOMImplementation>(messageProxy);
  auto domImplWrapper = new DOMImplementationWrapper(domImplObj);
  auto domImpl = domImplWrapper->CreateJSObject(jsContext);
  if (not domImpl) {
    document_registry->GetMetrics().ReportError(msc::NativeDOMMetrics::Tags{
      msc::NativeDOMMetrics::ErrCode::JSInject,
      msc::NativeDOMMetrics::API::kSetupJSContext,
      msc::NativeDOMMetrics::Message::kCreateJSObject
    });
  }
  
  JSStringRef propertyName = JSStringCreateWithUTF8CString("implementation");
  JSObjectSetProperty(jsContext, nativeObj, propertyName, domImpl, kJSPropertyAttributeNone, NULL);
  JSStringRelease(propertyName);

  { // native.IntsersectionObserver
    JSObjectRef ioConstructor = JSObjectMakeConstructor(
      jsContext,
      intersectionObserverClass,
      IntersectionObserverWrapper::CreateIntersectionObserver);

    JSStringRef ioName = JSStringCreateWithUTF8CString("IntersectionObserver");
    JSObjectSetProperty(jsContext, nativeObj, ioName, JSValueToObject(jsContext, ioConstructor, nullptr), kJSPropertyAttributeNone, NULL);
    JSStringRelease(ioName);
  }
  
  return nativeObj;
#else
  return nullptr;
#endif
}

bool JSCBinding::SetupJSContext(JSGlobalContextRef js_context,
                                const std::shared_ptr<DocumentRegistry> &document_registry,
                                const char *app_id,
                                const char *pure_path) {
#if USE_JSC
  auto global_object = JSContextGetGlobalObject(js_context);
  
  JSStringRef native_property_name = JSStringCreateWithUTF8CString("native");
  JSValueRef native_value = JSObjectGetProperty(js_context, global_object, native_property_name, nullptr);
  JSObjectRef native_obj = nullptr;
  if (JSValueIsUndefined(js_context, native_value)) {
    native_obj = InjectAPIs(js_context, global_object, native_property_name, document_registry);
  } else {
    native_obj = JSValueToObject(js_context, native_value, nullptr);
  }
  JSStringRelease(native_property_name);
  
  //给前端注入NativeDOM开关
  JSStringRef enable_native_dom_key = JSStringCreateWithUTF8CString("enableNativeDOM");
  JSStringRef app_id_str = JSStringCreateWithUTF8CString(app_id);
  JSStringRef page_path_str = JSStringCreateWithUTF8CString(pure_path);
  
  JSObjectRef config_obj = nullptr;
  JSObjectRef app_obj = nullptr;
  
  //global.native.enableNativeDOM
  JSValueRef config_value = JSObjectGetProperty(js_context, native_obj, enable_native_dom_key, nullptr);
  if (!JSValueIsUndefined(js_context, config_value)) {
    config_obj = JSValueToObject(js_context, config_value, nullptr);
    //global.native.enableNativeDOM[app_id]
    JSValueRef app_value = JSObjectGetProperty(js_context, config_obj, app_id_str, nullptr);
    if (!JSValueIsUndefined(js_context, app_value)) {
      app_obj = JSValueToObject(js_context, app_value, nullptr);
    }
  } else {
    config_obj = JSObjectMake(js_context, nullptr, nullptr);
  }
  
  if (!app_obj) {
    app_obj = JSObjectMake(js_context, nullptr, nullptr);
  }

  //app_obj.pagePath = true
  JSObjectSetProperty(js_context, app_obj, page_path_str, JSValueMakeBoolean(js_context, true), kJSPropertyAttributeNone, nullptr);
  //config_obj.appId = app_obj
  JSObjectSetProperty(js_context, config_obj, app_id_str, app_obj, kJSPropertyAttributeNone, nullptr);
  //global.native.enableNativeDOM = config_obj
  JSObjectSetProperty(js_context, native_obj, enable_native_dom_key, config_obj, kJSPropertyAttributeNone, nullptr);
  
  JSStringRelease(app_id_str);
  JSStringRelease(page_path_str);
  JSStringRelease(enable_native_dom_key);
  
#endif
  
  return true;
}

}
}


