//
//  NodeWrapper.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/13.
//

#include "node_wrapper.h"
#include "../element.h"
#include "event_listener_jsc.h"

#if USE_JSC

namespace msc {
namespace native_dom {

#define JSC_NATIVE_DOM_FUNCTION_DEF(V)                                         \
  V(NodeWrapper, Node, Node::kAppendChild.c_str(), AppendChild)                \
  V(NodeWrapper, Node, Node::kInsertBefore.c_str(), InsertBefore)              \
  V(NodeWrapper, Node, Node::kRemove.c_str(), Remove)                          \
  V(NodeWrapper, Node, Node::kAddEventListener.c_str(), AddEventListener)      \
  V(NodeWrapper, Node, Node::kRemoveEventListener.c_str(),                     \
    RemoveEventListener)                                                       \
  V(NodeWrapper, Node, "hasChildNodes", HasChildNodes)

#define JSC_NATIVE_DOM_PROP_GETTER_DEF(V)                  \
  V(NodeWrapper, Node, "firstChild", FirstChild)           \
  V(NodeWrapper, Node, "lastChild", LastChild)             \
  V(NodeWrapper, Node, "previousSibling", PreviousSibling) \
  V(NodeWrapper, Node, "nextSibling", NextSibling)         \
  V(NodeWrapper, Node, "parentNode", ParentNode)           \
  V(NodeWrapper, Node, "parentElement", ParentElement)

#define JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V) \
  V(NodeWrapper, Node, "textContent", TextContent)

#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V) \
    JSC_NATIVE_DOM_FUNCTION_DEF(V)             \
    JSC_STATIC_FUNCTION_END

#define JSC_NATIVE_DOM_PROP_INITIALIZER(V) JSC_NATIVE_DOM_PROP_GETTER_DEF(V)

#define JSC_NATIVE_DOM_PROP_READ_WRITE_INITIALIZER(V) \
  JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V)

JSC_NATIVE_DOM_FUNCTION_DEF(JSC_OBJ_FUNCTION_IMPL);
JSC_NATIVE_DOM_PROP_GETTER_DEF(JSC_BINDING_PROP_GET)
JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(JSC_BINDING_PROP_SET_AND_GET);

JSClassRef NodeWrapper::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
       JSC_NATIVE_DOM_FUNCTION_INITIALIZER(JSC_STATIC_FUNCTION)
   };

  static JSStaticValue static_values[] = {
      JSC_NATIVE_DOM_PROP_READ_WRITE_INITIALIZER(JSC_STATIC_VALUE_READ_WRITE)
          JSC_NATIVE_DOM_PROP_INITIALIZER(JSC_STATIC_VALUE_READ_ONLY)
              JSC_STATIC_VALUE_END};
  return registerJSClass("Node", static_functions, static_values, parent_class);
}

JSValueRef NodeWrapper::CreateJSObject(JSContextRef context) {
  JSObjectRef ret = tryCreateJSObject(context, "Node", this);
  return ret;
}

JSValueRef NodeWrapper::AppendChild(JSContextRef context,
                                    JSObjectRef thisObject, size_t argc,
                                    const JSValueRef* argv,
                                    JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'appendChild' on 'Node':1 argument required");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'appendChild' on 'Node':1st argument must be object");
  auto childObj = JSValueToObject(context, argv[0], NULL);
  auto child = getJSCInterface(childObj);
  if (child) {
    auto node = GetInstance<Node>();
    auto child_node = child->GetInstance<Node>().get();
    if (node && child_node) {
      node->AppendChild(child_node);
      return argv[0];
    }
  }
  
  return JSValueMakeNull(context);
}

static inline Node *GetNodeFromJSValue(JSContextRef context,
                                       JSValueRef value,
                                       bool to_check) {
  // check if value is a JSObject and not null
  if (to_check and
      (!JSValueIsObject(context, value) or JSValueIsNull(context, value))) {
    return nullptr;
  }
  auto obj = JSValueToObject(context, value, NULL);
  auto wrapper = getJSCInterface(obj);
  if (not wrapper) {
    return nullptr;
  }

  // get the Node instance from the wrapper
  return wrapper->GetInstance<Node>().get();
}

JSValueRef NodeWrapper::InsertBefore(JSContextRef context,
                                     JSObjectRef thisObject, size_t argc,
                                     const JSValueRef* argv,
                                     JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 2,
      "Failed to execute 'insertBefore' on 'Node':2 arguments required");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'insertBefore' on 'Node':1st argument must be object");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
      context, exception, argv[1],
      "Failed to execute 'insertBefore' on 'Node':2nd argument must be object");

  // new_node: 要插入的节点。
  // reference_node: 在其之前插入 new_node 的节点。如果为 null，new_node
  // 将被插入到节点的子节点列表末尾。不是可选参数。你必须显式传递 Node 或 null。
  auto new_node = GetNodeFromJSValue(context, argv[0], false);
  auto reference_node = GetNodeFromJSValue(context, argv[1], true);
  auto node = GetInstance<Node>();
  if (node && new_node) {
    node->InsertBefore(new_node, reference_node);
    return argv[0];
  } else {
    return JSValueMakeNull(context);
  }
}

JSValueRef NodeWrapper::Remove(JSContextRef context, JSObjectRef thisObject,
                               size_t argc, const JSValueRef* argv,
                               JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  GetInstance<Node>()->Remove();
  return JSValueMakeNull(context);
}

JSValueRef NodeWrapper::RemoveChild(JSContextRef context,
                                    JSObjectRef thisObject, size_t argc,
                                    const JSValueRef* argv,
                                    JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'removeChild' on 'Node':1 argument required");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'removeChild' on 'Node':1st argument must be object");
  auto childObj = JSValueToObject(context, argv[0], NULL);
  auto child = getJSCInterface(childObj);
  if (child) {
    auto node = GetInstance<Node>();
    auto child_node = child->GetInstance<Node>().get();
    if (node && child_node) {
      node->RemoveChild(child_node);
      return argv[0];
    }
  }
  
  return JSValueMakeNull(context);
}

JSValueRef NodeWrapper::AddEventListener(JSContextRef context,
                                         JSObjectRef thisObject, size_t argc,
                                         const JSValueRef* argv,
                                         JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 2,
      "Failed to execute 'addEventListener' on 'Node':2 arguments required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'addEventListener' on 'Node':1st argument must be "
      "string");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
      context, exception, argv[1],
      "Failed to execute 'addEventListener' on 'Node':2nd argument must be "
      "object");
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeBoolean(context, false));
  
  auto eventName = JSValueToStringCopy(context, argv[0], NULL);
  auto eventNameStr = JSStringToMSCString(eventName);
  JSStringRelease(eventName);
  
  auto listenerObj = JSValueToObject(context, argv[1], NULL);
  bool capture = false;
  if (argc >= 3 && JSValueIsBoolean(context, argv[2])) {
    capture = JSValueToBoolean(context, argv[2]);
  }
  
  GetInstance<Node>()->AddEventListener(eventNameStr, std::make_unique<JSCEventListener>(capture, listenerObj, context));
  return JSValueMakeBoolean(context, true);
}

JSValueRef NodeWrapper::RemoveEventListener(JSContextRef context,
                                            JSObjectRef thisObject, size_t argc,
                                            const JSValueRef* argv,
                                            JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 2,
      "Failed to execute 'removeEventListener' on 'Node':2 arguments required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'removeEventListener' on 'Node':1st argument must be "
      "string");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
      context, exception, argv[1],
      "Failed to execute 'removeEventListener' on 'Node':2nd argument must be "
      "object");
  
  auto eventName = JSValueToStringCopy(context, argv[0], NULL);
  auto eventNameStr = JSStringToMSCString(eventName);
  JSStringRelease(eventName);
  
  auto listenerObj = JSValueToObject(context, argv[1], NULL);
  bool capture = false;
  if (argc >= 3 && JSValueIsBoolean(context, argv[2])) {
    capture = JSValueToBoolean(context, argv[2]);
  }
  
  auto listener = std::make_unique<JSCEventListener>(capture, listenerObj, context);
  GetInstance<Node>()->RemoveEventListener(eventNameStr, listener.get());
  return JSValueMakeNull(context);
}

JSValueRef NodeWrapper::HasChildNodes(JSContextRef context,
                                      JSObjectRef thisObject, size_t argc,
                                      const JSValueRef* argv,
                                      JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeBoolean(context, false));
  
  auto node = GetInstance<Node>();
  return JSValueMakeBoolean(context,
                            node->firstChild() != nullptr && node->lastChild());
}

JSValueRef NodeWrapper::getFirstChild(JSContextRef ctx, JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto firstChild = GetInstance<Node>()->firstChild();
  if (firstChild) {
     return GET_JSOBJECT_FROM(firstChild);
  }
  return JSValueMakeNull(ctx);
}

JSValueRef NodeWrapper::getLastChild(JSContextRef ctx, JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto lastChild = GetInstance<Node>()->lastChild();
  if (lastChild) {
     return GET_JSOBJECT_FROM(lastChild);
  }
  return JSValueMakeNull(ctx);
}

JSValueRef NodeWrapper::getPreviousSibling(JSContextRef ctx,
                                           JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto previousSibling = GetInstance<Node>()->previousSibling();
  if (previousSibling) {
     return GET_JSOBJECT_FROM(previousSibling);
  }
  return JSValueMakeNull(ctx);
}

JSValueRef NodeWrapper::getNextSibling(JSContextRef ctx,
                                       JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto nextSibling = GetInstance<Node>()->nextSibling();
  if (nextSibling) {
     return GET_JSOBJECT_FROM(nextSibling);
  }
  return JSValueMakeNull(ctx);
}

JSValueRef NodeWrapper::getParentNode(JSContextRef ctx, JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto parent = GetInstance<Node>()->parentNode();
  if (parent) {
     return GET_JSOBJECT_FROM(parent);
  }
  return JSValueMakeNull(ctx);
}

JSValueRef NodeWrapper::getParentElement(JSContextRef ctx,
                                         JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto parent_element = GetInstance<Node>()->parentElement();
  if (parent_element != nullptr) {
    return GET_JSOBJECT_FROM(parent_element);
  }
  return JSValueMakeNull(ctx);
}

JSValueRef NodeWrapper::getTextContent(JSContextRef ctx,
                                       JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto text_content = GetInstance<Node>()->TextContent();
  auto js_text_content = JSStringCreateWithUTF8CString(text_content.c_str());
  return JSValueMakeString(ctx, js_text_content);
}

bool NodeWrapper::setTextContent(JSContextRef context, JSValueRef value,
                                 JSValueRef* exception) {
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR_RETURN_FALSE(
      context, exception, value,
      "Failed to execute 'setTextContent' on 'Node':value must be string");
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, false);
  
  auto stringValue = JSValueToStringCopy(context, value, nullptr);
  GetInstance<Node>()->SetTextContent(JSStringToMSCString(stringValue));
  JSStringRelease(stringValue);
  return true;
}

}  // namespace native_dom
}  // namespace msc

#endif
