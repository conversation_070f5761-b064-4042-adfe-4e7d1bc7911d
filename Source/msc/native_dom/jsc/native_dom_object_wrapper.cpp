//
//  NativeDOMObjectWrapper.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/13.
//

#include "native_dom_object_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

///////////////////////

static std::unordered_map<std::string, JSClassRef> s_jsClassMap;

void saveJSClass(const char *class_name, JSClassRef js_class) {
  s_jsClassMap[class_name] = js_class;
}

JSClassRef queryJSClass(const char *className) {
  auto iter = s_jsClassMap.find(className);
  if (iter != s_jsClassMap.end()) {
    return iter->second;
  }
  return NULL;
}

void BindingFinalize(JSObjectRef object)
{
  MSC_RENDERER_LOG_DEBUG("BindingFinalize jsobject: %p", object);
  auto wrapper = static_cast<NativeDOMObjectWrapper*>(JSObjectGet<PERSON>rivate(object));
  if (wrapper == nullptr) {
    //本地测试看同一个JSObjectRef可能会多次回调BIndingFinalize，这里不上报异常
    MSC_RENDERER_LOG_DEBUG("JSObjectRef finalize时wrapper为空");
    return;
  }
  JSObjectSetPrivate(object, nullptr);
  std::shared_ptr<ScriptWrappable> script_wrappable = wrapper->GetInstance<ScriptWrappable>();
  if (script_wrappable) {
    script_wrappable->ClearJSWrapper(false);
  }
  
  MSC_RENDERER_LOG_DEBUG("[native_dom] finalizer deleting wrapper for script_wrappable: %p", script_wrappable.get());
  delete wrapper;
  //Delete wrapper后，wrapper中的ScriptWrappable会销毁
}

JSClassRef registerJSClass(const char* class_name, JSStaticFunction static_functions[], JSStaticValue static_values[], JSClassRef parent_class)
{
  auto cls = queryJSClass(class_name);
  if (cls) {
    return cls;
  }
  
  JSClassDefinition class_definition = kJSClassDefinitionEmpty;
  class_definition.className = class_name;
  class_definition.parentClass = parent_class;
  class_definition.initialize = BindingInitialize;
  class_definition.finalize = BindingFinalize;
  if (static_functions != NULL)
    class_definition.staticFunctions = static_functions;
  if (static_values != NULL)
    class_definition.staticValues = static_values;
  JSClassRef js_class = JSClassCreate(&class_definition);
  //    [runtime registerJSClass:class_name js_class:js_class];
  saveJSClass(class_name, js_class);
  return js_class;
}

}  // namespace native_dom
}  // namespace msc

#endif
