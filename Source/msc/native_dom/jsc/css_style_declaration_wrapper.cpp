//
//  css_style_declaration_wrapper.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/3.
//

#include "css_style_declaration_wrapper.h"

#include "../css_style_declaration.h"

#if USE_JSC

namespace msc {
namespace native_dom {

#define JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V) \
    V(CSSStyleDeclarationWrapper, CSSStyleDeclaration, CSSStyleDeclaration::kCSSText.c_str(), CSSText)

#define JSC_NATIVE_DOM_PROP_INITIALIZER(V)   \
    JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V) \
    JSC_STATIC_VALUE_END

JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(JSC_BINDING_PROP_SET_AND_GET);

JSClassRef CSSStyleDeclarationWrapper::SetupJSClass(JSClassRef parent_class) {
    static JSStaticValue static_values[] = {
        JSC_NATIVE_DOM_PROP_INITIALIZER(JSC_STATIC_VALUE_READ_WRITE)};
    return registerJSClass("CSSStyleDeclaration", NULL, static_values, parent_class);
}

JSValueRef CSSStyleDeclarationWrapper::CreateJSObject(JSContextRef context) {
    JSObjectRef ret = tryCreateJSObject(context, "CSSStyleDeclaration", this);
    return ret;
}

JSValueRef CSSStyleDeclarationWrapper::getCSSText(JSContextRef ctx,
                                                  JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(ctx));
  
  auto css_text = GetInstance<CSSStyleDeclaration>()->cssText();
  auto js_string = JSStringCreateWithUTF8CString(css_text.c_str());
  return JSValueMakeString(ctx, js_string);
}

bool CSSStyleDeclarationWrapper::setCSSText(JSContextRef context,
                                            JSValueRef value,
                                            JSValueRef* exception) {
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR_RETURN_FALSE(
      context, exception, value,
      "Failed to execute 'setCSSText' on 'CSSStyleDeclaration':value must be "
      "string");
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, false);
  
  auto string_value = JSValueToStringCopy(context, value, nullptr);
  if (!string_value) {
    return false;
  }
    GetInstance<CSSStyleDeclaration>()->setCSSText(JSStringToMSCString(string_value));
    JSStringRelease(string_value);
    return true;
}

}  // namespace native_dom
}  // namespace msc

#endif
