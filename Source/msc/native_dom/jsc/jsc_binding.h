//
//  JSCBinding.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/18.
//

#ifndef JSCBinding_hpp
#define JSCBinding_hpp

#include <JavaScriptCore/JavaScriptCore.h>
#include <MSCRenderer/document_registry.h>

namespace msc {
namespace native_dom {

class DocumentRegistry;

class JSCBinding {
public:
  JSCBinding() = default;
  ~JSCBinding() = default;
  
  static bool SetupJSContext(JSGlobalContextRef jsContext,
                             const std::shared_ptr<DocumentRegistry> &documentRegistry,
                             const char *app_id,
                             const char *pure_path);
  
private:
  static JSObjectRef InjectAPIs(JSGlobalContextRef jsContext,
                                JSObjectRef globalObject,
                                JSStringRef nativePropertyName,
                                const std::shared_ptr<DocumentRegistry> &document_registry);
};

}
}

#endif /* JSCBinding_hpp */
