//
//  jsc_binding_utils.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/11.
//

#ifndef JSCBindingUtils_h
#define JSCBindingUtils_h

#include "../native_dom_define.h"

#if USE_JSC

#import <JavaScriptCore/JavaScriptCore.h>
#include "../msc_string.h"

// StaticFunction Struct
#define JSC_OBJ_FUNCTION_IMPL(clazz, jsClassName, name, holdFunc)         \
  static JSValueRef onCall##clazz##holdFunc(                              \
      JSContextRef context, JSObjectRef function, JSObjectRef thisObject, \
      size_t argc, const JSValueRef argv[], JSValueRef* exception) {      \
    clazz* element = static_cast<clazz*>(getJSCInterface(thisObject));    \
    if (element == nullptr) return JSValueMakeNull(context);              \
    return element->holdFunc(context, thisObject, argc, argv, exception); \
  }

#define JSC_STATIC_FUNCTION(clazz, jsClassName, name, holdFunc) \
    { name, onCall##clazz##holdFunc, kJSPropertyAttributeDontDelete | kJSPropertyAttributeReadOnly },

#define DECLARE_JSC_FUNCTION(Name)                                           \
  JSValueRef Name(JSContextRef context, JSObjectRef thisObject, size_t argc, \
                  const JSValueRef argv[], JSValueRef* exception)

#define DECLARE_JSC_FUNCTION_VIRTUAL(Name) \
    virtual DECLARE_JSC_FUNCTION(Name)

// StaticValue Sturct
#define JSC_BINDING_PROP_GET(clazz, jsClassName, name, prop)          \
  static JSValueRef onCall##clazz##get##prop(                         \
      JSContextRef ctx, JSObjectRef object, JSStringRef propertyName, \
      JSValueRef* exception) {                                        \
    clazz* element = static_cast<clazz*>(getJSCInterface(object));    \
    if (element == nullptr) return JSValueMakeNull(ctx);              \
    ;                                                                 \
    return element->get##prop(ctx, exception);                        \
  }

#define DECLARE_JSC_PROP_GETTER(Prop) \
  JSValueRef get##Prop(JSContextRef ctx, JSValueRef* exception)

#define DECLARE_JSC_PROP_GETTER_VIRTUAL(Prop) \
    virtual DECLARE_JSC_PROP_GETTER(Prop)

#define JSC_BINDING_PROP_SET(clazz, jsClassName, name, prop)          \
  static bool onCall##clazz##set##prop(                               \
      JSContextRef ctx, JSObjectRef object, JSStringRef propertyName, \
      JSValueRef value, JSValueRef* exception) {                      \
    clazz* element = static_cast<clazz*>(getJSCInterface(object));    \
    if (element == nullptr) return false;                             \
    return element->set##prop(ctx, value, exception);                 \
  }

#define JSC_BINDING_PROP_SET_AND_GET(clazz, jsClassName, name, prop) \
    JSC_BINDING_PROP_SET(clazz, jsClassName, name, prop);            \
    JSC_BINDING_PROP_GET(clazz, jsClassName, name, prop)

#define DECLARE_JSC_PROP_SETTER(Prop) \
  bool set##Prop(JSContextRef context, JSValueRef value, JSValueRef* exception)

#define DECLARE_JSC_PROP_SETTER_VIRTUAL(Prop) \
    virtual DECLARE_JSC_PROP_SETTER(Prop)

#define DECLARE_JSC_PROP_GETTER_AND_SETTER(Prop) \
    DECLARE_JSC_PROP_SETTER(Prop);               \
    DECLARE_JSC_PROP_GETTER(Prop)

#define DECLARE_JSC_PROP_GETTER_AND_SETTER_VIRTUAL(Prop) \
    DECLARE_JSC_PROP_SETTER_VIRTUAL(Prop);               \
    DECLARE_JSC_PROP_GETTER_VIRTUAL(Prop)

#define JSC_STATIC_VALUE_READ_ONLY(clazz, jsClassName, name, prop) \
    { name, onCall##clazz##get##prop, NULL, kJSPropertyAttributeDontDelete | kJSPropertyAttributeReadOnly },

#define JSC_STATIC_VALUE_READ_WRITE(clazz, jsClassName, name, prop) \
    { name, onCall##clazz##get##prop, onCall##clazz##set##prop, kJSPropertyAttributeDontDelete },

#define JSC_STATIC_VALUE_WRITE_ONLY(clazz, jsClassName, name, prop) \
    { name, NULL, onCall##clazz##set##prop, kJSPropertyAttributeDontDelete },

#define RUNTIME_GET_CLASSID_CALL(jsClassName) getJSClass(context, #jsClassName)

#define JSC_STATIC_VALUE_END \
    {                        \
        0, 0, 0, 0           \
    }

#define JSC_STATIC_FUNCTION_END \
    {                           \
        0, 0, 0                 \
    }

#define JSC_API_CHECK_FAILL_RETURN(compare, block, value) \
  do {                                                    \
    if (!(compare)) {                                     \
      block;                                              \
      return (value);                                     \
    }                                                     \
  } while (0)

#define JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(         \
    context, exception, argc, expected_argc, msg)                     \
  JSC_API_CHECK_FAILL_RETURN((argc) >= (expected_argc),               \
                             ThrowTypeError(context, exception, msg), \
                             JSValueMakeUndefined(context))

#define JSC_API_CHECK_NUMBER_FAILL_THROW_TYPEERROR(context, exception, obj, \
                                                   msg)                     \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsNumber(context, obj),                 \
                             ThrowTypeError(context, exception, msg),       \
                             JSValueMakeUndefined(context))

#define JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(context, exception, obj, \
                                                   msg)                     \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsString(context, obj),                 \
                             ThrowTypeError(context, exception, msg),       \
                             JSValueMakeUndefined(context))

#define JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(context, exception, obj, \
                                                   msg)                     \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsObject(context, obj),                 \
                             ThrowTypeError(context, exception, msg),       \
                             JSValueMakeUndefined(context))

#define JSC_API_CHECK_BOOLEAN_FAILL_THROW_TYPEERROR(context, exception, obj, \
                                                    msg)                     \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsBoolean(context, obj),                 \
                             ThrowTypeError(context, exception, msg),        \
                             JSValueMakeUndefined(context))

#define JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR_RETURN_NULL( \
    context, exception, argc, expected_argc, msg)                         \
  JSC_API_CHECK_FAILL_RETURN((argc) >= (expected_argc),                   \
                             ThrowTypeError(context, exception, msg), nullptr)

#define JSC_API_CHECK_NUMBER_FAILL_THROW_TYPEERROR_RETURN_NULL( \
    context, exception, obj, msg)                               \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsNumber(context, obj),     \
                             ThrowTypeError(context, exception, msg), nullptr)

#define JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR_RETURN_NULL( \
    context, exception, obj, msg)                               \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsString(context, obj),     \
                             ThrowTypeError(context, exception, msg), nullptr)

#define JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR_RETURN_NULL( \
    context, exception, obj, msg)                               \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsObject(context, obj),     \
                             ThrowTypeError(context, exception, msg), nullptr)

#define JSC_API_CHECK_BOOLEAN_FAILL_THROW_TYPEERROR_RETURN_NULL( \
    context, exception, obj, msg)                                \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsBoolean(context, obj),     \
                             ThrowTypeError(context, exception, msg), nullptr)

#define JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR_RETURN_FALSE( \
    context, exception, argc, expected_argc, msg)                          \
  JSC_API_CHECK_FAILL_RETURN((argc) >= (expected_argc),                    \
                             ThrowTypeError(context, exception, msg), false)

#define JSC_API_CHECK_NUMBER_FAILL_THROW_TYPEERROR_RETURN_FALSE( \
    context, exception, obj, msg)                                \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsNumber(context, obj),      \
                             ThrowTypeError(context, exception, msg), false)

#define JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR_RETURN_FALSE( \
    context, exception, obj, msg)                                \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsString(context, obj),      \
                             ThrowTypeError(context, exception, msg), false)

#define JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR_RETURN_FALSE( \
    context, exception, obj, msg)                                \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsObject(context, obj),      \
                             ThrowTypeError(context, exception, msg), false)

#define JSC_API_CHECK_BOOLEAN_FAILL_THROW_TYPEERROR_RETURN_FALSE( \
    context, exception, obj, msg)                                 \
  JSC_API_CHECK_FAILL_RETURN(JSValueIsBoolean(context, obj),      \
                             ThrowTypeError(context, exception, msg), false)

#define JSC_API_GUARD_INSTANCE_AVAILABLE(obj, ret)  \
  JSC_API_CHECK_FAILL_RETURN(obj->GetInstance<ScriptWrappable>(), {}, ret)

namespace msc {
namespace native_dom {

MSCString JSStringToMSCString(JSStringRef str);

inline unsigned GetJSArrayLength(JSContextRef context, JSObjectRef js_array) {
  JSStringRef length_key = JSStringCreateWithUTF8CString("length");
  JSValueRef length_value =
      JSObjectGetProperty(context, js_array, length_key, nullptr);
  JSStringRelease(length_key);

  if (JSValueIsNumber(context, length_value)) {
    return static_cast<unsigned>(
        JSValueToNumber(context, length_value, nullptr));
  }
  return 0;
}

inline JSObjectRef GetJSArrayObjectItem(JSContextRef context,
                                        JSObjectRef js_array, size_t index) {
  auto value = JSObjectGetPropertyAtIndex(context, js_array, index, nullptr);
  if (JSValueIsObject(context, value)) {
    return JSValueToObject(context, value, nullptr);
  }
  return nullptr;
}

template <typename V>
inline JSValueRef MakeJSValue(JSContextRef context, V&& value) {
  using T = std::decay_t<V>;
  if constexpr (std::is_same_v<T, JSValueRef>) {
    return value;
  } else if constexpr (std::is_arithmetic_v<T>) {
    return JSValueMakeNumber(context, static_cast<double>(value));
  } else if constexpr (std::is_same_v<T, std::string>) {
    JSStringRef str_ref = JSStringCreateWithUTF8CString(value.c_str());
    auto js_value = JSValueMakeString(context, str_ref);
    JSStringRelease(str_ref);
    return js_value;
  }
  return nullptr;
}

template <typename T>
inline void SetJSObjectProperty(JSContextRef context, JSObjectRef object,
                                const char* key, T&& value) {
  auto value_obj = MakeJSValue(context, std::forward<decltype(value)>(value));
  if (!value_obj) {
    return;
  }
  JSStringRef key_str = JSStringCreateWithUTF8CString(key);
  JSObjectSetProperty(context, object, key_str, value_obj,
                      kJSPropertyAttributeNone, nullptr);
  JSStringRelease(key_str);
}

template <typename T>
inline const char* GetJSArrayItem(JSContextRef context, JSObjectRef js_array,
                                  unsigned index, T& value) {
  JSValueRef property_value =
      JSObjectGetPropertyAtIndex(context, js_array, index, nullptr);
  if constexpr (std::is_arithmetic_v<T>) {
    if (!JSValueIsNumber(context, property_value)) {
      return "require number";
    }
    value = static_cast<T>(JSValueToNumber(context, property_value, nullptr));
  } else if constexpr (std::is_same<T, std::string>::value) {
    if (JSValueIsString(context, property_value)) {
      auto str_ref = JSValueToStringCopy(context, property_value, nullptr);
      value = JSStringToMSCString(str_ref);
      JSStringRelease(str_ref);
    } else {
      return "require string";
    }
  }
  return nullptr;
}

template <typename T>
inline const char* GetObjectProperty(JSContextRef ctx, JSObjectRef object,
                                     JSStringRef property_name, T& value) {
  const char* invalid_msg = nullptr;
  JSValueRef property_value =
      JSObjectGetProperty(ctx, object, property_name, nullptr);
  JSStringRelease(property_name);
  if constexpr (std::is_same<T, bool>::value) {
    if (JSValueIsBoolean(ctx, property_value)) {
      value = JSValueToBoolean(ctx, property_value);
    } else {
      invalid_msg = "require boolean";
    }
  } else if constexpr (std::is_arithmetic_v<T>) {
    if (JSValueIsNumber(ctx, property_value)) {
      value = JSValueToNumber(ctx, property_value, nullptr);
    } else {
      invalid_msg = "require number";
    }
  } else if constexpr (std::is_same<T, std::string>::value) {
    if (JSValueIsString(ctx, property_value)) {
      auto str_ref = JSValueToStringCopy(ctx, property_value, nullptr);
      value = JSStringToMSCString(str_ref);
      JSStringRelease(str_ref);
    } else {
      invalid_msg = "require string";
    }
  }
  return invalid_msg;
}

static inline std::pair<bool, JSStringRef> HasJSObjectProperty(JSContextRef ctx,
                                                 JSObjectRef object,
                                                 const char* property_name) {
  JSStringRef js_property_name = JSStringCreateWithUTF8CString(property_name);
  if (JSObjectHasProperty(ctx, object, js_property_name)) {
    return {true, js_property_name};
  }
  JSStringRelease(js_property_name);
  return {false, nullptr};
}

template <typename T>
inline std::pair<bool, const char*> GetObjectPropertyIfRequired(
    JSContextRef ctx, JSObjectRef object, const char* property_name, T& value,
    const char* required_msg) {
  auto has = HasJSObjectProperty(ctx, object, property_name);
  if (!has.first) {
    return {false, required_msg};
  }
  return {true, GetObjectProperty(ctx, object, has.second, value)};
}

template <typename P>
inline std::pair<bool, const char*> GetArrayPropertyIfRequired(
    JSContextRef ctx, JSObjectRef object, const char* property_name,
    std::vector<P>& value, const char* required_msg) {
  auto has = HasJSObjectProperty(ctx, object, property_name);
  if (!has.first) {
    return {false, required_msg};
  }
  auto property_value = JSObjectGetProperty(ctx, object, has.second, nullptr);
  JSStringRelease(has.second);
  if (!JSValueIsArray(ctx, property_value)) {
    return {true, "require array"};
  }
  JSObjectRef array_object = JSValueToObject(ctx, property_value, nullptr);
  unsigned array_object_len = GetJSArrayLength(ctx, array_object);
  value.reserve(array_object_len);
  for (unsigned i = 0; i < array_object_len; ++i) {
    const char *invalid_msg{};
    {
      double item;
      invalid_msg = GetJSArrayItem(ctx, array_object, i, item);
      if (!invalid_msg) {
        value.push_back(std::move(item));
        continue;
      }
    }
    {
      std::string item;
      invalid_msg = GetJSArrayItem(ctx, array_object, i, item);
      if (!invalid_msg) {
        value.push_back(std::move(item));
        continue;
      }
    }
    if (invalid_msg) {
      return {true, invalid_msg};
    }
  }
  return {true, nullptr};
}

JSObjectRef JSObjectFromProps(JSContextRef ctx, const blink::mt::Props& props,
                              const std::string& exclude_key);
JSValueRef PropValueToJS(JSContextRef ctx, const blink::mt::PropValue& prop);
blink::mt::PropValue JSValueToPropValue(JSContextRef ctx, const JSValueRef &jsValue);

void ThrowError(JSContextRef ctx, JSValueRef* exception, const char* message, const char* error_type = "Error");
inline void ThrowTypeError(JSContextRef ctx, JSValueRef* exception, const char* message) {
  ThrowError(ctx, exception, message, "TypeError");
}
inline void ThrowInternalError(JSContextRef ctx, JSValueRef* exception, const char* message) {
  ThrowError(ctx, exception, message, "InternalError");
}

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* JSCBindingUtils_h */
