//
//  css_style_declaration_wrapper.hpp
//  MSC
//
//  Created by q<PERSON><PERSON> on 2025/4/3.
//

#ifndef MSC_NATIVE_DOM_JSC_CSS_STYLE_DECLARATION_WRAPPER_H
#define MSC_NATIVE_DOM_JSC_CSS_STYLE_DECLARATION_WRAPPER_H

#include "../css_style_declaration.h"
#include "native_dom_object_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

class CSSStyleDeclarationWrapper : public NativeDOMObjectWrapper {
   public:
    CSSStyleDeclarationWrapper(std::shared_ptr<CSSStyleDeclaration> node)
        : NativeDOMObjectWrapper(node) {}

    static JSClassRef SetupJSClass(JSClassRef parent_class);
    JSValueRef CreateJSObject(JSContextRef context);

    DECLARE_JSC_PROP_GETTER_AND_SETTER(CSSText);
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* MSC_NATIVE_DOM_JSC_CSS_STYLE_DECLARATION_WRAPPER_H */
