//
//  document_wrapper.h
//  MSC
//
//  Created by ji<PERSON><PERSON> zhang on 2025/2/13.
//

#ifndef DocumentWrapper_hpp
#define DocumentWrapper_hpp

#include "../document.h"
#include "element_wrapper.h"
#include "text_node_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

class DocumentWrapper : public ElementWrapper {
 public:
  DocumentWrapper(std::shared_ptr<Document> document)
      : ElementWrapper(document) {}
  static JSClassRef SetupJSClass(JSClassRef parent_class);

  JSValueRef CreateJSObject(JSContextRef context);

  DECLARE_JSC_FUNCTION(CreateElement);
  DECLARE_JSC_FUNCTION(CreateTextNode);
  DECLARE_JSC_FUNCTION(CreateKeyframesAnimationEnhanced);
  DECLARE_JSC_FUNCTION(ClearKeyframesAnimationEnhanced);
  DECLARE_JSC_FUNCTION(QueryEnhanced);
  DECLARE_JSC_FUNCTION(FrameMarker);
  DECLARE_JSC_PROP_GETTER(Body);
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* DocumentWrapper_hpp */
