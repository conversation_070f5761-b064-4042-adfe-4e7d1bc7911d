//
//  event_wrapper.hpp
//  MSCRenderer
//
//  Created by ji<PERSON><PERSON> zhang on 2025/5/30.
//

#ifndef event_wrapper_hpp
#define event_wrapper_hpp

#include "native_dom_object_wrapper.h"
#include "../event.h"

#if USE_JSC

namespace msc {
namespace native_dom {

class EventWrapper : public NativeDOMObjectWrapper {
public:
  EventWrapper(std::shared_ptr<Event> event) : NativeDOMObjectWrapper(event) {}
  
  static JSClassRef SetupJSClass(JSClassRef parent_class);
  JSValueRef CreateJSObject(JSContextRef context);
  
  DECLARE_JSC_FUNCTION(StopPropagation);
  
  DECLARE_JSC_PROP_GETTER(Type);
  DECLARE_JSC_PROP_GETTER(TimeStamp);
  DECLARE_JSC_PROP_GETTER(CurrentTarget);
  DECLARE_JSC_PROP_GETTER(Target);
  DECLARE_JSC_PROP_GETTER(EventPhase);
  DECLARE_JSC_PROP_GETTER(Detail);
};

}
}

#endif

#endif /* event_wrapper_hpp */
