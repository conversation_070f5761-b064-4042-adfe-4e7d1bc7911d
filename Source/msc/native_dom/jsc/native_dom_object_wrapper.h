//
//  native_dom_object_wrapper.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/2/13.
//

#ifndef NativeDOMObjectWrapper_hpp
#define NativeDOMObjectWrapper_hpp

#include <JavaScriptCore/JavaScriptCore.h>
#include <string>
#include <unordered_map>

#include "jsc_binding_utils.h"
#include "../script_wrappable.h"

#if USE_JSC


#if DEBUG
extern "C" {
  void JSSynchronousGarbageCollectForDebugging(JSContextRef ctx);
}
#endif


namespace msc {
namespace native_dom {

class NativeDOMObjectWrapper {
public:
  NativeDOMObjectWrapper(std::shared_ptr<ScriptWrappable> instance) : instance_(instance), jsObject_(nullptr), jsContext_(nullptr) {
    instance_->SetJSWrapper(this);
  }
  
  virtual ~NativeDOMObjectWrapper() {
    instance_ = nullptr;
  };
  
  JSValueRef CreateJSObject(JSContextRef context) {
    return nullptr;
  }
  
  void SetJSObject(JSContextRef ctx, JSObjectRef object) {
    jsObject_ = object;
    jsContext_ = ctx;
    JSValueProtect(ctx, object);
  }

  JSObjectRef GetJSObject() {
    return jsObject_;
  }
  
  void ClearJSObject() {
    if (jsContext_ && jsObject_) {
      JSValueUnprotect(jsContext_, jsObject_);
      jsContext_ = nullptr;
      jsObject_ = nullptr;
    }
  }
  
  void Destroy() {
    MSC_RENDERER_LOG_DEBUG("[native_dom] deleting wrapper for script_wrappable: %p", this);
    
    JSObjectRef js_object = this->jsObject_;
    
    this->ClearJSObject();
    this->ClearInstance();
    
    if (js_object && JSObjectGetPrivate(js_object) == this) {
        JSObjectSetPrivate(js_object, nullptr);
        delete this;
    }
  }
  
  template <typename T>
  std::shared_ptr<T> GetInstance() {
    return std::static_pointer_cast<T>(instance_);
  }
  
  void ClearInstance() {
    instance_ = nullptr;
  }
  
  void ForceGCDebug() {
#if DEBUG
    MSC_RENDERER_LOG_DEBUG("ForceGCDebug");
    JSGarbageCollect(jsContext_);
    JSSynchronousGarbageCollectForDebugging(jsContext_);
#endif
  }
  
private:
  std::shared_ptr<ScriptWrappable> instance_;
  JSObjectRef jsObject_;
  JSContextRef jsContext_;
};

void saveJSClass(const char *class_name, JSClassRef js_class);
JSClassRef queryJSClass(const char *class_name);

static inline void BindingInitialize(JSContextRef ctx, JSObjectRef object)
{
}

JSClassRef registerJSClass(const char* class_name, JSStaticFunction static_functions[], JSStaticValue static_values[], JSClassRef parent_class);

static inline JSClassRef getJSClass(const char* class_name)
{
  return queryJSClass(class_name);
}

static inline NativeDOMObjectWrapper* getJSCInterface(JSObjectRef object) {
  if (object == nullptr) {
    return nullptr;
  }
  
  auto privateData = JSObjectGetPrivate(object);
  if (privateData == nullptr) {
    return nullptr;
  }
  
  return static_cast<NativeDOMObjectWrapper*>(privateData);
}


static inline JSObjectRef tryCreateJSObject(JSContextRef context, const char * jsClassName, NativeDOMObjectWrapper *ptr, bool retainJSObject = true) {
  auto cls = getJSClass(jsClassName);
  auto jsObj = JSObjectMake(context, cls, ptr);
  if (retainJSObject) {
    ptr->SetJSObject(context, jsObj);
  }
  MSC_RENDERER_LOG_DEBUG("tryCreateJSObject: %s, context: %p, jsObj: %p", jsClassName, context, jsObj);
  return jsObj;
}

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* NativeDOMObjectWrapper_hpp */
