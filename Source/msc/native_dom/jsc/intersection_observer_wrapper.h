//
//  intersection_observer_wrapper.h
//  MSC
//

#ifndef INTERSECTION_OBSERVER_WRAPPER_H
#define INTERSECTION_OBSERVER_WRAPPER_H

#include "../intersection_observer.h"
#include "native_dom_object_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

class IntersectionObserverWrapper : public NativeDOMObjectWrapper {
 public:
  IntersectionObserverWrapper(
      std::shared_ptr<IntersectionObserver> intersection_observer)
      : NativeDOMObjectWrapper(intersection_observer) {}

  static JSClassRef SetupJSClass(JSClassRef parent_class);
  JSValueRef CreateJSObject(JSContextRef context);

  static JSObjectRef CreateIntersectionObserver(JSContextRef context,
                                                JSObjectRef constructor,
                                                size_t argc,
                                                const JSValueRef argv[],
                                                JSV<PERSON>ue<PERSON><PERSON> *exception);

  DECLARE_JSC_FUNCTION(Observe);
  DECLARE_JSC_FUNCTION(Disconnect);
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* INTERSECTION_OBSERVER_WRAPPER_H */
