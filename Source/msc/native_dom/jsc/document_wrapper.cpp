//
//  DocumentWrapper.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/13.
//

#include "document_wrapper.h"

#include <sstream>
#include <tuple>

#include "../../final_action.h"
#include "../../logger.h"
#include "../view_component.h"
#include "jsc_js_callback.h"
#include "text_node_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

#define JSC_NATIVE_DOM_FUNCTION_DEF(V)                            \
  V(DocumentWrapper, Document, Document::kCreateElement.c_str(),  \
    CreateElement)                                                \
  V(DocumentWrapper, Document, Document::kCreateTextNode.c_str(), \
    CreateTextNode)                                               \
  V(DocumentWrapper, Document,                                    \
    Document::kCreateKeyframesAnimationEnhanced.c_str(),          \
    CreateKeyframesAnimationEnhanced)                             \
  V(DocumentWrapper, Document,                                    \
    Document::kClearKeyframesAnimationEnhanced.c_str(),           \
    ClearKeyframesAnimationEnhanced)                              \
  V(DocumentWrapper, Document, Document::kQueryEnhanced.c_str(),  \
    QueryEnhanced)                                                \
  V(DocumentWrapper, Document, Document::kFrameMarker.c_str(), FrameMarker)

#define JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V)  \
    V(DocumentWrapper, Document, "body", Body)


#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V) \
    JSC_NATIVE_DOM_FUNCTION_DEF(V)             \
    JSC_STATIC_FUNCTION_END

#define JSC_NATIVE_DOM_PROP_INITIALIZER(V)   \
    JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V) \
    JSC_STATIC_VALUE_END

JSC_NATIVE_DOM_FUNCTION_DEF(JSC_OBJ_FUNCTION_IMPL);
JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(JSC_BINDING_PROP_GET);

JSClassRef DocumentWrapper::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
       JSC_NATIVE_DOM_FUNCTION_INITIALIZER(JSC_STATIC_FUNCTION)
   };

   static JSStaticValue static_values[] = {
       JSC_NATIVE_DOM_PROP_INITIALIZER(JSC_STATIC_VALUE_READ_ONLY)
   };
   return registerJSClass("Document", static_functions, static_values, parent_class);
}

JSValueRef DocumentWrapper::CreateJSObject(JSContextRef context) {
     JSObjectRef ret = tryCreateJSObject(context, "Document", this);
  return ret;
}

JSValueRef DocumentWrapper::CreateElement(JSContextRef context,
                                          JSObjectRef thisObject, size_t argc,
                                          const JSValueRef *argv,
                                          JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'createElement' on 'Document':1 argument required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'createElement' on 'Document':1st argument must be "
      "string");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));

  JSStringRef tagName = JSValueToStringCopy(context, argv[0], nullptr);
  std::string tagNameStr = JSStringToMSCString(tagName);
  JSStringRelease(tagName);

  auto *element = GetInstance<Document>()->CreateElement(tagNameStr);
  auto wrapper = new ElementWrapper(std::shared_ptr<Element>(element));
  auto jsObject = wrapper->CreateJSObject(context);
  MSC_RENDERER_LOG_DEBUG("CreateElement element: %p, js element: %p, tagName: %s", element, jsObject, tagNameStr.c_str());
  return jsObject;
}

JSValueRef DocumentWrapper::CreateTextNode(JSContextRef context,
                                           JSObjectRef thisObject, size_t argc,
                                           const JSValueRef *argv,
                                           JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'createTextNode' on 'Document':1 argument required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'createTextNode' on 'Document':1st argument must be "
      "string");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  auto text = JSValueToStringCopy(context, argv[0], nullptr);
  auto textStr = JSStringToMSCString(text);
  JSStringRelease(text);

  auto text_node = GetInstance<Document>()->CreateTextNode(textStr);
  auto wrapper = new TextNodeWrapper(std::shared_ptr<TextNode>(text_node));
  auto jsObject = wrapper->CreateJSObject(context);
  
  MSC_RENDERER_LOG_DEBUG("CreateTextNode element: %p, js element: %p, tagName: %s", text_node, jsObject, textStr.c_str());
  
  return jsObject;
}

JSValueRef DocumentWrapper::CreateKeyframesAnimationEnhanced(JSContextRef context, JSObjectRef thisObject, size_t argc,
                                                             const JSValueRef *argv, JSValueRef *exception) {
  // ref.
  // https://km.sankuai.com/collabpage/2714606667
  // https://km.sankuai.com/page/1302701926

  // e.g.
  // document.createKeyFramesAnimationEnhanced(elements, keyframes, duration, callback);
  const char *invalid_msg{};
  std::shared_ptr<Document> document{};
  blink::mt::PropsBuilder builder;
  JSObjectRef callback{};
  do {
    // get document
    document = GetInstance<Document>();
    if (MSC_UNLIKELY(document == nullptr)) {
      invalid_msg = "found no document";
      break;
    }

    // check args
    if (argc < 3) {
      invalid_msg = "require parameters: elements, keyframes, duration";
      break;
    }
    if (!JSValueIsArray(context, argv[0]) || !JSValueIsArray(context, argv[1]) || !JSValueIsNumber(context, argv[2]) ||
        (argc > 3 && !JSObjectIsFunction(context, callback = JSValueToObject(context, argv[3], nullptr)))) {
      invalid_msg = "require parameters' type: array, array, number, function";
      break;
    }

    // get elements's tags
    std::vector<blink::mt::PropValue> tags;
    auto elements_array = JSValueToObject(context, argv[0], nullptr);
    auto elements_array_len = GetJSArrayLength(context, elements_array);
    tags.reserve(elements_array_len);
    for (size_t i = 0; i < elements_array_len; ++i) {
      auto element_wrapper = getJSCInterface(GetJSArrayObjectItem(context, elements_array, i));
      if (element_wrapper) {
        auto node = element_wrapper->GetInstance<Node>().get();
        tags.push_back(static_cast<blink::mt::PropValueType::Number>(node->GetNodeId()));
      }
    }

    // get keyframes
    std::vector<blink::mt::PropValue> keyframes;
    auto keyframes_array = JSValueToObject(context, argv[1], nullptr);
    auto keyframes_array_len = GetJSArrayLength(context, keyframes_array);
    keyframes.reserve(keyframes_array_len);
    for (size_t i = 0; i < keyframes_array_len; ++i) {
      auto keyframe_object = GetJSArrayObjectItem(context, keyframes_array, i);
      blink::mt::PropValueType::Dictionary keyframe;
      blink::mt::PropValueType::Number number_value;
      blink::mt::PropValueType::String string_value;
      blink::mt::PropValueType::Array array_value;

      auto get_prop = [context, keyframe_object, &invalid_msg, &keyframe](const char *prop_name, auto &prop_value,
                                                                          const char *require_msg) {
        auto has_prop = false;
        using T = std::decay_t<decltype(prop_value)>;
        if constexpr (!std::is_same_v<T, blink::mt::PropValueType::Array>) {
          std::tie(has_prop, invalid_msg) =
              GetObjectPropertyIfRequired(context, keyframe_object, prop_name, prop_value, require_msg);
        } else {
          std::tie(has_prop, invalid_msg) = GetArrayPropertyIfRequired(
              context, keyframe_object, prop_name, prop_value, require_msg);
        }

        if (invalid_msg) {
          return false;
        }
        if (has_prop) {
          keyframe[prop_name] = std::move(prop_value);
        }
        return true;
      };

      if (!get_prop("opacity", number_value, nullptr) || !get_prop("rotate", number_value, nullptr) ||
          !get_prop("offset", number_value, nullptr) || !get_prop("ease", string_value, nullptr) ||
          !get_prop("scale", array_value, nullptr) || !get_prop("translate", array_value, nullptr) ||
          !get_prop("width", array_value, nullptr)) {
        break;
      }

      // add keyframe
      keyframes.push_back(std::move(keyframe));
    }

    // get duration
    auto duration_value = JSValueToNumber(context, argv[2], nullptr);

    builder.setProp("tags", std::move(tags));
    builder.setProp("keyframes", std::move(keyframes));
    builder.setProp("duration", duration_value);
  } while (false);

  if (invalid_msg) {
    std::string message = std::string(invalid_msg) + ", in document.createKeyframesAnimationEnhanced";
    ThrowTypeError(context, exception, message.c_str());
  } else {
    auto animation_pros = std::make_shared<blink::mt::AnimationProperties>();
    animation_pros->props = std::const_pointer_cast<blink::mt::Props>(builder.getProps());

    // get callback
    JSCallbackInfo callback_info{};
    callback_info.type = JSCallbackInfo::Type::CreateKeyframesAnimation;
    if (callback) {
      auto callable = new JSCallbackForSimple([context, callback]() {
        JSObjectCallAsFunction(context, callback, JSContextGetGlobalObject(context), 0, nullptr, nullptr);
      });
      auto js_callback = new JSCJSCallback(std::unique_ptr<JSCallable>(callable), context, callback);
      callback_info.callback_index_ = document->ProtectJSCallback(std::unique_ptr<JSCallback>(js_callback));
    } else {
      callback_info.is_valid = false;
    }
    document->CreateKeyframesAnimationEnhanced(std::move(animation_pros),
                                               callback_info);
  }
  return JSValueMakeUndefined(context);
}

JSValueRef DocumentWrapper::ClearKeyframesAnimationEnhanced(JSContextRef context, JSObjectRef thisObject, size_t argc,
                                                            const JSValueRef *argv, JSValueRef *exception) {
  // e.g.
  // document.cxlearKeyFramesAnimationEnhanced(elements, options, callbag275ck);
  const char *invalid_msg{};
  std::shared_ptr<Document> document{};
  blink::mt::PropsBuilder builder;
  JSObjectRef callback{};
  do {
    document = GetInstance<Document>();
    if (MSC_UNLIKELY(document == nullptr)) {
      invalid_msg = "found no document";
      break;
    }

    // check args
    bool null_options = JSValueIsNull(context, argv[1]);
    if (argc < 2) {
      invalid_msg = "require parameters: elements, options";
      break;
    }
    if (!JSValueIsArray(context, argv[0]) || (!(null_options || JSValueIsObject(context, argv[1]))) ||
        (argc > 2 && !JSObjectIsFunction(context, callback = JSValueToObject(context, argv[2], nullptr)))) {
      invalid_msg = "require parameters' type: array, object, function";
      break;
    }

    // get elements's tags
    std::vector<blink::mt::PropValue> tags;
    auto elements_array = JSValueToObject(context, argv[0], nullptr);
    auto elements_array_len = GetJSArrayLength(context, elements_array);
    tags.reserve(elements_array_len);
    for (size_t i = 0; i < elements_array_len; ++i) {
      auto element_wrapper = getJSCInterface(GetJSArrayObjectItem(context, elements_array, i));
      if (element_wrapper) {
        auto node = element_wrapper->GetInstance<Node>().get();
        if (node) {
          tags.push_back(static_cast<blink::mt::PropValueType::Number>(node->GetNodeId()));
        }
      }
    }
    builder.setProp("tags", std::move(tags));

    if (null_options) {
      builder.setProp("options", blink::mt::PropValueType::Null());
      break;
    }

    // get options
    // options = {scale: true, translate: true, rotate: true, opacity: true};
    blink::mt::PropValueType::Dictionary options;
    auto options_object = JSValueToObject(context, argv[1], nullptr);

    auto get_prop = [context, options_object, &invalid_msg, &options](const char *prop_name) {
      blink::mt::PropValueType::Boolean bool_value{false};
      auto has_property = false;
      std::tie(has_property, invalid_msg) =
          GetObjectPropertyIfRequired(context, options_object, prop_name, bool_value, nullptr);
      if (invalid_msg) {
        return false;
      }
      if (has_property) {
        options[prop_name] = bool_value;
      }
      return true;
    };

    if (!get_prop("scale") || !get_prop("translate") || !get_prop("rotate") || !get_prop("opacity")) {
      break;
    }

    builder.setProp("options", std::move(options));
  } while (false);
  if (invalid_msg) {
    std::string invalid_msg = std::string(invalid_msg) + ", in document.clearKeyframesAnimationEnhanced";
    ThrowTypeError(context, exception, invalid_msg.c_str());
    return JSValueMakeUndefined(context);
  } else {
    auto clear_animation_properties = std::make_shared<blink::mt::ClearAnimationProperties>();
    clear_animation_properties->props = std::const_pointer_cast<blink::mt::Props>(builder.getProps());
    // get callback
    JSCallbackInfo callback_info{};
    callback_info.type = JSCallbackInfo::Type::ClearKeyframesAnimation;
    if (callback) {
      auto callable = new JSCallbackForSimple([context, callback]() {
        JSObjectCallAsFunction(context, callback, JSContextGetGlobalObject(context), 0, nullptr, nullptr);
      });
      auto js_callback = new JSCJSCallback(std::unique_ptr<JSCallable>(callable), context, callback);
      callback_info.callback_index_ = document->ProtectJSCallback(std::unique_ptr<JSCallback>(js_callback));
    } else {
      callback_info.is_valid = false;
    }
    document->ClearKeyframesAnimationEnhanced(std::move(clear_animation_properties), callback_info);
  }
  return JSValueMakeUndefined(context);
}

static inline std::vector<JSValueRef> MakeJSObjectForQueryEnhancedEntries(
    bool need_rect, bool need_size_specific, bool need_scroll_offset, JSContextRef context,
    const blink::mt::QueryEnhancedEntries &entries) {
  unsigned size = entries.size();
  std::vector<JSValueRef> entries_res;
  entries_res.reserve(size);
  for (size_t i = 0; i < size; ++i) {
    const blink::mt::QueryEnhancedEntry &entry = entries[i];
    JSObjectRef entry_object = JSObjectMake(context, nullptr, nullptr);
    if (!entry.invalid) {
      {
        // SetJSObjectProperty(context, entry_object, "first_ele",
        // first_element_value);
      }
      if (need_rect) {  // needRect
        SetJSObjectProperty(context, entry_object, "top", entry.y);
        SetJSObjectProperty(context, entry_object, "bottom", entry.y + entry.height);
        SetJSObjectProperty(context, entry_object, "left", entry.x);
        SetJSObjectProperty(context, entry_object, "right", entry.x + entry.width);
      }
      if (need_size_specific) {  // needSize
        SetJSObjectProperty(context, entry_object, "width", entry.width);
        SetJSObjectProperty(context, entry_object, "height", entry.height);
      }
      if (need_scroll_offset) {
        SetJSObjectProperty(context, entry_object, "scrollTop", entry.scroll_top);
        SetJSObjectProperty(context, entry_object, "scrollLeft", entry.scroll_left);
      }
    }
    entries_res.push_back(entry_object);
  }
  return entries_res;
}

JSValueRef DocumentWrapper::QueryEnhanced(JSContextRef context, JSObjectRef thisObject, size_t argc,
                                          const JSValueRef *argv, JSValueRef *exception) {
  // e.g.
  // document.queryEnhanced(
  //   /* elements */ elements,
  //   /* needRect */ false,
  //   /* needSize */ true,
  //   /* needScrollOffset */ false,
  //   /* isViewport */ false,
  //   /* callback */ function(res)
  //   {
  //     let i = 0;
  //     /* size */
  //     res[i].width;
  //     res[i].height;
  //   }
  // );

  // get document
  auto document = GetInstance<Document>();
  if (MSC_UNLIKELY(document == nullptr)) {
    ThrowInternalError(context, exception, "queryEnhanced: no document");
    return JSValueMakeUndefined(context);
  }

  // check args
  if (argc < 6 || !JSValueIsBoolean(context, argv[1]) ||  // needRect
      !JSValueIsBoolean(context, argv[2]) ||              // needSizeSpecific
      !JSValueIsBoolean(context, argv[3]) ||              // needScrollOffset
      !JSValueIsBoolean(context, argv[4]) ||              // isViewport
      !JSValueIsObject(context, argv[5])) {               // callback
    ThrowTypeError(context, exception,
                   "queryEnhanced: 6 arguments required: elements, needRect, "
                   "needSizeSpecific, needScrollOffset, isViewport, callback");
    return JSValueMakeUndefined(context);
  }

  auto callback = JSValueToObject(context, argv[5], nullptr);
  auto is_viewport = JSValueToBoolean(context, argv[4]);
  if ((!is_viewport and !JSValueIsObject(context, argv[0])) or !JSObjectIsFunction(context, callback)) {
    ThrowTypeError(context, exception, "queryEnhanced: elements required if not viewport");
    return JSValueMakeUndefined(context);
  }
  // Rect = Location + Size
  auto need_rect = JSValueToBoolean(context, argv[1]);
  auto need_location = need_rect;
  auto need_size_specific = JSValueToBoolean(context, argv[2]);
  auto need_size = need_size_specific or need_rect;
  auto need_scroll_offset = JSValueToBoolean(context, argv[3]);

  // // debug
  // JSValueRef first_element_value{};

  // get elements' tags
  std::vector<int> tags;
  if (is_viewport) {
    // mark@qqiwei: actually done in blink
    // tags.push_back(1);
  } else {
    auto elements_array = JSValueToObject(context, argv[0], nullptr);
    auto elements_length =
        JSObjectGetProperty(context, elements_array, JSStringCreateWithUTF8CString("length"), nullptr);
    auto elements_length_int = JSValueToNumber(context, elements_length, nullptr);
    if (elements_length_int == 0) {
      ThrowTypeError(context, exception, "queryEnhanced: no elements");
      return JSValueMakeUndefined(context);
    }
    tags.reserve(elements_length_int);
    {  // debug
       // first_element_value =
       //     JSObjectGetPropertyAtIndex(context, elements_array, 0, nullptr);
    }
    for (int i = 0; i < elements_length_int; ++i) {
      JSValueRef element_value = JSObjectGetPropertyAtIndex(context, elements_array, i, nullptr);
      JSObjectRef element = JSValueToObject(context, element_value, nullptr);
      auto element_wrapper = getJSCInterface(element);
      if (element_wrapper) {
        auto node = element_wrapper->GetInstance<Node>().get();
        if (node) {
          tags.push_back(node->GetNodeId());
        }
      }
    }
    if (tags.size() != elements_length_int) {
      MSC_RENDERER_LOG_ERROR("QueryEnhanced: elements length not match: %zu, %f", tags.size(), elements_length_int);
    }
  }

  // create params
  blink::mt::QueryEnhancedParams params{std::move(tags), need_location, need_size, need_scroll_offset, is_viewport};

  // create callback_c
  auto callback_c = [context, callback, need_rect, need_size_specific,
                     need_scroll_offset /*, first_element_value*/](const blink::mt::QueryEnhancedEntries &entries,
                                                                   Element *element) {
    auto entries_res = MakeJSObjectForQueryEnhancedEntries(need_rect, need_size_specific, need_scroll_offset, context, entries);
    auto res = JSObjectMakeArray(context, entries_res.size(), entries_res.data(), nullptr);
    JSValueRef args[] = {res};
    JSObjectCallAsFunction(context, callback, JSContextGetGlobalObject(context), 1, args, nullptr);
  };
  JSCallbackInfo callback_info{};
  callback_info.type = JSCallbackInfo::Type::QueryEnhanced;
  auto callable = new JSCallbackForQueryEnhanced(std::move(callback_c));
  auto js_callback = new JSCJSCallback(std::unique_ptr<JSCallable>(callable), context, callback);
  callback_info.callback_index_ = document->ProtectJSCallback(std::unique_ptr<JSCallback>(js_callback));

  // post to blink thread
  document->QueryEnhanced(std::move(params), callback_info);
  return JSValueMakeUndefined(context);
}

JSValueRef DocumentWrapper::FrameMarker(JSContextRef context,
                                        JSObjectRef thisObject, size_t argc,
                                        const JSValueRef argv[],
                                        JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'frameMarker' on 'Document':1 argument required");
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'frameMarker' on 'Document':1st argument must be "
      "string");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));

  JSStringRef stringRef = JSValueToStringCopy(context, argv[0], nullptr);
  if (stringRef == nullptr) {
    return JSValueMakeNull(context);
  }

  // static std::chrono::system_clock::time_point begin;
  // static os_log_t nativeLog = nullptr;
  // if (nativeLog == nullptr) {
  //   nativeLog = os_log_create("com.sankuai.msc.native", "native1");
  // }
  
  if (JSStringIsEqualToUTF8CString(stringRef, Document::kFrameMarkerTypeBegin.c_str())) {
    // begin = std::chrono::system_clock::now();
    //      fprintf(stderr, "===== FrameMarker begin\n");
    // os_log(nativeLog, "===== FrameMarker begin");
    
    GetInstance<Document>()->FrameMarker(Document::FrameMarkerType::Begin);
  } else if (JSStringIsEqualToUTF8CString(stringRef, Document::kFrameMarkerTypeEnd.c_str())) {
    GetInstance<Document>()->FrameMarker(Document::FrameMarkerType::End);
   
    // auto end = std::chrono::system_clock::now();
    // auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - begin);
    // os_log(nativeLog, "===== FrameMarker end: %f\n", double(duration.count()));
  } else if (JSStringIsEqualToUTF8CString(stringRef, Document::kFrameMarkerTypePreEnd.c_str())) {
    GetInstance<Document>()->FrameMarker(Document::FrameMarkerType::PreEnd);
  }
  
  JSStringRelease(stringRef);
  return JSValueMakeNull(context);
}

JSValueRef DocumentWrapper::getBody(JSContextRef context,
                                    JSValueRef *exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  auto body = GetInstance<Document>()->body();
  if (body->GetJSWrapper() == nullptr) {
    auto wrapper = new ElementWrapper(body);
    wrapper->CreateJSObject(context);
//    body->SetJSWrapper(wrapper);
  }
  return GET_JSOBJECT_FROM(body);
}
}  // namespace native_dom
}  // namespace msc

#endif
