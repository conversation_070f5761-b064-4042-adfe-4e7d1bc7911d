//
// Created by <PERSON><PERSON><PERSON> on 2025/4/9.
//

#ifndef MSC_NATIVE_DOM_JSC_TEXT_NODE_WRAPPER_H
#define MSC_NATIVE_DOM_JSC_TEXT_NODE_WRAPPER_H

#include "element_wrapper.h"
#include "native_dom_object_wrapper.h"
#include "../text_node.h"

#if USE_JSC

namespace msc {
namespace native_dom {

class TextNodeWrapper : public ElementWrapper {
 public:
  TextNodeWrapper(std::shared_ptr<TextNode> node) : ElementWrapper(node) {}

  static JSClassRef SetupJSClass(JSClassRef parent_class) {
    return registerJSClass("TextNode", nullptr, nullptr, parent_class);
  }
  JSValueRef CreateJSObject(JSContextRef context) {
    return tryCreateJSObject(context, "TextNode", this);
  }
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif  // MSC_NATIVE_DOM_JSC_TEXT_NODE_WRAPPER_H
