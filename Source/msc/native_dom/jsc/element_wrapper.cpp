//
//  ElementWrapper.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/13.
//

#include "element_wrapper.h"
#include "dom_token_list_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

#define JSC_NATIVE_DOM_FUNCTION_DEF(V)                                                \
    V(ElementWrapper, Element, Element::kGetAttribute.c_str(), GetAttribute)  \
    V(ElementWrapper, Element, Element::kSetAttribute.c_str(), SetAttribute)  \
    V(ElementWrapper, Element, Element::kRemoveAttribute.c_str(), RemoveAttribute)


#define JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V)  \
    V(ElementWrapper, Element, "id", Id) \
    V(ElementWrapper, Element, "className", ClassName)  \
    V(ElementWrapper, Element, "class", Class)

#define JSC_NATIVE_DOM_PROP_GETTER_DEF(V)                                      \
  V(<PERSON>ement<PERSON>rap<PERSON>, El<PERSON>, "style", Style)                                   \
  V(<PERSON>ementWrap<PERSON>, El<PERSON>, "tagName", TagName)                               \
  V(ElementWrapper, Element, "classList", ClassList)

#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V) \
    JSC_NATIVE_DOM_FUNCTION_DEF(V)             \
    JSC_STATIC_FUNCTION_END

#define JSC_NATIVE_DOM_PROP_INITIALIZER(V)   \
    JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(V)

#define JSC_NATIVE_DOM_PROP_READ_ONLY_INITIALIZER(V)   \
    JSC_NATIVE_DOM_PROP_GETTER_DEF(V)

JSC_NATIVE_DOM_FUNCTION_DEF(JSC_OBJ_FUNCTION_IMPL);
JSC_NATIVE_DOM_PROP_SETTER_GETTER_DEF(JSC_BINDING_PROP_SET_AND_GET);
JSC_NATIVE_DOM_PROP_GETTER_DEF(JSC_BINDING_PROP_GET);

JSClassRef ElementWrapper::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
       JSC_NATIVE_DOM_FUNCTION_INITIALIZER(JSC_STATIC_FUNCTION)
   };

   static JSStaticValue static_values[] = {
       JSC_NATIVE_DOM_PROP_INITIALIZER(JSC_STATIC_VALUE_READ_WRITE)
       JSC_NATIVE_DOM_PROP_READ_ONLY_INITIALIZER(JSC_STATIC_VALUE_READ_ONLY)
       JSC_STATIC_VALUE_END
   };
   return registerJSClass("Element", static_functions, static_values, parent_class);
}

JSValueRef ElementWrapper::CreateJSObject(JSContextRef context) {
  JSObjectRef ret = tryCreateJSObject(context, "Element", this);
  return ret;
}

ElementWrapper::~ElementWrapper() {
}

bool ElementWrapper::setId(JSContextRef context, JSValueRef value,
                           JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, false);
  
  auto stringValue = JSValueToStringCopy(context, value, nullptr);
  GetInstance<Element>()->SetAttribute("id", JSStringToMSCString(stringValue));
  JSStringRelease(stringValue);
  return true;
}

JSValueRef ElementWrapper::getId(JSContextRef context, JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  MSCString idString = GetInstance<Element>()->idAttribute();
  auto jsString = JSStringCreateWithUTF8CString(idString.c_str());
  return JSValueMakeString(context, jsString);
}

JSValueRef ElementWrapper::getTagName(JSContextRef context,
                                      JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  MSCString tag = GetInstance<Element>()->tagName();
  auto jsString = JSStringCreateWithUTF8CString(tag.c_str());
  return JSValueMakeString(context, jsString);
}

JSValueRef ElementWrapper::getClassList(JSContextRef context,
                                        JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  auto dom_token_list = GetInstance<Element>()->GetDOMTokenList();
  if (dom_token_list->GetJSWrapper() == nullptr) {
    auto *wrapper = new DOMTokenListWrapper(dom_token_list);
    wrapper->CreateJSObject(context);
  }
  return GET_JSOBJECT_FROM(dom_token_list);
}

// bool ElementWrapper::setStyle(JSContextRef context, JSValueRef value) {
//   auto stringValue = JSValueToStringCopy(context, value, nullptr);
//   GetInstance<Element>()->SetAttribute("style", JSStringToMSCString(stringValue));
//   JSStringRelease(stringValue);
//   return true;
// }
//
// JSValueRef ElementWrapper::getStyle(JSContextRef context) {
//   MSCString styleString;
//   GetInstance<Element>()->GetAttribute("style", styleString);
//   auto jsString = JSStringCreateWithUTF8CString(styleString.c_str());
//   return JSValueMakeString(context, jsString);
// }

bool ElementWrapper::setClassName(JSContextRef context, JSValueRef value,
                                  JSValueRef* exception) {
  return setClass(context, value, exception);
}

JSValueRef ElementWrapper::getClassName(JSContextRef context,
                                        JSValueRef* exception) {
  return getClass(context, exception);
}

bool ElementWrapper::setClass(JSContextRef context, JSValueRef value,
                              JSValueRef* exception) {
  JSC_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      context, exception, value,
      "Failed to execute 'setClass' on 'Element':value must be string");
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, false);
  
  auto stringValue = JSValueToStringCopy(context, value, nullptr);
  GetInstance<Element>()->SetAttribute("class", JSStringToMSCString(stringValue));
  JSStringRelease(stringValue);
  return true;
}

JSValueRef ElementWrapper::getClass(JSContextRef context,
                                    JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  MSCString&& classNameString = GetInstance<Element>()->classAttribute();

  auto jsString = JSStringCreateWithUTF8CString(classNameString.c_str());
  return JSValueMakeString(context, jsString);
}

// Readonly API
JSValueRef ElementWrapper::getStyle(JSContextRef context,
                                    JSValueRef* exception) {
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  auto css_style_declaration = GetInstance<Element>()->GetCSSStyleDeclaration();
  if (css_style_declaration->GetJSWrapper() == nullptr) {
    auto* wrapper = new CSSStyleDeclarationWrapper(css_style_declaration);
    wrapper->CreateJSObject(context);
  }
  return GET_JSOBJECT_FROM(css_style_declaration);
}

JSValueRef ElementWrapper::GetAttribute(JSContextRef context,
                                        JSObjectRef thisObject, size_t argc,
                                        const JSValueRef* argv,
                                        JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'getAttribute' on 'Element':1 argument required");
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeNull(context));
  
  auto attrName = JSValueToStringCopy(context, argv[0], nullptr);
  auto attrNameStr = JSStringToMSCString(attrName);
  JSStringRelease(attrName);
  
  blink::mt::PropValue attrValue;
  GetInstance<Element>()->GetAttribute(attrNameStr, attrValue);
  
  if (!attrValue.isA<blink::mt::PropValueType::String>()) {
    attrValue = "";
  }
  
  return PropValueToJS(context, attrValue);
}

JSValueRef ElementWrapper::SetAttribute(JSContextRef context,
                                        JSObjectRef thisObject, size_t argc,
                                        const JSValueRef* argv,
                                        JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 2,
      "Failed to execute 'setAttribute' on 'Element':2 arguments required");

  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeUndefined(context));
  
  auto attr_name_value = JSValueToStringCopy(context, argv[0], nullptr);
  auto attr_name = JSStringToMSCString(attr_name_value);
  JSStringRelease(attr_name_value);
  
  std::shared_ptr<Element> element = GetInstance<Element>();
  blink::mt::PropValue attr_value = JSValueToPropValue(context, argv[1]);
  
  if (attr_value.isA<blink::mt::PropValueType::Boolean>()) {
    element->SetAttribute(attr_name, attr_value.boolValue() ? "true" : "false");
  } else if (attr_value.isA<blink::mt::PropValueType::Number>()) {
    auto value = attr_value.numberValue();
    std::string value_str;
    // 针对整数做特殊处理
    if (std::abs(value - std::round(value)) < std::numeric_limits<double>::epsilon()) {
      value_str = std::to_string(static_cast<int>(std::round(value)));
    } else {
      value_str = std::to_string(value);
    }
    element->SetAttribute(attr_name, value_str);
  } else {
    element->SetAttribute(attr_name, attr_value);
  }
  
  return JSValueMakeUndefined(context);
}

JSValueRef ElementWrapper::RemoveAttribute(JSContextRef context,
                                           JSObjectRef thisObject, size_t argc,
                                           const JSValueRef* argv,
                                           JSValueRef* exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'removeAttribute' on 'Element':1 argument required");
  
  JSC_API_GUARD_INSTANCE_AVAILABLE(this, JSValueMakeUndefined(context));

  auto attrName = JSValueToStringCopy(context, argv[0], nullptr);
  auto attrNameStr = JSStringToMSCString(attrName);
  JSStringRelease(attrName);

  auto success = GetInstance<Element>()->RemoveAttribute(attrNameStr);

  return JSValueMakeUndefined(context);
}

}  // namespace native_dom
}  // namespace msc

#endif
