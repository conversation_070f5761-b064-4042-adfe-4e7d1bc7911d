//
//  event_listener_jsc.h
//  MSCRenderer
//
//  Created by ji<PERSON><PERSON> <PERSON><PERSON> on 2025/6/4.
//

#ifndef event_listener_jsc_h
#define event_listener_jsc_h

#include "../event.h"
#include "event_wrapper.h"
#include "touch_event_wrapper.h"

#if USE_JSC

namespace msc {
namespace native_dom {

static inline void fireEventToJSC(JSObjectRef listener, JSContextRef context, const std::shared_ptr<Event>& event) {
  auto currentTarget = GET_JSOBJECT_FROM(event->currentTarget());
  
  if (event->GetJSWrapper() == nullptr) {
    auto event_category = event->GetEventCategory();
    switch (event_category) {
      case Event::EventCategory::TOUCH:
      case Event::EventCategory::TAP: {
        TouchEventWrapper *touch_event_wrapper = new TouchEventWrapper(static_pointer_cast<TouchEvent>(event));
        touch_event_wrapper->CreateJSObject(context);
        break;
      }
      case Event::EventCategory::COMPONENT:
      default: {
        EventWrapper *event_wrapper = new EventWrapper(event);
        event_wrapper->CreateJSObject(context);
        break;
      }
    }
  }
  auto eventObject = GET_JSOBJECT_FROM(event);

  JSValueRef args[] = {
    eventObject
  };
  
  JSValueRef exc = nullptr;
  JSValueRef result = JSObjectCallAsFunction(context, listener, currentTarget, 1, args, &exc);
  if (JSValueIsNull(context, result) || JSValueIsUndefined(context, result)) {
  }
  
}


class JSCEventListener : public EventListener {
public:
  JSCEventListener(bool capture, JSObjectRef listener, JSContextRef context) : EventListener(capture), listener_(listener), context_(context) {}
  ~JSCEventListener() override {
  }

  void FireEvent(const std::shared_ptr<Event>& event) override {
    if ((capture_ && event->stage() ==  Event::EventStage::CAPTURING) || (!capture_ && event->stage() == Event::EventStage::BUBBLING)) {
      fireEventToJSC(listener_, context_, event);
    }
  }

  EventListenerType type() override {
    return EventListenerType::JSC_EVENT_LISTENER_TYPE;
  }

  bool IsSameObject(EventListener* listener) override {
    if (listener->type() == EventListenerType::JSC_EVENT_LISTENER_TYPE) {
        auto jsc_listener = static_cast<JSCEventListener*>(listener);
        return this->capture() == listener->capture() && JSValueIsEqual(context_, this->listener_, jsc_listener->listener_, nullptr);
    } else {
        return false;
    }
}
  
private:
  JSObjectRef listener_;
  JSContextRef context_;
};

}
}

#endif

#endif /* event_listener_jsc_h */
