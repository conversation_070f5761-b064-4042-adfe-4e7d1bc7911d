//
//  IntersectionObserverWrapper.cpp
//  MSC
//

#include "intersection_observer_wrapper.h"

#include "../../final_action.h"
#include "../document.h"
#include "jsc_js_callback.h"
#include "native_dom/bridge/message_proxy.h"

#if USE_JSC

namespace msc {
namespace native_dom {

#define JSC_NATIVE_DOM_FUNCTION_DEF(V)                                     \
  V(IntersectionObserverWrapper, IntersectionObserver, "observe", Observe) \
  V(IntersectionObserverWrapper, IntersectionObserver, "disconnect", Disconnect)

#define JSC_NATIVE_DOM_FUNCTION_INITIALIZER(V) \
  JSC_NATIVE_DOM_FUNCTION_DEF(V)               \
  JSC_STATIC_FUNCTION_END

JSC_NATIVE_DOM_FUNCTION_DEF(JSC_OBJ_FUNCTION_IMPL);

JSObjectRef IntersectionObserverWrapper::CreateIntersectionObserver(
    JSContextRef context, JSObjectRef constructor, size_t argc,
    const JSValueRef argv[], J<PERSON><PERSON><PERSON><PERSON><PERSON> *exception) {
  // the JS code to create an IntersectionObserver would be like this:
  //
  // query.select('.the-scroll-view')
  //   .scrollOffset((res) => { global.q.first_ele = res.first_ele; });
  // let options = {
  //   // root is required. it's either a scroll-view element or the document
  //   // and the document is for the viewport, however the viewport is actually
  //   // the document.body
  //   root: global.q.first_ele,
  //   rootMargins: "0px",
  //   thresholds: null,
  // };
  // let observer = new native.IntersectionObserver(callback, options);
  // observer.observe(global.q.first_ele.firstChild.nextSibling.nextSibling);

  blink::mt::CreateIntersectionObserverParams params;

  // check args
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR_RETURN_NULL(
      context, exception, argc, 2,
      "Failed to execute 'createIntersectionObserver' on "
      "'IntersectionObserver':2 arguments required");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR_RETURN_NULL(
      context, exception, argv[0],
      "Failed to execute 'createIntersectionObserver' on "
      "'IntersectionObserver':1st argument must be object");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR_RETURN_NULL(
      context, exception, argv[0],
      "Failed to execute 'createIntersectionObserver' on "
      "'IntersectionObserver':2nd argument must be object");

  auto callback = JSValueToObject(context, argv[0], nullptr);
  if (!JSObjectIsFunction(context, callback)) {
    return nullptr;
  }
  auto options = JSValueToObject(context, argv[1], nullptr);

  // get options
  auto key_root = JSStringCreateWithUTF8CString("root");
  auto key_root_margins = JSStringCreateWithUTF8CString("rootMargin");
  auto key_thresholds = JSStringCreateWithUTF8CString("threshold");
  auto key_length = JSStringCreateWithUTF8CString("length");
  auto guard_key_release = defer([=]() {
    JSStringRelease(key_root);
    JSStringRelease(key_root_margins);
    JSStringRelease(key_thresholds);
    JSStringRelease(key_length);
  });

  // get options.root
  if (not JSObjectHasProperty(context, options, key_root)) {
    return nullptr;  // root is required
  }
  auto root = JSObjectGetProperty(context, options, key_root, nullptr);
  JSObjectRef root_object = JSValueToObject(context, root, nullptr);
  auto root_wrapper = getJSCInterface(root_object);
  if (root_wrapper == nullptr) {
    return nullptr;
  }
  auto root_element = root_wrapper->GetInstance<Element>();
  if (root_element == nullptr) {
    return nullptr;
  }
  params.root_tag_ = root_element->GetNodeId();
  params.is_viewport_ = root_element->nodeTag() == Tag::DOCUMENT;

  auto document = root_element->documentElement();
  if (document == nullptr) {
    return nullptr;
  }

  // get the options.rootMargins
  if (JSObjectHasProperty(context, options, key_root_margins)) {
    auto root_margins =
        JSObjectGetProperty(context, options, key_root_margins, nullptr);
    if (JSValueIsString(context, root_margins)) {
      JSStringRef js_str = JSValueToStringCopy(context, root_margins, nullptr);
      params.margins_str_ = JSStringToMSCString(js_str);
      JSStringRelease(js_str);
    } else if (JSValueIsArray(context, root_margins)) {
      auto root_margins_object =
          JSValueToObject(context, root_margins, nullptr);
      auto length = JSObjectGetProperty(context, root_margins_object,
                                        key_length, nullptr);
      if (JSValueToNumber(context, length, nullptr) == 4) {
        params.margins_.reserve(4);
        for (int i = 0; i < 4; ++i) {
          auto value = JSObjectGetPropertyAtIndex(context, root_margins_object,
                                                  i, nullptr);
          if (JSValueIsNumber(context, value)) {
            auto value_num = JSValueToNumber(context, value, nullptr);
            params.margins_.push_back(value_num);
          }
        }
      }
    }
  }

  // get options.thresholds
  if (JSObjectHasProperty(context, options, key_thresholds)) {
    auto thresholds =
        JSObjectGetProperty(context, options, key_thresholds, nullptr);
    auto thresholds_object = JSValueToObject(context, thresholds, nullptr);
    if (JSValueIsArray(context, thresholds_object)) {
      auto length =
          JSObjectGetProperty(context, thresholds_object, key_length, nullptr);
      auto length_int = JSValueToNumber(context, length, nullptr);
      params.thresholds_.reserve(length_int);
      for (int i = 0; i < length_int; ++i) {
        auto value =
            JSObjectGetPropertyAtIndex(context, thresholds_object, i, nullptr);
        if (JSValueIsNumber(context, value)) {
          auto value_num = JSValueToNumber(context, value, nullptr);
          params.thresholds_.push_back(value_num);
        }
      }
    }
  }
  if (params.thresholds_.empty()) {
    params.thresholds_.push_back(0.0f);
  }

  // e.g.
  // callback = (entries) => {
  //   entries.forEach((entry) => {
  //     console.log(entry.boundingClientRect);  // DOMRectReadOnly
  //     console.log(entry.intersectionRatio);
  //     console.log(entry.intersectionRect);  // DOMRectReadOnly
  //     console.log(entry.isIntersecting);
  //     console.log(entry.rootBounds);  // DOMRectReadOnly
  //     console.log(entry.target);  // Element
  //     console.log(entry.time);
  //   });
  // };
  auto callback_c = [context, callback](
                        const std::vector<blink::mt::IntersectionEntryData>
                            &entries) {
    // make entries
    std::vector<JSValueRef> entries_res;
    entries_res.reserve(entries.size());

    JSStringRef key_x = JSStringCreateWithUTF8CString("x");
    JSStringRef key_y = JSStringCreateWithUTF8CString("y");
    JSStringRef key_width = JSStringCreateWithUTF8CString("width");
    JSStringRef key_height = JSStringCreateWithUTF8CString("height");
    JSStringRef key_bounding_client_rect =
        JSStringCreateWithUTF8CString("boundingClientRect");
    JSStringRef key_intersection_ratio =
        JSStringCreateWithUTF8CString("intersectionRatio");
    JSStringRef key_intersection_rect =
        JSStringCreateWithUTF8CString("intersectionRect");
    JSStringRef key_is_intersecting =
        JSStringCreateWithUTF8CString("isIntersecting");
    JSStringRef key_root_bounds = JSStringCreateWithUTF8CString("rootBounds");

    for (const auto &entry : entries) {
      JSObjectRef entry_obj = JSObjectMake(context, nullptr, nullptr);

      {  // boundingClientRect
        JSObjectRef bounding_client_rect =
            JSObjectMake(context, nullptr, nullptr);

        JSObjectSetProperty(
            context, bounding_client_rect, key_x,
            JSValueMakeNumber(context, entry.bounding_client_rect.x),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, bounding_client_rect, key_y,
            JSValueMakeNumber(context, entry.bounding_client_rect.y),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, bounding_client_rect, key_width,
            JSValueMakeNumber(context, entry.bounding_client_rect.width),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, bounding_client_rect, key_height,
            JSValueMakeNumber(context, entry.bounding_client_rect.height),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(context, entry_obj, key_bounding_client_rect,
                            bounding_client_rect, kJSPropertyAttributeNone,
                            nullptr);
      }

      // intersectionRatio
      JSObjectSetProperty(context, entry_obj, key_intersection_ratio,
                          JSValueMakeNumber(context, entry.intersection_ratio),
                          kJSPropertyAttributeNone, nullptr);

      // intersectionRect
      if (entry.is_intersecting) {
        JSObjectRef intersection_rect = JSObjectMake(context, nullptr, nullptr);
        JSObjectSetProperty(
            context, intersection_rect, key_x,
            JSValueMakeNumber(context, entry.intersection_rect.x),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, intersection_rect, key_y,
            JSValueMakeNumber(context, entry.intersection_rect.y),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, intersection_rect, key_width,
            JSValueMakeNumber(context, entry.intersection_rect.width),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, intersection_rect, key_height,
            JSValueMakeNumber(context, entry.intersection_rect.height),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(context, entry_obj, key_intersection_rect,
                            intersection_rect, kJSPropertyAttributeNone,
                            nullptr);
      } else {
        JSObjectSetProperty(context, entry_obj, key_intersection_rect,
                            JSValueMakeNull(context), kJSPropertyAttributeNone,
                            nullptr);
      }

      // isIntersecting
      JSObjectSetProperty(context, entry_obj, key_is_intersecting,
                          JSValueMakeBoolean(context, entry.is_intersecting),
                          kJSPropertyAttributeNone, nullptr);

      {  // rootBounds
        JSObjectRef root_bounds_rect = JSObjectMake(context, nullptr, nullptr);
        JSObjectSetProperty(
            context, root_bounds_rect, key_x,
            JSValueMakeNumber(context, entry.root_bounds_rect.x),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, root_bounds_rect, key_y,
            JSValueMakeNumber(context, entry.root_bounds_rect.y),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, root_bounds_rect, key_width,
            JSValueMakeNumber(context, entry.root_bounds_rect.width),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(
            context, root_bounds_rect, key_height,
            JSValueMakeNumber(context, entry.root_bounds_rect.height),
            kJSPropertyAttributeNone, nullptr);
        JSObjectSetProperty(context, entry_obj, key_root_bounds,
                            root_bounds_rect, kJSPropertyAttributeNone,
                            nullptr);
      }

      // push the entry object to entries_res
      entries_res.push_back(entry_obj);
    }

    JSStringRelease(key_x);
    JSStringRelease(key_y);
    JSStringRelease(key_width);
    JSStringRelease(key_height);
    JSStringRelease(key_bounding_client_rect);
    JSStringRelease(key_intersection_ratio);
    JSStringRelease(key_intersection_rect);
    JSStringRelease(key_is_intersecting);
    JSStringRelease(key_root_bounds);

    // call the callback with entries_res
    JSValueRef entries_array = JSObjectMakeArray(context, entries_res.size(),
                                                 entries_res.data(), nullptr);
    JSValueRef args[] = {entries_array};
    JSObjectCallAsFunction(context, callback, JSContextGetGlobalObject(context), 1, args, nullptr);
  };

  auto callable = new JSCallbackForIntersectionObserver(std::move(callback_c));
  auto js_callback = new JSCJSCallback(std::unique_ptr<JSCallable>(callable),
                                       context, callback);
  JSCallbackInfo callback_info{
      root_element->ProtectJSCallback(std::unique_ptr<JSCallback>(js_callback)),
      params.root_tag_,
      true,
      false,
      true,
      JSCallbackInfo::Type::IntersectionObserver};

  // create the observer
  auto observer =
      document->CreateIntersectionObserver(std::move(params), callback_info);
  // create the wrapper, and return the JS object
  auto wrapper = new IntersectionObserverWrapper(observer);
  return const_cast<JSObjectRef>(wrapper->CreateJSObject(context));
}

JSClassRef IntersectionObserverWrapper::SetupJSClass(JSClassRef parent_class) {
  static JSStaticFunction static_functions[] = {
      JSC_NATIVE_DOM_FUNCTION_INITIALIZER(JSC_STATIC_FUNCTION)};
  static JSStaticValue static_values[] = {};

  return registerJSClass("IntersectionObserver", static_functions,
                         static_values, parent_class);
}

JSValueRef IntersectionObserverWrapper::CreateJSObject(JSContextRef context) {
  JSObjectRef ret = tryCreateJSObject(context, "IntersectionObserver", this, false);
  return ret;
}

JSValueRef IntersectionObserverWrapper::Observe(JSContextRef context,
                                                JSObjectRef thisObject,
                                                size_t argc,
                                                const JSValueRef *argv,
                                                JSValueRef *exception) {
  JSC_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      context, exception, argc, 1,
      "Failed to execute 'observe' on 'IntersectionObserver':1 argument "
      "required");
  JSC_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
      context, exception, argv[0],
      "Failed to execute 'observe' on 'IntersectionObserver':1st argument must "
      "be object");

  do {
    auto target_object = JSValueToObject(context, argv[0], nullptr);
    if (!target_object) {
      break;
    }
    auto target_wrapper = getJSCInterface(target_object);
    if (!target_wrapper) {
      break;
    }
    auto target_element = target_wrapper->GetInstance<Element>();
    if (!target_element) {
      break;
    }
    auto document = target_element->documentElement();
    if (!document) {
      break;
    }
    auto observer = GetInstance<IntersectionObserver>();

    if (!(observer and
          document->GetElementByNodeId(observer->GetParams().root_tag_))) {
      break;
    }
    document->IntersectionObserverObserve(observer->GetId(),
                                          target_element->GetNodeId());
  } while (false);

  return JSValueMakeUndefined(context);
}

JSValueRef IntersectionObserverWrapper::Disconnect(JSContextRef context,
                                                   JSObjectRef thisObject,
                                                   size_t argc,
                                                   const JSValueRef *argv,
                                                   JSValueRef *exception) {
  auto observer = GetInstance<IntersectionObserver>();
  if (observer) {
    observer->message_proxy_->SendIntersectionObserverObserveMessage(
                                                                     observer->page_id_, observer->GetId(), 0);
  }
  return JSValueMakeUndefined(context);
}

}  // namespace native_dom
}  // namespace msc

#endif
