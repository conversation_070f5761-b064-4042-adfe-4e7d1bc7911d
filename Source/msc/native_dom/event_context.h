//
//  event_context.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef EventContext_h
#define EventContext_h

namespace msc {
namespace native_dom {

class ContainerNode;

class EventContext {
public:
  EventContext() = default;
  virtual ~EventContext() = default;
  
  void SetHoverableTarget(ContainerNode *hoverable_target) { hoverable_target_ = hoverable_target; }
  ContainerNode *hoverable_target() const { return hoverable_target_; }
  
  void SetJSContext(void *context) {
    js_context_ = context;
  }
  
  void *jsContext() const { return js_context_; }
  
protected:
  ContainerNode *hoverable_target_ = nullptr;
  void *js_context_ = nullptr;
};

}  // namespace native_dom
}  // namespace msc

#endif /* EventContext_h */
