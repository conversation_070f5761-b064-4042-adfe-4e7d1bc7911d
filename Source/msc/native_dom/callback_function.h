//
//  callback_function.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef CALLBACK_FUNCTION_H
#define CALLBACK_FUNCTION_H

namespace msc {
namespace native_dom {

class CallbackFunction {
public:
  CallbackFunction() {}
  virtual ~CallbackFunction() {}
  
  template<typename Derived, typename T>
  T* GetCallbackPtr() {
    retrun ((Derived *)(this))->callback();
  }
  
  template<typename Derived, typename T>
  T GetCallback() {
    return ((Derived *)(this))->callback();
  }
};

}
}

#endif /* CallbackFunction_h */
