//
//  script_wrappable.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/16.
//

#ifndef SCRIPT_WRAPPABLE_H
#define SCRIPT_WRAPPABLE_H

#include "native_dom_define.h"
#include "msc_string.h"
#include "../logger.h"

#if USE_V8
#include "../../../libs/include_v8.0/v8.h"
#endif

namespace msc {
namespace native_dom {

#if USE_JSI //Android Uses JSI
class NativeDOMObjectHostObject;
using JS_WRAPPER_BASE_TYPE = NativeDOMObjectHostObject *;
#elif USE_JSC  //iOS uses JSC
class NativeDOMObjectWrapper;
using JS_WRAPPER_BASE_TYPE = NativeDOMObjectWrapper *;
#elif USE_V8
class NativeDOMObjectWrapper;
using JS_WRAPPER_BASE_TYPE = NativeDOMObjectWrapper *;
#endif

#if USE_V8
#define GET_JSOBJECT_FROM(isolate, wrappable) (wrappable->GetJSWrapper() ? wrappable->GetJSWrapper()->GetJSObject() : v8::Local<v8::Object>())
#else
#define GET_JSOBJECT_FROM(wrappable) (wrappable->GetJSWrapper() ? wrappable->GetJSWrapper()->GetJSObject() : nullptr)
#endif

class ScriptWrappable {
public:
  ScriptWrappable() : wrapper_(nullptr) {
      MSC_RENDERER_LOG_DEBUG("[native_dom] Constructor of script_wrappable: %p, total_count: %ld", this, ++total_count_);
  }

  virtual ~ScriptWrappable() {
    MSC_RENDERER_LOG_DEBUG("Destructor of script_wrappable: %p, total_count: %ld", this, --total_count_);
  }

  JS_WRAPPER_BASE_TYPE GetJSWrapper() {
    return wrapper_;
  }
  JS_WRAPPER_BASE_TYPE GetJSWrapper() const {
    return wrapper_;
  }
  void SetJSWrapper(JS_WRAPPER_BASE_TYPE wrapper) {
    wrapper_ = wrapper;
  }
    
  virtual void ClearJSWrapper(bool clear_jsobject = true);
  
  void DestroyJSWrapper();
  
  void ForceGCDebug();
  
private:
  JS_WRAPPER_BASE_TYPE wrapper_;

#if DEBUG
  static int total_count_;
#endif

};

}  // namespace native_dom
}  // namespace msc
#endif /* SCRIPT_WRAPPABLE_H */
