//
//  event.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef Event_h
#define Event_h

#include "event_context.h"
#include "msc_string.h"
#include "msc_time.h"
#include "touch.h"
#include "props.h"
#include "script_wrappable.h"
#include "event_data.h"

namespace msc {
namespace native_dom {

class EventTarget;

class Event : public ScriptWrappable {
public:
  static const std::string kStopPropagation;
  
  enum EventPhase {
      NONE = 0,
      CAPTURING_PHASE = 1,
      AT_TARGET = 2,
      BUBBLING_PHASE = 3,
  };

  enum EventStage {
      CAPTURING,
      BUBBLING
  };
  
  enum EventCategory {
    DEFAULT = 0,
    TOUCH = 1,
    TAP = 2,
    COMPONENT = 3
  };
  
  Event(EventData &event_data) :
    event_phase_(EventPhase::NONE),
    target_(nullptr),
    current_target_(nullptr),
    timestamp_(event_data.GetTimeStamp()),
    type_name_(event_data.GetEventName()),
    detail_(event_data.GetDetail()),
    bubbles_(false),
    propagation_stopped_(false),
    default_handled_(false)
  {
    MSC_RENDERER_LOG_DEBUG("constructor of Event: %p", this);
  };
  
  virtual ~Event() = default;

  
  EventPhase eventPhase()
  {
      return event_phase_;
  }

  void SetEventPhase(EventPhase phase)
  {
      event_phase_ = phase;
  }

  EventTarget* target()
  {
      return target_;
  }

  void SetTarget(EventTarget* target)
  {
      target_ = target;
  }

  EventTarget* currentTarget()
  {
      return current_target_;
  }

  void SetCurrentTarget(EventTarget* target)
  {
      current_target_ = target;
  }

  void SetDetail(std::shared_ptr<const blink::mt::Props> detail)
  {
    detail_ = detail;
  }

  std::shared_ptr<const blink::mt::Props> detail()
  {
      return detail_;
  }

  bool bubbles()
  {
      return bubbles_;
  }

  void SetBubbles(bool bubbles)
  {
      bubbles_ = bubbles;
  }

  MSCString typeName()
  {
      return type_name_;
  }

  void SetTypeName(const MSCString& typeName)
  {
      type_name_ = typeName;
  }

  MSCTimePoint timestamp()
  {
      return timestamp_;
  }

  // TODO(Gin): tract to EventContext
  void SetStage(EventStage stage)
  {
      stage_ = stage;
  }

  EventStage stage()
  {
      return stage_;
  }

  void SetStopPropagation() { propagation_stopped_ = true; }
  void ResetStopPropagation() { propagation_stopped_ = false; }
  bool propagationStopped() const
  {
      return propagation_stopped_;
  }

  bool defaultHandled() const { return default_handled_; }
  void SetDefaultHandled() { default_handled_ = true; }
  
  virtual EventCategory GetEventCategory() { return EventCategory::DEFAULT; }

protected:
private:
  EventPhase event_phase_;
  EventTarget* target_;
  EventTarget* current_target_;

  MSCTimePoint timestamp_;
  std::shared_ptr<const blink::mt::Props> detail_;
  MSCString type_name_;

  EventStage stage_;
  bool bubbles_;
  bool propagation_stopped_;
  bool default_handled_;
};

class TouchEvent: public Event {
public:
    TouchEvent(EventData &event_data) :
      Event(event_data),
      touches_(std::move(event_data.GetTouches())),
      changed_touches_(std::move(event_data.GetTouches())) {
    };
  
    virtual ~TouchEvent() {};

    std::vector<Touch>& touches() { return touches_; }
    void SetTouches(std::vector<Touch>& touches) { touches_ = std::move(touches); }

    std::vector<Touch>& changedTouches() { return changed_touches_; }
    void SetChangedTouches(std::vector<Touch>& changedTouches) { changed_touches_ = changedTouches; }
  
    Event::EventCategory GetEventCategory() override { return Event::EventCategory::TOUCH; }
    
private:
    std::vector<Touch> touches_;
    std::vector<Touch> changed_touches_;
};

class TapEvent: public TouchEvent {
public:
    TapEvent(EventData &event_data): TouchEvent(event_data) {}
    virtual ~TapEvent() {};

    Event::EventCategory GetEventCategory() override { return Event::EventCategory::TAP; }
};

class ComponentEvent: public Event {
public:
  ComponentEvent(EventData &event_data): Event(event_data) {}
  virtual ~ComponentEvent() {};
  
  Event::EventCategory GetEventCategory() override { return Event::EventCategory::COMPONENT; }
};

}  // namespace native_dom
}  // namespace msc
#endif /* Event_h */
