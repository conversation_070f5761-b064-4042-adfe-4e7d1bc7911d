//
//  native_dom_callback.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/13.
//

#ifndef NativeDOMCallback_h
#define NativeDOMCallback_h

#include "msc_string.h"

namespace msc {
namespace native_dom {

class NativeDOMCallback {
public:
  
  static uint32_t generateNewCallbackId() {
    static uint32_t callbackId = 0;
    return ++callbackId;
  }
  
  NativeDOMCallback(int pageId, int elementId, bool isOnce)
    : m_pageId(pageId),
      m_elementId(elementId),
      m_isOnce(isOnce),
      m_callbackId(generateNewCallbackId())
  {}
  
  int pageId() const {
    return m_pageId;
  }
  
  int elementId() const {
    return m_elementId;
  }
  
  bool isOnce() const {
    return m_isOnce;
  }
  
  uint32_t callbackId() const {
    return m_callbackId;
  }
  
private:
  int m_pageId;
  int m_elementId;
  bool m_isOnce;
  uint32_t m_callbackId;
};

}  // namespace native_dom
}  // namespace msc

#endif /* NativeDOMCallback_h */
