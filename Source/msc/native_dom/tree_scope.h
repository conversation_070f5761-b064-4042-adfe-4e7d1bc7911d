//
//  tree_scope.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/14.
//

#ifndef TreeScope_hpp
#define TreeScope_hpp

#include "msc_string.h"
#include "element_type.h"

#include <unordered_map>

namespace msc {
namespace native_dom {

class Element;
class Document;
class ContainerNode;

//class TreeScope {
//public:
//  
//  std::shared_ptr<Element> CreateElement(const MSCString& local_name);
//  
//  Document& GetDocument() const {
//    return *document_;
//  }
//
//protected:
//  explicit TreeScope(ContainerNode&, Document&);
//  explicit TreeScope(Document&);
//  virtual ~TreeScope() = default;
//  
//  void SetDocument(Document& document) { document_ = &document; }
//  void SetParentTreeScope(TreeScope&);
//  
//private:
//  Document* document_;
//  ContainerNode *root_node_;
//  TreeScope *parent_tree_scope_;
//};

class TreeScope {
public:
  TreeScope() = default;
  ~TreeScope() = default;
  
  template<typename ElementType, typename DocumentType>
  static ElementType* matchIdAttribute(ElementType* element, const MSCString& idStr)
  {
    if (element->idAttribute() == idStr)
      return element;
    
    if (element->nodeTag() == Tag::DOCUMENT)
      return matchIdAttribute<ElementType, DocumentType>(static_cast<DocumentType*>(element)->bodyElement(), idStr);
    
    for (auto* first = element->firstChild(); first != element->lastChild(); first = first->nextSibling()) {
      ContainerNode* result = matchIdAttribute<ElementType, DocumentType>(static_cast<ElementType*>(first), idStr);
      if (result)
        return static_cast<ElementType*>(result);
    }
    return nullptr;
  }
  
  template<typename ElementType, typename DocumentType>
  ContainerNode* Get(const MSCString& idStr, ContainerNode* root)
  {
    auto it = m_getElementById.find(idStr);
    if (it == m_getElementById.end()) {
      return nullptr;
    } else {
      auto* element = it->second.element();
      if (element)
        return element;
      
      return matchIdAttribute<ElementType, DocumentType>(static_cast<ElementType*>(root), idStr);
    }
  }

  void Add(const MSCString& idStr, ContainerNode*);
  void Remove(const MSCString& idStr, ContainerNode*);
  
private:
  class ElementIdEntry {
      public:
          ElementIdEntry()
              : m_element(nullptr)
          {
          }
          ContainerNode* element()
          {
              return m_element;
          }

          void setElement(ContainerNode* element)
          {
              m_element = element;
          }

      private:
          ContainerNode* m_element;
      };

      std::unordered_map<MSCString, ElementIdEntry> m_getElementById;
};
  

}  // namespace native_dom
}  // namespace msc

#endif /* TreeScope_hpp */
