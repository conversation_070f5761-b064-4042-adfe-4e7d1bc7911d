//
//  msc_time.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef MSCTime_h
#define MSCTime_h

#include <chrono>

namespace msc {
namespace native_dom {

using MSCTimePoint = std::chrono::time_point<std::chrono::system_clock>;
using MSCTimeDuration = std::chrono::milliseconds;
using MSCClock = std::chrono::system_clock;

static inline long long pointToMillisecondsFromEpoch(const MSCTimePoint& tp) {
    return std::chrono::duration_cast<std::chrono::milliseconds>(tp.time_since_epoch()).count();
}

}  // namespace native_dom
}  // namespace msc

#endif /* MSCTime_h */
