//
//  native_dom_define.h
//  MSC
//
//  Created by ji<PERSON><PERSON> zhang on 2025/2/13.
//

#ifndef NativeDOMDefine_h
#define NativeDOMDefine_h


#ifdef __APPLE__
#define USE_JSI 0
#define USE_JSC 1
#define USE_V8  0
#else
#define USE_JSI 0
#define USE_JSC 0
#define USE_V8  1

#endif


#define MSC_HAS_BUILTIN_EXPECT (__has_builtin(__builtin_expect))

// A macro to provide the compiler with branch prediction information.
#if MSC_HAS_BUILTIN_EXPECT
# define MSC_UNLIKELY(condition) (__builtin_expect(!!(condition), 0))
# define MSC_LIKELY(condition) (__builtin_expect(!!(condition), 1))
#else
# define MSC_UNLIKELY(condition) (condition)
# define MSC_LIKELY(condition) (condition)
#endif

#endif /* NativeDOMDefine_h */
