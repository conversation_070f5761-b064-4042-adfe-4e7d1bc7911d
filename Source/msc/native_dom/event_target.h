//
//  event_target.h
//  MSC
//
//  Created by ji<PERSON><PERSON> on 2025/1/10.
//

#ifndef EventTarget_h
#define EventTarget_h

#include "msc_string.h"
#include "event_listener.h"
#include "event_target_data.h"
#include "script_wrappable.h"

namespace msc {
namespace native_dom {

using EventTargetDataMap = std::unordered_map<MSCString, EventTargetData>;

class EventTarget : public ScriptWrappable {
public:
  EventTarget() = default;
  virtual ~EventTarget() = default;
  
  void AddEventListener(const MSCString& eventName, std::unique_ptr<EventListener> listener);
  void RemoveEventListener(const MSCString& eventName, EventListener* listener);
  
  std::vector<EventTarget*> generateEventPath();
  
  virtual bool eventListened(const MSCString& type) const;
  
protected:
  virtual EventTargetDataMap* GetEventTargetData() = 0;
  virtual void EnsureEventTargetData() = 0;
  
  virtual void OnListenerFromZeroToOne(const MSCString&) {}
  virtual void OnListenerFromOneToZero(const MSCString&) {}
};


}  // namespace native_dom
}  // namespace msc

#endif /* EventTarget_h */
