//
//  properties_wrapper.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/17.
//

#ifndef PropertiesWrapper_h
#define PropertiesWrapper_h

#include "msc_string.h"

#include <unordered_map>
#include "props.h"

namespace msc {
namespace native_dom {

class PropertiesWrapper {
public:
  PropertiesWrapper() = default;
  ~PropertiesWrapper() {
      if (m_attributes != nullptr) {
          delete m_attributes;
      }
  }
  
  bool SetAttribute(const MSCString &key, const MSCString &value);
  bool SetAttribute(const MSCString& key, const blink::mt::PropValue &value);
  
  bool GetAttribute(const MSCString &key, blink::mt::PropValue &value);
  bool RemoveAttribute(const MSCString &key);
  bool hasAttribute(const MSCString& key);
  
  void populate(blink::mt::PropsBuilder &propsBuilder);
  
private:
  void EnsureAttribute();
  std::unordered_map<MSCString, MSCString> *m_attributes = nullptr;
  blink::mt::Pro<PERSON><PERSON><PERSON>er props_builder_;
};

}  // namespace native_dom
}  // namespace msc


#endif /* PropertiesWrapper_h */
