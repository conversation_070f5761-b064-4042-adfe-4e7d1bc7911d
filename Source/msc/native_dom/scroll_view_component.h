//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/4/7.
//

#ifndef MSC_ANDROID_SCROLL_VIEW_COMPONENT_H
#define MSC_ANDROID_SCROLL_VIEW_COMPONENT_H

#include "view_component.h"

namespace msc {
namespace native_dom {

class ScrollViewComponent : public ViewComponent {
public:
    ScrollViewComponent(ContainerNode *root, Tag tag = Tag::MSC_SCROLL_VIEW) : ViewComponent(root, tag) {};
    virtual ~ScrollViewComponent() override {};

    bool IsContainerElement() const override {
        return true;
    }
};

} // native_dom
} // msc

#endif //MSC_ANDROID_SCROLL_VIEW_COMPONENT_H
