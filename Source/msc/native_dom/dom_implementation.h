//
//  dom_implementation.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef DOMImplementation_h
#define DOMImplementation_h

#include "element.h"
#include "document.h"
#include "script_wrappable.h"

#include <chrono>

namespace msc {
namespace native_dom {

class DOMImplementation : public ScriptWrappable {
public:
  DOMImplementation(std::shared_ptr<MessageProxy> messageProxy) : messageProxy_(messageProxy) {
    MSC_RENDERER_LOG_DEBUG("constructor of DOMImplementation: %p", this);
  };
  
  std::shared_ptr<MessageProxy> GetMessageProxy() { return messageProxy_; }
  
  std::shared_ptr<Document> CreateDocument(int pageId);
  
private:
  std::shared_ptr<MessageProxy> messageProxy_;

public:
  static const std::string kCreateDocument;
  static const std::string kCreateIntersectionObserver;
};

}  // namespace native_dom
}  // namespace msc


#endif /* DOMImplementation_h */
