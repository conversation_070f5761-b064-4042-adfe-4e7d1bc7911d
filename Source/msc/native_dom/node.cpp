//
//  Node.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/13.
//

#include "node.h"
#include "container_node.h"
#include "element.h"

namespace {

//STATIC_ASSERT((unsigned)msc::native_dom::Tag::TAG_COUNT < (1 << 8));

// static const char* kTag = "ndom";

// TODO: 目前只有 ContainerNode 继承 Node，所以不需要判断直接强制转换
template<typename Target, typename Source>
const Target* dynamicDowncast(const Source* s)
{
    return static_cast<const Target*>(s);
}

// TODO: 目前只有 ContainerNode 继承 Node，所以不需要判断直接强制转换
template<typename Target, typename Source>
Target* dynamicDowncast(Source* s)
{
    return static_cast<Target*>(s);
}

} // namespace


namespace msc {
namespace native_dom {

using namespace std::string_literals;
const std::string Node::kAppendChild = "appendChild"s;
const std::string Node::kInsertBefore = "insertBefore"s;
const std::string Node::kRemove = "remove"s;
const std::string Node::kRemoveChild = "removeChild"s;
const std::string Node::kAddEventListener = "addEventListener"s;
const std::string Node::kRemoveEventListener = "removeEventListener"s;

Node::Node()
    : node_tag_(0)
    , connected_(false)
    , mounted_(false)
    // Feature
    , eat_text_node_feature_(false) //TODO
    , text_nesting_feature_(false) //TODO
    // Current node is be eated or not
    , text_node_feature_eated_(false)
    , first_appear_on_capture_(false)
    , first_appear_on_bubble_(false)
{
  node_id_ = Node::getNextNodeId();
}

int Node::getNextNodeId() {
  static int s_nextNodeId = 3;
  auto tag = s_nextNodeId;
  if (tag % 10 == 1) {
    tag += 2;
  }
  s_nextNodeId = tag + 2;
  return tag;
}

Element* Node::parentElement() const {
  if (nodeType() == NodeType::kElementNode) {
    return dynamicDowncast<Element>(parentNode());
  }

  return nullptr;
}

Node* Node::firstChild() const
{
    auto* containerNode = dynamicDowncast<ContainerNode>(this);
    return containerNode ? containerNode->firstChild() : nullptr;
}

Node* Node::lastChild() const
{
    auto* containerNode = dynamicDowncast<ContainerNode>(this);
    return containerNode ? containerNode->lastChild() : nullptr;
}

void Node::SetNodeTag(Tag t)
{
    node_tag_ = (unsigned)t;

    // TagLogD(kTag, "{} Tag:[{}] id:[{}]", __PRETTY_FUNCTION__,
    //         ENUM_NAME(t).c_str(), idStr().c_str());
}

void Node::AppendChild(Node* child)
{
    if (auto* containerNode = dynamicDowncast<ContainerNode>(this))
        containerNode->AppendChild(dynamicDowncast<ContainerNode>(child));
}

void Node::RemoveChild(Node* child)
{
    if (auto* containerNode = dynamicDowncast<ContainerNode>(this))
        containerNode->RemoveChild(dynamicDowncast<ContainerNode>(child));
}

void Node::InsertBefore(Node* child, Node* anchor)
{
    if (auto* containerNode = dynamicDowncast<ContainerNode>(this))
        containerNode->InsertBefore(dynamicDowncast<ContainerNode>(child), dynamicDowncast<ContainerNode>(anchor));
}

void Node::Remove()
{
    if (auto* containerNode = dynamicDowncast<ContainerNode>(this))
        containerNode->Remove();
}

MSCString Node::nodeName()
{
    if (auto* containerNode = dynamicDowncast<ContainerNode>(this))
        return containerNode->nodeName();
    return MSCString();
}

int Node::nodeType() const
{
    if (auto* containerNode = dynamicDowncast<ContainerNode>(this))
        return containerNode->nodeType();
    return -1;
}

void Node::SetTextContent(const MSCString& text) {
  //TODO: node.cc
  
//  switch (nodeType()) {
//      case NodeType::TEXT_NODE:
//      setNodeValue(text);
//  }
}

const MSCString& Node::TextContent() const {
  return g_EmptyString;
}


//Value NodeHostObject::get(Runtime& runtime, const PropNameID& name) {
//  auto propName = name.utf8(runtime);
//
//
//  if (propName == "nodeName") {
//    return facebook::jsi::String::createFromUtf8(runtime, GetInstance<Node>()->nodeName());
//  }
//  else if (propName == Node::kAppendChild.c_str()) {
//    return jsi::Function::createFromHostFunction(
//                                                 runtime,
//                                                 name,
//                                                 1,
//                                                 [this](jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count) -> jsi::Value {
//                                                   if (count != 1 || !args[0].isObject()) {
//                                                     throw jsi::JSError(rt, "AppendChild requires a single Node object argument");
//                                                   }
//                                                   auto child = args[0].asObject(rt).getHostObject<NodeHostObject>(rt);
//
//                                                   if (child) {
//                                                     GetInstance<Node>()->AppendChild(child->GetInstance<Node>().get());
//                                                     return args[0].asObject(rt);
//                                                   }
//                                                   return jsi::Value::undefined();
//                                                 }
//                                                 );
// } else if (propName == Node::kInsertBefore.c_str()) {
//    return jsi::Function::createFromHostFunction(
//                                                 runtime,
//                                                 name,
//                                                 2,
//                                                 [this](jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count) -> jsi::Value {
//                                                   if (count <= 1 || !args[0].isObject()) {
//                                                     throw jsi::JSError(rt, "InsertBefore requires 2 argument");
//                                                   }
//                                                   auto child = args[0].asObject(rt).getHostObject<NodeHostObject>(rt);
//                                                   auto anchor = args[1].asObject(rt).getHostObject<NodeHostObject>(rt);
//                                                   if (child && anchor) {
//                                                     GetInstance<Node>()->InsertBefore(child->GetInstance<Node>().get(), anchor->GetInstance<Node>().get());
//                                                   } else if (anchor == nullptr) {
//
//                                                   }
//                                                   return jsi::Value::undefined();
//                                                 }
//                                                 );
//  } else if (propName == "remove") {
//    return jsi::Function::createFromHostFunction(
//                                                 runtime,
//                                                 name,
//                                                 1,
//                                                 [this](jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count) -> jsi::Value {
//
//                                                   GetInstance<Node>()->Remove();
//
//                                                   return jsi::Value::undefined();
//                                                 }
//                                                 );
// } else if (propName == Node::kRemoveChild.c_str()) {
//    return jsi::Function::createFromHostFunction(
//                                                 runtime,
//                                                 name,
//                                                 1,
//                                                 [this](jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count) -> jsi::Value {
//                                                   if (count < 1 || !args[0].isObject()) {
//                                                     throw jsi::JSError(rt, "InsertBefore requires 2 argument");
//                                                   }
//                                                   auto child = args[0].asObject(rt).getHostObject<NodeHostObject>(rt);
//                                                   GetInstance<Node>()->RemoveChild(child->GetInstance<Node>().get());
//
//                                                   return args[0].asObject(rt);
//                                                 }
//                                                 );
//  } else if (propName == Node::kAddEventListener.c_str()) {
//  return jsi::Function::createFromHostFunction(
//                                               runtime,
//                                               name,
//                                               2,
//                                               [this](jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count) -> jsi::Value {
//                                                 if (count <= 1) {
//                                                   throw jsi::JSError(rt, "AddEventListener requires at least 2 argument");
//                                                 }
//                                                 auto eventName = args[0].asString(rt).utf8(rt);
//                                                 auto listener = args[1].asObject(rt);
//                                                 bool capture = false;
//                                                 if (count > 2) {
//                                                   capture = args[2].getBool();
//                                                 }
//
//                                                 GetInstance<Element>()->AddEventListener(eventName, std::make_unique<JSIEventListener>(capture, std::move(listener), rt));
//
//                                                 return nullptr;
//                                               }
//                                               );
//  } else if (propName == "RemoveEventListener") {
//    return jsi::Function::createFromHostFunction(
//                                             runtime,
//                                             name,
//                                             2,
//                                             [this](jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count) -> jsi::Value {
//                                               if (count <= 1) {
//                                                 throw jsi::JSError(rt, "AddEventListener requires at least 2 argument");
//                                               }
//                                               auto eventName = args[0].asString(rt).utf8(rt);
//                                               auto listenerObj = args[1].asObject(rt);
//                                               bool capture = false;
//                                               if (count > 2) {
//                                                 capture = args[2].getBool();
//                                               }
//                                               auto listener = std::make_unique<JSIEventListener>(capture, std::move(listenerObj), rt);
//                                               GetInstance<Element>()->RemoveEventListener(eventName, listener.get());
//
//                                               return nullptr;
//                                             }
//                                             );
//}
//  else if (propName == "firstChild") {
//    auto firstChild = GetInstance<Node>()->firstChild();
//    if (firstChild) {
////      return jsi::Value::null();
//      return (StaticCastToElement(firstChild)->GetJSObject());
////      return Object::createFromHostObject(runtime, StaticCastToElement(firstChild)->GetJSObject());
//    } else {
//      return jsi::Value::null();
//    }
//  }
//  else if (propName == "lastChild") {
//    auto lastChild = GetInstance<Node>()->lastChild();
//    if (lastChild) {
//      return (StaticCastToElement(lastChild)->GetJSObject());
//    } else {
//      return jsi::Value::null();
//    }
//  }
//  else if (propName == "previousSibling") {
//    auto previousSibling = GetInstance<Node>()->previousSibling();
//    if (previousSibling) {
//      return (StaticCastToElement(previousSibling)->GetJSObject());
//    } else {
//      return jsi::Value::null();
//    }
//  }
//  else if (propName == "nextSibling") {
//    auto nextSibling = GetInstance<Node>()->nextSibling();
//    if (nextSibling) {
//      return (StaticCastToElement(nextSibling)->GetJSObject());
//    } else {
//      return jsi::Value::null();
//    }
//  }
//  else if (propName == "parentNode") {
//    auto parentNode = GetInstance<Node>()->parentNode();
//    if (parentNode) {
//      return (StaticCastToElement(parentNode)->GetJSObject());
//    } else {
//      return jsi::Value::null();
//    }
//  }
//  else if (propName == "nodeType") {
//    auto nodeType = GetInstance<Node>()->nodeType();
//    if (nodeType > 0) {
//      return jsi::Value(nodeType);
//    } else {
//      return jsi::Value::null();
//    }
//  }
//  return EventTargetHostObject::get(runtime, name);
//}

}  // namespace native_dom
}  // namespace msc
