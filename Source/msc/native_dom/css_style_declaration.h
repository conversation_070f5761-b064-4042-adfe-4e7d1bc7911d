//
//  css_style_declaration.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/3.
//

#ifndef MSC_NATIVE_DOM_CSS_STYLE_DECLARATION_H
#define MSC_NATIVE_DOM_CSS_STYLE_DECLARATION_H

#include "event_listener.h"
#include "script_wrappable.h"
#include "msc_string.h"

namespace msc {
namespace native_dom {

class Element;

// Through ScriptWrappable, this class supports interaction with JavaScript
// engines (such as JSC or V8), allowing scripts to manipulate CSS styles.
class CSSStyleDeclaration : public ScriptWrappable {
   public:
    CSSStyleDeclaration(Element &ele) : element_(ele) {
      MSC_RENDERER_LOG_DEBUG("constructor of CSSStyleDeclaration: %p", this);
    }
    virtual ~CSSStyleDeclaration() = default;

    MSCString cssText() const;
    void setCSSText(MSCString const &cssText);

   private:
    Element &element_;  // Holds the CSS style string in its properties

   public:
    static const std::string kCSSText;
};

}  // namespace native_dom
}  // namespace msc

#endif /* MSC_NATIVE_DOM_CSS_STYLE_DECLARATION_H */
