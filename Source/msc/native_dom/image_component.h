//
// Created by <PERSON><PERSON><PERSON> on 2025/4/7.
//

#ifndef MSC_ANDROID_IMAGE_COMPONENT_H
#define MSC_ANDROID_IMAGE_COMPONENT_H

#include "view_component.h"

namespace msc {
namespace native_dom {

class ImageComponent : public ViewComponent {
public:
    ImageComponent(ContainerNode *root, Tag tag = Tag::MSC_IMAGE) : ViewComponent(root, tag) {};
    virtual ~ImageComponent() override {};
};

} // msc
} // native_dom

#endif //MSC_ANDROID_IMAGE_COMPONENT_H
