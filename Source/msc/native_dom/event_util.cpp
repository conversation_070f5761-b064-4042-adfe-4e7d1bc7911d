//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/19.
//

#include "event_util.h"

static const int ANIMATION_EVENT_NUM = 0;//4;
static const int TRANSITION_EVENT_NUM = 0;//4;

static const char* animationEvents[ANIMATION_EVENT_NUM] = {
//        CKEVENT_ANIMATION_END,
//        CKEVENT_ANIMATION_START,
//        CKEVENT_ANIMATION_CANCEL,
//        CKEVENT_ANIMATION_ITERATION
};

static const char* transitionEvents[TRANSITION_EVENT_NUM] = {
//        CKEVENT_TRANSITION_END,
//        CKEVENT_TRANSITION_START,
//        CKEVENT_TRANSITION_CANCEL,
//        CKEVENT_TRANSITION_RUN
};

namespace msc {
namespace native_dom {

    bool EventUtil::needBubbles(const MSCString &type) {
      return type == "tap" || type == "longtap" || type == "longpress" || isTouchE<PERSON>(type);//|| isAnimationEvent(type) || isTransitionEvent(type) || isInputEvent(type);
    }

    bool EventUtil::isTouchEvent(const MSCString &type) {
        return type.starts_with("touch");
    }

    bool EventUtil::isAnimationEvent(const MSCString &type) {
        if (!type.starts_with("animation"))
            return false;

        for (int i = 0; i < ANIMATION_EVENT_NUM; ++i) {
            if (type == animationEvents[i])
                return true;
        }
        return false;
    }

    bool EventUtil::isTransitionEvent(const MSCString &type) {
        if (!type.starts_with("transition"))
            return false;

        for (int i = 0; i < TRANSITION_EVENT_NUM; ++i) {
            if (type == transitionEvents[i])
                return true;
        }
        return false;
    }

    bool EventUtil::isInputEvent(const MSCString &type) {
        return type == "focus" || type == "blur" || type == "confirm" || type == "input";
    }

    bool EventUtil::existAnimationEvent(const EventTargetDataMap &eventTarget) {
        for (int i = 0; i < ANIMATION_EVENT_NUM; ++i) {
            auto it = eventTarget.find(animationEvents[i]);
            if (it != eventTarget.end() && it->second.size())
                return true;
        }
        return false;
    }

    bool EventUtil::existTransitionEvent(const EventTargetDataMap &eventTarget) {
        for (int i = 0; i < TRANSITION_EVENT_NUM; ++i) {
            auto it = eventTarget.find(transitionEvents[i]);
            if (it != eventTarget.end() && it->second.size())
                return true;
        }
        return false;
    }

    const MSCString &EventUtil::GetMappedEventName(const MSCString& event_name) {
        static const std::unordered_map<MSCString, MSCString> s_event_name_map = {
            //TouchEvents.ts
            {"topTouchStart", "touchstart"},
            {"topTouchMove", "touchmove"},
            {"topTouchEnd", "touchend"},
            {"topTouchCancel", "touchcancel"},
            //ComponentEvents.ts
            {"onError", "error"},
            {"onLoad", "load"},
            {"onScrollToUpper", "scrolltoupper"},
            {"onScrollToLower", "scrolltolower"},
            {"onScroll", "mt-scroll"},
            {"onChange", "change"},
            {"onTransition", "transition"},
            {"onAnimationfinish", "animationfinish"},
            {"onPlay", "play"},
            {"onPause", "pause"},
            {"onWaiting", "waiting"},
            {"onLoadedMetadata", "loadedmetadata"},
            {"onEnded", "ended"},
            {"onInput", "input"},
            {"onFocus", "focus"},
            {"onBlur", "blur"},
            {"onConfirm", "confirm"},
            {"onKeyboardHeightChange", "keyboardheightchange"},
            {"onKeyBoardHeightChange", "keyboardheightchange"},
            {"onTextAreaHeightChange", "linechange"},
            {"onKeyboardCompositionUpdate", "compositionupdate"},
            {"onRefresherpulling", "refresherpulling"},
            {"onRefresherrefresh", "refresherrefresh"},
            {"onRefresherrestore", "refresherrestore"},
            {"onRefresherabort", "refresherabort"},
            {"onLivePlayerStateChange", "statechange"},
            {"onLivePlayerNetStatus", "netstatus"},
            {"onLivePlayerFullScreenChange", "fullscreenchange"},
            {"onLivePlayerAudioVolume", "audiovolumenotify"},
            // movable 比例改变
            {"onScale", "scale"},
            // movable 拖动事件
            {"onHTouchMove", "htouchmove"},
            {"onVTouchMove", "vtouchmove"},
            // 双击状态栏的事件
            {"onScrollToTop", "scrolltotop"},
            // scroll-view 拖拽的事件
            {"onDragStart", "dragstart"},
            {"onDragging", "dragging"},
            {"onDragEnd", "dragend"},
            // picker 列改变时触发的事件
            {"onColumnChange", "columnchange"},
            {"onCancel", "cancel"},
            // editor 编辑器的事件
            {"onReady", "ready"},
            {"onStatusChange", "statuschange"},
            // native 渲染 CSS 动画事件：https://km.sankuai.com/collabpage/**********
            {"onAnimationStart", "animationstart"},
            {"onAnimationIteration", "animationiteration"},
            {"onAnimationEnd", "animationend"},
            {"onTransitionEnd", "transitionend"},
            {"onLabelTap", "labeltap"},
            {"EffectVideo.onError", "error"},
            {"EffectVideo.onEffectCallback", "effect"},
            {"onPickStart", "pickstart"},
            {"onPickEnd", "pickend"},
            // list-view 组件列表项创建事件
            {"onItemBuild", "itembuild"},
            // list-view 组件列表项回收事件
            {"onItemDispose", "itemdispose"},
            {"onActiveEnd", "activeend"},
            {"onChanging", "changing"},
            {"onBeforeEnter", "beforeenter"},
            {"onEnter", "enter"},
            {"onAfterEnter", "afterenter"},
            {"onBeforeLeave", "beforeleave"},
            {"onLeave", "leave"},
            {"onAfterLeave", "afterleave"},
            {"onClickOverlay", "clickoverlay"},
            // -----直播卡片消息接收
            /**
             * 直播卡片消息接收
             */
            // 房间状态消息监听
            {"MLiveCard.onRoomStatusMessageReceived", "mlivecardroomstatusreceived"},
            // 直播基础信息监听
            {"MLiveCard.onLiveBasicMessageReceived", "mlivecardlivebasicreceived"},
            // 直播货架消息监听
            {"MLiveCard.onGoodsMessageReceived", "mlivecardgoodsreceived"},
            // 直播自定义消息监听
            {"MLiveCard.onCustomMessageReceived", "mlivecardcustomreceived"},
            // 切片直播/点播 播放进度监听
            {"MLiveCard.onProgressChange", "mlivecardprogresschange"},
            /**
             * 长列表吸顶状态变化事件
             * 需求文档：https://km.sankuai.com/collabpage/**********
             */
            {"onStickOnTopChange", "stickontopchange"},
            
            //CameraEvents.ts
            {"camera.onError", "error"},
            {"camera.onStop", "stop"},
            {"camera.onInitDone", "initdone"},
            {"camera.onScanCode", "scancode"},
            {"camera.onSwitchTorch", "switchtorch"},
            {"camera.onPictureTaken", "picturetaken"},
            {"camera.onPictureSaved", "picturesaved"},
            {"camera.onRecordingStart", "recordingstart"},
            {"camera.onRecordingEnd", "recordingend"},
            {"camera.onTouch", "touch"},
            {"camera.onSendZoom", "sendzoom"},
            {"camera.onCameraOrientation", "cameraorientation"},
            {"camera.onAudioInterrupted", "audiointerrupted"},
            {"camera.onAudioConnected", "audioconnected"},
            {"camera.onLightSensor", "lightsensor"},
            
            //VideoEvents.ts
            {"video.onError", "error"},
            {"video.onWaiting", "waiting"},
            {"video.onLoadedMetadata", "loadedmetadata"},
            {"video.onPlay", "play"},
            {"video.onPause", "pause"},
            {"video.onEnded", "ended"},
            {"video.onTimeUpdate", "timeupdate"},
            {"video.onFullScreenChange", "fullscreenchange"},
            {"video.onProgress", "progress"},
            {"video.onSeekComplete", "seekcomplete"},
        };
        auto iter = s_event_name_map.find(event_name);
        if (iter != s_event_name_map.end()) {
            return iter->second;
        }
        else
            return event_name;
    }
} // native_dom
} // msc
