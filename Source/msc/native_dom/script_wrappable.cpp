//
//  INativeDOMObject.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/16.
//

#include "script_wrappable.h"

#if USE_JSC
#include "jsc/native_dom_object_wrapper.h"
#endif

#if USE_V8
#include "v8/native_dom_object_wrapper.h"
#include "element.h"
#endif

namespace msc {
namespace native_dom {

#if DEBUG
    int ScriptWrappable::total_count_ = 0;
#endif

void ScriptWrappable::DestroyJSWrapper() {
  if (wrapper_) {
    MSC_RENDERER_LOG_DEBUG("[native_dom] Destroy wrapper for script_wrappable: %p", this);
    NativeDOMObjectWrapper *wrapper = wrapper_;
    wrapper_ = nullptr;
    wrapper->Destroy();
  }
}

void ScriptWrappable::ClearJSWrapper(bool clear_jsobject) {
    if (wrapper_) {
        MSC_RENDERER_LOG_DEBUG("[native_dom] Clear wrapper for script_wrappable: %p", this);
        if (clear_jsobject) {
          wrapper_->ClearJSObject();
        }
        wrapper_ = nullptr;
    }
}

void ScriptWrappable::ForceGCDebug() {
  if (wrapper_) {
    wrapper_->ForceGCDebug();
  }
}

}  // namespace native_dom
}  // namespace msc
