//
//  dom_implementation.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/20.
//

#include "dom_implementation.h"
#include "bridge/message_proxy.h"
#include "../logger.h"

namespace msc {
namespace native_dom {

using namespace std::string_literals;
const std::string DOMImplementation::kCreateDocument = "createDocument"s;
const std::string DOMImplementation::kCreateIntersectionObserver = "createIntersectionObserver"s;

std::shared_ptr<Document> DOMImplementation::CreateDocument(int pageId) {
  MSC_RENDERER_LOG_DEBUG("[native_dom] DOMImplementation::CreateDocument, pageId: %d", pageId);
  auto messageProxy = this->GetMessageProxy();
  auto obj = std::make_shared<Document>(Tag::DOCUMENT, nullptr, pageId, messageProxy);
  messageProxy->GetDocumentRegistry()->registerDOMDocument(pageId, obj);
  return obj;
}

} // namespace native_dom
} // namespace msc
