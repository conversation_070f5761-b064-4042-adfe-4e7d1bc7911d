//
//  node.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/10.
//

#ifndef Node_h
#define Node_h

#include "event_target.h"
#include "element_type.h"
#include "tree_scope.h"

namespace msc {
namespace native_dom {

inline Element *StaticCastToElement(void *node) {
  return static_cast<Element *>(node);
}

class ContainerNode;
class Document;
class Element;

class Node : public EventTarget {
public:
  static const int kNodeIdInvalid = -1;
public:
  Node();
  virtual ~Node() = default;
  
  static int getNextNodeId();
  
//  // Returns the document associated with this node. A Document node returns
//  // itself.
//  Document& GetDocument() const { return GetTreeScope().GetDocument(); }
//
//  TreeScope& GetTreeScope() const {
////    DCHECK(tree_scope_);
//    return *tree_scope_;
//  }
//
//  TreeScope& ContainingTreeScope() const {
////    DCHECK(IsInTreeScope());
//    return *tree_scope_;
//  }
//  void SetTreeScope(TreeScope* scope) { tree_scope_ = scope; }
//  
  
  ContainerNode* rootNode() const { return root_; }

  ContainerNode* parentNode() const { return parent_; }
  Element* parentElement() const;
  void SetParentNode(ContainerNode* parent) { parent_ = parent; }

  Node* previousSibling() const { return prev_; }
  Node* nextSibling() const { return next_; }

  Tag nodeTag() const { return (Tag)node_tag_; }
  bool IsCommentNode() const { return nodeTag() == Tag::COMMENT; }

  bool mounted() const { return mounted_; }
  void SetMounted(bool mounted) { mounted_ = mounted; }
  bool connected() const { return connected_; }
  // void SetConnected(bool connected) { connected_ = connected; }

  bool IsQuickJSImpl() const { return quicks_js_impl_; }

  bool textNodeFeatureEated() { return text_node_feature_eated_; }

  void AppendChild(Node* child);
  void RemoveChild(Node* child);
  void InsertBefore(Node* child, Node* anchor);
  void Remove();
  Node* firstChild() const;
  Node* lastChild() const;
  MSCString nodeName();
  int nodeType() const;

  // TODO(wesley): TextContent
  virtual void SetTextContent(const MSCString& content);
  virtual const MSCString& TextContent() const;

  bool textNestingFeature() { return text_nesting_feature_; }
  
  int GetNodeId() const { return node_id_; }

protected:
  void SetNodeId(int nodeId) { node_id_ = nodeId;}

protected:
  // TODO
  void SetQuickJSImpl(bool v) { quicks_js_impl_ = v; }
  void SetNodeTag(Tag t);
  void SetTextNodeFeatureEated(bool v) { text_node_feature_eated_ = v; }

  // View Component
  bool firstAppearOnCapture() const { return first_appear_on_capture_; }
  void SetFirstAppearOnCapture(bool v) { first_appear_on_capture_ = v; }
  bool firstAppearOnBubble() const { return first_appear_on_bubble_; }
  void SetFirstAppearOnBubble(bool v) { first_appear_on_bubble_ = v; }

  
  
private:
    friend class ContainerNode;
    friend class Document;
    friend class JSIBasicElement;

    void SetRootNode(ContainerNode* root) { root_ = root; }
    void SetPreviousSibling(Node* prev) { prev_ = prev; }
    void SetNextSibling(Node* next) { next_ = next; }

    bool EatTextNodeFeature() const { return eat_text_node_feature_; }
    void SetEatTextNodeFeature(bool v) { eat_text_node_feature_ = v; }

    unsigned node_tag_ : 8;

    unsigned connected_ : 1;
    unsigned mounted_ : 1;
    unsigned text_node_feature_eated_ : 1;
    unsigned text_nesting_feature_ : 1;
    unsigned eat_text_node_feature_ : 1;
    // Gin: use for diff JSIElement and QJSElement
    unsigned quicks_js_impl_ : 1;

    // View Component
    unsigned first_appear_on_capture_ : 1;
    unsigned first_appear_on_bubble_ : 1;

    ContainerNode* root_ = nullptr;
    ContainerNode* parent_ = nullptr;
    Node* prev_ = nullptr;
    Node* next_ = nullptr;

  int node_id_ = kNodeIdInvalid;

//    TreeScope *tree_scope_;

public:
  static const std::string kAppendChild;
  static const std::string kInsertBefore;
  static const std::string kRemove;
  static const std::string kRemoveChild;
  static const std::string kAddEventListener;
  static const std::string kRemoveEventListener;
};

}  // namespace native_dom
}  // namespace msc


#endif /* Node_h */
