//
//  intersection_observer.h
//  MSC
//
//

#ifndef IntersectionObserver_h
#define IntersectionObserver_h

#include "mtdocument.h"
#include "script_wrappable.h"

namespace msc {
namespace native_dom {

class MessageProxy;

class IntersectionObserver : public ScriptWrappable {
 public:
  IntersectionObserver(
      std::shared_ptr<MessageProxy> message_proxy, int page_id,
      const blink::mt::CreateIntersectionObserverParams &params)
      : message_proxy_(message_proxy), page_id_(page_id), params_(params) {
        MSC_RENDERER_LOG_DEBUG("constructor of IntersectionObserver: %p", this);
      }
  virtual ~IntersectionObserver() = default;

  int GetId() const { return id_; }
  blink::mt::CreateIntersectionObserverParams const &GetParams() const {
    return params_;
  }

  std::shared_ptr<MessageProxy> message_proxy_;
  int page_id_;
  blink::mt::CreateIntersectionObserverParams params_;
  static const std::string kObserver;
  static const std::string kDisconnect;

 private:
  static int id_next_;
  int id_{++id_next_};
};

}  // namespace native_dom
}  // namespace msc

#endif /* IntersectionObserver_h */
