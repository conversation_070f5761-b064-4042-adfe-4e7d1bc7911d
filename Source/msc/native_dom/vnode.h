//
// Created by <PERSON><PERSON><PERSON> on 2025/3/26.
//

#ifndef MSC_ANDROID_VNODE_H
#define MSC_ANDROID_VNODE_H

#include "script_wrappable.h"
#include "document.h"

namespace msc {
namespace native_dom {

class VNode : public ScriptWrappable {
public:
    VNode(Document *document) : document_(document) {}

private:
    Document *document_;
};

} // msc
} // native_dom

#endif //MSC_ANDROID_VNODE_H
