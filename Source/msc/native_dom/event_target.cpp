//
//  event_target.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#include "event_target.h"
#include "container_node.h"

namespace msc {
namespace native_dom {

std::vector<EventTarget*> EventTarget::generateEventPath()
{
    std::vector<EventTarget*> result;
    auto* p = static_cast<ContainerNode*>(this);
    ContainerNode* lastP = p;
    while (p) {
        result.push_back(p);
        lastP = p;
        p = p->parentNode();
    }

    auto* element = static_cast<ContainerNode*>(lastP);
    if (result.back() != element->rootNode())
        result.push_back(element->rootNode());

    return result;
}

bool EventTarget::eventListened(const MSCString& type) const
{
    if (EventTargetDataMap* data = const_cast<EventTarget*>(this)->GetEventTargetData())
        return data->find(type) != data->end();
    return false;
}

void EventTarget::AddEventListener(const MSCString& eventName,
                                   std::unique_ptr<EventListener> listener)
{
    EnsureEventTargetData();

    if (EventTargetDataMap* data = GetEventTargetData()) {
        if (data->find(eventName) == data->end())
            data->insert(std::make_pair(eventName, EventTargetData()));

        bool fromZeroToOne = false;
        data->at(eventName).AppendIfNotPresent(std::move(listener), fromZeroToOne);
        if (fromZeroToOne)
            OnListenerFromZeroToOne(eventName);
    }
}

void EventTarget::RemoveEventListener(const MSCString& eventName, EventListener* listener)
{
    if (EventTargetDataMap* data = GetEventTargetData()) {
        auto it = data->find(eventName);
        if (it != data->end()) {
            bool fromOneToZero = false;
            data->at(eventName).RemoveIfPresent(listener, fromOneToZero);
            if (fromOneToZero)
                OnListenerFromOneToZero(eventName);

            if (!it->second.size())
                data->erase(it);
        }
    }
}
}  // namespace native_dom
}  // namespace msc
