//
//  JSINamedNodeMap.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/13.
//

#ifndef JSINamedNodeMap_h
#define JSINamedNodeMap_h

#include "dom_named_node_map.h"
#include "element.h"

namespace msc {
namespace native_dom {

class JSINamedNodeMap : public DOMNamedNodeMap {
public:
  JSINamedNodeMap(std::weak_ptr<Element> element);
  virtual ~JSINamedNodeMap() = default;
  
  void SetJSObject(jsi::Object *object);
  jsi::Object *GetJSObject(jsi::Runtime &runtime) const;
  
  uint32_t length(jsi::Runtime &runtime) const;
  jsi::Value getNamedItem(jsi::Runtime &runtime, const jsi::PropNameID &name) const;
  jsi::Value getItemByIndex(jsi::Runtime &runtime, uint32_t index) const;
  jsi::Value removeNamedItem(jsi::Runtime &runtime, const jsi::PropNameID &name);
  jsi::Value setNamedItem(jsi::Runtime &runtime, const jsi::PropNameID &name, const jsi::Value &value);
};

}  // namespace native_dom
}  // namespace msc

#endif /* JSINamedNodeMap_h */
