//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/4/7.
//

#ifndef MSC_ANDROID_SWIPER_COMPONENT_H
#define MSC_ANDROID_SWIPER_COMPONENT_H

#include "view_component.h"

namespace msc {
namespace native_dom {


class SwiperItemComponent : public ViewComponent {
public:
    SwiperItemComponent(ContainerNode *root, Tag tag = Tag::SWIPER_ITEM) : ViewComponent(root, tag) {};
    virtual ~SwiperItemComponent() override {};

    bool IsContainerElement() const override {
        return true;
    }
};


    class SwiperComponent : public ViewComponent {
public:
    SwiperComponent(ContainerNode *root, Tag tag = Tag::SWIPER) : ViewComponent(root, tag) {};
    virtual ~SwiperComponent() override {};

    bool IsContainerElement() const override {
        return true;
    }
};

} // msc
} // native_dom

#endif //MSC_ANDROID_SWIPER_COMPONENT_H
