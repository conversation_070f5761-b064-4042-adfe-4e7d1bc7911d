//
//  css_style_declaration.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/4/3.
//

#include "css_style_declaration.h"

#include "element.h"

namespace msc {
namespace native_dom {

using namespace std::string_literals;
const std::string CSSStyleDeclaration::kCSSText = "cssText"s;
static const std::string kCSSTextAttributeName = "style"s;

MSCString CSSStyleDeclaration::cssText() const {
    blink::mt::PropValue css_text;
    this->element_.GetAttribute(kCSSTextAttributeName, css_text);
    return css_text.stringValue();
}
void CSSStyleDeclaration::setCSSText(MSCString const &css_text) {
    this->element_.SetAttribute(kCSSTextAttributeName, css_text);
}

}  // namespace native_dom
}  // namespace msc
