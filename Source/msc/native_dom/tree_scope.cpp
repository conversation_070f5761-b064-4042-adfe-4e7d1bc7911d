//
//  TreeScope.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/14.
//

#include "tree_scope.h"
//#include "element.h"
//#include "document.h"

namespace msc {
namespace native_dom {

//TreeScope::TreeScope(ContainerNode& root_node, Document& document)
//    : document_(&document),
//      root_node_(&root_node),
//      parent_tree_scope_(&document) {
//  root_node_->SetTreeScope(this);
//}
//
//TreeScope::TreeScope(Document& document)
//    : document_(&document),
//      root_node_(&document),
//      parent_tree_scope_(nullptr) {
//  root_node_->SetTreeScope(this);
//}
//
//void TreeScope::SetParentTreeScope(TreeScope& new_parent_scope) {
//  // A document node cannot be re-parented.
////  DCHECK(!RootNode().IsDocumentNode());
//
//  parent_tree_scope_ = &new_parent_scope;
//  SetDocument(new_parent_scope.GetDocument());
//}
//
//std::shared_ptr<Element> TreeScope::CreateElement(const MSCString& local_name) {
//  if (local_name == DOM_TAG_VIEW) {
//    return std::make_shared<Element>(Tag::MSC_VIEW, nullptr);
//  }
//  return nullptr;
//}

void TreeScope::Add(const MSCString& idStr, ContainerNode* element)
{
//    CKASSERT(element);
    auto it = m_getElementById.find(idStr);
    if (it == m_getElementById.end() || it->second.element() == element) {
        m_getElementById[idStr].setElement(element);
    } else {
        m_getElementById[idStr].setElement(nullptr);
    }
}

void TreeScope::Remove(const MSCString& idStr, ContainerNode* element)
{
    auto it = m_getElementById.find(idStr);
    if (it != m_getElementById.end()) {
        if (it->second.element()) {
            m_getElementById.erase(it);
        } else {
            // empty
        }
    } else {
        // empty
    }
}

}  // namespace native_dom
}  // namespace msc
