//
//  msc_string.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/1/13.
//

#ifndef MSCString_h
#define MSCString_h

#include <string>

#include <array>
#ifdef __APPLE__
#include <JavaScriptCore/JavaScriptCore.h>
#else
#include <msc-jsi/jsi.h>
#endif


#include <vector>

namespace msc {
namespace native_dom {
//TODO: 改名STLString，统一使用一个名字
using MSCString = std::string;

extern const std::string &g_EmptyString;

std::string MSCConvertToString(const std::vector<int> &v);

class StringView {
public:
    // 类型别名 (C++11 using)
    typedef size_t size_type;
    static const size_type npos = -1;

    // 构造函数
    constexpr StringView() noexcept
            : data_(nullptr), size_(0) {}

    StringView(const char *str)
            : data_(str), size_(str ? strlen(str) : 0) {}

    StringView(const char *data, size_type len)
            : data_(data), size_(len) {}

    StringView(const std::string &str) noexcept
            : data_(str.data()), size_(str.size()) {}

    // 基础访问
    constexpr const char *data() const noexcept { return data_; }

    constexpr size_type size() const noexcept { return size_; }

    constexpr size_type length() const noexcept { return size_; }

    constexpr bool empty() const noexcept { return size_ == 0; }

    // 元素访问
    const char &operator[](size_type pos) const {
        return data_[pos];
    }

    // 比较操作符
    bool operator==(const StringView &rhs) const noexcept {
        return (size_ == rhs.size_) &&
               (memcmp(data_, rhs.data_, size_) == 0);
    }

    bool operator!=(const StringView &rhs) const noexcept {
        return !(*this == rhs);
    }

    // 转换为 std::string
    explicit operator std::string() const {
        return std::string(data_, size_);
    }

private:
    const char *data_;
    size_type size_;
};

}  // namespace native_dom
}  // namespace msc

#endif /* MSCString_h */
