#pragma once

#ifndef DOM_TOKEN_LIST_H
#define DOM_TOKEN_LIST_H

#include <functional>
#include <sstream>
#include <string>
#include <deque>
#include <unordered_set>

#include "script_wrappable.h"

template <typename Iter>
std::string Join(const std::string &separator, Iter begin, Iter end) {
  std::ostringstream oss;
  if (begin != end) {
    oss << *begin;  // 添加第一个元素
    ++begin;
    for (; begin != end; ++begin) {
      oss << separator << *begin;  // 后续元素前加分隔符
    }
  }
  return oss.str();
}

namespace msc {
namespace native_dom {

class DOMTokenList : public ScriptWrappable {
 public:
  using DOMTokenListChangedCallback = std::function<void(const std::string &new_value)>;

  class Iterator : public ScriptWrappable {
   public:
    Iterator(DOMTokenList &token_list) : token_list_{token_list} {
      MSC_RENDERER_LOG_DEBUG("constructor of DOMTokenList::Iterator: %p", this);
    }

    bool HasNext() const { return index_ < token_list_.GetLength(); }

    std::tuple<size_t, std::string> Next() {
      auto &&value = token_list_.GetItem(static_cast<int>(index_));
      auto key = index_;
      ++index_;
      return {key, value};
    }

    std::unique_ptr<Iterator> Clone() {
      return std::make_unique<Iterator>(token_list_);
    }

    DOMTokenList &token_list_;
    size_t index_{0};
  };

  DOMTokenList(DOMTokenListChangedCallback on_token_changed)
      : on_token_changed_{on_token_changed}, tokens_{4} {
        MSC_RENDERER_LOG_DEBUG("constructor of DOMTokenList: %p", this);
  }

  std::string GetValue() { return ToString(); }

  size_t GetLength() { return tokens_.size(); }

  std::string GetItem(int index);

  bool Contains(const std::string &token) {
    return std::find(std::begin(tokens_), std::end(tokens_), token) !=
           std::end(tokens_);
  }

  void Add(const std::vector<std::string> &tokens);

  void Remove(const std::vector<std::string> &tokens);

  bool Replace(const std::string &old_token, const std::string &new_token);

  bool Supports(const std::string &token);

  bool Toggle(const std::string &token, bool force = false,
              bool has_force = false);

  using ForEachCallback = std::function<void(const std::string &, size_t)>;
  void ForEach(const ForEachCallback &callback);

  std::string ToString() const {
    return Join(" ", std::begin(tokens_), std::end(tokens_));
  }

  void FromString(const std::string &s);

  void Clear() { tokens_.clear(); }

 private:
  std::vector<std::string> tokens_;
  DOMTokenListChangedCallback on_token_changed_{};
};

}  // namespace native_dom
}  // namespace msc

#endif  // DOM_TOKEN_LIST_H
