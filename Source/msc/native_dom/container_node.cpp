//
//  container_node.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/13.
//

#include "container_node.h"
#include <stack>

namespace msc {
namespace native_dom {

ContainerNode::ContainerNode(Tag type, ContainerNode* root)
//    : CKSafeUnique<ContainerNode>()
{
    SetRootNode(root);
    SetNodeTag(type);

//    INC_TYPE_COUNT(kCKNativeContainerNodeType);
}

ContainerNode::~ContainerNode()
{
//    TagLogDebug("ndom") << __PRETTY_FUNCTION__ << " " << this;
//    DEC_TYPE_COUNT(kCKNativeContainerNodeType);
}

void ContainerNode::InsertBefore(ContainerNode* child,
                                 ContainerNode* anchor)
{
    MSC_RENDERER_LOG_DEBUG("ContainerNode.InsertBefore, parent: %s, %d, child: %s, %d, anchor: %s, %d", this->nodeN<PERSON>(), this->GetNodeId(), child->nodeName(), child->GetNodeId(), anchor ? anchor->nodeName(): "null", anchor ? anchor->GetNodeId() : 0);
//    DOMASSERT(!anchor || anchor->parentNode() == this);
//    DOM_RELEASE_ASSERT(child != anchor);

    bool child_has_parent = child->parentNode() != nullptr;
    child->detachFromParent();
    int original_index = -1;
    if (child->mounted()) {
        original_index = GetIndex(child, false);
    }

    child->SetParentNode(this);
    if (anchor) {
        child->SetPreviousSibling(anchor->previousSibling());
        child->SetNextSibling(anchor);
        if (anchor->previousSibling())
            anchor->previousSibling()->SetNextSibling(child);
        anchor->SetPreviousSibling(child);
        if (anchor == first_child_)
            first_child_ = child;
    } else {
        child->SetNextSibling(first_child_);
        if (first_child_) {
            first_child_->SetPreviousSibling(child);
        } else {
            last_child_ = child;
        }
        first_child_ = child;
    }

    OnEvent(DOMEventType::INSERT_BEFORE, child, anchor, child_has_parent, original_index);

//    NativeElementClient* client = currentClient(rootNode());
//    if (client)
//        client->InsertBefore(this, child, anchor, child_has_parent);
}

void ContainerNode::InsertAfterInternal(ContainerNode* child,
                                        ContainerNode* anchor)
{
    if (!child->parentNode()) {
        child->SetParentNode(this);

        Node* oldNext = anchor->nextSibling();
        anchor->SetNextSibling(child);
        child->SetPreviousSibling(anchor);
        child->SetNextSibling(oldNext);
        if (oldNext)
            oldNext->SetPreviousSibling(child);

        if (last_child_ == anchor)
            last_child_ = child;
    }
}

size_t ContainerNode::GetIndex(ContainerNode* anchor, bool skipComment)
{
//    DOM_RELEASE_ASSERT(first_child_);
//    DOM_RELEASE_ASSERT((skipComment && anchor->nodeTag() != Tag::COMMENT) || !skipComment);

    size_t result = 0;
    if (skipComment) {
        for (Node* node = first_child_; node; node = node->nextSibling()) {
            if (anchor != node) {
                if (!node->IsCommentNode())
                    result++;
            } else {
                return result;
            }
        }
    } else {
        for (Node* node = first_child_; node; node = node->nextSibling()) {
            if (anchor != node) {
                result++;
            } else {
                return result;
            }
        }
    }

//    NOT_REACHED();
    return -1;
}

Node* ContainerNode::getElement(int index, bool skipComment)
{
    if (skipComment) {
        for (Node* node = first_child_; node; node = node->nextSibling()) {
            if (index) {
                if (!node->IsCommentNode())
                    index--;
            } else {
                return node;
            }
        }
    } else {
        for (Node* node = first_child_; node; node = node->nextSibling()) {
            if (index) {
                index--;
            } else {
                return node;
            }
        }
    }
    return nullptr;
}

void ContainerNode::AppendChildInternal(ContainerNode* child)
{
    if (!child->parentNode()) {
//        DOMASSERT(!child->nextSibling() && !child->previousSibling());

        child->SetParentNode(this);
        if (last_child_) {
            last_child_->SetNextSibling(child);
            child->SetPreviousSibling(last_child_);
            last_child_ = child;
        } else {
            first_child_ = last_child_ = child;
        }
    }
}

void ContainerNode::AppendChild(ContainerNode* child)
{
  MSC_RENDERER_LOG_DEBUG("[native_dom] ContainerNode::AppendChild - (%d, %s, %d, %s) \n", GetNodeId(), nodeName(), child->GetNodeId(), child->nodeName());

  int original_index = -1;
  if (child->mounted()) {
    original_index = GetIndex(child, false);
  }

    bool child_has_parent = child->parentNode() != nullptr;
    if (child_has_parent)
        child->detachFromParent();
    AppendChildInternal(child);
    OnEvent(DOMEventType::APPEND_CHILD, child, nullptr, child_has_parent, original_index);
//    NativeElementClient* client = currentClient(rootNode());
//    if (client)
//        client->AppendChild(this, child, child_has_parent);
}

void ContainerNode::DestroySubtree() {
  std::vector<Node *> nodes;
  nodes.push_back(this);
  VisitAllDescendants_PostOrder([&nodes](ContainerNode* currentParent, Node* currentChild) {
    nodes.push_back(currentChild);
  });
  for (Node *node : nodes) {
    node->parent_ = nullptr;
    node->prev_ = nullptr;
    node->next_ = nullptr;
    static_cast<ContainerNode *>(node)->first_child_ = nullptr;
    static_cast<ContainerNode *>(node)->last_child_ = nullptr;
    node->DestroyJSWrapper();
  }
}

void ContainerNode::detachFromParent()
{
    if (parentNode() == previousSibling() && previousSibling() == nextSibling() && nextSibling() == nullptr)
        return;

    ContainerNode* oldParent = parentNode();
    Node* oldChildPrev = previousSibling();
    Node* oldChildNext = nextSibling();
    SetParentNode(nullptr);
    if (previousSibling())
        previousSibling()->SetNextSibling(nextSibling());
    if (nextSibling())
        nextSibling()->SetPreviousSibling(previousSibling());

    if (this == oldParent->first_child_)
        oldParent->first_child_ = oldChildNext;

    if (this == oldParent->last_child_)
        oldParent->last_child_ = oldChildPrev;

    SetPreviousSibling(nullptr);
    SetNextSibling(nullptr);
}

//void ContainerNode::addEvent(const VString& e)
//{
//    NativeElementClient* client = currentClient(rootNode());
//    if (client)
//        client->addEvent(this, e);
//}
//
//void ContainerNode::removeEvent(const VString& e)
//{
//    NativeElementClient* client = currentClient(rootNode());
//    if (client)
//        client->removeEvent(this, e);
//}

void ContainerNode::RemoveChild(ContainerNode* child)
{
//    DOM_RELEASE_ASSERT(child->parent_ == this);
    child->Remove();
}

void ContainerNode::Remove()
{
    detachFromParent();

    OnEvent(DOMEventType::REMOVE, nullptr, nullptr, false, -1);
//    NativeElementClient* client = currentClient(rootNode());
//    if (client)
//        client->Remove(this);
}

void ContainerNode::VisitAllDescendants(VisitCallback callback)
{
    for (Node* node = first_child_; node; node = node->nextSibling()) {
        callback(this, node);
        if (node->firstChild())
            static_cast<ContainerNode*>(node)->VisitAllDescendants(callback);
    }
}

void ContainerNode::VisitAllDescendants_PostOrder(VisitCallback callback) {
    for (Node* node = first_child_; node; node = node->nextSibling()) {
        if (node->firstChild())
            static_cast<ContainerNode*>(node)->VisitAllDescendants_PostOrder(callback);
        callback(this, node);
    }
}

void ContainerNode::VisitEachChild(VisitCallback callback)
{
    for (Node* node = first_child_; node; node = node->nextSibling())
        callback(this, node);
}

void ContainerNode::VisitEachChildCanStop(VisitBoolCallback callback)
{
    for (Node* node = first_child_; node; node = node->nextSibling()) {
        if (callback(this, node))
            return;
    }
}

bool ContainerNode::contains(const Node* target)
{
    bool contains = false;
    VisitEachChild([&](ContainerNode* self, Node* child) {
        if (child == target) {
            contains = true;
            return;
        }
    });
    return contains;
}

int ContainerNode::nodeType() const
{
#define FALLTHROUGH [[fallthrough]]
  
    switch (nodeTag()) {
    case Tag::DIV:
        FALLTHROUGH;
    case Tag::SPAN:
        FALLTHROUGH;
    case Tag::STYLE:
        FALLTHROUGH;
    case Tag::HEAD:
        FALLTHROUGH;
    case Tag::IMG:
        FALLTHROUGH;
    case Tag::MSC_VIEW:
        FALLTHROUGH;
    case Tag::MSC_IMAGE:
        FALLTHROUGH;
    case Tag::MSC_TEXT:
        FALLTHROUGH;
    case Tag::MSC_VIRTUAL_TEXT:
        FALLTHROUGH;
    case Tag::MSC_SCROLLER:
        FALLTHROUGH;
    case Tag::MSC_SCROLL_VIEW:
        FALLTHROUGH;
    case Tag::SWIPER:
        FALLTHROUGH;
    case Tag::SWIPER_ITEM:
        FALLTHROUGH;
    case Tag::RADIO:
        FALLTHROUGH;
    case Tag::RADIO_GROUP:
        FALLTHROUGH;
    case Tag::CHECKBOX:
        FALLTHROUGH;
    case Tag::CHECKBOX_GROUP:
        FALLTHROUGH;
    case Tag::ICON:
        FALLTHROUGH;
    case Tag::LABEL:
        FALLTHROUGH;
    case Tag::BUTTON:
        FALLTHROUGH;
    case Tag::NAVIGATOR:
        FALLTHROUGH;
    case Tag::FORM:
        FALLTHROUGH;
    case Tag::SWITCH:
        FALLTHROUGH;
    case Tag::EMBED:
        FALLTHROUGH;
    case Tag::MSC_INPUT:
        FALLTHROUGH;
    case Tag::INPUT:
        FALLTHROUGH;
    case Tag::TEXT_AREA:
        return NodeType::kElementNode;
    case Tag::DOCUMENT:
        return NodeType::kDocumentNode;
    case Tag::TEXT_NODE:
        return NodeType::kTextNode;
    case Tag::COMMENT:
        return NodeType::kCommentNode;
    default:
        return -1;
    }
}

const char* ContainerNode::nodeName() const
{
    switch (nodeTag()) {
    case Tag::DIV:
        return "DIV";
    case Tag::SPAN:
        return "SPAN";
    case Tag::IMG:
        return "IMG";
    case Tag::STYLE:
        return "STYLE";
    case Tag::HEAD:
        return "HEAD";
    case Tag::BODY:
        return "BODY";
    case Tag::DOCUMENT:
        return "#document";
    case Tag::COMMENT:
        return "#comment";
    case Tag::ICON:
        return "ICON";
    case Tag::LABEL:
        return "LABEL";
    case Tag::FORM:
        return "FORM";
    case Tag::RADIO:
        return "RADIO";
    case Tag::RADIO_GROUP:
        return "RADIO_GROUP";
    case Tag::CHECKBOX:
        return "CHECKBOX";
    case Tag::CHECKBOX_GROUP:
        return "CHECKBOX_GROUP";
    case Tag::SWITCH:
        return "SWITCH";
    case Tag::BUTTON:
        return "BUTTON";
    case Tag::EMBED:
        return "EMBED";
    case Tag::MSC_INPUT:
        return "INPUT";
    case Tag::INPUT:
        return "INPUT";
    case Tag::TEXT_AREA:
        return "TEXT_AREA";
    case Tag::NAVIGATOR:
        return "NAVIGATOR";
    case Tag::MSC_SCROLLER:
        return "MSCScrollView";
    case Tag::MSC_SCROLL_VIEW:
        return "MSCScrollView";
    case Tag::MSC_IMAGE:
        return "MSCImage";
    case Tag::MSC_VIEW:
        return "MSCView";
    case Tag::MSC_TEXT:
        return "MSCText";
    case Tag::MSC_VIRTUAL_TEXT:
        return "MSCVirtualText";
    case Tag::SWIPER:
        return "MSCSwiper";
    case Tag::SWIPER_ITEM:
        return "MSCSwiperItem";
    case Tag::TEXT_NODE:
        return "MSCRawText";
    default:
        return "";
    }
}

const char* ContainerNode::tagName() const
{
    switch (nodeTag()) {
    case Tag::DIV:
        FALLTHROUGH;
    case Tag::SPAN:
        FALLTHROUGH;
    case Tag::IMG:
        FALLTHROUGH;
    case Tag::HEAD:
        FALLTHROUGH;
    case Tag::BODY:
        FALLTHROUGH;
    case Tag::STYLE:
        FALLTHROUGH;
    case Tag::ICON:
        FALLTHROUGH;
    case Tag::LABEL:
        FALLTHROUGH;
    case Tag::FORM:
        FALLTHROUGH;
    case Tag::RADIO:
        FALLTHROUGH;
    case Tag::RADIO_GROUP:
        FALLTHROUGH;
    case Tag::CHECKBOX:
        FALLTHROUGH;
    case Tag::CHECKBOX_GROUP:
        FALLTHROUGH;
    case Tag::SWITCH:
        FALLTHROUGH;
    case Tag::BUTTON:
        FALLTHROUGH;
    case Tag::MSC_INPUT:
        FALLTHROUGH;
    case Tag::INPUT:
        FALLTHROUGH;
    case Tag::EMBED:
        FALLTHROUGH;
    case Tag::MSC_VIDEO:
        FALLTHROUGH;
    case Tag::NAVIGATOR:
        FALLTHROUGH;
    case Tag::TEXT_AREA:
        FALLTHROUGH;
    case Tag::MSC_VIEW:
        FALLTHROUGH;
    case Tag::MSC_TEXT:
        FALLTHROUGH;
    case Tag::MSC_VIRTUAL_TEXT:
        FALLTHROUGH;
    case Tag::MSC_SCROLLER:
        FALLTHROUGH;
    case Tag::MSC_SCROLL_VIEW:
        FALLTHROUGH;
    case Tag::MSC_IMAGE:
        FALLTHROUGH;
    case Tag::TEXT_NODE:
        FALLTHROUGH;
    case Tag::SWIPER:
        FALLTHROUGH;
    case Tag::SWIPER_ITEM:
        return nodeName();
    default:
        return "";
    }
}

#if DOM_TEST
// static
ContainerNode* ContainerNode::CreateElement(Tag type,
                                            ContainerNode* root)
{
    ContainerNode* e = new ContainerNode(type, root);
    NativeElementClient* client = currentClient(root);
    if (client)
        client->CreateElement(e);
    return e;
}

// static
ContainerNode* ContainerNode::createElementForTest(Tag type,
                                                   ContainerNode* root, int num)
{
    auto* e = CreateElement(type, root);
    return e;
}

// static
ContainerNode* ContainerNode::createRootForTest(Tag type, int num)
{
    auto* e = CreateElement(type, nullptr);
    return e;
}

// static
void ContainerNode::dumpTree(Node* element, int tab, bool attrs)
{
    // TagLogD("vdom", "%s\n", element->toString(attrs).c_str());
    TagLogD(kTagNDOM, "    ");
    for (Node* node = element->firstChild(); node; node = node->nextSibling()) {
        auto ntab(tab);
        while (ntab--) {
            TagLogD(kTagNDOM, "    ");
        }
        dumpTree(node, tab + 1, attrs);
    }
}

// ACKString ContainerNode::toString(bool onlyAttrs)
//{
//     std::vector<char> result;
//     return ACKString(result.data());
// }
#endif // DOM_TEST


}  // namespace native_dom
}  // namespace msc
