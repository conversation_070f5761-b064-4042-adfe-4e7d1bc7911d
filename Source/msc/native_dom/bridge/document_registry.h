//
//  document_registry.hpp
//  MSCRenderer
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/24.
//

#ifndef DocumentRegistry_hpp
#define DocumentRegistry_hpp

#include <unordered_map>
#include <mutex>

#include "native_dom_metrics.h"

namespace blink {
namespace mt {
  class MTDocument;
}
}

namespace msc {
namespace native_dom {

class Document;

//管理所有的MTDocument和NativeDOM::Document：
//方便NativeDOM::Document通过pageId找到对应的MTDocument，或让MTDocument通过pageId找到对应的NativeDOM::Document
class DocumentRegistry {
public:
  DocumentRegistry() = default;
  ~DocumentRegistry() = default;
  
  void registerDocument(int page_id, const std::shared_ptr<blink::mt::MTDocument>& document);
  void unregisterDocument(int documentId);
  std::shared_ptr<blink::mt::MTDocument> getDocument(int page_id);
  
  void registerDOMDocument(int page_id, const std::shared_ptr<msc::native_dom::Document> document);
  void unregisterDOMDocument(int documentId);
  std::shared_ptr<msc::native_dom::Document> getDOMDocument(int page_id);

  // for reporting metrics
  msc::NativeDOMMetrics& GetMetrics();
  void ReportMetrics(msc::NativeDOMMetrics::ErrCode errcode,
                     const std::string& native_stack = msc::NativeDOMMetrics::API::kNone,
                     const std::string& message = msc::NativeDOMMetrics::Message::kNone);

private:
  msc::NativeDOMMetrics native_dom_metrics_;
  std::unordered_map<int, std::shared_ptr<blink::mt::MTDocument>> documents_;
  std::unordered_map<int, std::shared_ptr<msc::native_dom::Document>> dom_documents_;
  std::mutex mutex_;
};

}
}

#endif /* DocumentRegistry_hpp */
