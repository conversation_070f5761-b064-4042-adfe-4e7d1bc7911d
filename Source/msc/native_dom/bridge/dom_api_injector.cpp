//
// Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/6/24.
//

#include "dom_api_injector.h"

#include "../native_dom_define.h"

#if USE_JSC
#include "../jsc/jsc_binding.h"
#elif USE_JSI
#include "../JSI/JSIBinding.h"
#endif

namespace msc {
namespace native_dom {

void DOMAPIInjector::InjectToJSEngine(void *js_engine, std::shared_ptr<DocumentRegistry> documentRegistry, const char *app_id, const char *pure_path) {
#if USE_JSC
    JSCBinding::SetupJSContext((JSGlobalContextRef)js_engine, documentRegistry, app_id, pure_path);
#elif USE_JSI
    JSIBinding::setupJSContext((msc::jsi::Runtime *)[[service valueForKey:@"jsContext"] runtime], documentRegistry);
#endif
}

} // msc
} // native_dom
