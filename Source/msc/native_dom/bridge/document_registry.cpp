//
//  document_registry.cpp
//  MSCRenderer
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/24.
//

#include "document_registry.h"
#include "../document.h"

namespace msc {
namespace native_dom {

void DocumentRegistry::registerDocument(int page_id, const std::shared_ptr<blink::mt::MTDocument>& document) {
  if (page_id == -1) {
      return;
  }
  MSC_RENDERER_LOG_DEBUG("[native_dom] DocumentRegistry::registerDocument: %d", page_id);
  std::lock_guard<std::mutex> lock(mutex_);
  documents_[page_id] = document;
}

void DocumentRegistry::unregisterDocument(int documentId) {
  std::lock_guard<std::mutex> lock(mutex_);
  documents_.erase(documentId);
}

std::shared_ptr<blink::mt::MTDocument> DocumentRegistry::getDocument(int page_id) {
  std::shared_ptr<blink::mt::MTDocument> document;
  {
    std::lock_guard<std::mutex> lock(mutex_);
    if (auto it = documents_.find(page_id); it != documents_.end()) {
      document = it->second;
    }
  }
  if (not document) {
    this->native_dom_metrics_.ReportError(
      msc::NativeDOMMetrics::Tags{
        msc::NativeDOMMetrics::ErrCode::MTDocumentNotFound,
        msc::NativeDOMMetrics::API::kGetDocument,
        msc::NativeDOMMetrics::Message::kNone
      }
    );
  }
  return document;
}

void DocumentRegistry::registerDOMDocument(int page_id, const std::shared_ptr<msc::native_dom::Document> document) {
  if (page_id == -1) {
    this->native_dom_metrics_.ReportError(
      msc::NativeDOMMetrics::Tags{
        msc::NativeDOMMetrics::ErrCode::InvalidDocumentId,
        msc::NativeDOMMetrics::API::kRegisterDOMDocument,
        msc::NativeDOMMetrics::Message::kInvalidPageId
      }
    );
    return;
  }
  std::lock_guard<std::mutex> lock(mutex_);
  dom_documents_[page_id] = document;
}

void DocumentRegistry::unregisterDOMDocument(int documentId) {
  std::lock_guard<std::mutex> lock(mutex_);
  dom_documents_.erase(documentId);
}

std::shared_ptr<msc::native_dom::Document> DocumentRegistry::getDOMDocument(int page_id) {
  if (page_id == -1) {
      return nullptr;
  }
  std::lock_guard<std::mutex> lock(mutex_);
  auto it = dom_documents_.find(page_id);
  if (it == dom_documents_.end()) {
    return nullptr;
  }
  return it->second;
}

// for reporting metrics
msc::NativeDOMMetrics &DocumentRegistry::GetMetrics() { return this->native_dom_metrics_; }

void DocumentRegistry::ReportMetrics(msc::NativeDOMMetrics::ErrCode errcode, const std::string& native_stack,
                   const std::string& message) {
  this->native_dom_metrics_.ReportError(msc::NativeDOMMetrics::Tags{errcode, native_stack, message});
}

}
}
