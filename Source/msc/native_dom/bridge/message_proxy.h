//
//  message_proxy.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/14.
//

#ifndef MessageProxy_hpp
#define MessageProxy_hpp

#include "../msc_string.h"
#include "../element_type.h"
#include "../container_node.h"
#include "../element.h"
#include "document_registry.h"

#include "mtdocument.h"

namespace msc {
namespace native_dom {

class Element;
class JSIBasicElement;
class TextNode;

//用于让NativeDOM给blink线程发消息
class MessageProxy {
public:
  MessageProxy(std::shared_ptr<msc::native_dom::DocumentRegistry> documentRegistry);
  ~MessageProxy();
  
  std::shared_ptr<msc::native_dom::DocumentRegistry> &GetDocumentRegistry() {
    return document_registry_;
  }
  
  void SendCreateViewMessage(int page_id, JSIBasicElement &element);
  void SendCreateTextNodeMessage(int page_id, const TextNode &text_node);
  void SendUpdateViewMessage(int page_id, Element &element, const MSCString &attribute_name, const blink::mt::PropValue &attribute_value);
  void SendRemoveViewMessage(int page_id, int viewId);
  void SendQueryEnhancedMessage(int pageId,
                                blink::mt::QueryEnhancedParams &&params,
                                JSCallbackInfo callback_info);
  void SendCreateIntersectionObserverMessage(
      int page_id, blink::mt::CreateIntersectionObserverParams &&params,
      JSCallbackInfo callback_info);
  void SendIntersectionObserverObserveMessage(int page_id,
                                              int intersection_observer_id,
                                              int target_id);

  void SendAppendChildMessage(int page_id, int parentId, const std::shared_ptr<const std::vector<int>>& child_tags);
  void SendManageChildrenMessage(int page_id, int parentId,  const std::shared_ptr<const blink::mt::MTDocument::ChildrenChanges>& children_changes);

  void SendBDCMessage(int page_id, blink::mt::LayoutReason layoutReason);
  void SendCreateKeyframesAnimationEnhancedMessage(
      int page_id,
      const std::shared_ptr<blink::mt::AnimationProperties> &properties,
      JSCallbackInfo callback_info);
  void SendClearKeyframesAnimationEnhancedMessage(
      int page_id,
      const std::shared_ptr<blink::mt::ClearAnimationProperties> &options,
      JSCallbackInfo callback_info);

 private:
  inline std::shared_ptr<blink::mt::MTDocument> GetDocument(int page_id);

  std::shared_ptr<msc::native_dom::DocumentRegistry> document_registry_;
};

}  // namespace native_dom
}  // namespace msc

#endif /* MessageProxy_hpp */
