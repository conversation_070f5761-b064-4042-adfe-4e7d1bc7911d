//
// Created by <PERSON><PERSON><PERSON> on 2025/6/24.
//

#ifndef MSC_ANDROID_DOM_API_INJECTOR_H
#define MSC_ANDROID_DOM_API_INJECTOR_H

namespace msc {
namespace native_dom {

class DocumentRegistry;

class DOMAPIInjector {
public:
  static void InjectToJSEngine(void *js_engine, std::shared_ptr<DocumentRegistry> documentRegistry, const char *app_id, const char *pure_path);
  
};

} // msc
} // native_dom

#endif //MSC_ANDROID_DOM_API_INJECTOR_H
