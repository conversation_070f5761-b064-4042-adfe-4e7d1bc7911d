//
//  event_target_data.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/13.
//

#include "event_target_data.h"

namespace msc {
namespace native_dom {

void EventTargetData::AppendIfNotPresent(std::unique_ptr<EventListener> listener, bool& frmoZeroToOne)
{
    bool exists = false;
    for (auto& l : m_listeners) {
        if (l->IsSameObject(listener.get())) {
            exists = true;
            break;
        }
    }

    if (!exists) {
        if (m_listeners.size() == 0)
            frmoZeroToOne = true;
        m_listeners.push_back(std::move(listener));
    }
}

void EventTargetData::RemoveIfPresent(EventListener* listener, bool& fromOneToZero)
{
    for (auto it = m_listeners.begin(); it != m_listeners.end(); ++it) {
        if ((*it).get() == listener || (*it)->IsSameObject(listener)) {
            m_listeners.erase(it);
            if (m_listeners.size() == 0)
                fromOneToZero = true;
            break;
        }
    }
}

size_t EventTargetData::size() const
{
    return m_listeners.size();
}

void EventTargetData::FireEvent(std::shared_ptr<Event> event)
{
    for (auto& item : m_listeners)
        item->FireEvent(event);
}

}
}

