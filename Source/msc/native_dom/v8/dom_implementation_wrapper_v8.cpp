//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/21.
//

#include "dom_implementation_wrapper_v8.h"
#include "wrapper_type_info.h"
#include "v8_binding_utils.h"
#include "../dom_implementation.h"
#include "../document.h"
#include "document_wrapper_v8.h"
#include <chrono>

#if USE_V8

namespace msc {
namespace native_dom {

    void DOMImplementationWrapperV8::CreateDocument(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMImplementationWrapperV8::CreateDocument);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'createDocument' on 'DOMImplementation':1 "
            "argument required");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        v8::HandleScope handle_scope(isolate);
        auto context = isolate->GetCurrentContext();
        DOMImplementation *dom_impl = static_cast<DOMImplementation *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(dom_impl);

        v8::Local<v8::Value> arg0 = info[0];
        auto page_id = arg0->ToInt32(context).ToLocalChecked()->Value();
        std::shared_ptr<Document> document = dom_impl->CreateDocument(page_id);
        auto v8_document = CREATE_V8_OBJECT(isolate, context, DocumentWrapperV8::GetWrapperTypeInfo(), document);
        info.GetReturnValue().Set(v8_document);
    }

    void DOMImplementationWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMImplementationWrapperV8::InstallInterface);
        INTERFACE_SETUP(DOMImplementationWrapperV8)

        static const OperationConfig kOperationTable[] = {
            {DOMImplementation::kCreateDocument.c_str(), CreateDocument, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
        };
        INSTALL_INTERFACE_OPERATIONS(kOperationTable)
    }

    const WrapperTypeInfo DOMImplementationWrapperV8::wrapper_type_info_ = {
        .install_interface_template_func = &InstallInterface,
        .interface_name = "DOMImplementation",
        .parent_class = nullptr,
        .dom_node_type = WrapperTypeInfo::DOMNodeType::kDocumentImplementation,
    };
} // msc
} // native_dom

#endif