//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/26.
//

#include "vnode_wrapper_v8.h"
#include "wrapper_type_info.h"
#include "v8_binding_utils.h"
#include "../vnode.h"

#if USE_V8

namespace msc {
namespace native_dom {

    void GetType(const v8::FunctionCallbackInfo<v8::Value>& info) {

    }

    void GetProps(const v8::FunctionCallbackInfo<v8::Value>& info) {

    }

    void GetChildren(const v8::FunctionCallbackInfo<v8::Value>& info) {

    }

    void GetFlag(const v8::FunctionCallbackInfo<v8::Value>& info) {

    }

    void GetChildFlag(const v8::FunctionCallbackInfo<v8::Value>& info) {

    }

    void GetOwner(const v8::FunctionCallbackInfo<v8::Value>& info) {

    }

    void GetISV(const v8::FunctionCallbackInfo<v8::Value>& info) {
        info.GetReturnValue().Set(v8::Boolean::New(info.GetIsolate(), true));
    }

    void VNodeWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
        INTERFACE_SETUP(VNodeWrapperV8);

//        static const OperationConfig kOperationTable[] = {
//                {Document::kCreateElement.c_str(), CreateElement, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
//                {Document::kCreateVNode.c_str(), CreateVNode, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
//        };
//        INSTALL_INTERFACE_OPERATIONS(kOperationTable);

//        static const AttributeConfig kAttributeTable[] = {
//            {"type", GetType, nullptr, 0, unsigned(FlagLocation::kPrototype)},
//            {"props", GetProps, nullptr, 0, unsigned(FlagLocation::kPrototype)},
//            {"children", GetChildren, nullptr, 0, unsigned(FlagLocation::kPrototype)},
//            {"flag", GetFlag, nullptr, 0, unsigned(FlagLocation::kPrototype)},
//            {"childFlag", GetChildFlag, nullptr, 0, unsigned(FlagLocation::kPrototype)},
//            {"owner", GetOwner, nullptr, 0, unsigned(FlagLocation::kPrototype)},
//            {"__isv__", GetISV, nullptr, 0, unsigned(FlagLocation::kPrototype)},
//        };
//        INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable);
    }

    const WrapperTypeInfo VNodeWrapperV8::wrapper_type_info_ = {
            .install_interface_template_func = &InstallInterface,
            .interface_name = "VNode",
            .parent_class = nullptr,
            .dom_node_type = WrapperTypeInfo::DOMNodeType::kVNode,
    };

}
}

#endif