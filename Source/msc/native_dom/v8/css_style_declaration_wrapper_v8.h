//
// Created by qin<PERSON>wei02 on 2025/4/8.
//

#ifndef MSC_NATIVE_DOM_JSC_CSS_STYLE_DECLARATION_WRAPPER_V8_H
#define MSC_NATIVE_DOM_JSC_CSS_STYLE_DECLARATION_WRAPPER_V8_H

#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

class CSSStyleDeclarationWrapperV8 {
   public:
    static constexpr const WrapperTypeInfo& GetWrapperTypeInfo() {
        return wrapper_type_info_;
    }

   private:
    static void GetCSSText(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void SetCSSText(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void InstallInterface(v8::Isolate* isolate,
                                 v8::Local<v8::Template> interface_template);
    static const WrapperTypeInfo wrapper_type_info_;
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif  // MSC_NATIVE_DOM_JSC_CSS_STYLE_DECLARATION_WRAPPER_V8_H
