//
// Created by <PERSON><PERSON><PERSON> on 2025/3/21.
//

#ifndef MSC_ANDROID_V8INTERFACEBRIDGE_H
#define MSC_ANDROID_V8INTERFACEBRIDGE_H

#include "../../../../libs/include_v8.0/v8.h"
#include "../native_dom_define.h"

#if USE_V8

namespace msc {
namespace native_dom {

class V8InterfaceBridge {
public:
    using InstallInterfaceTemplateFuncType = void (*)(v8::Isolate* isolate, v8::Local<v8::Template> interface_template);
};

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_V8INTERFACEBRIDGE_H
