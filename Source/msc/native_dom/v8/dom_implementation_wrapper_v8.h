//
// Created by <PERSON><PERSON><PERSON> on 2025/3/21.
//

#ifndef MSC_ANDROID_DOMIMPLEMENTATIONWRAPPERV8_H
#define MSC_ANDROID_DOMIMPLEMENTATIONWRAPPERV8_H

#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

class DOMImplementationWrapperV8 {
public:
    static const WrapperTypeInfo &GetWrapperTypeInfo() { return wrapper_type_info_; }

private:
    static void CreateDocument(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void FrameMarker(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);
    static const WrapperTypeInfo wrapper_type_info_;
};

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_DOMIMPLEMENTATIONWRAPPERV8_H
