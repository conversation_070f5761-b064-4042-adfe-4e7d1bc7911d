//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/22.
//

#ifndef MSC_ANDROID_DOCUMENTWRAPPERV8_H
#define MSC_ANDROID_DOCUMENTWRAPPERV8_H

#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

    class DocumentWrapperV8 {
    public:
        static constexpr const WrapperTypeInfo& GetWrapperTypeInfo() {
            return wrapper_type_info_;
        }
    private:
        static void CreateElement(const v8::FunctionCallbackInfo<v8::Value>& info);
        static void CreateTextNode(const v8::FunctionCallbackInfo<v8::Value>& info);
        static void CreateVNode(const v8::FunctionCallbackInfo<v8::Value>& info);
        static void GetBody(const v8::FunctionCallbackInfo<v8::Value>& info);
        static void FrameMarker(const v8::FunctionCallbackInfo<v8::Value>& info);
        static void QueryEnhanced(const v8::FunctionCallbackInfo<v8::Value>& info);
        static void CreateKeyframesAnimationEnhanced(
            const v8::FunctionCallbackInfo<v8::Value>& info);
        static void ClearKeyframesAnimationEnhanced(
            const v8::FunctionCallbackInfo<v8::Value>& info);

        static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);
        static const WrapperTypeInfo wrapper_type_info_;
    };

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_DOCUMENTWRAPPERV8_H
