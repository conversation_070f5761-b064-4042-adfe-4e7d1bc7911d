//
// Created by jack on 2025/6/5.
//

#ifndef MSC_ANDROID_V8_JS_CALLBACK_H
#define MSC_ANDROID_V8_JS_CALLBACK_H

#include "../../Public/task_runner.h"
#include "../../../../libs/include_v8.0/v8.h"

namespace msc {
namespace native_dom {

struct V8JSCallback : JSCallback {
  V8JSCallback(std::shared_ptr<v8::Global<v8::Value>> callback,
               std::shared_ptr<v8::Global<v8::Context>> context,
               std::unique_ptr<JSCallable> callable)
      : global_callback_(callback),
        global_context_(context),
        JSCallback(std::move(callable)) {}
  ~V8JSCallback() override {
    global_callback_->Reset();
    global_context_->Reset();
  }

  std::shared_ptr<v8::Global<v8::Value>> global_callback_;
  std::shared_ptr<v8::Global<v8::Context>> global_context_;
};

}  // namespace native_dom
}  // namespace msc

#endif  // MSC_ANDROID_V8_JS_CALLBACK_H
