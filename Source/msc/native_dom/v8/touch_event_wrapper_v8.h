//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/6/4.
//

#ifndef MSC_ANDROID_TOUCH_EVENT_WRAPPER_V8_H
#define MSC_ANDROID_TOUCH_EVENT_WRAPPER_V8_H

#include "event_wrapper_v8.h"

namespace msc {
namespace native_dom {

class TouchEventWrapperV8 {
public:
    static constexpr const WrapperTypeInfo &GetWrapperTypeInfo() {
        return wrapper_type_info_;
    }

private:
    static void GetTouches(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetChangedTouches(const v8::FunctionCallbackInfo<v8::Value> &info);

    static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);

    static const WrapperTypeInfo wrapper_type_info_;
};

} // msc
} // native_dom

#endif //MSC_ANDROID_TOUCH_EVENT_WRAPPER_V8_H
