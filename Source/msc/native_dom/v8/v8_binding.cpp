//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/20.
//

#include "v8_binding.h"

//JSI binding
#include "../bridge/message_proxy.h"
#include "../dom_implementation.h"
#include "v8_binding_utils.h"
#include "dom_implementation_wrapper_v8.h"
#include "intersection_observer_wrapper_v8.h"
#include "event_wrapper_v8.h"

#if USE_V8

namespace msc {
    namespace native_dom {
#ifndef __APPLE__
        std::unordered_map<long,  std::shared_ptr<DocumentRegistry>> V8Binding::registryForJsRuntimeMap_;

        void V8Binding::newDocumentRegistryInUIThread(long jsRuntimePtr) {
            if (registryForJsRuntimeMap_.find(jsRuntimePtr) == registryForJsRuntimeMap_.end()) {
                auto documentRegister = std::make_shared<msc::native_dom::DocumentRegistry>();
                registryForJsRuntimeMap_[jsRuntimePtr] = documentRegister;
            }
        }

        std::shared_ptr<DocumentRegistry> V8Binding::getDocumentRegistryByRuntime(long jsRuntimePtr) {
            auto it = registryForJsRuntimeMap_.find(jsRuntimePtr);
            if (it == registryForJsRuntimeMap_.end()) {
                return nullptr;
            }
            return it->second;
        }

        void V8Binding::unregisterDocumentRegistryByRuntime(long jsRuntimePtr) {
            registryForJsRuntimeMap_.erase(jsRuntimePtr);
            auto *runtime = reinterpret_cast<msc::jsi::Runtime *>(jsRuntimePtr);
            auto isolate = static_cast<v8::Isolate *>(runtime->GetAddressOfIsolate());
            WrapperTypeInfo::ClearV8ClassTemplate(isolate);
        }

        std::unique_ptr<v8::Function> bindClassConstructor(v8::Isolate *isolate, v8::Context *context, const char *name, v8::FunctionCallback callback) {
            v8::Local<v8::FunctionTemplate> functionTemplate = v8::FunctionTemplate::New(isolate, callback);

            //TODO: ToLocalChecked可能需要先判断是否为空，确认最佳实践
            functionTemplate->SetClassName(v8::String::NewFromUtf8(isolate, name).ToLocalChecked());
//            auto function = std::make_unique<v8::Function>(functionTemplate->GetFunction(context).ToLocalChecked());
//            context->Global()->Set(isolate->GetCurrentContext(), v8::String::NewFromUtf8(isolate, name).ToLocalChecked(), function).ToChecked();
        }

        void V8Binding::NativeNow(const v8::FunctionCallbackInfo<v8::Value>& args) {
            auto now = std::chrono::high_resolution_clock::now();
            auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
            args.GetReturnValue().Set(v8::Number::New(args.GetIsolate(), nanoseconds));
            return;
        }

        void V8Binding::setupJSContext(long jsRuntimePtr, const char* app_id, const char* pure_path) {
            MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(V8Binding::setupJSContext);
            auto *runtime = reinterpret_cast<msc::jsi::Runtime *>(jsRuntimePtr);

            //Get Isolate
            auto isolate = static_cast<v8::Isolate *>(runtime->GetAddressOfIsolate());

            // 兜底：初始化的时候先清理一下template
            WrapperTypeInfo::ClearV8ClassTemplate(isolate);

            MSC_RENDERER_LOG_DEBUG("V8Binding::setupJSContext, runtime: %p, isolate: %p", jsRuntimePtr, isolate);


            //Get Context
            v8::Global<v8::Context> *global_context = static_cast<v8::Global<v8::Context>*>(runtime->GetAddressOfContext());
            v8::Isolate::Scope isolateScope(isolate);
            v8::HandleScope handle_scope(isolate);
            v8::Local<v8::Context> context = v8::Local<v8::Context>::New(isolate, *global_context);
            v8::Context::Scope context_scope(context);

            //Get globalObject
            v8::Local<v8::Object> globalObject = context->Global();

            //Create global.native Object
            v8::Local<v8::Object> nativeObject = v8::Object::New(isolate);
            //Set global.native
            globalObject->Set(context, v8::String::NewFromUtf8(isolate, "native").FromMaybe(v8::Local<v8::String>()), nativeObject).ToChecked();

            //Set global.nativeNow
            v8::Local<v8::FunctionTemplate> native_now_func_template = v8::FunctionTemplate::New(isolate, V8Binding::NativeNow);
            v8::Local<v8::Function> native_now_func = native_now_func_template->GetFunction(context).ToLocalChecked();
            globalObject->Set(context, v8::String::NewFromUtf8(isolate, "nativeNow").ToLocalChecked(), native_now_func).ToChecked();


            std::shared_ptr<DocumentRegistry> registry = getDocumentRegistryByRuntime(jsRuntimePtr);
            auto messageProxy = std::make_shared<MessageProxy>(registry);
            DOMImplementation *domImplementation = new DOMImplementation(messageProxy);
            auto v8_dom_implementation = CREATE_V8_OBJECT(isolate, context, DOMImplementationWrapperV8::GetWrapperTypeInfo(), domImplementation);

            nativeObject->Set(context, v8::String::NewFromUtf8(isolate, "implementation").ToLocalChecked(), v8_dom_implementation).ToChecked();

            IntersectionObserverWrapperV8::GetWrapperTypeInfo().GetV8ClassTemplate(isolate);
            EventWrapperV8::GetWrapperTypeInfo().GetV8ClassTemplate(isolate);

            v8::Local<v8::String> enable_native_dom_key = v8::String::NewFromUtf8(isolate, "enableNativeDOM").ToLocalChecked();
            v8::Local<v8::String> app_id_str = v8::String::NewFromUtf8(isolate, app_id).ToLocalChecked();
            v8::Local<v8::String> page_path_str = v8::String::NewFromUtf8(isolate, pure_path).ToLocalChecked();

            v8::Local<v8::Object> config_obj;
            v8::Local<v8::Object> app_obj;

            // global.native.enableNativeDOM
            v8::Local<v8::Value> config_value;
            if (nativeObject->Get(context, enable_native_dom_key).ToLocal(&config_value) &&
                !config_value->IsUndefined()) {
              config_obj = config_value->ToObject(context).ToLocalChecked();

              // global.native.enableNativeDOM[app_id]
              v8::Local<v8::Value> app_value;
              if (config_obj->Get(context, app_id_str).ToLocal(&app_value) &&
                  !app_value->IsUndefined()) {
                app_obj = app_value->ToObject(context).ToLocalChecked();
              }
            } else {
              config_obj = v8::Object::New(isolate);
            }

            if (app_obj.IsEmpty()) {
              app_obj = v8::Object::New(isolate);
            }

            // app_obj.pagePath = true
            app_obj->Set(context, page_path_str, v8::Boolean::New(isolate, true)).Check();
            // config_obj.appId = app_obj
            config_obj->Set(context, app_id_str, app_obj).Check();
            // global.native.enableNativeDOM = config_obj
            nativeObject->Set(context, enable_native_dom_key, config_obj).Check();
        }
#endif
    }
}

#endif
