//
// Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/6/9.
//

#ifndef MSC_ANDROID_NATIVE_DOM_OBJECT_WRAPPER_H
#define MSC_ANDROID_NATIVE_DOM_OBJECT_WRAPPER_H

#include "../script_wrappable.h"
#include "../native_dom_define.h"

#include <memory>
#include <type_traits>
#include "../../logger.h"

#if USE_V8

namespace msc {
namespace native_dom {

//引用关系说明：
//1. JSObject通过raw指针强引用NativeDOMObjectWrapper, JSObject销毁时, NativeDOMObjectWrapper销毁（delete）
//2. NativeDOMObjectWrapper强引用ScriptWrappable, NativeDOMObjectWrapper销毁时, ScriptWrappable销毁
//3. NativeDOMObjectWrapper一开始是强引用JSObject，后续结点下树时会变成弱引用JSObject, 后续JSObject销毁时, NativeDOMObjectWrapper释放资源
class NativeDOMObjectWrapper {
public:
    template <typename T>
    NativeDOMObjectWrapper(T script_wrappable) {
        if constexpr (std::is_pointer_v<T> && std::is_base_of_v<ScriptWrappable, std::remove_pointer_t<T>>) {
            MSC_RENDERER_LOG_DEBUG("NativeDOMObjectWrapper: pointer %p, script_wrappable: %p", this, script_wrappable);
            // 存储ScriptWrappable*
            script_wrappable_ptr_ = script_wrappable;
            script_wrappable_shared_ptr_.reset();
        } else if constexpr (std::is_same_v<T, std::shared_ptr<ScriptWrappable>> || std::is_base_of_v<ScriptWrappable, typename T::element_type>) {
            MSC_RENDERER_LOG_DEBUG("NativeDOMObjectWrapper: shared_ptr %p", this);
            // 存储std::shared_ptr<ScriptWrappable>
            script_wrappable_shared_ptr_ = script_wrappable;
            script_wrappable_ptr_ = nullptr;
        } else {
            static_assert(always_false<T>::value, "Unsupported type");
        }
        js_object_.Reset();
    }

    ~NativeDOMObjectWrapper() {
        MSC_RENDERER_LOG_DEBUG("NativeDOMObjectWrapper: ~NativeDOMObjectWrapper: %p", this);
        // 释放资源
        this->ClearInstance();
    }

    ScriptWrappable* GetScriptWrappablePtr() const {
        if (script_wrappable_ptr_) {
            return script_wrappable_ptr_;
        } else {
            return script_wrappable_shared_ptr_.get();
        }
    }

    std::shared_ptr<ScriptWrappable> GetScriptWrappableSharedPtr() const {
        return script_wrappable_shared_ptr_;
    }

    void SetJSObject(v8::Isolate *isolate, v8::Local<v8::Object> object);

    v8::Local<v8::Object> GetJSObject() const {
        return js_object_.Get(isolate_);
    }

    void ClearJSObject() {
        if (!js_object_.IsEmpty()) {
            js_object_.SetWeak(this, NativeDOMObjectWrapper::FinalizeJSObject,
                               v8::WeakCallbackType::kParameter);
        }
    }

    void ResetJSObject() {
        MSC_RENDERER_LOG_DEBUG("NativeDOMObjectWrapper::ResetJSObject, setting internal_field to nullptr");
        js_object_.Reset();
    }

    void ClearInstance() {
        MSC_RENDERER_LOG_DEBUG("NativeDOMObjectWrapper:ClearInstance: %p, deleting script_wrappable_ptr", this);
        if (script_wrappable_ptr_) {
            delete script_wrappable_ptr_;
            script_wrappable_ptr_ = nullptr;
        }
        script_wrappable_shared_ptr_.reset();
    }

    void Destroy() {
        ClearJSObject();
        ClearInstance();
    }

    void ForceGCDebug() {
//        isolate_->RequestGarbageCollectionForTesting(v8::Isolate::kFullGarbageCollection);
    }

private:

    static void FinalizeJSObject(const v8::WeakCallbackInfo<NativeDOMObjectWrapper>& info);

    ScriptWrappable* script_wrappable_ptr_ = nullptr;
    std::shared_ptr<ScriptWrappable> script_wrappable_shared_ptr_ = nullptr;

    v8::Persistent<v8::Object, v8::CopyablePersistentTraits<v8::Value>> js_object_;
    v8::Isolate *isolate_ = nullptr;

    // 辅助模板，用于static_assert
    template <typename T>
    struct always_false : std::false_type {};

//public:
//    enum class WrappedObjectType {
//        kRawPtr = 0,
//        kSharedPtr = 1
//    };
//
//    NativeDOMObjectWrapper(WrappedObjectType type, void *ptr) : type_(type), wrapped_ptr_(ptr) {}
//
//    template <typename T>
//    T *GetInstance() {
//        if (type_ == WrappedObjectType::kRawPtr) {
//            return static_cast<T *>(wrapped_ptr_);
//        } else if (type_ == WrappedObjectType::kSharedPtr){
//            std::shared_ptr<T> *shared_ptr_value = static_cast<std::shared_ptr<T> *>(wrapped_ptr_);
//            return shared_ptr_value ? shared_ptr_value->get() : nullptr;
//        }
//        return nullptr;
//    }
//
//    virtual ~NativeDOMObjectWrapper() {
//        delete wrapped_ptr_;
//    };
//
//private:
//    void *wrapped_ptr_;
//    WrappedObjectType type_;
};

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_NATIVE_DOM_OBJECT_WRAPPER_H
