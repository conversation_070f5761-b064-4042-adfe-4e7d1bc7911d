//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/23.
//

#include "event_wrapper_v8.h"
#include "v8_binding_utils.h"
#include "../event.h"
#include "../event_target.h"
#include "../node.h"

#if USE_V8

namespace msc {
namespace native_dom {

    void EventWrapperV8::StopPropagation(const v8::FunctionCallbackInfo<v8::Value> &info) {
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Event *event = static_cast<Event *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(event);
        event->SetStopPropagation();
    }

    void EventWrapperV8::GetType(const v8::FunctionCallbackInfo<v8::Value> &info) {
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();

        Event *event = static_cast<Event *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(event);

        v8::Local<v8::String> result = V8BindingUtils::ToV8String(isolate, event->typeName());
        info.GetReturnValue().Set(result);
    }

    void EventWrapperV8::GetTimeStamp(const v8::FunctionCallbackInfo<v8::Value> &info) {
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();

        Event *event = static_cast<Event *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(event);
        long long ts = pointToMillisecondsFromEpoch(event->timestamp());
        info.GetReturnValue().Set((double)ts);
    }

    void EventWrapperV8::GetCurrentTarget(const v8::FunctionCallbackInfo<v8::Value> &info) {
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();

        Event *event = static_cast<Event *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(event);
        v8::Local<v8::Object> result = GET_JSOBJECT_FROM(isolate, event->currentTarget());

        info.GetReturnValue().Set(result);
    }

    void EventWrapperV8::GetTarget(const v8::FunctionCallbackInfo<v8::Value> &info) {
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();

        Event *event = static_cast<Event *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(event);
        v8::Local<v8::Object> result = GET_JSOBJECT_FROM(isolate, event->target());
        info.GetReturnValue().Set(result);
    }

    void EventWrapperV8::GetEventPhase(const v8::FunctionCallbackInfo<v8::Value> &info) {
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();

        Event *event = static_cast<Event *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(event);

        info.GetReturnValue().Set(event->eventPhase());
    }

    void EventWrapperV8::GetDetail(const v8::FunctionCallbackInfo<v8::Value> &info) {
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();

        Event *event = static_cast<Event *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(event);
        if (event->detail()) {
            v8::Local<v8::Object> detail = V8BindingUtils::V8ObjectFromProps(isolate, *(event->detail()), "target");
            info.GetReturnValue().Set(detail);
        } else {
            info.GetReturnValue().Set(v8::Object::New(isolate));
        }
    }

    void EventWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(EventWrapperV8::InstallInterface);
        INTERFACE_SETUP(EventWrapperV8);

        static const OperationConfig kOperationTable[] = {
                {Event::kStopPropagation.c_str(), StopPropagation, 0, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
        };
        INSTALL_INTERFACE_OPERATIONS(kOperationTable);

        static const AttributeConfig kAttributeTable[] = {
                {"type", GetType, nullptr, 0, unsigned(FlagLocation::kPrototype) },
                {"timeStamp", GetTimeStamp, nullptr, 0, unsigned(FlagLocation::kPrototype)},
                {"currentTarget", GetCurrentTarget, nullptr, 0, unsigned(FlagLocation::kPrototype)},
                {"target", GetTarget, nullptr, 0, unsigned(FlagLocation::kPrototype)},
                {"eventPhase", GetEventPhase, nullptr, 0, unsigned(FlagLocation::kPrototype)},
                {"detail", GetDetail, nullptr, 0, unsigned(FlagLocation::kPrototype)},
        };
        INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable);

    }

    const WrapperTypeInfo EventWrapperV8::wrapper_type_info_ = {
            .install_interface_template_func = &InstallInterface,
            .interface_name = "Event",
            .parent_class = nullptr,
            .dom_node_type = WrapperTypeInfo::DOMNodeType::kEvent,
    };
} // msc
} // native_dom

#endif