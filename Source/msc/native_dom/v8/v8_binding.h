//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/20.
//

#ifndef MSC_ANDROID_V8BINDING_H
#define MSC_ANDROID_V8BINDING_H

#include "../bridge/document_registry.h"
#include "../../../../libs/include_v8.0/v8.h"
#include "../native_dom_define.h"

#if USE_V8

namespace msc {
namespace native_dom {

class V8Binding {
public:
    V8Binding() = default;
    ~V8Binding() = default;

#ifndef __APPLE__
        static void newDocumentRegistryInUIThread(long jsRuntimePtr);
        static void unregisterDocumentRegistryByRuntime(long jsRuntimePtr);
        static  std::shared_ptr<DocumentRegistry> getDocumentRegistryByRuntime(long jsRuntimePtr);
        static void setupJSContext(long jsRuntimePtr, const char* app_id, const char* pure_path);
private:
        static void NativeNow(const v8::FunctionCallbackInfo<v8::Value>& args);
        static std::unordered_map<long,  std::shared_ptr<DocumentRegistry>> registryForJsRuntimeMap_;
#endif
};

}
}

#endif

#endif //MSC_ANDROID_V8BINDING_H
