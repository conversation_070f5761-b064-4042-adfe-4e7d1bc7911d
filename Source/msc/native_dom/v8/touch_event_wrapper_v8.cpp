//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/6/4.
//

#include "touch_event_wrapper_v8.h"

#include "v8_binding_utils.h"
#include "../event.h"

#if USE_V8

namespace msc {
namespace native_dom {

static v8::Local<v8::Array> CreateTouchArray(v8::Isolate* isolate, const std::vector<msc::native_dom::Touch>& touches) {
    v8::EscapableHandleScope handle_scope(isolate);
    v8::Local<v8::Context> context = isolate->GetCurrentContext();

#define DECLARE_TOUCH_PROP(name) v8::Local<v8::String> prop_##name = v8::String::NewFromUtf8(isolate, #name).ToLocalChecked();

    // 预创建属性名称
    DECLARE_TOUCH_PROP(clientX)
    DECLARE_TOUCH_PROP(clientY)
    DECLARE_TOUCH_PROP(pageX)
    DECLARE_TOUCH_PROP(pageY)
    DECLARE_TOUCH_PROP(identifier)
    DECLARE_TOUCH_PROP(force)

    // 创建数组
    v8::Local<v8::Array> jsArray = v8::Array::New(isolate, touches.size());

    for (size_t i = 0; i < touches.size(); ++i) {
        const auto& touch = touches[i];
        // 创建触摸对象
        v8::Local<v8::Object> obj = v8::Object::New(isolate);

#define SET_TOUCH_PROP(name, getter) obj->Set(context, prop_##name, v8::Number::New(isolate, touch.getter())).Check();

        // 设置属性
        SET_TOUCH_PROP(clientX, GetClientX)
        SET_TOUCH_PROP(clientY, GetClientY)
        SET_TOUCH_PROP(pageX, GetPageX)
        SET_TOUCH_PROP(pageY, GetPageY)
        SET_TOUCH_PROP(identifier, GetIdentifier)
        SET_TOUCH_PROP(force, GetForce)

        // 将对象添加到数组
        jsArray->Set(context, i, obj).Check();
    }

    return handle_scope.Escape(jsArray);
}

void TouchEventWrapperV8::GetTouches(const v8::FunctionCallbackInfo<v8::Value> &info) {
    v8::Local<v8::Object> v8_receiver = info.This();
    v8::Isolate *isolate = info.GetIsolate();
    TouchEvent *event = static_cast<TouchEvent *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
    V8_API_CHECK_NULL_RETURN(event);
    v8::Local<v8::Array> result = CreateTouchArray(isolate, event->touches());
    info.GetReturnValue().Set(result);
}

void TouchEventWrapperV8::GetChangedTouches(const v8::FunctionCallbackInfo<v8::Value> &info) {
    v8::Local<v8::Object> v8_receiver = info.This();
    v8::Isolate *isolate = info.GetIsolate();
    TouchEvent *event = static_cast<TouchEvent *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
    V8_API_CHECK_NULL_RETURN(event);
    v8::Local<v8::Array> result = CreateTouchArray(isolate, event->changedTouches());
    info.GetReturnValue().Set(result);
}

void TouchEventWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
    MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(TouchEventWrapperV8::InstallInterface);
    INTERFACE_SETUP(TouchEventWrapperV8);


    static const AttributeConfig kAttributeTable[] = {
            {"touches", GetTouches, nullptr, 0, unsigned(FlagLocation::kPrototype) },
            {"changedTouches", GetChangedTouches, nullptr, 0, unsigned(FlagLocation::kPrototype)},
    };
    INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable);

}

const WrapperTypeInfo TouchEventWrapperV8::wrapper_type_info_ = {
        .install_interface_template_func = &InstallInterface,
        .interface_name = "TouchEvent",
        .parent_class = &EventWrapperV8::GetWrapperTypeInfo(),
        .dom_node_type = WrapperTypeInfo::DOMNodeType::kTouchEvent,
};
} // msc
} // native_dom

#endif