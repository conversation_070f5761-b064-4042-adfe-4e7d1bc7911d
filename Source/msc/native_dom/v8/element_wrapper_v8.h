//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/22.
//

#ifndef MSC_ANDROID_ELEMENTWRAPPERV8_H
#define MSC_ANDROID_ELEMENTWRAPPERV8_H

#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

class ElementWrapperV8 {
public:
    static constexpr const WrapperTypeInfo& GetWrapperTypeInfo() {
        return wrapper_type_info_;
    }
private:
    static void GetAttribute(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void SetAttribute(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void RemoveAttribute(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void GetClassName(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void SetClassName(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void GetClass(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void SetClass(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void GetClassList(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void GetTagName(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void GetStyle(const v8::FunctionCallbackInfo<v8::Value>& info);
//    static void SetStyle(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void GetId(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void SetId(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);
    static const WrapperTypeInfo wrapper_type_info_;
};

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_ELEMENTWRAPPERV8_H
