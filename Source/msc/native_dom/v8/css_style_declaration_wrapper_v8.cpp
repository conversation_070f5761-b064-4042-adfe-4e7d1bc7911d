//
// Created by qinqiwei02 on 2025/4/8.
//
#include "css_style_declaration_wrapper_v8.h"

#include "../css_style_declaration.h"
#include "node_wrapper_v8.h"
#include "v8_binding_utils.h"
#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

void CSSStyleDeclarationWrapperV8::GetCSSText(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
    MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(CSSStyleDeclarationWrapperV8::GetCSSText);
    v8::Local<v8::Object> v8_receiver = info.This();
    v8::Isolate *isolate = info.GetIsolate();
    CSSStyleDeclaration *css_style_declaration =
        static_cast<CSSStyleDeclaration *>(
            V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
    V8_API_CHECK_NULL_RETURN(css_style_declaration);
    MSCString css_text = css_style_declaration->cssText();
    info.GetReturnValue().Set(
        V8BindingUtils::ToV8String(isolate, css_text.c_str()));
}
void CSSStyleDeclarationWrapperV8::SetCSSText(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
    MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(CSSStyleDeclarationWrapperV8::SetCSSText);
    V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
        info, 1,
        "Failed to execute 'SetCSSText' on 'CSSStyleDeclaration':1 argument "
        "required");
    V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
        info, info[0],
        "Failed to execute 'SetCSSText' on 'CSSStyleDeclaration':1st argument "
        "must be string");
    v8::Local<v8::Object> v8_receiver = info.This();
    v8::Isolate *isolate = info.GetIsolate();
    CSSStyleDeclaration *css_style_declaration =
        static_cast<CSSStyleDeclaration *>(
            V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
    V8_API_CHECK_NULL_RETURN(css_style_declaration);
    v8::MaybeLocal<v8::String> css_text_v8 =
        info[0]->ToString(isolate->GetCurrentContext());
    std::string css_text = V8BindingUtils::ToSTDString(
        isolate, css_text_v8.ToLocalChecked().As<v8::String>());
    css_style_declaration->setCSSText(css_text);
}

void CSSStyleDeclarationWrapperV8::InstallInterface(
    v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
    MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(CSSStyleDeclarationWrapperV8::InstallInterface);
    INTERFACE_SETUP(CSSStyleDeclarationWrapperV8);

    static const AttributeConfig kAttributeTable[] = {
        {CSSStyleDeclaration::kCSSText.c_str(), GetCSSText, SetCSSText, 0,
         unsigned(FlagLocation::kPrototype)},
    };
    INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable);
}

const WrapperTypeInfo CSSStyleDeclarationWrapperV8::wrapper_type_info_ = {
    .install_interface_template_func = &InstallInterface,
    .interface_name = "CSSStyleDeclaration",
    .parent_class = nullptr,
    .dom_node_type = WrapperTypeInfo::DOMNodeType::kCSSStyleDeclaration,
};

}  // namespace native_dom
}  // namespace msc

#endif
