//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/22.
//

#include "node_wrapper_v8.h"
#include "v8_binding_utils.h"
#include "event_target_wrapper_v8.h"
#include "../node.h"
#include "../container_node.h"
#include "element_wrapper_v8.h"
#include "native_dom/element.h"
#include "../msc_string.h"
#include "event_wrapper_v8.h"
#include "event_listener_v8.h"

#if USE_V8

#define V8_STRING_FROM_CSTR(str) v8::String::NewFromUtf8(isolate, str).ToLocalChecked()

namespace msc {
namespace native_dom {

    void NodeWrapperV8::AppendChild(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::AppendChild);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'appendChild' on 'Node':1 argument required");
        V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'appendChild' on 'Node':1 argument must be "
            "object");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *parent_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(parent_node);

        //TODO: 参数类型校验和兼容转换（尽量按标准，不兼容，实在需要兼容再case by case看）
        auto child_value = info[0]->ToObject(isolate->GetCurrentContext());
        auto child_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(isolate, child_value.ToLocalChecked().As<v8::Object>()));
        V8_API_CHECK_NULL_RETURN(child_node);
        parent_node->AppendChild(child_node);
        info.GetReturnValue().Set(info[0]);
    }

    void NodeWrapperV8::InsertBefore(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::InsertBefore);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 2,
            "Failed to execute 'insertBefore' on 'Node':2 arguments required");
        V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'insertBefore' on 'Node':1st argument must be "
            "object");
        V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
            info, info[1],
            "Failed to execute 'insertBefore' on 'Node':2rd argument must be "
            "object");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *parent_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(parent_node);

        //TODO: 参数类型校验和兼容转换（尽量按标准，不兼容，实在需要兼容再case by case看）
        auto new_node_value = info[0]->ToObject(isolate->GetCurrentContext());
        auto new_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(isolate, new_node_value.ToLocalChecked().As<v8::Object>()));
        V8_API_CHECK_NULL_RETURN(new_node);

        auto reference_node_value = info[1]->ToObject(isolate->GetCurrentContext());
        auto reference_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(isolate, reference_node_value.ToLocalChecked().As<v8::Object>()));
        V8_API_CHECK_NULL_RETURN(reference_node);
        parent_node->InsertBefore(new_node, reference_node);
    }

    void NodeWrapperV8::RemoveChild(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::RemoveChild);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'removeChild' on 'Node':1 argument required");
        V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'removeChild' on 'Node':1st argument must be "
            "object");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *parent_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(parent_node);

        //TODO: 参数类型校验和兼容转换（尽量按标准，不兼容，实在需要兼容再case by case看）
        auto child_value = info[0]->ToObject(isolate->GetCurrentContext());
        auto child_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(isolate, child_value.ToLocalChecked().As<v8::Object>()));
        V8_API_CHECK_NULL_RETURN(child_node);
        parent_node->RemoveChild(child_node);
    }

    void NodeWrapperV8::Remove(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::Remove);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(node);
        node->Remove();
    }

    void NodeWrapperV8::GetFirstChild(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::GetFirstChild);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *parent_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(parent_node);
        Node *first_child = parent_node->firstChild();
        if (first_child) {
            info.GetReturnValue().Set(GET_JSOBJECT_FROM(isolate, first_child));
        }
    }

    void NodeWrapperV8::GetLastChild(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::GetLastChild);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *parent_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(parent_node);
        Node *last_child = parent_node->lastChild();
        if (last_child) {
            info.GetReturnValue().Set(GET_JSOBJECT_FROM(isolate, last_child));
        }
    }

    void NodeWrapperV8::GetPreviousSibling(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::GetPreviousSibling);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *current_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(current_node);
        Node *previous_sibling = current_node->previousSibling();
        if (previous_sibling) {
            info.GetReturnValue().Set(GET_JSOBJECT_FROM(isolate, previous_sibling));
        }
    }

    void NodeWrapperV8::GetNextSibling(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::GetNextSibling);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *current_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(current_node);
        Node *next_sibling = current_node->nextSibling();
        if (next_sibling) {
            info.GetReturnValue().Set(GET_JSOBJECT_FROM(isolate, next_sibling));
        }
    }

    void NodeWrapperV8::GetParentNode(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::GetParentNode);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *current_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(current_node);
        Node *parent_node = current_node->parentNode();
        if (parent_node) {
            info.GetReturnValue().Set(GET_JSOBJECT_FROM(isolate, parent_node));
        }
    }

    void NodeWrapperV8::HasChildNodes(
        const v8::FunctionCallbackInfo<v8::Value> &info) {
      MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::HasChildNodes);
      v8::Local<v8::Object> v8_receiver = info.This();
      v8::Isolate *isolate = info.GetIsolate();
      Node *current_node = static_cast<Node *>(
          V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
      V8_API_CHECK_NULL_RETURN(current_node);

      info.GetReturnValue().Set(current_node->firstChild() != nullptr &&
                                current_node->lastChild() != nullptr);
    }

    void NodeWrapperV8::GetParentElement(
        const v8::FunctionCallbackInfo<v8::Value> &info) {
      MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::GetParentElement);
      v8::Local<v8::Object> v8_receiver = info.This();
      v8::Isolate *isolate = info.GetIsolate();
      Node *current_node = static_cast<Node *>(
          V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
      V8_API_CHECK_NULL_RETURN(current_node);

      auto parent_element = current_node->parentElement();
      if (parent_element != nullptr) {
        auto obj = GET_JSOBJECT_FROM(isolate, parent_element);
        info.GetReturnValue().Set(obj);
      } else {
        info.GetReturnValue().SetNull();
      }
    }

    void NodeWrapperV8::GetTextContent(
        const v8::FunctionCallbackInfo<v8::Value> &info) {
      MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::GetTextContent);
      v8::Local<v8::Object> v8_receiver = info.This();
      v8::Isolate *isolate = info.GetIsolate();
      Node *current_node = static_cast<Node *>(
          V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
      V8_API_CHECK_NULL_RETURN(current_node);

      auto text_contect = current_node->TextContent();

      info.GetReturnValue().Set(
          V8BindingUtils::ToV8String(isolate, text_contect.c_str()));
    }

    void NodeWrapperV8::SetTextContent(
        const v8::FunctionCallbackInfo<v8::Value> &info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::SetTextContent);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1, "Failed to execute 'setTextContext':1 argument required");
        V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'setTextContext':1 argument must be string");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *current_node = static_cast<Node *>(
                V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(current_node);

        auto text_content = V8BindingUtils::ToSTDString(
                isolate,
                info[0]->ToString(isolate->GetCurrentContext()).ToLocalChecked());
        current_node->SetTextContent(text_content);
    }

    void NodeWrapperV8::AddEventListener(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::AddEventListener);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 2,
            "Failed to execute 'addEventListener' on 'EventTarget':2 arguments "
            "required");
        V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'addEventListener' on 'EventTarget':1st "
            "argument must be string");
        V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
            info, info[1],
            "Failed to execute 'addEventListener' on 'EventTarget':2rd "
            "argument must be Object");

        int args_length = info.Length();
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *current_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(current_node);
        auto event_name = info[0]->ToString(isolate->GetCurrentContext()).ToLocalChecked();
        auto event_listener = info[1]->ToObject(isolate->GetCurrentContext()).ToLocalChecked();

        bool capture = false;
        if (args_length >= 3 && info[2]->IsBoolean()) {
            capture = info[2]->BooleanValue(info.GetIsolate());
        }
        MSCString event_name_str = V8BindingUtils::ToSTDString(isolate, event_name);

        auto g_listener = std::make_shared<v8::Global<v8::Object>>(isolate, event_listener);
        auto g_context = std::make_shared<v8::Global<v8::Context>>(isolate, isolate->GetCurrentContext());
        current_node->AddEventListener(event_name_str, std::make_unique<V8EventListener>(isolate, g_listener, g_context, capture));
    }

    void NodeWrapperV8::RemoveEventListener(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::RemoveEventListener);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 2,
            "Failed to execute 'removeEventListener' on 'EventTarget':2 "
            "arguments required");
        V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'removeEventListener' on 'EventTarget':1st "
            "argument must be string");
        V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
            info, info[1],
            "Failed to execute 'removeEventListener' on 'EventTarget':2rd "
            "argument must be Object");

        int args_length = info.Length();
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Node *current_node = static_cast<Node *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(current_node);
        auto event_name = info[0]->ToString(isolate->GetCurrentContext()).ToLocalChecked();
        auto event_listener = info[1]->ToObject(isolate->GetCurrentContext()).ToLocalChecked();

        bool capture = false;
        if (args_length >= 3 && info[2]->IsBoolean()) {
            capture = info[2]->BooleanValue(info.GetIsolate());
        }
        MSCString event_name_str = V8BindingUtils::ToSTDString(isolate, event_name);
        auto g_listener = std::make_shared<v8::Global<v8::Object>>(isolate, event_listener);
        auto g_context = std::make_shared<v8::Global<v8::Context>>(isolate, isolate->GetCurrentContext());
        auto v8_listener = std::make_unique<V8EventListener>(isolate, g_listener, g_context, capture);
        current_node->RemoveEventListener(event_name_str, v8_listener.get());
    }

    void NodeWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(NodeWrapperV8::InstallInterface);
        INTERFACE_SETUP(NodeWrapperV8);

        static const OperationConfig kOperationTable[] = {
            {Node::kAppendChild.c_str(), AppendChild, 2, unsigned(v8::None),
             unsigned(FlagLocation::kPrototype)},
            {Node::kInsertBefore.c_str(), InsertBefore, 2, unsigned(v8::None),
             unsigned(FlagLocation::kPrototype)},
            {Node::kRemove.c_str(), Remove, 0, unsigned(v8::None),
             unsigned(FlagLocation::kPrototype)},
            {"hasChildNodes", HasChildNodes, 2, unsigned(v8::None),
             unsigned(FlagLocation::kPrototype)},
            {Node::kAddEventListener.c_str(), AddEventListener, 3, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
            {Node::kRemoveEventListener.c_str(), RemoveEventListener, 3, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
        };
        INSTALL_INTERFACE_OPERATIONS(kOperationTable);
//
        static const AttributeConfig kAttributeTable[] = {
            {"firstChild", GetFirstChild, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
            {"lastChild", GetLastChild, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
            {"previousSibling", GetPreviousSibling, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
            {"nextSibling", GetNextSibling, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
            {"parentNode", GetParentNode, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
            {"parentElement", GetParentElement, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
            {"textContent", GetTextContent, SetTextContent, 0,
             unsigned(FlagLocation::kPrototype)},
        };
        INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable);
    }

    const WrapperTypeInfo NodeWrapperV8::wrapper_type_info_ = {
            .install_interface_template_func = &InstallInterface,
            .interface_name = "Node",
            .parent_class = nullptr, //EventTargetWrapperV8::GetWrapperTypeInfo(),
            .dom_node_type = WrapperTypeInfo::DOMNodeType::kNode,
    };

} // msc
} // native_dom

#endif
