//
// Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/4/7.
//

#ifndef MSC_ANDROID_TEXT_NODE_WRAPPER_V8_H
#define MSC_ANDROID_TEXT_NODE_WRAPPER_V8_H

#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

class TextNodeWrapperV8 {
public:
    static constexpr const WrapperTypeInfo& GetWrapperTypeInfo() {
        return wrapper_type_info_;
    }
private:
    static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);
    static const WrapperTypeInfo wrapper_type_info_;
};

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_TEXT_NODE_WRAPPER_V8_H
