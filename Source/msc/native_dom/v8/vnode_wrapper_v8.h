//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/26.
//

#ifndef MSC_ANDROID_VNODEWRAPPERV8_H
#define MSC_ANDROID_VNODEWRAPPERV8_H

#include "wrapper_type_info.h"
#include "../vnode.h"

#if USE_V8

namespace msc {
namespace native_dom {
class VNodeWrapperV8 {
public:
    static constexpr const WrapperTypeInfo& GetWrapperTypeInfo() {
        return wrapper_type_info_;
    }

private:
    static void GetType(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetProps(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetChildren(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetFlag(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetChildFlag(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetOwner(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetISV(const v8::FunctionCallbackInfo<v8::Value> &info);

    static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);

    static const WrapperTypeInfo wrapper_type_info_;
};
}
}

#endif

#endif //MSC_ANDROID_VNODEWRAPPERV8_H
