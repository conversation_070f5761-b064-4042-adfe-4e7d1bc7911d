//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/22.
//

#include "element_wrapper_v8.h"
#include "v8_binding_utils.h"
#include "../css_style_declaration.h"
#include "../element.h"
#include "node_wrapper_v8.h"
#include "css_style_declaration_wrapper_v8.h"
#include "dom_token_list_wrapper_v8.h"

#if USE_V8

namespace msc {
namespace native_dom {
    void ElementWrapperV8::GetAttribute(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::GetAttribute);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'getAttribute' on 'Element':1 argument "
            "required");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(element);

        auto attr_name_value = info[0]->ToString(isolate->GetCurrentContext());
        auto attr_name = V8BindingUtils::ToSTDString(isolate,
                                                     attr_name_value.ToLocalChecked().As<v8::String>());
        blink::mt::PropValue  attr_value;
        element->GetAttribute(attr_name, attr_value);
        if (!attr_value.isA<blink::mt::PropValueType::String>()) {
            attr_value = "";
        }
        info.GetReturnValue().Set(V8BindingUtils::PropValueToV8(isolate, attr_value));
    }

    void ElementWrapperV8::SetAttribute(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::SetAttribute);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 2,
            "Failed to execute 'setAttribute' on 'Element':2 arguments "
            "required");
        // https://developer.mozilla.org/zh-CN/docs/Web/API/Element/setAttribute#value
        // 对于 value 而已，如果不是字符串类型则自动转换成字符串
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(element);

        v8::MaybeLocal<v8::String> attr_name_value = info[0]->ToString(isolate->GetCurrentContext());
        auto attr_name = V8BindingUtils::ToSTDString(isolate,
                                                     attr_name_value.ToLocalChecked());

        blink::mt::PropValue attr_value = V8BindingUtils::V8ValueToPropValue(isolate, info[1]);
        if (attr_value.isA<blink::mt::PropValueType::Boolean>()) {
            element->SetAttribute(attr_name, attr_value.boolValue() ? "true" : "false");
        } else if (attr_value.isA<blink::mt::PropValueType::Number>()) {
            auto value = attr_value.numberValue();
            std::string value_str;
            // 针对整数做特殊处理
            if (std::abs(value - std::round(value)) < std::numeric_limits<double>::epsilon()) {
                value_str = std::to_string(static_cast<int>(std::round(value)));
            } else {
                value_str = std::to_string(value);
            }
            element->SetAttribute(attr_name, value_str);
        } else {
            element->SetAttribute(attr_name, attr_value);
        }
    }

    void ElementWrapperV8::RemoveAttribute(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::RemoveAttribute);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'removeAttribute' on 'Element':1 argument "
            "required");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(element);

        auto attr_name_value = info[0]->ToString(isolate->GetCurrentContext());
        auto attr_name = V8BindingUtils::ToSTDString(isolate,
                                                     attr_name_value.ToLocalChecked().As<v8::String>());

        bool success = element->RemoveAttribute(attr_name);
    }

    void ElementWrapperV8::GetClassName(const v8::FunctionCallbackInfo<v8::Value>& info) {
       GetClass(info);
    }

    void ElementWrapperV8::SetClassName(const v8::FunctionCallbackInfo<v8::Value>& info) {
        SetClass(info);
    }

    void ElementWrapperV8::GetTagName(
        const v8::FunctionCallbackInfo<v8::Value> &info) {
      MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::GetTagName);
      v8::Local<v8::Object> v8_receiver = info.This();
      v8::Isolate *isolate = info.GetIsolate();
      Element *element = static_cast<Element *>(
          V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
      V8_API_CHECK_NULL_RETURN(element);
      auto tag = element->tagName();
      info.GetReturnValue().Set(V8BindingUtils::ToV8String(isolate, tag));
    }

    void ElementWrapperV8::GetClassList(
        const v8::FunctionCallbackInfo<v8::Value> &info) {
      MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::GetClassList);
      v8::Local<v8::Object> v8_receiver = info.This();
      v8::Isolate *isolate = info.GetIsolate();
      Element *element = static_cast<Element *>(
          V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
      V8_API_CHECK_NULL_RETURN(element);
      std::shared_ptr<DOMTokenList> dom_token_list = element->GetDOMTokenList();
      auto obj = CREATE_V8_OBJECT(
          isolate, isolate->GetCurrentContext(), DOMTokenListWrapperV8::GetWrapperTypeInfo(),
          dom_token_list);
      info.GetReturnValue().Set(obj);
    }

    void ElementWrapperV8::GetClass(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::GetClass);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(element);

        info.GetReturnValue().Set(V8BindingUtils::ToV8String(isolate, element->classAttribute().c_str()));
    }

    void ElementWrapperV8::SetClass(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::SetClass);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'SetClass' on 'Element':1 argument required");
        V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'SetClass' on 'Element':1st argument must be "
            "string");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        //TODO: element是否还有效
        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(element);

        //TODO: magic number，check args
        //TODO: auto，ToLocalChecked
        auto attr_value_value = info[0]->ToString(isolate->GetCurrentContext());
        auto attr_value = V8BindingUtils::ToSTDString(isolate,
                                                      attr_value_value.ToLocalChecked().As<v8::String>());
        //TODO: 改成常量字符串 STLString
        bool success = element->SetAttribute("class", attr_value);
    }

    void ElementWrapperV8::GetStyle(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::GetStyle);
        v8::Isolate *isolate = info.GetIsolate();
        v8::Local<v8::Object> v8_receiver = info.This();
        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(element);
        std::shared_ptr<CSSStyleDeclaration> css_style_declaration = element->GetCSSStyleDeclaration();
        if (not css_style_declaration) return;

        auto css_style_declaration_v8 = GET_JSOBJECT_FROM(isolate, css_style_declaration);
        if (css_style_declaration_v8.IsEmpty()) {
            css_style_declaration_v8 = CREATE_V8_OBJECT(isolate, isolate->GetCurrentContext(), CSSStyleDeclarationWrapperV8::GetWrapperTypeInfo(), css_style_declaration);
        }
        info.GetReturnValue().Set(css_style_declaration_v8);
    }

//    void ElementWrapperV8::SetStyle(const v8::FunctionCallbackInfo<v8::Value>& info) {
//        v8::Local<v8::Object> v8_receiver = info.This();
//        v8::Isolate *isolate = info.GetIsolate();
//        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
//
//        auto attr_value_value = info[0]->ToString(isolate->GetCurrentContext());
//        auto attr_value = V8BindingUtils::ToSTDString(isolate,
//                                                      attr_value_value.ToLocalChecked().As<v8::String>());
//        bool success = element->SetAttribute("style", attr_value);
//    }

    void ElementWrapperV8::GetId(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::GetId);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(element);

        MSCString attr_value = element->idAttribute();
        info.GetReturnValue().Set(V8BindingUtils::ToV8String(isolate, attr_value.c_str()));
    }

    void ElementWrapperV8::SetId(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::SetId);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'SetId' on 'Element':1 argument required");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Element *element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(info.GetIsolate(), v8_receiver));
        V8_API_CHECK_NULL_RETURN(element);

        auto attr_value_value = info[0]->ToString(isolate->GetCurrentContext());
        auto attr_value = V8BindingUtils::ToSTDString(isolate,
                                                      attr_value_value.ToLocalChecked().As<v8::String>());
        bool success = element->SetAttribute("id", attr_value);
    }

//    void ConstructorCallback(const v8::FunctionCallbackInfo<v8::Value>& args) {
//        v8::Isolate *isolate = args.GetIsolate();
//        if (args.IsConstructCall()) {
//            v8::Local<v8::Object> obj = args.This();
//            //
//            args.GetReturnValue().Set(obj);
//
//
//        } else {
//            // 如果不是构造调用，抛出异常
//            isolate->ThrowException(v8::String::NewFromUtf8(isolate, "Must be called as a constructor").ToLocalChecked());
//        }
//    }

    void ElementWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::InstallInterface);
//        INTERFACE_SETUP_WITH_CONSTRUCTOR(ElementWrapperV8, ConstructorCallback, 1);
        INTERFACE_SETUP(ElementWrapperV8)

        static const OperationConfig kOperationTable[] = {
                {Element::kGetAttribute.c_str(), GetAttribute, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
                {Element::kSetAttribute.c_str(), SetAttribute, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
                {Element::kRemoveAttribute.c_str(), RemoveAttribute, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
        };
        INSTALL_INTERFACE_OPERATIONS(kOperationTable);

        static const AttributeConfig kAttributeTable[] = {
            {"className", ElementWrapperV8::GetClassName,
             ElementWrapperV8::SetClassName, 0,
             unsigned(FlagLocation::kPrototype)},
            {"class", ElementWrapperV8::GetClassName,
             ElementWrapperV8::SetClassName, 0,
             unsigned(FlagLocation::kPrototype)},
            {"id", ElementWrapperV8::GetId, ElementWrapperV8::SetId, 0,
             unsigned(FlagLocation::kPrototype)},
            {"style", ElementWrapperV8::GetStyle, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
            {"tagName", ElementWrapperV8::GetTagName, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
            {"classList", ElementWrapperV8::GetClassList, nullptr, 0,
             unsigned(FlagLocation::kPrototype)},
        };
        INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable);

//        auto context = isolate->GetCurrentContext();
//        v8::Local<v8::Object> global = context->Global();
//        auto func = interface_function_template->GetFunction(context).ToLocalChecked();
//        v8::Local<v8::Value> nativeValue = global->Get(context, v8::String::NewFromUtf8(isolate, "native").ToLocalChecked()).ToLocalChecked();
//        v8::Local<v8::Object> nativeObject = nativeValue.As<v8::Object>();
//        nativeObject->Set(context,
//                    v8::String::NewFromUtf8(isolate, "Element").ToLocalChecked(),
//                    func).Check();
    }

    const WrapperTypeInfo ElementWrapperV8::wrapper_type_info_ = {
            .install_interface_template_func = &InstallInterface,
            .interface_name = "Element",
            .parent_class = &NodeWrapperV8::GetWrapperTypeInfo(),
            .dom_node_type = WrapperTypeInfo::DOMNodeType::kElement,
    };

} // native_dom
} // msc

#endif