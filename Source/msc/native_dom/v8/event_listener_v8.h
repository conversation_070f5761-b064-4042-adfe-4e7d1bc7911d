//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/6/4.
//

#ifndef MSC_ANDROID_EVENT_LISTENER_V8_H
#define MSC_ANDROID_EVENT_LISTENER_V8_H

#include "../event.h"
#include "event_wrapper_v8.h"
#include "touch_event_wrapper_v8.h"
#include "../event_listener.h"
#include "v8_binding_utils.h"

#if USE_V8
namespace msc {
namespace native_dom {

    static inline void FireEventToV8(v8::Isolate* isolate, const std::shared_ptr<v8::Global<v8::Object>>& listener,
                       const std::shared_ptr<v8::Global<v8::Context>>& context, const std::shared_ptr<Event>& event) {
        v8::HandleScope handle_scope(isolate);
        v8::Local<v8::Object> local_listener = listener->Get(isolate);
        v8::Local<v8::Context> local_context = context->Get(isolate);
        v8::Local<v8::Object> event_object = GET_JSOBJECT_FROM(isolate, event);
        if (event_object.IsEmpty())
        {
            auto event_category = event->GetEventCategory();
            switch (event_category) {
                case Event::EventCategory::TOUCH:
                case Event::EventCategory::TAP: {
                    v8::Local<v8::Object> touch_element_v8 = CREATE_V8_OBJECT(isolate, local_context, TouchEventWrapperV8::GetWrapperTypeInfo(), event);
                    break;
                }
                case Event::EventCategory::COMPONENT:
                default: {
                    v8::Local<v8::Object> component_element_v8 = CREATE_V8_OBJECT(isolate, local_context, EventWrapperV8::GetWrapperTypeInfo(), event);
                    break;
                }
            }
        }
        event_object = GET_JSOBJECT_FROM(isolate, event);

        EventTarget *current_target = event->currentTarget();
        EventTarget *target = event->target();
        auto currentTarget_js = GET_JSOBJECT_FROM(isolate, current_target);

        v8::Local<v8::Value> argv[1] = { event_object };
        local_listener.As<v8::Function>()->Call(local_context, currentTarget_js, 1, argv);
    }

    class V8EventListener : public EventListener {
    public:
        V8EventListener(v8::Isolate* isolate, std::shared_ptr<v8::Global<v8::Object>> listener, std::shared_ptr<v8::Global<v8::Context>> context, bool capture)
          : EventListener(capture), isolate_(isolate), listener_(listener), context_(context) {
        }

        ~V8EventListener() override {
            isolate_ = nullptr;
            listener_->Reset();
            context_->Reset();
        }

        void FireEvent(const std::shared_ptr<Event>& event) override {
            if ((capture_ && event->stage() ==  Event::EventStage::CAPTURING) || (!capture_ && event->stage() == Event::EventStage::BUBBLING)) {
                MSC_RENDERER_LOG_DEBUG("[NativeDom event]start FireEventToV8: %s", event->typeName().c_str())
                FireEventToV8(isolate_, listener_, context_, event);
            }
        }

        EventListenerType type() override {
            return EventListenerType::V8_EVENT_LISTENER_TYPE;
        }

        bool IsSameObject(EventListener* listener) override {
            if (listener->type() == EventListenerType::V8_EVENT_LISTENER_TYPE) {
                auto v8_listener = static_cast<V8EventListener*>(listener);

                v8::Local<v8::Value> this_listener = listener_->Get(isolate_);
                v8::Local<v8::Value> other_listener = v8_listener->listener_->Get(isolate_);

                return this->capture() == listener->capture() && this_listener->StrictEquals(other_listener);
            } else {
                return false;
            }
        }

    private:
        v8::Isolate* isolate_;
        std::shared_ptr<v8::Global<v8::Object>> listener_;
        std::shared_ptr<v8::Global<v8::Context>> context_;
    };
}
}
#endif

#endif //MSC_ANDROID_EVENT_LISTENER_V8_H
