//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/21.
//

#include "v8_binding_utils.h"

#if USE_V8

#define kScriptWrappableIndex 0

namespace msc {
namespace native_dom {

    void V8BindingUtils::FinalizeJSObject(const v8::WeakCallbackInfo<NativeDOMObjectWrapper>& info) {
        MSC_RENDERER_LOG_DEBUG("[native_dom] FinalizeJSObject");
        NativeDOMObjectWrapper* wrapper = info.GetParameter();
        delete wrapper;
    }

    NativeDOMObjectWrapper* V8BindingUtils::CreateNativeDOMObjectWrapper(ScriptWrappable *script_wrappable) {
        return new NativeDOMObjectWrapper(script_wrappable);
    }

    NativeDOMObjectWrapper* V8BindingUtils::CreateNativeDOMObjectWrapper(std::shared_ptr<ScriptWrappable> script_wrappable_ptr) {
        return new NativeDOMObjectWrapper(script_wrappable_ptr);
    }

    v8::Local<v8::Object> V8BindingUtils::CreateV8Object(v8::Isolate *isolate, v8::Local<v8::Context> context, const WrapperTypeInfo &type, NativeDOMObjectWrapper *wrapper, bool retained) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(V8BindingUtils::CreateV8Object);
        v8::EscapableHandleScope handle_scope(isolate);
        v8::Context::Scope context_scope(context);

        v8::Local<v8::Template> local_template = type.GetV8ClassTemplate(isolate);
        auto v8_template = local_template.As<v8::FunctionTemplate>();
        auto instance_template = v8_template->InstanceTemplate();
//        instance_template->SetInternalFieldCount(1);

        auto new_obj = instance_template->NewInstance(context);
        if (new_obj.IsEmpty()) { //TODO: add Unlike(...)
            MSC_RENDERER_LOG_DEBUG("[native_dom] CreateV8Object failed with empty object for class_name: %s", type.interface_name);
            return handle_scope.Escape(v8::Local<v8::Object>());
        }
        v8::Local<v8::Object> object = new_obj.ToLocalChecked();

        bool is_object = object->IsObject();
        int field_count = object->InternalFieldCount();

        //TODO: 减短宏名称， 后面改成不用格式串（用三方库）
        MSC_RENDERER_LOG_DEBUG("[native_dom] CreateV8Object for: %s, before SetInternalField, is_object: %d, field_count: %d", type.interface_name, is_object, field_count);

        v8::Local<v8::External> ext = v8::External::New(isolate, wrapper);
        object->SetInternalField(kScriptWrappableIndex, ext);

        //Native侧需要持有wrapper，也即持有JSObject
        if (retained) {
            wrapper->SetJSObject(isolate, object);
            ScriptWrappable *script_wrappable = wrapper->GetScriptWrappablePtr();
            if (script_wrappable) {
                script_wrappable->SetJSWrapper(wrapper);
            }
        }

        return handle_scope.Escape(object);
    }

    ScriptWrappable *V8BindingUtils::ToScriptWrappable(v8::Isolate* isolate, v8::Local<v8::Object> object) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(V8BindingUtils::ToScriptWrappable);
        v8::Local<v8::Value> external = object->GetInternalField(kScriptWrappableIndex);
        if (!external->IsExternal()) {
            MSC_RENDERER_LOG_DEBUG("[native_dom] ToScriptWrappable failed with external is not external, field_count: %d",object->InternalFieldCount());
            return nullptr;
        }
        NativeDOMObjectWrapper *wrapper = static_cast<NativeDOMObjectWrapper *>(external.As<v8::External>()->Value());
        return wrapper->GetScriptWrappablePtr();
    }

    std::shared_ptr<ScriptWrappable> V8BindingUtils::ToScriptWrappableSharedPtr(v8::Isolate* isolate, v8::Local<v8::Object> object) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(V8BindingUtils::ToScriptWrappable);
        v8::Local<v8::Value> external = object->GetInternalField(kScriptWrappableIndex);
        if (!external->IsExternal()) {
            MSC_RENDERER_LOG_DEBUG("[native_dom] ToScriptWrappable failed with external is not external, field_count: %d",object->InternalFieldCount());
            return nullptr;
        }
        NativeDOMObjectWrapper *wrapper = static_cast<NativeDOMObjectWrapper *>(external.As<v8::External>()->Value());
        return wrapper->GetScriptWrappableSharedPtr();
    }

    void V8BindingUtils::SetupIDLInterfaceTemplate(
            v8::Isolate* isolate,
            const WrapperTypeInfo& wrapper_type_info,
            v8::Local<v8::ObjectTemplate> instance_template,
            v8::Local<v8::ObjectTemplate> prototype_template,
            v8::Local<v8::FunctionTemplate> interface_template,
            v8::Local<v8::FunctionTemplate> parent_interface_template) {
        v8::Local<v8::String> class_string =
                v8::String::NewFromUtf8(isolate, wrapper_type_info.interface_name).ToLocalChecked();

        if (!parent_interface_template.IsEmpty())
            interface_template->Inherit(parent_interface_template);
//        interface_template->ReadOnlyPrototype();
        interface_template->SetClassName(class_string);

//        prototype_template->Set(
//                v8::Symbol::GetToStringTag(isolate), class_string,
//                static_cast<v8::PropertyAttribute>(v8::ReadOnly | v8::DontEnum));
    }

    enum class FunctionKind {
        kAttributeGet,
        kAttributeSet,
        kOperation,
    };
    template <FunctionKind kind, typename Config>
    v8::FunctionCallback GetConfigCallback(const Config& config);
    template <>
    v8::FunctionCallback GetConfigCallback<FunctionKind::kAttributeGet>(
            const AttributeConfig& config) {
        return config.callback_for_get;
    }
    template <>
    v8::FunctionCallback GetConfigCallback<FunctionKind::kAttributeSet>(
            const AttributeConfig& config) {
        return config.callback_for_set;
    }
    template <>
    v8::FunctionCallback GetConfigCallback<FunctionKind::kOperation>(
            const OperationConfig& config) {
        return config.callback;
    }

    template <FunctionKind kind, typename Config>
    int GetConfigLength(const Config& config);
    template <>
    int GetConfigLength<FunctionKind::kAttributeGet>(
            const AttributeConfig& config) {
        return 0;
    }
    template <>
    int GetConfigLength<FunctionKind::kAttributeSet>(
            const AttributeConfig& config) {
        return 1;
    }
    template <>
    int GetConfigLength<FunctionKind::kOperation>(
            const OperationConfig& config) {
        return config.length;
    }

    template <FunctionKind kind, typename Config>
    v8::Local<v8::FunctionTemplate> CreateFunctionTemplate(
            v8::Isolate* isolate,
            v8::Local<v8::Signature> signature,
            v8::Local<v8::String> name,
            const Config& config) {
        v8::FunctionCallback callback = GetConfigCallback<kind>(config);
        if (!callback)
            return v8::Local<v8::FunctionTemplate>();

        int length = GetConfigLength<kind>(config);
//        v8::SideEffectType v8_side_effect = GetConfigSideEffect<kind>(config);
//        V8PrivateProperty::CachedAccessor v8_cached_accessor =
//                GetConfigV8CachedAccessor<kind>(config);

        v8::Local<v8::FunctionTemplate> function_template;
        function_template = v8::FunctionTemplate::New(isolate, callback);
        function_template->SetClassName(name);

        return function_template;
    }

    void InstallOperation(v8::Isolate* isolate,
                          v8::Local<v8::Template> instance_template,
                          v8::Local<v8::Template> prototype_template,
                          v8::Local<v8::Template> interface_template,
                          v8::Local<v8::Signature> signature,
                          const OperationConfig& config) {
        FlagLocation location =
                static_cast<FlagLocation>(config.location);
        if (location == FlagLocation::kInterface)
            signature = v8::Local<v8::Signature>();

        v8::Local<v8::String> name = v8::String::NewFromUtf8(isolate, config.name).ToLocalChecked();
        v8::Local<v8::FunctionTemplate> func =
                CreateFunctionTemplate<FunctionKind::kOperation>(
                        isolate, signature, name, config);

        v8::Local<v8::Template> target_template;
        switch (location) {
            case FlagLocation::kInstance:
                target_template = instance_template;
                break;
            case FlagLocation::kPrototype:
                target_template = prototype_template;
                break;
            case FlagLocation::kInterface:
                target_template = interface_template;
                break;
            default:
                break;
        }
        target_template->Set(
                name, func,
                static_cast<v8::PropertyAttribute>(config.v8_property_attribute));
    }

    void V8BindingUtils::InstallOperations(
            v8::Isolate* isolate,
            v8::Local<v8::Template> instance_template,
            v8::Local<v8::Template> prototype_template,
            v8::Local<v8::Template> interface_template,
            v8::Local<v8::Signature> signature,
            base::span<const OperationConfig> configs) {
        for (const auto& config : configs) {
            InstallOperation(isolate, instance_template, prototype_template,
                             interface_template, signature, config);
        }
    }

    void InstallAttribute(v8::Isolate* isolate,
                          v8::Local<v8::Template> instance_template,
                          v8::Local<v8::Template> prototype_template,
                          v8::Local<v8::Template> interface_template,
                          v8::Local<v8::Signature> signature,
                          const AttributeConfig& config) {

        FlagLocation location = static_cast<FlagLocation>(config.location);
//        if (static_cast<FlagReceiverCheck>(
//                    config.receiver_check) ==
//            IDLMemberInstaller::FlagReceiverCheck::kDoNotCheck ||
//            location == IDLMemberInstaller::FlagLocation::kInterface)
//            signature = v8::Local<v8::Signature>();

//        StringView name_as_view(config.name);
//        v8::Local<v8::String> name = V8AtomicString(isolate, name_as_view);
//        v8::Local<v8::String> get_name = V8AtomicString(
//                isolate, static_cast<String>(StringView("get ", 4) + name_as_view));
//        v8::Local<v8::String> set_name = V8AtomicString(
//                isolate, static_cast<String>(StringView("set ", 4) + name_as_view));

        v8::Local<v8::String> name = v8::String::NewFromUtf8(isolate, config.name).ToLocalChecked();
        v8::Local<v8::String> get_name = v8::String::NewFromUtf8(isolate, (std::string("get ") + config.name).c_str()).ToLocalChecked();
        v8::Local<v8::String> set_name = v8::String::NewFromUtf8(isolate, (std::string("set ") + config.name).c_str()).ToLocalChecked();

        v8::Local<v8::FunctionTemplate> get_func =
                CreateFunctionTemplate<FunctionKind::kAttributeGet>(
                        isolate, signature, get_name, config);
        v8::Local<v8::FunctionTemplate> set_func =
                CreateFunctionTemplate<FunctionKind::kAttributeSet>(
                        isolate, signature, set_name, config);

        v8::Local<v8::Template> target_template;
        switch (location) {
            case FlagLocation::kInstance:
                target_template = instance_template;
                break;
            case FlagLocation::kPrototype:
                target_template = prototype_template;
                break;
            case FlagLocation::kInterface:
                target_template = interface_template;
                break;
            default:
                break;
        }
        target_template->SetAccessorProperty(
                name, get_func, set_func,
                static_cast<v8::PropertyAttribute>(config.v8_property_attribute));
    }

    // static
    void V8BindingUtils::InstallAttributes(
            v8::Isolate* isolate,
            v8::Local<v8::Template> instance_template,
            v8::Local<v8::Template> prototype_template,
            v8::Local<v8::Template> interface_template,
            v8::Local<v8::Signature> signature,
            base::span<const AttributeConfig> configs) {
        for (const auto& config : configs) {
            InstallAttribute(isolate, instance_template, prototype_template,
                             interface_template, signature, config);
        }
    }

    blink::mt::PropValue V8BindingUtils::V8ValueToPropValue(v8::Isolate *isolate, v8::Local<v8::Value> value) {
        if (value->IsNull()) {
            return blink::mt::PropValueType::Null{};
        } else if (value->IsNumber()) {
            return blink::mt::PropValueType::Number{
                    value->NumberValue(isolate->GetCurrentContext()).ToChecked()};
        } else if (value->IsString()) {
            v8::String::Utf8Value utf8(isolate, value);
            return blink::mt::PropValueType::String{*utf8};
        } else if (value->IsBoolean()) {
            return blink::mt::PropValueType::Boolean{value->BooleanValue(isolate)};
        } else if (value->IsArray()) {
            v8::Local<v8::Array> array = value.As<v8::Array>();
            blink::mt::PropValueType::Array prop_array;
            for (uint32_t i = 0; i < array->Length(); ++i) {
                prop_array.push_back(V8ValueToPropValue(isolate,
                                                        array->Get(isolate->GetCurrentContext(),
                                                                   i).ToLocalChecked()));
            }
            return prop_array;
        } else if (value->IsObject()) {
            v8::Local<v8::Object> obj = value.As<v8::Object>();

            //如果是个特殊对象（如NativeDOM的Element），则直接取其string，否则可能会有无穷递归
            if (obj->InternalFieldCount() > 0) {
                v8::String::Utf8Value utf8(isolate, value);
                return blink::mt::PropValueType::String{*utf8};
            }

            blink::mt::PropValueType::Dictionary prop_dict;
            v8::Local<v8::Array> keys = obj->GetOwnPropertyNames(
                    isolate->GetCurrentContext()).ToLocalChecked();
            for (uint32_t i = 0; i < keys->Length(); ++i) {
                v8::Local<v8::Value> key = keys->Get(isolate->GetCurrentContext(),
                                                     i).ToLocalChecked();
                v8::String::Utf8Value utf8_key(isolate, key);
                v8::Local<v8::Value> val = obj->Get(isolate->GetCurrentContext(),
                                                    key).ToLocalChecked();
                prop_dict[*utf8_key] = V8ValueToPropValue(isolate, val);
            }
            return prop_dict;
        } else {
            return blink::mt::PropValueType::Null{};
        }
    }

    v8::Local<v8::Value> V8BindingUtils::PropValueToV8(v8::Isolate* isolate, const blink::mt::PropValue& prop) {
        return std::visit([isolate](auto &&arg) -> v8::Local<v8::Value> {
            using T = std::decay_t<decltype(arg)>;

            // Null类型处理
            if constexpr (std::is_same_v<T, blink::mt::PropValueType::Null>) {
                return v8::Null(isolate);
            }
                // 数值类型处理
            else if constexpr (std::is_same_v<T, blink::mt::PropValueType::Number>) {
                return v8::Number::New(isolate, arg);
            }
                // 字符串类型处理
            else if constexpr (std::is_same_v<T, blink::mt::PropValueType::String>) {
                return v8::String::NewFromUtf8(isolate, arg.c_str(),
                                                   v8::NewStringType::kNormal).ToLocalChecked();
            }
                // 布尔类型处理
            else if constexpr (std::is_same_v<T, blink::mt::PropValueType::Boolean>) {
                return v8::Boolean::New(isolate, arg);
            }
                // 数组类型递归处理
            else if constexpr (std::is_same_v<T, blink::mt::PropValueType::Array>) {
                v8::Local<v8::Array> array = v8::Array::New(isolate, arg.size());

                for (size_t i = 0; i < arg.size(); ++i) {
                    array->Set(isolate->GetCurrentContext(),
                               i,
                               PropValueToV8(isolate, arg[i])).Check();
                }
                  return array;
            }
                // 字典类型递归处理
            else if constexpr (std::is_same_v<T, blink::mt::PropValueType::Dictionary>) {
                v8::Local<v8::Object> obj = v8::Object::New(isolate);

                for (const auto &[key, value]: arg) {
                    obj->Set(isolate->GetCurrentContext(),
                             v8::String::NewFromUtf8(isolate, key.c_str()).ToLocalChecked(),
                             PropValueToV8(isolate, value)).Check();
                }
                return obj;
            }
                // 未知类型保护
            else {
                assert(false);
                return v8::Undefined(isolate);
            }
        }, static_cast<const blink::mt::PropValueBase &>(prop));
    }

    v8::Local<v8::Object> V8BindingUtils::V8ObjectFromProps(v8::Isolate* isolate, const blink::mt::Props &props, const std::string &exclude_name) {
        v8::Local<v8::Object> obj = v8::Object::New(isolate);
        v8::Local<v8::Context> context = isolate->GetCurrentContext();
        props.forEach([isolate, &context, &obj, &exclude_name](const std::string &key,
                                                               const blink::mt::PropValue &value) {
            if (key == exclude_name) {
                return;
            }
            obj->Set(context, v8::String::NewFromUtf8(isolate, key.c_str()).ToLocalChecked(),
                     PropValueToV8(isolate, value)).Check();
        });
        return obj;
    }

} // msc
} // native_dom

#endif