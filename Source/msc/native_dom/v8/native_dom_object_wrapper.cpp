//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/6/9.
//

#include "native_dom_object_wrapper.h"

namespace msc {
namespace native_dom {

    void NativeDOMObjectWrapper::FinalizeJSObject(const v8::WeakCallbackInfo<NativeDOMObjectWrapper>& info) {
        NativeDOMObjectWrapper* wrapper = static_cast<NativeDOMObjectWrapper *>(info.GetParameter());
        MSC_RENDERER_LOG_DEBUG("[native_dom] FinalizeJSObject called. wrapper: %p, script_wrappable: %p", wrapper, wrapper->GetScriptWrappablePtr());

        if (wrapper) {
            ScriptWrappable *script_wrappable = wrapper->GetScriptWrappablePtr();
            if (script_wrappable) {
                script_wrappable->ClearJSWrapper(false);
            }

            if (wrapper->GetScriptWrappableSharedPtr() == nullptr) {
                MSC_RENDERER_LOG_DEBUG("[native_dom] FinalizeJSObject will delete wrapper for shared_ptr: %p", wrapper);
            }

            wrapper->ResetJSObject();

            MSC_RENDERER_LOG_DEBUG("[native_dom] FinalizeJSObject will delete wrapper: %p", wrapper);
            delete wrapper;
        } else {
            MSC_RENDERER_LOG_DEBUG("[native_dom] FinalizeJSObject with null internal field");
        }
    }

    void NativeDOMObjectWrapper::SetJSObject(v8::Isolate *isolate, v8::Local<v8::Object> object) {
        js_object_.Reset(isolate, object);
        isolate_ = isolate;
    }

} // msc
} // native_dom