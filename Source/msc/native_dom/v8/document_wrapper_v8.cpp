//
// Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/3/22.
//

#include "document_wrapper_v8.h"
#include "wrapper_type_info.h"
#include "v8_binding_utils.h"
#include "../document.h"
#include "../view_component.h"
#include "element_wrapper_v8.h"
#include "vnode_wrapper_v8.h"
#include "text_node_wrapper_v8.h"
#include "native_dom/text_node.h"
#include "types_def.h"
#include "v8_js_callback.h"

#if USE_V8

namespace msc {
namespace native_dom {

    //TODO: 构造Element时如果有多个属性设置SetAttribute，看如何批量传给C++
    void DocumentWrapperV8::CreateElement(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DocumentWrapperV8::CreateElement);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'createElement' on 'Document':1 argument "
            "required");
        V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'createElement' on 'Document':1st argument must "
            "be string");
        auto isolate = info.GetIsolate();
        v8::Local<v8::Object> v8_receiver = info.This();

        auto *document = static_cast<Document *>(
            V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(document);
        v8::Local<v8::Context> context = isolate->GetCurrentContext();
        v8::MaybeLocal<v8::String> view_name_value = info[0]->ToString(context);
        //TODO: 简化string转换过程
//        std::string view_name = V8BindingUtils::ToSTDString(isolate,
//                                                     view_name_value.ToLocalChecked().As<v8::String>()); //TODO: 一眼看不出来类型的不要用auto

        v8::String::Utf8Value value(isolate, view_name_value.ToLocalChecked().As<v8::String>());
        StringView view_name(*value, value.length());

        Element *view = document->CreateElement(view_name); // std::make_shared<ViewComponent>(document);
        if (view) {
            //不用auto
            v8::Local<v8::Object> element_v8 = CREATE_V8_OBJECT(isolate, context, ElementWrapperV8::GetWrapperTypeInfo(), view);
            info.GetReturnValue().Set(element_v8);
        } else {
            MSC_RENDERER_LOG_ERROR("create view failed, type: %s", view_name.data());
        }
    }

    void DocumentWrapperV8::CreateTextNode(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DocumentWrapperV8::CreateTextNode);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'createTextNode' on 'Document':1 argument "
            "required");
        V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'createTextNode' on 'Document':1st argument "
            "must be string");
        auto isolate = info.GetIsolate();
        v8::Local<v8::Object> v8_receiver = info.This();

        auto *document = static_cast<Document *>(
            V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(document);
        v8::Local<v8::Context> context = isolate->GetCurrentContext();
        v8::MaybeLocal<v8::String> text_value = info[0]->ToString(context);
        //TODO: 简化string转换过程
        std::string text = V8BindingUtils::ToSTDString(isolate,
                                                     text_value.ToLocalChecked().As<v8::String>()); //TODO: 一眼看不出来类型的不要用auto

        TextNode *text_node = document->CreateTextNode(text);
        if (text_node) {
            //不用auto
            v8::Local<v8::Object> text_node_v8 = CREATE_V8_OBJECT(isolate, context, TextNodeWrapperV8::GetWrapperTypeInfo(), text_node);
            info.GetReturnValue().Set(text_node_v8);
        } else {
            MSC_RENDERER_LOG_ERROR("create textNode failed, text: %s", text.c_str());
            assert(false);
        }
    }

    //TODO: DOM对象和Wrapper的生命周期 - 后续再看

    void DocumentWrapperV8::CreateVNode(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DocumentWrapperV8::CreateVNode);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'createVNode' on 'Document':1 argument "
            "required");
        V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'createVNode' on 'Document':1st argument must "
            "be string");
        auto isolate = info.GetIsolate();
        v8::Local<v8::Object> v8_receiver = info.This();
        Document *document = static_cast<Document *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(document);
        auto view_name_value = info[0]->ToString(isolate->GetCurrentContext());
        auto view_name = V8BindingUtils::ToSTDString(isolate,
                                                     view_name_value.ToLocalChecked().As<v8::String>());

        auto vnode = new VNode(document);

    }

    void DocumentWrapperV8::GetBody(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DocumentWrapperV8::GetBody);
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        Document *document = static_cast<Document *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(document);
        std::shared_ptr<BodyElement> body = document->body();
        if (body) {
            v8::Local<v8::Object> body_v8 = GET_JSOBJECT_FROM(isolate, body);
            if (body_v8.IsEmpty()) {
                body_v8 = CREATE_V8_OBJECT(isolate, isolate->GetCurrentContext(), ElementWrapperV8::GetWrapperTypeInfo(), body);
            }
            info.GetReturnValue().Set(body_v8);
        }
     }

    void DocumentWrapperV8::FrameMarker(const v8::FunctionCallbackInfo<v8::Value>& info) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DocumentWrapperV8::FrameMarker);
        V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
            info, 1,
            "Failed to execute 'frameMarker' on 'Document':1 argument "
            "required");
        V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
            info, info[0],
            "Failed to execute 'frameMarker' on 'Document':1st argument must "
            "be string");
        v8::Local<v8::Object> v8_receiver = info.This();
        v8::Isolate *isolate = info.GetIsolate();
        auto context = isolate->GetCurrentContext();
        Document *document = static_cast<Document *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(document);

        auto action = info[0]->ToString(context).ToLocalChecked();
        auto action_str = V8BindingUtils::ToSTDString(isolate, action);

        if (action_str == "begin") {
            document->FrameMarker(Document::FrameMarkerType::Begin);
        } else if (action_str == "end") {
            document->FrameMarker(Document::FrameMarkerType::End);
        } else if (action_str == "preEnd") {
            document->FrameMarker(Document::FrameMarkerType::PreEnd);
        } else {
            isolate->ThrowException(v8::Exception::Error(
                   v8::String::NewFromUtf8((info).GetIsolate(),
                                           "frameMarker only support (begin, end, preEnd)").ToLocalChecked()));
        }
    }

    void DocumentWrapperV8::QueryEnhanced(const v8::FunctionCallbackInfo<v8::Value>& info) {
        v8::Isolate *isolate = info.GetIsolate();
        v8::Local<v8::Object> v8_receiver = info.This();
        auto document = static_cast<Document *>(V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
        V8_API_CHECK_NULL_RETURN(document);
        info.GetReturnValue().Set(v8::Undefined(isolate));

        auto context = isolate->GetCurrentContext();
        if (info.Length() < 6 ||
            !info[1]->IsBoolean() ||
            !info[2]->IsBoolean() ||
            !info[3]->IsBoolean() ||
            !info[4]->IsBoolean() ||
            !info[5]->IsFunction()) {
            MSC_RENDERER_LOG_ERROR("[queryEnhance] params error")
            return;
        }
        bool is_viewport = info[4]->BooleanValue(isolate);
        if (!is_viewport && !info[0]->IsObject()) {
            MSC_RENDERER_LOG_ERROR("[queryEnhance] no elements")
            return;
        }
        bool need_rect = info[1]->ToBoolean(isolate)->BooleanValue(isolate);
        bool need_size = info[2]->ToBoolean(isolate)->BooleanValue(isolate);
        bool need_scroll_offset = info[3]->ToBoolean(isolate)->BooleanValue(isolate);
        v8::Local<v8::Function> callback = v8::Local<v8::Function>::Cast(info[5]);

        std::vector<int> tags;
        if (!is_viewport) {
            auto element_array = v8::Local<v8::Array>::Cast(info[0]);
            for(size_t i = 0; i < element_array->Length(); i++) {
                auto ele_value = element_array->Get(context, i).ToLocalChecked();
                if (!ele_value->IsObject()) {
                    return;
                }
                auto ele_obj = ele_value->ToObject(context).ToLocalChecked();
                auto element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(isolate, ele_obj));
                V8_API_CHECK_NULL_RETURN(element);
                tags.push_back(element->GetNodeId());
            }
        }

        blink::mt::QueryEnhancedParams params {
            std::move(tags),
            need_rect,
            need_rect || need_size,
            need_scroll_offset,
            is_viewport,
        };
        auto persistent_callback = std::make_shared<v8::Global<v8::Value>>(isolate, callback);
        auto persistent_context = std::make_shared<v8::Global<v8::Context>>(isolate, context);
        auto callback_c =
            [isolate, persistent_context, persistent_callback,
             params](const blink::mt::QueryEnhancedEntries &entries, Element *element) {
              if (!isolate) {
                return;
              }
              unsigned size = entries.size();
              v8::HandleScope handleScope(isolate);
              v8::Local<v8::Context> context = persistent_context->Get(isolate);
              v8::Context::Scope context_scope(context);
              v8::Local<v8::Object> global = context->Global();
              v8::Local<v8::Value> callbackValue =
                  persistent_callback->Get(isolate);
              if (!callbackValue->IsFunction()) {
                MSC_RENDERER_LOG_ERROR("[queryEnhance] callback is empty")
              }
              v8::Local<v8::Function> callback =
                  callbackValue.As<v8::Function>();

              v8::Local<v8::Array> entries_res = v8::Array::New(isolate, size);
              for (unsigned i = 0; i < size; i++) {
                auto &entry = entries[i];
                v8::Local<v8::Object> entry_object = v8::Object::New(isolate);
                if (!entry.invalid) {
                  if (params.need_location) {
                    entry_object->Set(context, v8::String::NewFromUtf8(isolate, "top").ToLocalChecked(), v8::Number::New(isolate, entry.y)).Check();
                    entry_object->Set(context, v8::String::NewFromUtf8(isolate, "left").ToLocalChecked(), v8::Number::New(isolate, entry.x)).Check();
                  }

                  if (params.need_size) {
                    entry_object->Set(context, v8::String::NewFromUtf8(isolate, "width").ToLocalChecked(), v8::Number::New(isolate, entry.width)).Check();
                    entry_object->Set(context, v8::String::NewFromUtf8(isolate, "height").ToLocalChecked(), v8::Number::New(isolate, entry.height)).Check();
                  }
                  if (params.need_scroll_offset) {
                    entry_object->Set(context, v8::String::NewFromUtf8(isolate, "scrollTop").ToLocalChecked(), v8::Number::New(isolate, entry.scroll_top)).Check();
                    entry_object->Set(context, v8::String::NewFromUtf8(isolate, "scrollLeft").ToLocalChecked(), v8::Number::New(isolate, entry.scroll_left)).Check();
                  }
                }

                entries_res->Set(context, i, entry_object).Check();
              }
              v8::Local<v8::Value> args[] = {entries_res};
              auto r = callback->Call(context, global, 1, args);
              if (r.IsEmpty()) {
                MSC_RENDERER_LOG_ERROR("[queryEnhance] callback run failed")
              }
              persistent_callback->Reset();
              persistent_context->Reset();
            };
        JSCallbackInfo callback_info{};
        callback_info.type = JSCallbackInfo::Type::QueryEnhanced;
        auto callbale =
            std::make_unique<JSCallbackForQueryEnhanced>(std::move(callback_c));
        auto js_callback = std::make_unique<V8JSCallback>(
            persistent_callback, persistent_context, std::move(callbale));
        callback_info.callback_index_ =
            document->ProtectJSCallback(std::move(js_callback));

        document->QueryEnhanced(std::move(params), callback_info);
    }

    void DocumentWrapperV8::CreateKeyframesAnimationEnhanced(
        const v8::FunctionCallbackInfo<v8::Value> &info) {
      MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(
          DocumentWrapperV8::CreateKeyframesAnimationEnhanced);
      V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
          info, 3,
          "Failed to execute 'CreateKeyframesAnimationEnhanced' on "
          "'Document':3 "
          "arguments required");
      V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
          info, info[0],
          "Failed to execute 'CreateKeyframesAnimationEnhanced' on "
          "'Document':1st "
          "argument must be object");
      V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
          info, info[1],
          "Failed to execute 'CreateKeyframesAnimationEnhanced' on "
          "'Document':2nd "
          "argument must be object");
      V8_API_CHECK_NUMBER_FAILL_THROW_TYPEERROR(
          info, info[2],
          "Failed to execute 'CreateKeyframesAnimationEnhanced' on "
          "'Document':3rd "
          "argument must be number");

      auto v8_receiver = info.This();
      auto isolate = info.GetIsolate();
      auto context = isolate->GetCurrentContext();
      auto document = static_cast<Document *>(
          V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
      V8_API_CHECK_NULL_RETURN(document);

      blink::mt::PropsBuilder builder;
      auto animation_pros = std::make_shared<blink::mt::AnimationProperties>();

      auto elements = v8::Local<v8::Array>::Cast(info[0]);
      std::vector<blink::mt::PropValue> tags;
      for (auto i = 0; i < elements->Length(); i++) {
        v8::Local<v8::Value> element_value;
        if (!elements->Get(isolate->GetCurrentContext(), i)
                 .ToLocal(&element_value)) {
          // TODO: error handle
          return;  // 获取失败
        }

        // 处理元素...
        auto element = element_value.As<v8::Object>();

        auto e = static_cast<Element *>(
            V8BindingUtils::ToScriptWrappable(isolate, element));
        V8_API_CHECK_NULL_RETURN(e);
        tags.push_back(
            static_cast<blink::mt::PropValueType::Number>(e->GetNodeId()));
      }

      builder.setProp("tags", tags);

      // keyframes
      auto js_keyframes = v8::Local<v8::Array>::Cast(info[1]);
      std::vector<blink::mt::PropValue> keyframes;
      keyframes.reserve(js_keyframes->Length());
      for (auto i = 0; i < js_keyframes->Length(); i++) {
        blink::mt::PropValueType::Dictionary keyframe;
        v8::Local<v8::Value> keyframeValue;
        if (!js_keyframes->Get(context, i).ToLocal(&keyframeValue)) {
          // TODO: error handle
          return;  // 获取失败
        }

        auto keyframe_obj = keyframeValue.As<v8::Object>();

        // offset
        v8::Local<v8::String> offsetPropName =
            v8::String::NewFromUtf8(isolate, "offset").ToLocalChecked();
        if (keyframe_obj->Has(context, offsetPropName).FromJust()) {
          v8::Local<v8::Value> obj;
          keyframe_obj->Get(context, offsetPropName).ToLocal(&obj);
          blink::mt::PropValueType::Number offset;
          obj->NumberValue(context).To(&offset);
          keyframe["offset"] = offset;
        }

        // ease
        v8::Local<v8::String> easePropName =
            v8::String::NewFromUtf8(isolate, "ease").ToLocalChecked();
        if (keyframe_obj->Has(context, easePropName).FromJust()) {
          v8::Local<v8::Value> obj;
          keyframe_obj->Get(context, easePropName).ToLocal(&obj);
          keyframe["ease"] = V8BindingUtils::ToSTDString(
              isolate,
              obj->ToString(context).ToLocalChecked().As<v8::String>());
        }

        // opacity
        v8::Local<v8::String> opacityPropName =
            v8::String::NewFromUtf8(isolate, "opacity").ToLocalChecked();
        if (keyframe_obj->Has(context, opacityPropName).FromJust()) {
          v8::Local<v8::Value> obj;
          keyframe_obj->Get(context, opacityPropName).ToLocal(&obj);
          blink::mt::PropValueType::Number opacity;
          obj->NumberValue(context).To(&opacity);
          keyframe["opacity"] = opacity;
        }

        // rotate
        v8::Local<v8::String> rotatePropName =
            v8::String::NewFromUtf8(isolate, "rotate").ToLocalChecked();
        if (keyframe_obj->Has(context, rotatePropName).FromJust()) {
          v8::Local<v8::Value> obj;
          keyframe_obj->Get(context, rotatePropName).ToLocal(&obj);
          blink::mt::PropValueType::Number rotate;
          obj->NumberValue(context).To(&rotate);
          keyframe["rotate"] = rotate;
        }

        // scale
        v8::Local<v8::String> scalePropName =
            v8::String::NewFromUtf8(isolate, "scale").ToLocalChecked();
        if (keyframe_obj->Has(context, scalePropName).FromJust()) {
          v8::Local<v8::Value> obj;
          keyframe_obj->Get(context, scalePropName).ToLocal(&obj);
          auto scale_obj = v8::Local<v8::Array>::Cast(obj);
          std::vector<blink::mt::PropValue> scale;
          for (auto i = 0; i < scale_obj->Length(); i++) {
            v8::Local<v8::Value> scale_value;
            if (!scale_obj->Get(context, i).ToLocal(&scale_value)) {
              // TODO: error handle
              return;  // 获取失败
            }
            blink::mt::PropValueType::Number value;
            scale_value->NumberValue(context).To(&value);
            scale.push_back(value);
          }
          keyframe["scale"] = scale;
        }

        // translate
        v8::Local<v8::String> translatePropName =
            v8::String::NewFromUtf8(isolate, "translate").ToLocalChecked();
        if (keyframe_obj->Has(context, translatePropName).FromJust()) {
          v8::Local<v8::Value> obj;
          keyframe_obj->Get(context, translatePropName).ToLocal(&obj);
          std::vector<blink::mt::PropValue> translate;
          auto translate_obj = v8::Local<v8::Array>::Cast(obj);
          for (auto i = 0; i < translate_obj->Length(); i++) {
            v8::Local<v8::Value> translate_value;
            if (!translate_obj->Get(context, i).ToLocal(&translate_value)) {
              // TODO: error handle
              return;  // 获取失败
            }
            if (translate_value->IsNumber()) {
              blink::mt::PropValueType::Number transNum;
              translate_value->NumberValue(context).To(&transNum);
              translate.push_back(transNum);
              MSC_RENDERER_LOG_INFO("animation translate: %f", transNum)
            } else if (translate_value->IsString()) {
              auto trans_str = V8BindingUtils::ToSTDString(isolate, translate_value->ToString(context).ToLocalChecked().As<v8::String>());
              MSC_RENDERER_LOG_INFO("animation translate: %s", trans_str.c_str())
              translate.push_back(trans_str);
            }
          }
          keyframe["translate"] = translate;
        }

        // width
        v8::Local<v8::String> widthPropName =
            v8::String::NewFromUtf8(isolate, "width").ToLocalChecked();
        if (keyframe_obj->Has(context, widthPropName).FromJust()) {
          v8::Local<v8::Value> obj;
          keyframe_obj->Get(context, widthPropName).ToLocal(&obj);
          if (obj->IsNumber()) {
            blink::mt::PropValueType::Number width;
            obj->NumberValue(context).To(&width);
            keyframe["width"] = width;
          } else if (obj->IsString()) {
            keyframe["width"] = V8BindingUtils::ToSTDString(
                isolate,
                obj->ToString(context).ToLocalChecked().As<v8::String>());
          }
        }
        keyframes.emplace_back(keyframe);
      }
      builder.setProp("keyframes", keyframes);

      // duration
      double duration = 0;
      info[2]->NumberValue(isolate->GetCurrentContext()).To(&duration);
      builder.setProp("duration", duration);

      // callback
      JSCallbackInfo callback_info{};
      callback_info.type = JSCallbackInfo::Type::CreateKeyframesAnimation;
      callback_info.is_valid = false;

      std::function<void()> callback;
      if (info.Length() >= 4) {
        V8_API_CHECK_FUNCTION_FAILL_THROW_TYPEERROR(
            info, info[3],
            "Failed to execute 'CreateKeyframesAnimationEnhanced' on "
            "'Document':4th "
            "argument must be function");

        auto js_callback = std::make_shared<v8::Global<v8::Value>>(
            isolate, info[3].As<v8::Function>());
        auto js_context =
            std::make_shared<v8::Global<v8::Context>>(isolate, context);
        callback = [js_callback, js_context, isolate = info.GetIsolate()]() {
          v8::HandleScope handleScope(isolate);
          auto context = js_context->Get(isolate);
          v8::Context::Scope context_scope(context);
          js_callback->Get(isolate)
              .As<v8::Function>()
              ->Call(context, v8::Null(isolate), 0, nullptr)
              .ToLocalChecked();
        };

        auto callable = std::make_unique<JSCallbackForSimple>(callback);
        auto v8_js_callback = std::make_unique<V8JSCallback>(
            js_callback, js_context, std::move(callable));
        auto index = document->ProtectJSCallback(std::move(v8_js_callback));
        callback_info.callback_index_ = index;
        callback_info.is_valid = true;
      }
      animation_pros->props =
          std::const_pointer_cast<blink::mt::Props>(builder.getProps());

      document->CreateKeyframesAnimationEnhanced(animation_pros, callback_info);
    }

    void DocumentWrapperV8::ClearKeyframesAnimationEnhanced(
        const v8::FunctionCallbackInfo<v8::Value> &info) {
      MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(
          DocumentWrapperV8::ClearKeyframesAnimationEnhanced);
      V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
          info, 2,
          "Failed to execute 'ClearKeyframesAnimationEnhanced' on 'Document':2 "
          "arguments "
          "required");
      V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
          info, info[0],
          "Failed to execute 'ClearKeyframesAnimationEnhanced' on "
          "'Document':1st "
          "argument must "
          "be object");

      auto v8_receiver = info.This();
      auto isolate = info.GetIsolate();
      auto context = isolate->GetCurrentContext();
      auto document = static_cast<Document *>(
          V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));
      V8_API_CHECK_NULL_RETURN(document);

      blink::mt::PropsBuilder builder;
      auto clear_animation_properties =
          std::make_shared<blink::mt::ClearAnimationProperties>();

      auto elements = v8::Local<v8::Array>::Cast(info[0]);
      // tags
      std::vector<blink::mt::PropValue> tags;
      for (auto i = 0; i < elements->Length(); i++) {
        v8::Local<v8::Value> element_value;
        if (!elements->Get(isolate->GetCurrentContext(), i)
                 .ToLocal(&element_value)) {
          // TODO: error handle
          return;  // 获取失败
        }

        // 处理元素...
        auto element = element_value.As<v8::Object>();

        auto e = static_cast<Element *>(
            V8BindingUtils::ToScriptWrappable(isolate, element));
        V8_API_CHECK_NULL_RETURN(e);
        tags.push_back(
            static_cast<blink::mt::PropValueType::Number>(e->GetNodeId()));
      }
      builder.setProp("tags", tags);

      // options
      if (info[1]->IsObject()) {
        auto js_options = v8::Local<v8::Object>::Cast(info[1]);
        blink::mt::PropValueType::Dictionary options;

        // opacity
        v8::Local<v8::String> opacityPropName =
            v8::String::NewFromUtf8(isolate, "opacity").ToLocalChecked();
        if (js_options->Has(context, opacityPropName).FromJust()) {
          v8::Local<v8::Value> obj;
          js_options->Get(context, opacityPropName).ToLocal(&obj);
          options["opacity"] = obj->BooleanValue(isolate);
        }

        // scale
        v8::Local<v8::String> scalePropName =
            v8::String::NewFromUtf8(isolate, "scale").ToLocalChecked();
        if (js_options->Has(context, scalePropName).FromJust()) {
          v8::Local<v8::Value> obj;
          js_options->Get(context, scalePropName).ToLocal(&obj);
          options["scale"] = obj->BooleanValue(isolate);
        }

        // translate
        v8::Local<v8::String> translatePropName =
            v8::String::NewFromUtf8(isolate, "translate").ToLocalChecked();
        if (js_options->Has(context, translatePropName).FromJust()) {
          v8::Local<v8::Value> obj;
          js_options->Get(context, translatePropName).ToLocal(&obj);
          options["translate"] = obj->BooleanValue(isolate);
        }

        // rotate
        v8::Local<v8::String> rotatePropName =
            v8::String::NewFromUtf8(isolate, "rotate").ToLocalChecked();
        if (js_options->Has(context, rotatePropName).FromJust()) {
          v8::Local<v8::Value> obj;
          js_options->Get(context, rotatePropName).ToLocal(&obj);
          options["rotate"] = obj->BooleanValue(isolate);
        }

        builder.setProp("options", options);
      } else if (info[1]->IsNull()) {
        builder.setProp("options", blink::mt::PropValueType::Null{});
      }

      // callback
      JSCallbackInfo callback_info{};
      callback_info.type = JSCallbackInfo::Type::ClearKeyframesAnimation;
      callback_info.is_valid = false;
      if (info.Length() >= 3) {
        V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(
            info, info[2],
            "Failed to execute 'ClearKeyframesAnimationEnhanced' on "
            "'Document':3rd "
            "argument must "
            "be object");

        auto js_callback = std::make_shared<v8::Global<v8::Value>>(
            isolate, info[2].As<v8::Function>());
        auto js_context =
            std::make_shared<v8::Global<v8::Context>>(isolate, context);
        auto callback = [js_callback, js_context,
                         isolate = info.GetIsolate()]() {
          v8::HandleScope handleScope(isolate);
          auto context = js_context->Get(isolate);
          v8::Context::Scope context_scope(context);
          js_callback->Get(isolate).As<v8::Function>()->Call(
              context, v8::Null(isolate), 0, nullptr);
        };
        auto callable = std::make_unique<JSCallbackForSimple>(callback);
        auto v8_js_callback = std::make_unique<JSCallback>(std::move(callable));
        auto index = document->ProtectJSCallback(std::move(v8_js_callback));
        callback_info.callback_index_ = index;
        callback_info.is_valid = true;
      }

      clear_animation_properties->props =
          std::const_pointer_cast<blink::mt::Props>(builder.getProps());
      document->ClearKeyframesAnimationEnhanced(clear_animation_properties,
                                                callback_info);
    }

    void DocumentWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DocumentWrapperV8::InstallInterface);
        INTERFACE_SETUP(DocumentWrapperV8);

        // clang-format off
        static const OperationConfig kOperationTable[] = {
            {Document::kCreateElement.c_str(), CreateElement, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
            {Document::kCreateTextNode.c_str(), CreateTextNode, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
            {Document::kCreateVNode.c_str(), CreateVNode, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
            {Document::kFrameMarker.c_str(), FrameMarker, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
            {Document::kQueryEnhanced.c_str(), QueryEnhanced, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
            {Document::kCreateKeyframesAnimationEnhanced.c_str(), CreateKeyframesAnimationEnhanced, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
            {Document::kClearKeyframesAnimationEnhanced.c_str(), ClearKeyframesAnimationEnhanced, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
        };
        // clang-format on
        INSTALL_INTERFACE_OPERATIONS(kOperationTable);

        static const AttributeConfig kAttributeTable[] = {
            {"body", GetBody, nullptr, 0, unsigned(FlagLocation::kPrototype)},
        };
        INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable);
    }

    const WrapperTypeInfo DocumentWrapperV8::wrapper_type_info_ = {
            .install_interface_template_func = &InstallInterface,
            .interface_name = "Document",
            .parent_class = &ElementWrapperV8::GetWrapperTypeInfo(),
            .dom_node_type = WrapperTypeInfo::DOMNodeType::kDocument,
    };

} // msc
} // native_dom

#endif