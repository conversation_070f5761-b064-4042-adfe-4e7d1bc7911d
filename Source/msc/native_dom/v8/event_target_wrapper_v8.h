////
//// Created by ji<PERSON><PERSON> z<PERSON> on 2025/3/22.
////
//
//#ifndef MSC_ANDROID_EVENTTARGETWRAPPERV8_H
//#define MSC_ANDROID_EVENTTARGETWRAPPERV8_H
//
//#include "wrapper_type_info.h"
//
//namespace msc {
//namespace native_dom {
//
//class EventTargetWrapperV8 {
//public:
//    static constexpr const WrapperTypeInfo* GetWrapperTypeInfo() {
//        return &wrapper_type_info_;
//    }
//private:
//    static void AddEventListener(const v8::FunctionCallbackInfo<v8::Value>& info);
//    static void RemoveEventListener(const v8::FunctionCallbackInfo<v8::Value>& info);
//
//    static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);
//    static const WrapperTypeInfo wrapper_type_info_;
//};
//
//} // msc
//} // native_dom
//
//#endif //MSC_ANDROID_EVENTTARGETWRAPPERV8_H
