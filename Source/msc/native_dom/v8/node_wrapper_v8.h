//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/22.
//

#ifndef MSC_ANDROID_NODEWRAPPERV8_H
#define MSC_ANDROID_NODEWRAPPERV8_H

#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

class NodeWrapperV8 {
public:
    static constexpr const WrapperTypeInfo& GetWrapperTypeInfo() {
        return wrapper_type_info_;
    }
private:
    static void AppendChild(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void InsertBefore(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void RemoveChild(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void Remove(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void GetFirstChild(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void GetLastChild(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void GetPreviousSibling(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void GetNextSibling(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void GetParentNode(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void HasChildNodes(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void GetParentElement(
        const v8::FunctionCallbackInfo<v8::Value>& info);
    static void GetTextContent(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void SetTextContent(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void AddEventListener(const v8::FunctionCallbackInfo<v8::Value>& info);
    static void RemoveEventListener(const v8::FunctionCallbackInfo<v8::Value>& info);

    static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);
    static const WrapperTypeInfo wrapper_type_info_;
};

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_NODEWRAPPERV8_H
