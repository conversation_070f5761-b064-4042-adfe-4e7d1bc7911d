#include "dom_token_list_wrapper_v8.h"

#include "native_dom/dom_token_list.h"
#include "node_wrapper_v8.h"
#include "v8_binding_utils.h"

namespace msc {
namespace native_dom {
#if USE_V8

#define DOMTOKENLIST()                                         \
  v8::Local<v8::Object> v8_receiver = info.This();             \
  v8::Isolate *isolate = info.GetIsolate();                    \
  DOMTokenList *dom_token_list = static_cast<DOMTokenList *>(  \
      V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));\
  V8_API_CHECK_NULL_RETURN(dom_token_list)

void DOMTokenListWrapperV8::GetLength(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::GetLength);
  DOMTOKENLIST();
  auto l = dom_token_list->GetLength();
  info.GetReturnValue().Set(static_cast<uint32_t>(l));
}

void DOMTokenListWrapperV8::GetValue(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::GetValue);
  DOMTOKENLIST();
  auto v = dom_token_list->GetValue();
  info.GetReturnValue().Set(V8BindingUtils::ToV8String(isolate, v.c_str()));
}

void DOMTokenListWrapperV8::GetItem(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::GetItem);
  DOMTOKENLIST();
  V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      info, 1,
      "Failed to execute 'getItem' on 'DOMTokenList':1 argument required");
  V8_API_CHECK_NUMBER_FAILL_THROW_TYPEERROR(
      info, info[0],
      "Failed to execute 'getItem' on 'DOMTokenList':1st argument must be "
      "number");
  int32_t index = -1;
  info[0]->Int32Value(isolate->GetCurrentContext()).To(&index);

  auto v = dom_token_list->GetItem(index);
  info.GetReturnValue().Set(V8BindingUtils::ToV8String(isolate, v.c_str()));
}

void DOMTokenListWrapperV8::Contains(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::contains);
  DOMTOKENLIST();
  V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      info, 1,
      "Failed to execute 'contains' on 'DOMTokenList':1 argument required");
  V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      info, info[0],
      "Failed to execute 'contains' on 'DOMTokenList':1st argument must be "
      "string");
  auto token = V8BindingUtils::ToSTDString(
      isolate, info[0]
                   ->ToString(isolate->GetCurrentContext())
                   .ToLocalChecked()
                   .As<v8::String>());

  auto v = dom_token_list->Contains(token);
  info.GetReturnValue().Set(v);
}

void DOMTokenListWrapperV8::Add(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::Add);
  DOMTOKENLIST();
  std::vector<std::string> args;
  args.reserve(info.Length());
  for (int i = 0; i < info.Length(); i++) {
    V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
        info, info[i],
        "Failed to execute 'add' on 'DOMTokenList':argument must be string");
    auto arg = V8BindingUtils::ToSTDString(
        isolate,
        info[i]->ToString(isolate->GetCurrentContext()).ToLocalChecked());
    args.emplace_back(std::move(arg));
  }

  dom_token_list->Add(args);
}

void DOMTokenListWrapperV8::Remove(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::Remove);
  DOMTOKENLIST();

  std::vector<std::string> args;
  args.reserve(info.Length());
  for (int i = 0; i < info.Length(); i++) {
    V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
        info, info[i],
        "Failed to execute 'remove' on 'DOMTokenList':argument must be string");
    auto arg = V8BindingUtils::ToSTDString(
        isolate,
        info[i]->ToString(isolate->GetCurrentContext()).ToLocalChecked());
    args.emplace_back(std::move(arg));
  }

  dom_token_list->Remove(args);
}

void DOMTokenListWrapperV8::Replace(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::Replace);
  DOMTOKENLIST();
  V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      info, 2,
      "Failed to execute 'replace' on 'DOMTokenList':2 arguments required");
  V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      info, info[0],
      "Failed to execute 'replace' on 'DOMTokenList':1st argument must be "
      "string");
  V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      info, info[1],
      "Failed to execute 'replace' on 'DOMTokenList':2rd argument must be "
      "string");

  auto old_token = V8BindingUtils::ToSTDString(
      isolate,
      info[0]->ToString(isolate->GetCurrentContext()).ToLocalChecked());
  auto new_token = V8BindingUtils::ToSTDString(
      isolate,
      info[1]->ToString(isolate->GetCurrentContext()).ToLocalChecked());

  dom_token_list->Replace(old_token, new_token);
}

void DOMTokenListWrapperV8::Supports(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::Supports);
  DOMTOKENLIST();
  V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      info, 1,
      "Failed to execute 'supports' on 'DOMTokenList':1 argument required");
  V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      info, info[0],
      "Failed to execute 'supports' on 'DOMTokenList':1st argument must be "
      "string");
  auto token = V8BindingUtils::ToSTDString(
      isolate, info[0]
                   ->ToString(isolate->GetCurrentContext())
                   .ToLocalChecked()
                   .As<v8::String>());

  auto v = dom_token_list->Supports(token);
  info.GetReturnValue().Set(v);
}

void DOMTokenListWrapperV8::Toggle(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::Toggle);
  DOMTOKENLIST();
  V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      info, 1,
      "Failed to execute 'toggle' on 'DOMTokenList':1 argument required");
  V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(
      info, info[0],
      "Failed to execute 'toggle' on 'DOMTokenList':1st argument must be "
      "string");
  bool force{false}, has_force{false};
  auto token = V8BindingUtils::ToSTDString(
      isolate, info[0]
                   ->ToString(isolate->GetCurrentContext())
                   .ToLocalChecked()
                   .As<v8::String>());
  if (info.Length() == 2) {
    if (info[1]->IsBoolean()) {
      force = info[1]->BooleanValue(isolate);
      has_force = true;
    }
  }

  bool res = dom_token_list->Toggle(token, force, has_force);
  info.GetReturnValue().Set(res);
}

void DOMTokenListWrapperV8::ForEach(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::ForEach);
  DOMTOKENLIST();
  V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(
      info, 1,
      "Failed to execute 'forEach' on 'DOMTokenList':1 argument required");
  V8_API_CHECK_FUNCTION_FAILL_THROW_TYPEERROR(
      info, info[0],
      "Failed to execute 'forEach' on 'DOMTokenList':1st argument must be a "
      "function");
  v8::Local<v8::Function> callback = info[0].As<v8::Function>();
  v8::Local<v8::Value> thisArg = v8::Null(isolate);

  if (info.Length() == 2) {
    thisArg = info[1];
  }

  dom_token_list->ForEach(
      [isolate, &callback, &thisArg, list_obj = info.This()](
          const std::string &token, int index) {
        v8::Local<v8::Value> argv[] = {
            v8::String::NewFromUtf8(isolate, token.c_str()).ToLocalChecked(),
            v8::Integer::New(isolate, index), list_obj};

        callback->Call(isolate->GetCurrentContext(), thisArg, 3, argv);
      });
}

void DOMTokenListWrapperV8::ToString(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  GetValue(info);
}

template <typename IteratorWrapperV8>
void DOMTokenListWrapperV8::GetIterator(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::GetIterator);

  DOMTOKENLIST();

  auto iterator = new DOMTokenList::Iterator(*dom_token_list);
  auto obj = CREATE_V8_OBJECT_NO_RETAIN(
      isolate, isolate->GetCurrentContext(), IteratorWrapperV8::GetWrapperTypeInfo(), iterator);
  info.GetReturnValue().Set(obj);
}

void DOMTokenListWrapperV8::Values(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  GetIterator<DOMTokenListValueIteratorWrapperV8>(info);
}
void DOMTokenListWrapperV8::Keys(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  GetIterator<DOMTokenListKeyIteratorWrapperV8>(info);
}

void DOMTokenListWrapperV8::Entries(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  GetIterator<DOMTokenListEntryIteratorWrapperV8>(info);
}

void DOMTokenListWrapperV8::InstallInterface(
    v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListWrapperV8::InstallInterface);
  INTERFACE_SETUP(DOMTokenListWrapperV8);

  static const OperationConfig kOperationTable[] = {
      {"item", GetItem, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"contains", Contains, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"add", Add, 0, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
      {"remove", Remove, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"replace", Replace, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"supports", Supports, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"toggle", Toggle, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"forEach", ForEach, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"toString", ToString, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"entries", Entries, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"values", Values, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
      {"keys", Keys, 0, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
  };
  INSTALL_INTERFACE_OPERATIONS(kOperationTable);

  static const AttributeConfig kAttributeTable[] = {
      {"length", GetLength, nullptr, 0, unsigned(FlagLocation::kPrototype)},
      {"value", GetValue, nullptr, 0, unsigned(FlagLocation::kPrototype)},
  };
  INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable);
}

const WrapperTypeInfo DOMTokenListWrapperV8::wrapper_type_info_ = {
    .install_interface_template_func = &InstallInterface,
    .interface_name = "DOMTokenList",
    .parent_class = nullptr,
    .dom_node_type = WrapperTypeInfo::DOMNodeType::kDOMTokenList,
};

#define DOM_TOKEN_LIST_ITERATOR()                                           \
  v8::Local<v8::Object> v8_receiver = info.This();                          \
  v8::Isolate *isolate = info.GetIsolate();                                 \
  DOMTokenList::Iterator *iterator = static_cast<DOMTokenList::Iterator *>( \
      V8BindingUtils::ToScriptWrappable(isolate, v8_receiver));             \
  V8_API_CHECK_NULL_RETURN(iterator)

template <typename WrapperV8>
void DOMTokenListIteratorWrapperV8<WrapperV8>::GetNext(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  assert(info.Length() == 0);
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(DOMTokenListEntryIteratorWrapperV8::GetNext);
  DOM_TOKEN_LIST_ITERATOR();

  v8::Local<v8::Object> result = v8::Object::New(isolate);

  if (iterator->HasNext()) {
    auto next_tuple = iterator->Next();

    auto &&value = WrapperV8::GetNextValue(isolate, std::get<0>(next_tuple),
                                           std::get<1>(next_tuple));
    result->Set(isolate->GetCurrentContext(),
                v8::String::NewFromUtf8(isolate, "value").ToLocalChecked(),
                value);
    result->Set(isolate->GetCurrentContext(),
                v8::String::NewFromUtf8(isolate, "done").ToLocalChecked(),
                v8::Boolean::New(isolate, false));
  } else {
    result->Set(isolate->GetCurrentContext(),
                v8::String::NewFromUtf8(isolate, "done").ToLocalChecked(),
                v8::Boolean::New(isolate, true));
  }

  info.GetReturnValue().Set(result);
}

template <typename WrapperV8>
void DOMTokenListIteratorWrapperV8<WrapperV8>::SymbolIterator(
    const v8::FunctionCallbackInfo<v8::Value> &info) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(
      DOMTokenListEntryIteratorWrapperV8::SymbolIterator);
  info.GetReturnValue().Set(info.This());
}

template <typename WrapperV8>
void DOMTokenListIteratorWrapperV8<WrapperV8>::InstallInterface(
    v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(
      DOMTokenListEntryIteratorWrapperV8::InstallInterface);
  INTERFACE_SETUP(DOMTokenListEntryIteratorWrapperV8);

  v8::Local<v8::Symbol> symbol_iterator = v8::Symbol::GetIterator(isolate);
  instance_object_template->Set(
      symbol_iterator, v8::FunctionTemplate::New(isolate, SymbolIterator));

  static const OperationConfig kOperationTable[] = {
      {"next", GetNext, 0, unsigned(v8::None),
       unsigned(FlagLocation::kPrototype)},
  };
  INSTALL_INTERFACE_OPERATIONS(kOperationTable);
}

template <typename WrapperV8>
const WrapperTypeInfo
    DOMTokenListIteratorWrapperV8<WrapperV8>::wrapper_type_info_ = {
        .install_interface_template_func = &InstallInterface,
        .interface_name = WrapperV8::kInterfaceName,
        .parent_class = nullptr,
        .dom_node_type = WrapperV8::type,
};

v8::Local<v8::Value> DOMTokenListEntryIteratorWrapperV8::GetNextValue(
    v8::Isolate *isolate, size_t key, const std::string &value) {
  v8::Local<v8::Array> array = v8::Array::New(isolate, 2);
  array->Set(isolate->GetCurrentContext(), 0, v8::Integer::New(isolate, key));
  array->Set(isolate->GetCurrentContext(), 1,
             v8::String::NewFromUtf8(isolate, value.c_str()).ToLocalChecked());
  return array;
}

v8::Local<v8::Value> DOMTokenListKeyIteratorWrapperV8::GetNextValue(
    v8::Isolate *isolate, size_t key, const std::string &value) {
  return v8::Integer::New(isolate, key);
}

v8::Local<v8::Value> DOMTokenListValueIteratorWrapperV8::GetNextValue(
    v8::Isolate *isolate, size_t key, const std::string &value) {
  return v8::String::NewFromUtf8(isolate, value.c_str()).ToLocalChecked();
}

#endif  // USE_V8

}  // namespace native_dom

}  // namespace msc
