//
// Created by jack on 2025/6/4.
//

#include "intersection_observer_wrapper_v8.h"

#include "element_type.h"
#include "message_proxy.h"
#include "node_wrapper_v8.h"
#include "types_def.h"
#include "v8_binding_utils.h"
#include "v8_js_callback.h"
#include "../intersection_observer.h"
#include "../element.h"
#include "../document.h"

#ifdef USE_V8

namespace msc {
namespace native_dom {

v8::Local<v8::Object> wrapIntersectionRect(v8::Isolate* isolate, v8::Local<v8::Context> context, blink::mt::IntersectionEntryData::Rect rect) {
  v8::Local<v8::String> key_x = v8::String::NewFromUtf8(isolate, "x").ToLocalChecked();
  v8::Local<v8::String> key_y = v8::String::NewFromUtf8(isolate, "y").ToLocalChecked();
  v8::Local<v8::String> key_width = v8::String::NewFromUtf8(isolate, "width").ToLocalChecked();
  v8::Local<v8::String> key_height = v8::String::NewFromUtf8(isolate, "height").ToLocalChecked();

  v8::Local<v8::Object> rect_obj = v8::Object::New(isolate);
  rect_obj->Set(context, key_x, v8::Number::New(isolate, rect.x)).Check();
  rect_obj->Set(context, key_y, v8::Number::New(isolate, rect.y)).Check();
  rect_obj->Set(context, key_width, v8::Number::New(isolate, rect.width)).Check();
  rect_obj->Set(context, key_height, v8::Number::New(isolate, rect.height)).Check();

  return rect_obj;
}

void parseRootMargin(v8::Isolate* isolate, v8::Local<v8::Context> context, v8::Local<v8::Object> options,
                     blink::mt::CreateIntersectionObserverParams& params) {
  v8::Local<v8::String> key_root_margin = v8::String::NewFromUtf8(isolate, "rootMargin").ToLocalChecked();
  if (options->Has(context, key_root_margin).ToChecked()) {
    v8::Local<v8::Value> root_margin_value;
    if (options->Get(context, key_root_margin).ToLocal(&root_margin_value)) {
      if (root_margin_value->IsString()) {
        params.margins_str_ = V8BindingUtils::ToSTDString(isolate, root_margin_value->ToString(context).ToLocalChecked());
      } else if (root_margin_value->IsArray()) {
        v8::Local<v8::Array> margin_array = v8::Local<v8::Array>::Cast(root_margin_value);
        if (margin_array->Length() == 4) {
          params.margins_.reserve(4);
          for (uint32_t i = 0; i < 4; ++i) {
            v8::Local<v8::Value> value;
            if (margin_array->Get(context, i).ToLocal(&value) && value->IsNumber()) {
              params.margins_.push_back(value->NumberValue(context).ToChecked());
            }
          }
        }
      }
    }
  }
}

void parseThreshold(v8::Isolate* isolate, v8::Local<v8::Context> context, v8::Local<v8::Object> options,
                    blink::mt::CreateIntersectionObserverParams& params) {
  v8::Local<v8::String> key_threshold = v8::String::NewFromUtf8(isolate, "threshold").ToLocalChecked();
  if (options->Has(context, key_threshold).ToChecked()) {
    v8::Local<v8::Value> threshold_value;
    if (options->Get(context, key_threshold).ToLocal(&threshold_value)) {
      if (threshold_value->IsArray()) {
        v8::Local<v8::Array> threshold_array = v8::Local<v8::Array>::Cast(threshold_value);
        uint32_t length = threshold_array->Length();
        params.thresholds_.reserve(length);
        for (uint32_t i = 0; i < length; ++i) {
          v8::Local<v8::Value> value;
          if (threshold_array->Get(context, i).ToLocal(&value) && value->IsNumber()) {
            params.thresholds_.push_back(value->NumberValue(context).ToChecked());
          }
        }
      }
    }
  }
}

void ConstructorCallback(const v8::FunctionCallbackInfo<v8::Value>& args) {
  v8::Isolate *isolate = args.GetIsolate();
  if (args.IsConstructCall()) {
    v8::HandleScope handle_scope(isolate);
    v8::Local<v8::Context> context = isolate->GetCurrentContext();
    if (args.Length() < 2 || !args[0]->IsFunction() || !args[1]->IsObject()) {
      isolate->ThrowException(v8::Exception::TypeError(v8::String::NewFromUtf8(isolate, "Invalid arguments").ToLocalChecked()));
      return;
    }

    v8::Local<v8::Function> callback = v8::Local<v8::Function>::Cast(args[0]);
    v8::Local<v8::Object> options = v8::Local<v8::Object>::Cast(args[1]);

    v8::Local<v8::String> key_root = v8::String::NewFromUtf8(isolate, "root").ToLocalChecked();

    if (!options->Has(context, key_root).ToChecked()) {
      isolate->ThrowException(v8::Exception::TypeError(v8::String::NewFromUtf8(isolate, "root is required").ToLocalChecked()));
      return;
    }

    v8::Local<v8::Value> root_value;
    if (!options->Get(context, key_root).ToLocal(&root_value) || !root_value->IsObject()) {
      isolate->ThrowException(v8::Exception::TypeError(v8::String::NewFromUtf8(isolate, "root must be an object").ToLocalChecked()));
      return;
    }

    v8::Local<v8::Object> root_object = v8::Local<v8::Object>::Cast(root_value);
    auto root_element = static_cast<Element*>(V8BindingUtils::ToScriptWrappable(isolate, root_object));
    V8_API_CHECK_NULL_RETURN(root_element);

    blink::mt::CreateIntersectionObserverParams params;
    params.root_tag_ = root_element->GetNodeId();
    params.is_viewport_ = root_element->nodeTag() == Tag::DOCUMENT;

    auto document = root_element->documentElement();
    if (document == nullptr) {
      isolate->ThrowException(v8::Exception::Error(v8::String::NewFromUtf8(isolate, "Document not found").ToLocalChecked()));
      return;
    }

    // 获取options.rootMargin
    parseRootMargin(isolate, context, options, params);

    // 获取options.threshold
    parseThreshold(isolate, context, options, params);

    auto persistent_callback = std::make_shared<v8::Global<v8::Value>>(isolate, callback);
    auto persistent_context = std::make_shared<v8::Global<v8::Context>>(isolate, context);
    auto callback_c = [isolate, persistent_callback, persistent_context](
                          const std::vector<blink::mt::IntersectionEntryData>&
                              entries) {
      v8::HandleScope handleScope(isolate);
      v8::Local<v8::Context> context = persistent_context->Get(isolate);
      v8::Context::Scope context_scope(context);
      v8::Local<v8::Value> callbackValue = persistent_callback->Get(isolate);
      v8::Local<v8::Function> callback = callbackValue.As<v8::Function>();

      v8::Local<v8::Array> entries_array = v8::Array::New(isolate, entries.size());

      v8::Local<v8::String> key_bounding_client_rect = v8::String::NewFromUtf8(isolate, "boundingClientRect").ToLocalChecked();
      v8::Local<v8::String> key_intersection_ratio = v8::String::NewFromUtf8(isolate, "intersectionRatio").ToLocalChecked();
      v8::Local<v8::String> key_intersection_rect = v8::String::NewFromUtf8(isolate, "intersectionRect").ToLocalChecked();
      v8::Local<v8::String> key_is_intersecting = v8::String::NewFromUtf8(isolate, "isIntersecting").ToLocalChecked();
      v8::Local<v8::String> key_root_bounds = v8::String::NewFromUtf8(isolate, "rootBounds").ToLocalChecked();

      for (size_t i = 0; i < entries.size(); ++i) {
        const auto& entry = entries[i];
        v8::Local<v8::Object> entry_obj = v8::Object::New(isolate);

        v8::Local<v8::Object> bounding_client_rect = wrapIntersectionRect(isolate, context, entry.bounding_client_rect);
        entry_obj->Set(context, key_bounding_client_rect, bounding_client_rect).Check();

        entry_obj->Set(context, key_intersection_ratio, v8::Number::New(isolate, entry.intersection_ratio)).Check();

        if (entry.is_intersecting) {
          v8::Local<v8::Object> intersection_rect = wrapIntersectionRect(isolate, context, entry.intersection_rect);
          entry_obj->Set(context, key_intersection_rect, intersection_rect).Check();
        } else {
          entry_obj->Set(context, key_intersection_rect, v8::Null(isolate)).Check();
        }

        entry_obj->Set(context, key_is_intersecting, v8::Boolean::New(isolate, entry.is_intersecting)).Check();

        v8::Local<v8::Object> root_bounds_rect = wrapIntersectionRect(isolate, context, entry.root_bounds_rect);
        entry_obj->Set(context, key_root_bounds, root_bounds_rect).Check();

        entries_array->Set(context, i, entry_obj).Check();
      }

      // 调用回调函数
      v8::Local<v8::Value> argv[] = { entries_array };
      callback->Call(context, v8::Null(isolate), 1, argv).ToLocalChecked();
    };

    auto callable = std::make_unique<JSCallbackForIntersectionObserver>(
        std::move(callback_c));
    auto index = root_element->ProtectJSCallback(std::make_unique<V8JSCallback>(
        persistent_callback, persistent_context, std::move(callable)));
    JSCallbackInfo callback_info{
        index, params.root_tag_,
        true,  false,
        true,  JSCallbackInfo::Type::IntersectionObserver};

    auto observer =
        document->CreateIntersectionObserver(std::move(params), callback_info);
    if (!observer) {
      isolate->ThrowException(v8::Exception::Error(
      v8::String::NewFromUtf8(isolate, "Failed to create IntersectionObserver").ToLocalChecked()
      ));
      return;
    }
    v8::Local<v8::Object> obj = CREATE_V8_OBJECT_NO_RETAIN(isolate, context, IntersectionObserverWrapperV8::GetWrapperTypeInfo(), observer);
    args.GetReturnValue().Set(obj);
  } else {
    // 如果不是构造调用，抛出异常
    isolate->ThrowException(v8::String::NewFromUtf8(isolate, "Must be called as a constructor").ToLocalChecked());
  }
}


void IntersectionObserverWrapperV8::Observe(const v8::FunctionCallbackInfo<v8::Value>& args) {
  v8::Local<v8::Object> v8_receiver = args.This();
  v8::Isolate *isolate = args.GetIsolate();
  IntersectionObserver *observer = static_cast<IntersectionObserver *>(V8BindingUtils::ToScriptWrappable(args.GetIsolate(), v8_receiver));
  V8_API_CHECK_NULL_RETURN(observer);

  v8::Local<v8::Object> target_obj = v8::Local<v8::Object>::Cast(args[0]);
  auto target_element = static_cast<Element *>(V8BindingUtils::ToScriptWrappable(isolate, target_obj));
  V8_API_CHECK_NULL_RETURN(target_element);

  auto document = target_element->documentElement();
  if (!document) {
    return;
  }

  document->IntersectionObserverObserve(observer->GetId(), target_element->GetNodeId());
}

void IntersectionObserverWrapperV8::Disconnect(const v8::FunctionCallbackInfo<v8::Value>& args) {
  v8::Local<v8::Object> v8_receiver = args.This();
  v8::Isolate *isolate = args.GetIsolate();
  IntersectionObserver *observer = static_cast<IntersectionObserver *>(V8BindingUtils::ToScriptWrappable(args.GetIsolate(), v8_receiver));
  V8_API_CHECK_NULL_RETURN(observer);
  observer->message_proxy_->SendIntersectionObserverObserveMessage( observer->page_id_, observer->GetId(), 0);
}

void IntersectionObserverWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
  MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(ElementWrapperV8::InstallInterface);
  INTERFACE_SETUP_WITH_CONSTRUCTOR(IntersectionObserverWrapperV8, ConstructorCallback, 1);

  static const OperationConfig kOperationTable[] = {
    {IntersectionObserver::kObserver.c_str(), Observe, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
    {IntersectionObserver::kDisconnect.c_str(), Disconnect, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
  };
  INSTALL_INTERFACE_OPERATIONS(kOperationTable)
  auto context = isolate->GetCurrentContext();
  v8::Local<v8::Object> global = context->Global();
  auto func = interface_function_template->GetFunction(context).ToLocalChecked();
  v8::Local<v8::Value> nativeValue = global->Get(context, v8::String::NewFromUtf8(isolate, "native").ToLocalChecked()).ToLocalChecked();
  v8::Local<v8::Object> nativeObject = nativeValue.As<v8::Object>();
  nativeObject->Set(context,
              v8::String::NewFromUtf8(isolate, "IntersectionObserver").ToLocalChecked(),
              func).Check();
}

const WrapperTypeInfo IntersectionObserverWrapperV8::wrapper_type_info_ = {
  .install_interface_template_func = &InstallInterface,
  .interface_name = "IntersectionObserver",
  .parent_class = nullptr,
  .dom_node_type = WrapperTypeInfo::DOMNodeType::kIntersectionObserver,
};

}
}

#endif

