//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/21.
//

#ifndef MSC_ANDROID_V8BINDINGUTILS_H
#define MSC_ANDROID_V8BINDINGUTILS_H

#include "../../../../libs/include_v8.0/v8.h"
#include "wrapper_type_info.h"
#include "../script_wrappable.h"
#include "../../../chromium/base/containers/span.h"
#include "../../android/TraceUtils.h"
#include "./native_dom_object_wrapper.h"

#if USE_V8

#define INTERFACE_SETUP_COMMON(DOM_WRAPPER_CLASS) \
    const WrapperTypeInfo& wrapper_type_info = DOM_WRAPPER_CLASS::GetWrapperTypeInfo(); \
    v8::Local<v8::FunctionTemplate> interface_function_template = interface_template.As<v8::FunctionTemplate>();    \
    v8::Local<v8::ObjectTemplate> instance_object_template = interface_function_template->InstanceTemplate();       \
    instance_object_template->SetInternalFieldCount(1);                                        \
    v8::Local<v8::ObjectTemplate> prototype_object_template = interface_function_template->PrototypeTemplate(); \
    const WrapperTypeInfo *parent_class = wrapper_type_info.parent_class;  \
    v8::Local<v8::FunctionTemplate> parent_interface_template;  \
    if (parent_class != nullptr) {  \
        parent_interface_template = parent_class->GetV8ClassTemplate(isolate).As<v8::FunctionTemplate>();   \
    }   \
    V8BindingUtils::SetupIDLInterfaceTemplate(isolate, wrapper_type_info, instance_object_template, prototype_object_template, interface_function_template, parent_interface_template);

#define INTERFACE_SETUP(DOM_WRAPPER_CLASS)  \
    INTERFACE_SETUP_COMMON(DOM_WRAPPER_CLASS)   \
    v8::Local<v8::Signature> signature = v8::Signature::New(isolate, interface_function_template);

#define INTERFACE_SETUP_WITH_CONSTRUCTOR(DOM_WRAPPER_CLASS, Callback, ArgCount) \
    INTERFACE_SETUP_COMMON(DOM_WRAPPER_CLASS)                                  \
    interface_function_template->SetCallHandler(Callback);   \
    interface_function_template->SetLength(ArgCount);                          \
    v8::Local<v8::Signature> signature = v8::Signature::New(isolate, interface_function_template);

#define INSTALL_INTERFACE_OPERATIONS(kOperationTable)   \
    V8BindingUtils::InstallOperations(isolate, instance_object_template, prototype_object_template, interface_template, signature, kOperationTable);

#define INSTALL_INTERFACE_ATTRIBUTES(kAttributeTable) \
    V8BindingUtils::InstallAttributes(isolate, instance_object_template, prototype_object_template, interface_template, signature, kAttributeTable);

#define V8_API_CHECK_FAILL_RETURN(compare, block) \
  do {                                            \
    if (!(compare)) {                             \
      block;                                      \
      return;                                     \
    }                                             \
  } while (0)

#define V8_API_CHECK_NULL_RETURN(obj) \
  V8_API_CHECK_FAILL_RETURN(obj, {})

#define V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_RETURN_UNDEFINED(info,          \
                                                             excpeted_argc) \
  V8_API_CHECK_FAILL_RETURN(                                                \
      (info).Length() < (excpeted_argc),                                    \
      info.GetReturnValue().Set(v8::Undefined(info.GetIsolate())))

#define V8_API_CHECK_NUMBER_FAILL_RETURN_UNDEFINED(info, obj) \
  V8_API_CHECK_FAILL_RETURN(                                  \
      (obj)->IsNumber(),                                      \
      info.GetReturnValue().Set(v8::Undefined(info.GetIsolate())))

#define V8_API_CHECK_OBJECT_FAILL_RETURN_UNDEFINED(info, obj) \
  V8_API_CHECK_FAILL_RETURN(                                  \
      (obj)->IsObject(),                                      \
      info.GetReturnValue().Set(v8::Undefined(info.GetIsolate())))

#define V8_API_CHECK_STRING_FAILL_RETURN_UNDEFINED(info, obj) \
  V8_API_CHECK_FAILL_RETURN(                                  \
      (obj)->IsString(),                                      \
      info.GetReturnValue().Set(v8::Undefined(info.GetIsolate())))

#define V8_API_CHECK_FUNCTION_FAILL_RETURN_UNDEFINED(info, obj) \
  V8_API_CHECK_FAILL_RETURN(                                    \
      (obj)->IsFunction(),                                      \
      info.GetReturnValue().Set(v8::Undefined(info.GetIsolate())))

#define V8_API_CHECK_FAILL_THROW_TYPERROR(info, compare, msg)                \
  V8_API_CHECK_FAILL_RETURN(                                                 \
      compare, (info).GetIsolate()->ThrowException(v8::Exception::TypeError( \
                   v8::String::NewFromUtf8((info).GetIsolate(), msg)         \
                       .ToLocalChecked())))

#define V8_API_CHECK_ARGUMENTS_LENGTH_FAILL_THROW_TYPEERROR(                  \
    info, excpeted_argc, msg)                                                 \
  V8_API_CHECK_FAILL_THROW_TYPERROR(info, (info).Length() >= (excpeted_argc), \
                                    msg)

#define V8_API_CHECK_NUMBER_FAILL_THROW_TYPEERROR(info, obj, msg) \
  V8_API_CHECK_FAILL_THROW_TYPERROR(info, (obj)->IsNumber(), msg)

#define V8_API_CHECK_OBJECT_FAILL_THROW_TYPEERROR(info, obj, msg) \
  V8_API_CHECK_FAILL_THROW_TYPERROR(info, (obj)->IsObject(), msg)

#define V8_API_CHECK_STRING_FAILL_THROW_TYPEERROR(info, obj, msg) \
  V8_API_CHECK_FAILL_THROW_TYPERROR(info, (obj)->IsString(), msg)

#define V8_API_CHECK_FUNCTION_FAILL_THROW_TYPEERROR(info, obj, msg) \
  V8_API_CHECK_FAILL_THROW_TYPERROR(info, (obj)->IsFunction(), msg)

#define CREATE_V8_OBJECT(isolate, context, wrapperTypeInfo, script_wrappable) \
    V8BindingUtils::CreateV8Object(isolate, context, wrapperTypeInfo, V8BindingUtils::CreateNativeDOMObjectWrapper(script_wrappable), true)

#define CREATE_V8_OBJECT_NO_RETAIN(isolate, context, wrapperTypeInfo, script_wrappable) \
    V8BindingUtils::CreateV8Object(isolate, context, wrapperTypeInfo, V8BindingUtils::CreateNativeDOMObjectWrapper(script_wrappable), false)

namespace msc {
namespace native_dom {

    // On which object the property is defined
enum class FlagLocation {
    kInstance,
    kPrototype,
    kInterface,
};

// v8::Signature check against the receiver object
enum class FlagReceiverCheck {
    kCheck,
    kDoNotCheck,
};

struct OperationConfig {
    OperationConfig& operator=(const OperationConfig&) = delete;

    const char* name;
    v8::FunctionCallback callback;
    unsigned length : 8;
    unsigned v8_property_attribute : 3;  // v8::PropertyAttribute
    unsigned location : 2;               // FlagLocation
//    unsigned world : 2;                  // FlagWorld
//    unsigned receiver_check : 1;         // FlagReceiverCheck
//    unsigned cross_origin_check : 1;     // FlagCrossOriginCheck
//    unsigned v8_side_effect : 2;         // v8::SideEffectType
};

// Web IDL attribute
struct AttributeConfig {
    AttributeConfig& operator=(const AttributeConfig&) = delete;

    const char* name;
    v8::FunctionCallback callback_for_get;
    v8::FunctionCallback callback_for_set;
    unsigned v8_property_attribute : 3;       // v8::PropertyAttribute
    unsigned location : 2;                    // FlagLocation
//        unsigned world : 2;                       // FlagWorld
//        unsigned receiver_check : 1;              // FlagReceiverCheck
//        unsigned cross_origin_check_for_get : 1;  // FlagCrossOriginCheck
//        unsigned cross_origin_check_for_set : 1;  // FlagCrossOriginCheck
//        unsigned v8_side_effect : 2;              // v8::SideEffectType
//        unsigned v8_cached_accessor : 2;  // V8PrivateProperty::CachedAccessor
};

class V8BindingUtils {
public:
    static NativeDOMObjectWrapper* CreateNativeDOMObjectWrapper(ScriptWrappable *script_wrappable);
    static NativeDOMObjectWrapper* CreateNativeDOMObjectWrapper(std::shared_ptr<ScriptWrappable> script_wrappable_ptr);

    static void FinalizeJSObject(const v8::WeakCallbackInfo<NativeDOMObjectWrapper>& info);

    static v8::Local<v8::Object> CreateV8Object(v8::Isolate *isolate, v8::Local<v8::Context> context, const WrapperTypeInfo &type, NativeDOMObjectWrapper *wrapper, bool retained = true);

    static ScriptWrappable *ToScriptWrappable(v8::Isolate* isolate, v8::Local<v8::Object> object);
    std::shared_ptr<ScriptWrappable> ToScriptWrappableSharedPtr(v8::Isolate* isolate, v8::Local<v8::Object> object);

    static void SetupIDLInterfaceTemplate(
            v8::Isolate* isolate,
            const WrapperTypeInfo& wrapper_type_info,
            v8::Local<v8::ObjectTemplate> instance_template,
            v8::Local<v8::ObjectTemplate> prototype_template,
            v8::Local<v8::FunctionTemplate> interface_template,
            v8::Local<v8::FunctionTemplate> parent_interface_template);

    static void InstallOperations(
            v8::Isolate* isolate,
            v8::Local<v8::Template> instance_template,
            v8::Local<v8::Template> prototype_template,
            v8::Local<v8::Template> interface_template,
            v8::Local<v8::Signature> signature,
            base::span<const OperationConfig> configs);

    static void InstallAttributes(v8::Isolate* isolate,
                                  v8::Local<v8::Template> instance_template,
                                  v8::Local<v8::Template> prototype_template,
                                  v8::Local<v8::Template> interface_template,
                                  v8::Local<v8::Signature> signature,
                                  base::span<const AttributeConfig> configs);

    static std::string ToSTDString(v8::Isolate* isolate, v8::Local<v8::String> str) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(V8BindingUtils::ToSTDString);
        if (str.IsEmpty()) return "";

        v8::String::Utf8Value value(isolate, str);
        return std::string(*value, value.length());
    }

    static v8::Local<v8::String> ToV8String(v8::Isolate* isolate, const std::string& str) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(V8BindingUtils::ToV8String1);
        v8::MaybeLocal<v8::String> maybe = v8::String::NewFromUtf8(
                isolate, str.c_str(), v8::NewStringType::kNormal, str.length());
        return maybe.FromMaybe(v8::Local<v8::String>());
    }

    static v8::Local<v8::String> ToV8String(v8::Isolate* isolate, const char* str) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(V8BindingUtils::ToV8String2);
        return v8::String::NewFromUtf8(isolate, str).FromMaybe(v8::Local<v8::String>());
    }

    static blink::mt::PropValue V8ValueToPropValue(v8::Isolate *isolate, v8::Local<v8::Value> value);
    static v8::Local<v8::Value> PropValueToV8(v8::Isolate* isolate, const blink::mt::PropValue& prop);
    static v8::Local<v8::Object> V8ObjectFromProps(v8::Isolate *isolate, const blink::mt::Props &props, const std::string &exclude_name);
};

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_V8BINDINGUTILS_H
