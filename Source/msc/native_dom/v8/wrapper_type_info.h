//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/3/21.
//

#ifndef MSC_ANDROID_WRAPPERTYPEINFO_H
#define MSC_ANDROID_WRAPPERTYPEINFO_H

#include "../../../../libs/include_v8.0/v8.h"
#include "v8_interface_bridge.h"
#include "../../logger.h"

#if USE_V8

namespace msc {
namespace native_dom {

struct WrapperTypeInfo {
public:
    enum class DOMNodeType {
        kUndefined = 0,
        kEventTarget,
        kNode,
        kContainerNode,
        kElement,
        kDocument,
        kDocumentImplementation,
        kViewComponent,
        kTextNode,
        kVNode,
        kCSSStyleDeclaration,
        kDOMTokenList,
        kDOMTokenListEntryIterator,
        kDOMTokenListKeyIterator,
        kDOMTokenListValueIterator,
        kEvent,
        kTouchEvent,
        kIntersectionObserver,

        kMaxNodeType
    };

    v8::Local<v8::Template> GetV8ClassTemplate(v8::Isolate* isolate) const;

    static void ClearV8ClassTemplate(v8::Isolate* isolate);
    static void ClearAllV8ClassTemplates();

    V8InterfaceBridge::InstallInterfaceTemplateFuncType install_interface_template_func;
    const char* interface_name;
    const WrapperTypeInfo* parent_class;
    DOMNodeType dom_node_type;
};

} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_WRAPPERTYPEINFO_H
