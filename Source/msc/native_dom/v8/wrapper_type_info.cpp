//
// Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/3/21.
//

#include "wrapper_type_info.h"
#include <unordered_map>

#if USE_V8

namespace msc {
namespace native_dom {

//    std::unordered_map<v8::Isolate *, std::unordered_map<std::string, v8::Global<v8::Template>>>& GetV8TemplateMap() {
//        //TODO: isolate销毁时清理相应的项
//        //TODO: 确认多个app是否是同一线程，若否，需要考虑多线程问题
//        static std::unordered_map<v8::Isolate *, std::unordered_map<std::string, v8::Global<v8::Template>>> g_isolate_to_template_map;
//        return g_isolate_to_template_map;
//    }
    std::unordered_map<v8::Isolate *, std::vector<v8::Global<v8::Template>>>& GetV8TemplateMap() {
        //TODO: isolate销毁时清理相应的项
        //TODO: 确认多个app是否是同一线程，若否，需要考虑多线程问题
        static std::unordered_map<v8::Isolate *, std::vector<v8::Global<v8::Template>>> g_isolate_to_template_map;
        return g_isolate_to_template_map;
    }

    void WrapperTypeInfo::ClearV8ClassTemplate(v8::Isolate* isolate) {
        auto &isolate_to_template_map = GetV8TemplateMap();
        MSC_RENDERER_LOG_DEBUG("[native_dom] ClearV8ClassTemplate before find, isolate: %p", isolate);
        auto it = isolate_to_template_map.find(isolate);
        MSC_RENDERER_LOG_DEBUG("[native_dom] ClearV8ClassTemplate after find, isolate: %p", isolate);
        if (it != isolate_to_template_map.end()) {
            MSC_RENDERER_LOG_DEBUG("[native_dom] ClearV8ClassTemplate before erase, isolate: %p", isolate);
            isolate_to_template_map.erase(it);
            MSC_RENDERER_LOG_DEBUG("[native_dom] ClearV8ClassTemplate succeeded, isolate: %p", isolate);
        } else {
            MSC_RENDERER_LOG_DEBUG("[native_dom] ClearV8ClassTemplate failed, isolate: %p", isolate);
        }
    }

    void WrapperTypeInfo::ClearAllV8ClassTemplates() {
        auto &isolate_to_template_map = GetV8TemplateMap();
        isolate_to_template_map.clear();
    }

//    v8::MaybeLocal<v8::Template> FindV8Template(v8::Isolate* isolate, const std::string& class_name) {
//        v8::EscapableHandleScope handle_scope(isolate);
//        auto &isolate_to_template_map = GetV8TemplateMap();
//        auto it = isolate_to_template_map.find(isolate);
//        if (it != isolate_to_template_map.end()) {
//            auto &class_name_to_template_map = it->second;
//            auto it2 = class_name_to_template_map.find(class_name);
//            if (it2 != class_name_to_template_map.end()) {
//                MSC_RENDERER_LOG_DEBUG("[native_dom] FindV8Template found existing template for: %s", class_name.c_str());
//                return handle_scope.Escape(v8::Local<v8::Template>::New(isolate, it2->second));
//            }
//        }
//        return handle_scope.EscapeMaybe(v8::MaybeLocal<v8::Template>());
//    }
    v8::MaybeLocal<v8::Template> FindV8Template(v8::Isolate* isolate, const WrapperTypeInfo::DOMNodeType& type) {
        v8::EscapableHandleScope handle_scope(isolate);
        auto &isolate_to_template_map = GetV8TemplateMap();
        auto it = isolate_to_template_map.find(isolate);
        if (it != isolate_to_template_map.end()) {
            return handle_scope.Escape(v8::Local<v8::Template>::New(isolate, it->second[(int)type])) ;
        }
        return handle_scope.EscapeMaybe(v8::MaybeLocal<v8::Template>());
    }

    void SaveV8Template(v8::Isolate* isolate, const WrapperTypeInfo::DOMNodeType& type, v8::Local<v8::Template> v8_template) {
        auto &isolate_to_template_map = GetV8TemplateMap();
        auto it = isolate_to_template_map.find(isolate);
        if (it == isolate_to_template_map.end()) { //TODO: add UnLikely
            auto result = isolate_to_template_map.insert(std::make_pair(isolate, std::vector<v8::Global<v8::Template>>((int)(WrapperTypeInfo::DOMNodeType::kMaxNodeType))));
            it = result.first;
        }
        it->second[(int)type].Reset(isolate, v8_template);
//        //TODO: 找到和找不到的逻辑分开，不重复使用map下标
//        auto &class_name_to_template_map = isolate_to_template_map[isolate];
//        class_name_to_template_map[class_name].Reset(isolate, v8_template);
    }

//    void SaveV8Template(v8::Isolate* isolate, const std::string& class_name, v8::Local<v8::Template> v8_template) {
//        auto &isolate_to_template_map = GetV8TemplateMap();
//        auto it = isolate_to_template_map.find(isolate);
//        if (it == isolate_to_template_map.end()) { //TODO: add UnLikely
//            isolate_to_template_map[isolate] = std::unordered_map<std::string, v8::Global<v8::Template>>();
//        }
//        //TODO: 找到和找不到的逻辑分开，不重复使用map下标
//        auto &class_name_to_template_map = isolate_to_template_map[isolate];
//        class_name_to_template_map[class_name].Reset(isolate, v8_template);
//    }

    v8::Local<v8::Template> WrapperTypeInfo::GetV8ClassTemplate(v8::Isolate* isolate) const {
        MSC_RENDERER_LOG_DEBUG("[native_dom] GetV8ClassTemplate for: %s, isolate: %p", this->interface_name, isolate);

        v8::EscapableHandleScope handle_scope(isolate);
        //TODO: find template and return if found
        v8::MaybeLocal<v8::Template> v8_template_maybe = FindV8Template(isolate, dom_node_type);
        if (!v8_template_maybe.IsEmpty()) { //TODO: add Likely/Unlikely
            return handle_scope.Escape(v8_template_maybe.ToLocalChecked());
        }

//        v8_template = v8::FunctionTemplate::New(isolate, &ConstructorCallback);
        v8::Local<v8::Template> v8_template = v8::FunctionTemplate::New(isolate, nullptr);

        install_interface_template_func(isolate, v8_template);

//        v8::Local<v8::ObjectTemplate> instance_template = v8_template.As<v8::FunctionTemplate>()->InstanceTemplate();
//        instance_template->SetInternalFieldCount(1); //TODO: 不用magic number

        //Save
        SaveV8Template(isolate, dom_node_type, v8_template);

        return handle_scope.Escape(v8_template);
    }


} // msc
} // native_dom

#endif