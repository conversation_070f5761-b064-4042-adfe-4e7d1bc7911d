//
// Created by jack on 2025/6/4.
//

#ifndef MSC_ANDROID_INTERSECTION_OBSERVER_WRAPPER_V8_H
#define MSC_ANDROID_INTERSECTION_OBSERVER_WRAPPER_V8_H

#include "wrapper_type_info.h"

#ifdef USE_V8

namespace msc {
namespace native_dom {

class IntersectionObserverWrapperV8 {
public:
  static constexpr const WrapperTypeInfo& GetWrapperTypeInfo() {
    return wrapper_type_info_;
  }

private:
  static void Observe(const v8::FunctionCallbackInfo<v8::Value>& args);
  static void Disconnect(const v8::FunctionCallbackInfo<v8::Value>& args);

  static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);
  static const WrapperTypeInfo wrapper_type_info_;
};

}
}

#endif

#endif //MSC_ANDROID_INTERSECTION_OBSERVER_WRAPPER_V8_H
