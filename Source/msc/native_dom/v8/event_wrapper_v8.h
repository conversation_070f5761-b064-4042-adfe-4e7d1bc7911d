//
// Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/5/23.
//

#ifndef MSC_ANDROID_EVENT_WRAPPER_V8_H
#define MSC_ANDROID_EVENT_WRAPPER_V8_H

#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

class EventWrapperV8 {
public:
    static constexpr const WrapperTypeInfo &GetWrapperTypeInfo() {
        return wrapper_type_info_;
    }

private:
    static void StopPropagation(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetType(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetTimeStamp(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetCurrentTarget(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetTarget(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetEventPhase(const v8::FunctionCallbackInfo<v8::Value> &info);
    static void GetDetail(const v8::FunctionCallbackInfo<v8::Value> &info);

    static void InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template);

    static const WrapperTypeInfo wrapper_type_info_;
};
} // msc
} // native_dom

#endif

#endif //MSC_ANDROID_EVENT_WRAPPER_V8_H
