////
//// Created by <PERSON><PERSON><PERSON> on 2025/3/22.
////
//
//#include "event_target_wrapper_v8.h"
//#include "v8_binding_utils.h"
//
//namespace msc {
//namespace native_dom {
//    void EventTargetWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
//        INTERFACE_SETUP(EventTargetWrapperV8);
//
//        static const OperationConfig kOperationTable[] = {
//                {ElementTarget::kGetAttribute.c_str(), GetAttribute, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
//                {Element::kSetAttribute.c_str(), SetAttribute, 2, unsigned(v8::None), unsigned(FlagLocation::kPrototype)},
//        };
//        INSTALL_INTERFACE_OPERATIONS(kOperationTable);
//    }
//
//    const WrapperTypeInfo EventTargetWrapperV8::wrapper_type_info_ = {
//            .install_interface_template_func = &InstallInterface,
//            .interface_name = "EventTarget",
//            .parent_class = nullptr
//    };
//
//} // msc
//} // native_dom