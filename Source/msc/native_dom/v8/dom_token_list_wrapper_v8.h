#pragma once

#ifndef DOM_TOKEN_LIST_WRAPPER_V8_H
#define DOM_TOKEN_LIST_WRAPPER_V8_H

#include "wrapper_type_info.h"

#if USE_V8

namespace msc {
namespace native_dom {

template <typename WrapperV8>
class DOMTokenListIteratorWrapperV8 {
 public:
  static constexpr const WrapperTypeInfo &GetWrapperTypeInfo() {
    return wrapper_type_info_;
  }

 private:
  static void GetNext(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void SymbolIterator(const v8::FunctionCallbackInfo<v8::Value> &info);

  static void InstallInterface(v8::Isolate *isolate,
                               v8::Local<v8::Template> interface_template);

  static const WrapperTypeInfo wrapper_type_info_;
};

class DOMTokenListEntryIteratorWrapperV8
    : public DOMTokenListIteratorWrapperV8<DOMTokenListEntryIteratorWrapperV8> {
 public:
  static constexpr WrapperTypeInfo::DOMNodeType type =
      WrapperTypeInfo::DOMNodeType::kDOMTokenListEntryIterator;
  static constexpr char kInterfaceName[] = "DOMTokenListEntryIterator";
  static v8::Local<v8::Value> GetNextValue(v8::Isolate *isolate, size_t key,
                                           const std::string &value);
};

class DOMTokenListKeyIteratorWrapperV8
    : public DOMTokenListIteratorWrapperV8<DOMTokenListKeyIteratorWrapperV8> {
 public:
  static constexpr WrapperTypeInfo::DOMNodeType type =
      WrapperTypeInfo::DOMNodeType::kDOMTokenListKeyIterator;
  static constexpr char kInterfaceName[] = "DOMTokenListKeyIterator";
  static v8::Local<v8::Value> GetNextValue(v8::Isolate *isolate, size_t key,
                                           const std::string &value);
};

class DOMTokenListValueIteratorWrapperV8
    : public DOMTokenListIteratorWrapperV8<DOMTokenListValueIteratorWrapperV8> {
 public:
  static constexpr WrapperTypeInfo::DOMNodeType type =
      WrapperTypeInfo::DOMNodeType::kDOMTokenListValueIterator;
  static constexpr char kInterfaceName[] = "DOMTokenListValueIterator";
  static v8::Local<v8::Value> GetNextValue(v8::Isolate *isolate, size_t key,
                                           const std::string &value);
};

class DOMTokenListWrapperV8 {
 public:
  static constexpr const WrapperTypeInfo &GetWrapperTypeInfo() {
    return wrapper_type_info_;
  }

 private:
  static void GetLength(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void GetValue(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void GetItem(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Contains(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Add(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Remove(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Replace(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Supports(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Toggle(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void ForEach(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void ToString(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Values(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Keys(const v8::FunctionCallbackInfo<v8::Value> &info);
  static void Entries(const v8::FunctionCallbackInfo<v8::Value> &info);

  static void InstallInterface(v8::Isolate *isolate,
                               v8::Local<v8::Template> interface_template);

  template <typename IteratorWrapperV8>
  static void GetIterator(const v8::FunctionCallbackInfo<v8::Value> &info);

  static const WrapperTypeInfo wrapper_type_info_;
};

}  // namespace native_dom

}  // namespace msc

#endif  // USE_V8

#endif  // DOM_TOKEN_LIST_WRAPPER_V8_H