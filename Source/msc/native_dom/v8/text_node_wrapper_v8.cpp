//
// Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/4/7.
//

#include "text_node_wrapper_v8.h"
#include "node_wrapper_v8.h"
#include "v8_binding_utils.h"

#if USE_V8

namespace msc {
namespace native_dom {

    void TextNodeWrapperV8::InstallInterface(v8::Isolate *isolate, v8::Local<v8::Template> interface_template) {
        MSC_TRACE_SCOPED_EVENT_NDOM_FUNC(TextNodeWrapperV8::InstallInterface);
        INTERFACE_SETUP(TextNodeWrapperV8);
    }

    const WrapperTypeInfo TextNodeWrapperV8::wrapper_type_info_ = {
            .install_interface_template_func = &InstallInterface,
            .interface_name = "TextNode",
            .parent_class = &NodeWrapperV8::GetWrapperTypeInfo(),
            .dom_node_type = WrapperTypeInfo::DOMNodeType::kElement,
    };


} // msc
} // native_dom

#endif