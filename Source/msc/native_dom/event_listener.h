//
//  EventListener.hpp
//  MSC
//
//  Created by ji<PERSON><PERSON> zhang on 2025/1/13.
//

#ifndef EventListener_hpp
#define EventListener_hpp

#include "native_dom_define.h"


#include "event.h"

#if USE_JSI
#include "jsi/jsi.h"
using namespace facebook::jsi;
#endif

namespace msc {
namespace native_dom {

class EventListener {
public:
  
  enum class EventListenerType {
      COMMON_EVENT_LISTENER_TYPE,
      JSC_EVENT_LISTENER_TYPE,
      V8_EVENT_LISTENER_TYPE
  };
  
  explicit EventListener(bool capture) : capture_(capture) {};
  virtual ~EventListener() = default;
  
  virtual bool IsSameObject(EventListener *) { return false; }
  virtual EventListenerType type() { return EventListenerType::COMMON_EVENT_LISTENER_TYPE; }
  virtual void FireEvent(const std::shared_ptr<Event> &event) {};

  bool capture() const { return capture_; }
  
protected:
  bool capture_;
};

} // namespace native_dom
} // namespace msc

#endif /* EventListener_hpp */
