//
//  MSCString.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/13.
//

#include "msc_string.h"
#include <sstream>

namespace msc {
namespace native_dom {

const std::string& g_EmptyString = "";

std::string MSCConvertToString(const std::vector<int> &v) {
  std::stringstream ss;
  ss << "[";
  for (int i = 0; i < v.size(); i++) {
    ss << v[i];
    if (i != v.size() - 1) {
      ss << ",";
    }
  }
  ss << "]";
  return ss.str();
}

}  // namespace native_dom
}  // namespace msc
