//
//  ElementHostObject.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/13.
//

#ifndef ElementHostObject_hpp
#define ElementHostObject_hpp

#include "ContainerNodeHostObject.h"
#include "../element.h"

#if USE_JSI

namespace msc {
namespace native_dom {

class ElementHostObject : public ContainerNodeHostObject {
public:
  ElementHostObject(std::shared_ptr<Element> element) : ContainerNodeHostObject(std::static_pointer_cast<ContainerNode>(element)) {}
  virtual ~ElementHostObject() override {}
  
  Value getProperty(Runtime& runtime, const MSCString& name) override;
  void setProperty(Runtime& rt, const MSCString& name, const Value& value) override;
//  std::vector<PropNameID> getPropertyMembersNames(Runtime &runtime) override;
 
  void bindInstanceFunctions(jsi::Runtime& runtime) override;
  
private:
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(SetAttribute);
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(GetAttribute);
};


}  // namespace native_dom
}  // namespace msc

#endif

#endif /* ElementHostObject_hpp */
