//
//  jsi_binding.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/1/8.
//

#ifndef JSIBINDING_H_
#define JSIBINDING_H_

#include "../native_dom_define.h"

#if USE_JSI

#include <jsi/jsi.h>
#include "../bridge/document_registry.h"
#include <unordered_map>

namespace msc {
namespace native_dom {

class DocumentRegistry;

class JSIBinding {
public:
    JSIBinding() = default;
    ~JSIBinding() = default;

    //JSI binding
    static void setupJSContext(facebook::jsi::Runtime *runtime, std::shared_ptr<DocumentRegistry> documentRegistry);

#ifndef __APPLE__
    static void newDocumentRegistryInUIThread(long jsRuntimePtr);
    static void setupJSContext(long jsRuntimePtr);
    static void registerDocumentRegistry(int pageId, std::shared_ptr<DocumentRegistry> doc);
    static  std::shared_ptr<DocumentRegistry> getDocumentRegistryByRuntime(long jsRuntimePtr);
    static  std::shared_ptr<DocumentRegistry> getDocumentRegistryByPage(int pageId);
    static void unregisterDocument(long jsRuntimePtr, int pageId);
    static void unregisterAllDocuments(long jsRuntimePtr);
private:
    static std::unordered_map<long,  std::shared_ptr<DocumentRegistry>> registryForJsRuntimeMap_;
    static std::unordered_map<int,  std::shared_ptr<DocumentRegistry>> registryForPageMap_;
#endif
};

} // namespace native_dom
} // namespace msc

#endif


#endif /* JSIBINDING_H_ */
