//
//  ElementHostObject.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/13.
//

#include "ElementHostObject.h"
#include "TraceUtils.h"

#if USE_JSI

namespace msc {
namespace native_dom {

Value ElementHostObject::getProperty(Runtime &runtime, const MSCString &propName) {
    traceBegin("js_getProperty");
//    fprintf(stderr, "##### getProperty %s\n", propName.c_str());
    Value result;
    if (propName == "nodeName") {
        result = facebook::jsi::String::createFromUtf8(runtime,
                                                       GetInstance<Node>()->nodeName());
    } else if (propName == "className") {
        MSCString className;
        GetInstance<Element>()->GetAttribute("class", className);
        result = facebook::jsi::String::createFromUtf8(runtime, className);
    } else if (propName == "id" || propName == "style") {
        MSCString value;
        GetInstance<Element>()->GetAttribute(propName, value);
        result = facebook::jsi::String::createFromUtf8(runtime, value);
    } else {
        result = NodeHostObject::getProperty(runtime, propName);
    }
    traceEnd("js_getProperty");
    return result;
}

void ElementHostObject::setProperty(jsi::Runtime &runtime, const MSCString &propName,
                                    const Value &value) {
    traceBegin("js_setProperty");
//    fprintf(stderr, "##### setProperty %s\n", propName.c_str());
    MSCString propNameStr = propName;
    if (propName == "className") {
        propNameStr = "class";
    }
    if (propNameStr == "id" || propNameStr == "class" || propNameStr == "style") {
        GetInstance<Element>()->SetAttribute(propNameStr,
                                             value.asString(runtime).utf8(runtime));
    } else {
        NodeHostObject::setProperty(runtime, propNameStr, value);
    }
    traceEnd("js_setProperty");
}

//std::vector<PropNameID> ElementHostObject::getPropertyMembersNames(Runtime &runtime) {
//  auto result = NodeHostObject::getPropertyMembersNames(runtime);
//  result.push_back(PropNameID::forAscii(runtime, "className"));
//  result.push_back(PropNameID::forAscii(runtime, "id"));
//  result.push_back(PropNameID::forAscii(runtime, "style"));
//  return result;
//}

void ElementHostObject::bindInstanceFunctions(jsi::Runtime& runtime) {
  NodeHostObject::bindInstanceFunctions(runtime);
  registerFunction(runtime, Element::kSetAttribute.c_str(), &ElementHostObject::SetAttribute, 2);
  registerFunction(runtime, Element::kGetAttribute.c_str(), &ElementHostObject::GetAttribute, 1);
}

HOST_OBJECT_MEMBER_FUNCTIONS_IMP(ElementHostObject, SetAttribute) {
  if (count <= 1 || !args[0].isString()) {
    return jsi::Value::undefined();
//    throw jsi::JSError(rt, "InsertBefore requires 2 argument, key must be string and only string.");
  }
  auto key = args[0].asString(rt);
  if (args[1].isString()) {
    GetInstance<Element>()->SetAttribute(key.utf8(rt), args[1].asString(rt).utf8(rt));
  } else {
    if (args[1].isObject()) {
      auto val = args[1].asObject(rt);
    } else if (args[1].isUndefined()) {
      GetInstance<Element>()->SetAttribute(key.utf8(rt), "undefined");
    } else if (args[1].isNull()) {
      GetInstance<Element>()->SetAttribute(key.utf8(rt), "null");
    } else if (args[1].isBool()) {
      auto boolean = args[1].getBool();
      GetInstance<Element>()->SetAttribute(key.utf8(rt), boolean ? "true" : "false");
    } else {
      GetInstance<Element>()->SetAttribute(key.utf8(rt), args[1].toString(rt).utf8(rt));
    }
  }
  return jsi::Value::undefined();
}

HOST_OBJECT_MEMBER_FUNCTIONS_IMP(ElementHostObject, GetAttribute) {
  if (count < 1 || !args[0].isString()) {
    return jsi::Value::undefined();
//    throw jsi::JSError(rt, "'GetAttribute' key value must be string and only string.");
  }
  auto key = args[0].asString(rt);
  MSCString result;
  GetInstance<Element>()->GetAttribute(key.utf8(rt), result);
  return jsi::String::createFromUtf8(rt, result);
}


} // namespace native_dom
} // namespace msc

#endif
