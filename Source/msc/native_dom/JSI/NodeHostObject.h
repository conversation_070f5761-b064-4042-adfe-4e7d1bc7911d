//
//  NodeHostObject.hpp
//  MSC
//
//  Created by ji<PERSON><PERSON> zhang on 2025/2/13.
//

#ifndef NodeHostObject_hpp
#define NodeHostObject_hpp

#include "EventTargetHostObject.h"
#include "../node.h"

#if USE_JSI

namespace msc {
namespace native_dom {

class NodeHostObject : public EventTargetHostObject {
  
  friend class ContainerNodeHostObject;
  
public:
  NodeHostObject(std::shared_ptr<Node> node) : EventTargetHostObject(node) {}
  
  Value getProperty(Runtime& runtime, const MSCString& name) override;
//  std::vector<PropNameID> getPropertyMembersNames(Runtime &runtime) override;
 
  void bindInstanceFunctions(jsi::Runtime& runtime) override;
  
private:
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(AppendChild);
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(InsertBefore);
  HOST_OBJ<PERSON>T_MEMBER_FUNCTIONS_DEF(remove);
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(RemoveChild);
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(AddEventListener);
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(RemoveEventListener);
};


}  // namespace native_dom
}  // namespace msc

#endif

#endif /* NodeHostObject_hpp */
