//
//  ViewComponentHostObject.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/13.
//

#ifndef ViewComponentHostObject_hpp
#define ViewComponentHostObject_hpp

#include "ElementHostObject.h"
#include "../view_component.h"

#if USE_JSI

namespace msc {
namespace native_dom {

class ViewComponentHostObject : public ElementHostObject {
public:
  ViewComponentHostObject(std::shared_ptr<ViewComponent> view) : ElementHostObject(view) {}
  virtual ~ViewComponentHostObject() override {}
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* ViewComponentHostObject_hpp */
