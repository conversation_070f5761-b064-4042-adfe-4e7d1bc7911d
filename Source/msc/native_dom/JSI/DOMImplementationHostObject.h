//
//  DOMImplementationHostObject.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/13.
//

#ifndef DOMImplementationHostObject_hpp
#define DOMImplementationHostObject_hpp

#include "NativeDOMObjectHostObject.h"
#include "../document.h"
#include "DocumentHostObject.h"
#include "../dom_implementation.h"

#if USE_JSI

namespace msc {
namespace native_dom {

class DOMImplementationHostObject : public NativeDOMObjectHostObject {
public:
  DOMImplementationHostObject(std::shared_ptr<DOMImplementation> instance) : NativeDOMObjectHostObject(instance) {}
  
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(CreateDocument) {
    auto pageId = args[0].asNumber();
    auto document = GetInstance<DOMImplementation>()->CreateDocument(pageId);
    auto hostObject = std::make_shared<DocumentHostObject>(document);
    hostObject->bindInstanceFunctions(rt);
    auto jsObject = jsi::Object::createFromHostObject(rt, hostObject);
    hostObject->SetJSObject(std::move(jsObject), &rt);
    return hostObject->GetJSObject();
  }
  
  HOST_OBJECT_MEMBER_FUNCTIONS_DEF(FrameMarker);
  
  void bindInstanceFunctions(jsi::Runtime& rt) override {
    NativeDOMObjectHostObject::bindInstanceFunctions(rt);
    registerFunction(rt, DOMImplementation::kCreateDocument.c_str(), &DOMImplementationHostObject::CreateDocument, 1);
    // registerFunction(rt, "frameMarker", &DOMImplementationHostObject::FrameMarker, 1);
  };
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* DOMImplementationHostObject_hpp */
