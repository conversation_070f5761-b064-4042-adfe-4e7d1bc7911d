//
// Created by <PERSON><PERSON><PERSON> on 2025/2/27.
//


#include "JSIBinding.h"

#if USE_JSI

//JSI binding
#include "DOMImplementationHostObject.h"
#include "../bridge/message_proxy.h"
#include "../../logger.h"

namespace msc {
    namespace native_dom {
#ifndef __APPLE__
        std::unordered_map<long,  std::shared_ptr<DocumentRegistry>> JSIBinding::registryForJsRuntimeMap_;

        std::unordered_map<int,  std::shared_ptr<DocumentRegistry>> JSIBinding::registryForPageMap_;

        void JSIBinding::newDocumentRegistryInUIThread(long jsRuntimePtr) {
            auto documentRegister = std::make_shared<msc::native_dom::DocumentRegistry>();
            registryForJsRuntimeMap_[jsRuntimePtr] = documentRegister;
        }
        void JSIBinding::setupJSContext(long jsRuntimePtr) {
#if USE_JSI
            auto *runtime = reinterpret_cast<facebook::jsi::Runtime *>(jsRuntimePtr);
            if (runtime == nullptr) {
                return;
            }
            facebook::jsi::Runtime &rt = *runtime;
            Object globalNativeObject(rt);
            std::shared_ptr<DocumentRegistry> registry =  getDocumentRegistryByRuntime(jsRuntimePtr);
            auto messageProxy = std::make_shared<MessageProxy>(registry);
            auto domImplObj = std::make_shared<DOMImplementation>(messageProxy);
            auto hostObject = std::make_shared<DOMImplementationHostObject>(domImplObj);
            hostObject->bindInstanceFunctions(rt);
            auto domImplJSObject = Object::createFromHostObject(rt, hostObject);
            hostObject->SetJSObject(std::move(domImplJSObject), runtime);
//            LOGE("[native_dom][DEBUG] create domImplJSObject: %p", domImplJSObject.);
            globalNativeObject.setProperty(rt, "implementation",hostObject->GetJSObject());
            rt.global().setProperty(rt, "native", std::move(globalNativeObject));
#endif
        }

        void JSIBinding::registerDocumentRegistry(int pageId, std::shared_ptr<DocumentRegistry> registry) {
            registryForPageMap_[pageId] = registry;
        }

        std::shared_ptr<DocumentRegistry> JSIBinding::getDocumentRegistryByPage(int pageId) {
            auto it = registryForPageMap_.find(pageId);
            if (it == registryForPageMap_.end()) {
                return nullptr;
            }
            return it->second;
        }

        std::shared_ptr<DocumentRegistry> JSIBinding::getDocumentRegistryByRuntime(long jsRuntimePtr) {
            auto it = registryForJsRuntimeMap_.find(jsRuntimePtr);
            if (it == registryForJsRuntimeMap_.end()) {
                return nullptr;
            }
            return it->second;
        }

        void JSIBinding::unregisterDocument(long jsRuntimePtr, int pageId) {
            auto it = registryForPageMap_.find(jsRuntimePtr);
            if (it == registryForPageMap_.end()) {
                return;
            }
            registryForPageMap_.erase(pageId);
        }

        void JSIBinding::unregisterAllDocuments(long jsRuntimePtr) {
            auto it = registryForJsRuntimeMap_.find(jsRuntimePtr);
            if (it == registryForJsRuntimeMap_.end()) {
                return;
            }
            registryForJsRuntimeMap_.erase(it);
        }

#endif
    }
}

#endif
