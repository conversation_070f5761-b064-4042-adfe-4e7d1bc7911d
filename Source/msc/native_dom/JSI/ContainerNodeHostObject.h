//
//  ContainerNodeHostObject.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/13.
//

#ifndef ContainerNodeHostObject_hpp
#define ContainerNodeHostObject_hpp

#include "NodeHostObject.h"
#include "../container_node.h"

#if USE_JSI

namespace msc {
namespace native_dom {

using namespace facebook;

class ContainerNodeHostObject : public NodeHostObject {
public:
  ContainerNodeHostObject(std::shared_ptr<ContainerNode> containerNode) : NodeHostObject(containerNode) {}
};

}  // namespace native_dom
}  // namespace msc

#endif

#endif /* ContainerNodeHostObject_hpp */
