//
//  DocumentHostObject.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/2/13.
//

#ifndef DocumentHostObject_hpp
#define DocumentHostObject_hpp

#include "ElementHostObject.h"
#include "../document.h"
#include "ViewComponentHostObject.h"
#include "TraceUtils.h"

#if USE_JSI

namespace msc {
namespace native_dom {

class DocumentHostObject : public ElementHostObject {
  
  friend class JSIBinding;
  
public:
  DocumentHostObject(std::shared_ptr<Document> document) : ElementHostObject(document) {}

  void setProperty(Runtime& rt, const MSCString& name, const Value& value) override {
    if (name == "title") {
      return;
    }
    ElementHostObject::setProperty(rt, name, value);
  }

  Value getProperty(Runtime &runtime, const MSCString &name) override {
    if (name == "body") {
      auto bodyElement = GetInstance<msc::native_dom::Document>()->body();
      auto hostObject = bodyElement->GetJSWrapper();
      if (hostObject == nullptr) {
        auto bodyHostObject = std::make_shared<ElementHostObject>(bodyElement);
        hostObject = bodyHostObject.get();
        bodyHostObject->bindInstanceFunctions(runtime);
        auto jsObject = jsi::Object::createFromHostObject(runtime, bodyHostObject);
        bodyHostObject->SetJSObject(std::move(jsObject), &runtime);
      }
      return GET_JSOBJECT_FROM(bodyElement);
    }
    return ElementHostObject::getProperty(runtime, name);
  }

  void bindInstanceFunctions(jsi::Runtime& runtime) override {
    ElementHostObject::bindInstanceFunctions(runtime);
    registerFunction(runtime, Document::kCreateElement, &DocumentHostObject::CreateElement, 1);
  }
  
//  jsi::Value get(jsi::Runtime& rt, const jsi::PropNameID& name) override {
//    if (name.utf8(rt) == Document::kCreateElement) {
//      return jsi::Function::createFromHostFunction(
//                                                   rt,
//                                                   name,
//                                                   1,
//                                                   [this](jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count) -> jsi::Value {
//                                                     if (count != 1 || !args[0].isString()) {
//                                                       throw jsi::JSError(rt, "CreateElement requires a single string argument");
//                                                     }
//                                                     std::string tagName = args[0].asString(rt).utf8(rt);
//                                                     auto object = CreateElement(rt, tagName);
//                                                     return object;
////                                                     if (tagName == DOM_TAG_VIEW) {
////                                                       auto element = std::make_shared<ViewComponent>(instance_.get());
//////                                                       Object elementObj(rt);
////                                                       auto elementHostObject = std::make_shared<ViewComponentHostObject>(element);
////                                                       return jsi::Object::createFromHostObject(rt, elementHostObject);
//////                                                       elementObj.setProperty(rt, "__proto__", elementHostObject);
//////                                                       return elementObj;
////                                                     }
////                                                     auto element = instance_->CreateElement(tagName);
////                                                     auto elementHostObject = jsi::Object::createFromHostObject(rt, std::make_shared<ElementHostObject>(element));
////                                                     Object elementObj(rt);
////                                                     elementObj.setProperty(rt, "__proto__", elementHostObject);
////                                                     return jsi::Value::undefined();
//                                                   }
//                                                   );
//    }
//    return jsi::Value::undefined();
//  }
//
//  virtual void set(Runtime& rt, const PropNameID& name, const Value& value) override {
//    if (name.utf8(rt) == "title") {
//      return;
//    }
//    ElementHostObject::set(rt, name, value);
//  };
  
  std::vector<PropNameID> getPropertyNames(Runtime& runtime) override {
    auto props = ElementHostObject::getPropertyNames(runtime);
    props.push_back(PropNameID::forAscii(runtime, Document::kCreateElement.c_str()));
    return props;
  }
private:
    jsi::Value CreateElement(jsi::Runtime &runtime, const jsi::Value &, const jsi::Value *args,
                             size_t count) {
        traceBegin("js_create_element");
        if (count != 1 || !args[0].isString()) {
            auto result = jsi::Value::undefined();
            traceEnd("js_create_element");
            return result;
//      throw jsi::JSError(runtime, "CreateElement requires a single string argument");
        }


        std::string tagName = args[0].asString(runtime).utf8(runtime);

//    fprintf(stderr, "##### CreateElement %s\n", tagName.c_str());

        if (tagName == DOM_TAG_VIEW || tagName == "MSCView") {
            auto element = std::make_shared<ViewComponent>(GetInstance<Document>().get());
            auto hostObject = std::make_shared<ViewComponentHostObject>(element);
            hostObject->bindInstanceFunctions(runtime);
            auto jsObject = jsi::Object::createFromHostObject(runtime, hostObject);
//          //生成一个JS对象，让前端可以随便挂属性，如_vnode
//          Object wrapperObject(runtime);
//          wrapperObject.setProperty(runtime, "__proto__", jsObject);
//          element->SetJSObject(std::move(wrapperObject), &runtime);

            hostObject->SetJSObject(std::move(jsObject), &runtime);
            auto result = hostObject->GetJSObject();
            traceEnd("js_create_element");
            return result;

        }
        auto result = jsi::Value::undefined();
        traceEnd("js_create_element");
        return result;
    }
};


}  // namespace native_dom
}  // namespace msc

#endif

#endif /* DocumentHostObject_hpp */
