//
//  NativeDOMObjectHostObject.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/13.
//

#include "NativeDOMObjectHostObject.h"
#include "TraceUtils.h"

#if USE_JSI

namespace msc {
namespace native_dom {

#if USE_PER_CLASS_FUNCTION
std::unordered_map<Runtime *, std::shared_ptr<std::unordered_map<std::string, jsi::Function>>> NativeDOMObjectHostObject::registered_functions_;
#endif

Value NativeDOMObjectHostObject::getProperty(Runtime& runtime, const MSCString& propName) {
  auto iter = properties_.find(propName);
  if (iter != properties_.end()) {
    return Value(runtime, iter->second);
  }
  return Value::undefined();
};

void NativeDOMObjectHostObject::setProperty(jsi::Runtime& rt, const MSCString& propName, const Value& value) {
  auto iter = properties_.find(propName);
  if (iter != properties_.end()) {
    iter->second = Value(rt, value);
  }
  else {
    properties_.emplace(std::make_pair(propName, Value(rt, value)));
  }
};

Value NativeDOMObjectHostObject::get(jsi::Runtime &runtime, const jsi::PropNameID &propNameID) {
    traceBegin("js_get");
    auto propName = propNameID.utf8(runtime);

#if USE_PER_CLASS_FUNCTION
  auto iter = registered_functions_.find(&runtime);
  if (iter != registered_functions_.end()) {
    auto funcIter = iter->second->find(propName);
    if (funcIter != iter->second->end()) {
      return Value(runtime, funcIter->second);
    }
  }
#else
    auto iter1 = registered_instance_functions_.find(propName);
    if (iter1 != registered_instance_functions_.end()) {
        auto value = Value(runtime, iter1->second);
        traceEnd("js_get");
        return value;
    }
#endif

    auto value = getProperty(runtime, propName);
    traceEnd("js_get");
    return value;
}

void NativeDOMObjectHostObject::set(jsi::Runtime& rt, const PropNameID& name, const Value& value) {
  auto propName = name.utf8(rt);
  return setProperty(rt, propName, value);
}

//std::vector<PropNameID> NativeDOMObjectHostObject::getPropertyNames(Runtime& runtime) {
//  std::vector<PropNameID> result;
//  for (auto &kv : registered_instance_functions_) {
//    result.emplace_back(PropNameID::forAscii(runtime, kv.first));
//  }
//
//  auto propers = getPropertyMembersNames(runtime);
//  for (auto &prop : propers) {
//    result.emplace_back(std::move(prop));
//  }
//  return result;
//}

}  // namespace native_dom
}  // namespace msc

#endif
