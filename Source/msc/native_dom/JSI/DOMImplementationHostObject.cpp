//
//  DOMImplementationHostObject.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/13.
//

#include "DOMImplementationHostObject.h"

#include <chrono>

#ifdef __APPLE__
#include <os/log.h>
#endif

#if USE_JSI

namespace msc {
namespace native_dom {

  HOST_OBJECT_MEMBER_FUNCTIONS_IMP(DOMImplementationHostObject, FrameMarker) {
    auto action = args[0].asString(rt).utf8(rt);
    int pageId = args[1].asNumber();
    
    static std::chrono::system_clock::time_point begin;

#ifdef __APPLE__
    static os_log_t nativeLog = nullptr;
    if (nativeLog == nullptr) {
      nativeLog = os_log_create("com.sankuai.msc.native", "native1");
    }
#endif
    
    if (action == "begin") {
      begin = std::chrono::system_clock::now();
#ifdef __APPLE__
      os_log(nativeLog, "===== FrameMarker begin");
#endif
      GetInstance<DOMImplementation>()->FrameMarker(DOMImplementation::FrameMarkerType::Begin, pageId);
    } else if (action == "end") {
      auto end = std::chrono::system_clock::now();
      auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - begin);
#ifdef __APPLE__
      os_log(nativeLog, "===== FrameMarker end: %f\n", double(duration.count()));
#endif
      GetInstance<DOMImplementation>()->FrameMarker(DOMImplementation::FrameMarkerType::End, pageId);
    }
    
    return jsi::Value::undefined();
  }

}
}

#endif
