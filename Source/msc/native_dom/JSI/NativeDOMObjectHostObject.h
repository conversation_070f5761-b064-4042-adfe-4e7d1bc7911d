//
//  NativeDOMObjectHostObject.hpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/13.
//

#ifndef NativeDOMObjectHostObject_hpp
#define NativeDOMObjectHostObject_hpp

#include "../native_dom_define.h"

#if USE_JSI

#include <jsi/jsi.h>
#include <unordered_set>
#include <unordered_map>
#include <string>
#include <vector>
#include <functional>

#include "../../logger.h"
#include "../script_wrappable.h"

using namespace facebook::jsi;
using namespace facebook;

#define HOST_OBJECT_MEMBER_FUNCTIONS_DEF(FunctionName) jsi::Value FunctionName(jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count)
#define HOST_OBJECT_MEMBER_FUNCTIONS_IMP(HostObjectClass, FunctionName) jsi::Value HostObjectClass::FunctionName(jsi::Runtime& rt, const jsi::Value&, const jsi::Value* args, size_t count)

#define USE_PER_CLASS_FUNCTION 0

namespace msc {
namespace native_dom {


template <typename T>
using HostFunctionPointerType = Value (T::*)(Runtime& rt, const Value& thisVal, const Value* args, size_t count);

//template <typename T>
//class FunctionMetaData {
//public:
//  FunctionMetaData(HostFunctionPointerType<T> functionPointer, int argCount) : functionPointer_(functionPointer), argCount_(argCount) {}
//
//  HostFunctionPointerType<T> functionPointer() const { return functionPointer_; }
//  int argCount() const { return argCount_; }
//
//private:
//  HostFunctionPointerType<T> functionPointer_;
//  int argCount_;
//};

class NativeDOMObjectHostObject : public HostObject {
public:
  NativeDOMObjectHostObject(std::shared_ptr<ScriptWrappable> instance) : instance_(instance) {
    instance->SetJSWrapper(this);
  }
  virtual ~NativeDOMObjectHostObject() {
    instance_->SetJSWrapper(nullptr);
  };
  
  virtual void bindInstanceFunctions(jsi::Runtime& rt) {};
  
  Value get(jsi::Runtime& rt, const PropNameID& name) override;
  void set(jsi::Runtime& rt, const PropNameID& name, const Value& value) override;
//  std::vector<PropNameID> getPropertyNames(Runtime& runtime) override;
  
  void SetJSObject(Value &&object, Runtime *runtime) { m_jsObject = std::move(object); m_runtime = runtime; }
  Value GetJSObject() {
//    return m_jsObject.asObject(*m_runtime);
    return Value(*m_runtime, m_jsObject);
  };

protected:
  virtual Value getProperty(Runtime& runtime, const MSCString& name);
  virtual void setProperty(jsi::Runtime& rt, const MSCString& name, const Value& value);
//  virtual std::vector<PropNameID> getPropertyMembersNames(Runtime &runtime) { return std::vector<PropNameID>(); }
  
  template <typename T>
  //void registerFunction(jsi::Runtime &runtime, const std::string& funcName, FunctionMetaData<T> &&funcMetaData) {
  void registerFunction(jsi::Runtime &runtime, const std::string& funcName, HostFunctionPointerType<T> funcPtr, int argCount) {
//      auto funcPtr = funcMetaData.functionPointer();
//      auto argCount = funcMetaData.argCount();

#if USE_PER_CLASS_FUNCTION
    std::shared_ptr<std::unordered_map<std::string, jsi::Function>> funcMap = nullptr;
    auto iter = registered_functions_.find(&runtime);
    if (iter == registered_functions_.end()) {
      funcMap = std::make_shared<std::unordered_map<std::string, jsi::Function>>();
      registered_functions_.emplace(&runtime, funcMap);
    } else {
      funcMap = iter->second;
    }

      auto funcIter = funcMap->find(funcName);
      if (funcIter != funcMap->end()) {
        return;
      }
#endif

#if USE_PER_CLASS_FUNCTION
      auto jsFunc = jsi::Function::createFromHostFunction(runtime, PropNameID::forAscii(runtime, funcName), argCount, [funcPtr](jsi::Runtime& rt, const jsi::Value& val, const jsi::Value* args, size_t count) -> jsi::Value {
        auto object = val.getObject(rt);
          if (!object.template isHostObject(rt)) {
              return Value::undefined();
          }

          if (object.isFunction(rt)) {
              return Value::undefined();
          }

        std::shared_ptr<T> instance = std::static_pointer_cast<T>(object.getHostObject(rt));
        if (instance) {
          return ((instance.get())->*funcPtr)(rt, val, args, count);
        }
        return Value::undefined();
      });
#else
      auto jsFunc = jsi::Function::createFromHostFunction(runtime, PropNameID::forAscii(runtime, funcName), argCount, [this, funcPtr](jsi::Runtime& rt, const jsi::Value& val, const jsi::Value* args, size_t count) -> jsi::Value {
          return ((static_cast<T*>(this))->*funcPtr)(rt, val, args, count);
      });
#endif


#if USE_PER_CLASS_FUNCTION
//      registered_function_names_.emplace(funcName);
    funcMap->emplace(funcName, std::move(jsFunc));
#else
      registered_instance_functions_.emplace(funcName, std::move(jsFunc));
#endif
  }

  template <typename T>
  std::shared_ptr<T> GetInstance() {
    return std::static_pointer_cast<T>(instance_);
  }

private:
  std::shared_ptr<ScriptWrappable> instance_;

#if USE_PER_CLASS_FUNCTION
  static std::unordered_map<Runtime *, std::shared_ptr<std::unordered_map<std::string, jsi::Function>>> registered_functions_;
#else
  std::unordered_map<std::string, jsi::Function> registered_instance_functions_;
#endif

//  std::unordered_set<std::string> registered_function_names_;
  
  std::unordered_map<std::string, Value> properties_;
  
  Value m_jsObject;
  Runtime *m_runtime;
};


}  // namespace native_dom
}  // namespace msc

#endif // USE_JSI

#endif /* NativeDOMObjectHostObject_hpp */
