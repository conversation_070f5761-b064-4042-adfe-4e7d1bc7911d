//
//  JSIBinding.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/1/8.
//

#include "JSIBinding.h"

#include "../bridge/message_proxy.h"
#include "../bridge/document_registry.h"

//JSI binding
#include "DOMImplementationHostObject.h"

#if USE_JSI

namespace msc {
namespace native_dom {

//注：JSI的HostFunction不支持new XXX的方式：https://github.com/facebook/hermes/issues/524
void JSIBinding::setupJSContext(facebook::jsi::Runtime *runtime, std::shared_ptr<DocumentRegistry> documentRegistry) {
  Object globalNativeObject(*runtime);
  auto messageProxy = std::make_shared<MessageProxy>(documentRegistry);
  auto domImplObj = std::make_shared<DOMImplementation>(messageProxy);
  auto hostObject = std::make_shared<DOMImplementationHostObject>(domImplObj);
  hostObject->bindInstanceFunctions(*runtime);
  auto domImplHostObject = Object::createFromHostObject(*runtime, hostObject);
  globalNativeObject.setProperty(*runtime, "implementation", domImplHostObject);
  
  runtime->global().setProperty(*runtime, "native", std::move(globalNativeObject));
}

} // namespace native_dom
} // namespace msc

#endif
