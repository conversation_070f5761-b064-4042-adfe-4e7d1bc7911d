//
//  EventTargetHostObject.hpp
//  MSC
//
//  Created by ji<PERSON><PERSON> <PERSON>hang on 2025/2/13.
//

#ifndef EventTargetHostObject_hpp
#define EventTargetHostObject_hpp

#include "NativeDOMObjectHostObject.h"
#include "../event_target.h"

#if USE_JSI

namespace msc {
namespace native_dom {

class EventTargetHostObject : public NativeDOMObjectHostObject {
public:
  EventTargetHostObject(std::shared_ptr<EventTarget> instance) : NativeDOMObjectHostObject(instance) {}
  virtual ~EventTargetHostObject() = default;

};


class JSIEventListener : public EventListener {
public:
  explicit JSIEventListener(bool capture, Value &&listener, Runtime &runtime) : EventListener(capture), m_listener(std::move(listener)), m_runtime(runtime) {}
  virtual ~JSIEventListener() override = default;
  
  EventListenerType type() override { return EventListenerType::JSI_EVENT_LISTENER_TYPE; }

  bool IsSameObject(EventListener* listener) override
  {
      if (listener->type() == EventListenerType::JSI_EVENT_LISTENER_TYPE) {
          auto* jsiListener = static_cast<JSIEventListener*>(listener);
          return capture_ == jsiListener->capture_ && Value::strictEquals(m_runtime, m_listener, jsiListener->m_listener);
      }
      return false;
  }
private:
  Value m_listener;
  Runtime &m_runtime;
};



}  // namespace native_dom
}  // namespace msc

#endif

#endif /* EventTargetHostObject_hpp */
