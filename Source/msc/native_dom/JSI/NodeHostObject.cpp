//
//  NodeHostObject.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/2/13.
//

#include "NodeHostObject.h"
#include "../element.h"
#include "TraceUtils.h"

#if USE_JSI

namespace msc {
namespace native_dom {

void NodeHostObject::bindInstanceFunctions(jsi::Runtime& runtime) {
  EventTargetHostObject::bindInstanceFunctions(runtime);
  registerFunction(runtime, Node::kAppendChild, &NodeHostObject::AppendChild, 1);
  registerFunction(runtime, Node::kInsertBefore, &NodeHostObject::InsertBefore, 2);
  registerFunction(runtime, Node::kRemove, &NodeHostObject::remove, 1);
  registerFunction(runtime, Node::kRemoveChild, &NodeHostObject::RemoveChild, 1);
  registerFunction(runtime, Node::kAddEventListener, &NodeHostObject::AddEventListener, 2);
  registerFunction(runtime, Node::kRemoveEventListener, &NodeHostObject::RemoveEventListener, 2);
};

//std::vector<PropNameID> NodeHostObject::getPropertyMembersNames(Runtime &runtime) {
//  auto props = EventTargetHostObject::getPropertyMembersNames(runtime);
//  props.push_back(PropNameID::forAscii(runtime, "firstChild"));
//  props.push_back(PropNameID::forAscii(runtime, "lastChild"));
//  props.push_back(PropNameID::forAscii(runtime, "previousSibling"));
//  props.push_back(PropNameID::forAscii(runtime, "nextSibling"));
//  props.push_back(PropNameID::forAscii(runtime, "parentNode"));
//  return props;
//}

#define GET_HOST_OBJECT(obj) obj.asObject(rt).getHostObject<NodeHostObject>(rt)

HOST_OBJECT_MEMBER_FUNCTIONS_IMP(NodeHostObject, AppendChild) {
    traceBegin("js_appendChild");
    if (count != 1 || !args[0].isObject()) {
        Value result = jsi::Value::undefined();
        traceEnd("js_appendChild");
        return result;
//    throw jsi::JSError(rt, "AppendChild requires a single Node object argument");
    }
    auto child = GET_HOST_OBJECT(args[0]);
    if (child) {
        GetInstance<Node>()->AppendChild(child->GetInstance<Node>().get());
        Value value = Value(rt, args[0]);
        traceEnd("js_appendChild");
        return value;
    }
    Value result = jsi::Value::undefined();
    traceEnd("js_appendChild");
    return result;
}

HOST_OBJECT_MEMBER_FUNCTIONS_IMP(NodeHostObject, InsertBefore) {
  if (count <= 1 || !args[0].isObject()) {
    return jsi::Value::undefined();
//    throw jsi::JSError(rt, "InsertBefore requires 2 argument");
  }
  auto child = GET_HOST_OBJECT(args[0]);
  auto anchor = GET_HOST_OBJECT(args[1]);
  if (child && anchor) {
    GetInstance<Node>()->InsertBefore(child->GetInstance<Node>().get(), anchor->GetInstance<Node>().get());
  } else if (anchor == nullptr) {
    
  }
  return jsi::Value::undefined();
}

HOST_OBJECT_MEMBER_FUNCTIONS_IMP(NodeHostObject, remove) {
  GetInstance<Node>()->Remove();
  return jsi::Value::undefined();
}

HOST_OBJECT_MEMBER_FUNCTIONS_IMP(NodeHostObject, RemoveChild) {
  if (count < 1 || !args[0].isObject()) {
    return jsi::Value::undefined();
//    throw jsi::JSError(rt, "RemoveChild requires 1 argument");
  }
  auto child = GET_HOST_OBJECT(args[0]);
  GetInstance<Node>()->RemoveChild(child->GetInstance<Node>().get());
  
  return args[0].asObject(rt);
}

HOST_OBJECT_MEMBER_FUNCTIONS_IMP(NodeHostObject, AddEventListener) {
  if (count <= 1) {
    return jsi::Value::undefined();
//    throw jsi::JSError(rt, "AddEventListener requires at least 2 argument");
  }
  auto eventName = args[0].asString(rt).utf8(rt);
  auto listener = args[1].asObject(rt);
  bool capture = false;
  if (count > 2 && args[2].isBool()) {
    capture = args[2].getBool();
  }
  
  GetInstance<Element>()->AddEventListener(eventName, std::make_unique<JSIEventListener>(capture, std::move(listener), rt));
  
  return nullptr;
}

HOST_OBJECT_MEMBER_FUNCTIONS_IMP(NodeHostObject, RemoveEventListener) {
  if (count <= 1) {
    return jsi::Value::undefined();
//    throw jsi::JSError(rt, "AddEventListener requires at least 2 argument");
  }
  auto eventName = args[0].asString(rt).utf8(rt);
  auto listenerObj = args[1].asObject(rt);
  bool capture = false;
  if (count > 2) {
    capture = args[2].getBool();
  }
  auto listener = std::make_unique<JSIEventListener>(capture, std::move(listenerObj), rt);
  GetInstance<Element>()->RemoveEventListener(eventName, listener.get());
  
  return nullptr;
}

Value NodeHostObject::getProperty(Runtime& runtime, const MSCString& propName) {
  if (propName == "nodeName") {
    return facebook::jsi::String::createFromUtf8(runtime, GetInstance<Node>()->nodeName());
  }
  else if (propName == "firstChild") {
    auto firstChild = GetInstance<Node>()->firstChild();
    if (firstChild) {
      return (GET_JSOBJECT_FROM(StaticCastToElement(firstChild)));
    } else {
      return jsi::Value::null();
    }
  }
  else if (propName == "lastChild") {
    auto lastChild = GetInstance<Node>()->lastChild();
    if (lastChild) {
#if USE_JSI
      return (GET_JSOBJECT_FROM(StaticCastToElement(lastChild)));
#endif
    } else {
      return jsi::Value::null();
    }
  }
  else if (propName == "previousSibling") {
    auto previousSibling = GetInstance<Node>()->previousSibling();
    if (previousSibling) {
#if USE_JSI
      return GET_JSOBJECT_FROM(StaticCastToElement(previousSibling));
#endif
    } else {
      return jsi::Value::null();
    }
  }
  else if (propName == "nextSibling") {
    auto nextSibling = GetInstance<Node>()->nextSibling();
    if (nextSibling) {
#if USE_JSI
      return GET_JSOBJECT_FROM(StaticCastToElement(nextSibling));
#endif
    } else {
      return jsi::Value::null();
    }
  }
  else if (propName == "parentNode") {
    auto parentNode = GetInstance<Node>()->parentNode();
    if (parentNode) {
#if USE_JSI
      return GET_JSOBJECT_FROM(StaticCastToElement(parentNode));
#endif
    } else {
      return jsi::Value::null();
    }
  }
  else if (propName == "nodeType") {
    auto nodeType = GetInstance<Node>()->nodeType();
    if (nodeType > 0) {
      return jsi::Value(nodeType);
    } else {
      return jsi::Value::null();
    }
  }
  return EventTargetHostObject::getProperty(runtime, propName);
}


} // namespace native_dom
} // namespace msc

#endif
