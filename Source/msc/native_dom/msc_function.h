////
////  msc_function.h
////  MSC
////
////  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
////
//
//#ifndef MSCFunction_h
//#define MSCFunction_h
//
//#include <functional>
//
//namespace msc {
//namespace native_dom {
//
//template <class T>
//class MSCFunction {};
//
//template <typename R, typename... Args>
//class MSCFunction<R(Args...)> {
//  using Func = std::function<R(Args...)>;
//public:
//  MSCFunction(Func func) : _func(func) {}
//  MSCFunction(Func &&func) : _func(std::move(func)) {}
//  
//  R operator()(Args... args) {
//    return _func(args...);
//  }
//  
//  R call(Args... args) {
//    return _func(args...);
//  }
//  
//private:
//  Func _func;
//};
//
//}  // namespace native_dom
//}  // namespace msc
//
//#endif /* MSCFunction_h */
