//
//  JSIAttr.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/1/13.
//

#ifndef JSIAttr_h
#define JSIAttr_h

#include "dom_attr.h"
#include "MSCString"
#include "element.h"

namespace msc {
namespace native_dom {

class JSIAttr : public DOMAttr {
public:
  JSIAttr(std::weak_ptr<Element> element,
          const MSCString &name);
  
  JSIAttr(const MSCString& name,
          const MSCString& standAloneValue);
  
  ~JSIAttr() override;
  
  bool IsDetached() const override { return !ownElement(); }
  
  void SetJSObject(std::unique_ptr<jsi::Object> &&object);
  jsi::Object* GetJSObject(jsi::Runtime &runtime);
  
  JSIElement* ownElement() const { return m_element.get(); }
  
  const MSCString& name() const { return m_name; }
  MSCString value() const;
  
  void attachToElement(std::weak_ptr<Element> element);
  void detachFromElementWithValue(const MSCString& value);
  
private:
  std::unique_ptr<jsi::Object> m_object;
  std::weak_ptr<Element> m_element;
  MSCString m_name;
  MSCString m_standaloneValue;
};

}  // namespace native_dom
}  // namespace msc

#endif /* JSIAttr_h */
