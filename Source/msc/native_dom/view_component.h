//
//  view_component.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/15.
//

#ifndef ViewComponent_h
#define ViewComponent_h

#include "element.h"

namespace msc {
namespace native_dom {

class ViewComponent : public Element {
public:
  ViewComponent(ContainerNode *root, Tag tag = Tag::MSC_VIEW) : Element(tag, root) {};
  virtual ~ViewComponent() override {};
  
  bool IsContainerElement() const override {
    return true;
  }
  
//  void HandleLocalEvent(Event *event) override {};
};

}  // namespace native_dom
}  // namespace msc
#endif /* ViewComponent_h */
