//
//  character_data.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef CHARACTER_DATA_H
#define CHARACTER_DATA_H

#include "node.h"
#include "msc_string.h"

namespace msc {
namespace native_dom {

class CharacterData : public Node {
public:
    void SetTextContent(const MSCString& content) override { text_content_ = content; }
    const MSCString& TextContent() const override { return text_content_; }

private:
    MSCString text_content_;
};

}  // namespace native_dom
}  // namespace msc

#endif /* CharacterData_h */
