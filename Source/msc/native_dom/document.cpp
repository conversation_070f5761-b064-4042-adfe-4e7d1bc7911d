//
//  document.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/1/13.
//

#include "document.h"
#include "bridge/message_proxy.h"
#include "view_component.h"
#include "scroll_view_component.h"
#include "swiper_component.h"
#include "text_component.h"
#include "image_component.h"
#include "text_node.h"
#include "event_util.h"
#include "event.h"
#include "event_context.h"

//#if defined(ANDROID)
//#include "v8/wrapper_type_info.h"
//#endif

namespace msc {
namespace native_dom {

using namespace std::string_literals;
const std::string Document::kCreateElement = "createElement"s;
const std::string Document::kCreateTextNode = "createTextNode"s;
const std::string Document::kCreateVNode = "createVNode"s;
const std::string Document::kCreateKeyframesAnimationEnhanced =
    "createKeyFramesAnimationEnhanced"s;
const std::string Document::kClearKeyframesAnimationEnhanced =
    "clearKeyFramesAnimationEnhanced"s;
const std::string Document::kQueryEnhanced = "queryEnhanced"s;
const std::string Document::kFrameMarker = "frameMarker"s;
const std::string Document::kFrameMarkerTypeBegin = "begin"s;
const std::string Document::kFrameMarkerTypeEnd = "end"s;
const std::string Document::kFrameMarkerTypePreEnd = "preEnd"s;

void dispatchEventForBubblesEvent(Element* element, std::shared_ptr<Event> e)
{
    auto eventPath = element->generateEventPath();

    // capture
    bool continueDispatch = true;
    e->SetEventPhase(Event::EventPhase::CAPTURING_PHASE);
    e->SetStage(Event::EventStage::CAPTURING);
    for (auto it = eventPath.rbegin(); it != eventPath.rend(); ++it) {
        if (*it == element) {
            e->SetEventPhase(Event::EventPhase::AT_TARGET);
        }
        e->SetCurrentTarget(*it);
        static_cast<Element*>(*it)->HandleLocalEvent(e);
        e->SetEventPhase(Event::EventPhase::CAPTURING_PHASE);

        if (e->propagationStopped()) {
            continueDispatch = false;
            break;
        }
    }

    if (continueDispatch) {
        // bubble
        e->SetEventPhase(Event::EventPhase::BUBBLING_PHASE);
        e->SetStage(Event::EventStage::BUBBLING);
        for (auto it = eventPath.begin(); it != eventPath.end(); ++it) {
            if (*it == element) {
                e->SetEventPhase(Event::EventPhase::AT_TARGET);
            }
            e->SetCurrentTarget(*it);
            static_cast<Element*>(*it)->HandleLocalEvent(e);
            if (e->propagationStopped()) {
              break;
            }
            e->SetEventPhase(Event::EventPhase::BUBBLING_PHASE);
        }
    }
}

//void prepareEvent(const MSCString& key,
//                  std::unordered_map<std::string, std::string>& attrValue,
//                  EventTarget* target) {
//    bool need = target->eventListened(key);
//    if (need)
//        attrValue.insert(std::make_pair(key, key));
//}

Document::Document(ContainerNode* root, bool IsScrollable, bool standardRoot,
                   bool FrameMarker, int bridgeChannelId,
                   std::shared_ptr<MessageProxy> message_proxy)
    : Element(Tag::DOCUMENT, root),
//      head_element_(nullptr),
      body_element_(nullptr),
      //    m_engine(nullptr),
      //    m_contextId(0),
      is_scrollable_(IsScrollable),
      bridge_channel_id_(bridgeChannelId),
      root_created_(false),
      standard_root_(standardRoot),
      frame_marker_(FrameMarker),
      is_closing_(false),
      life_thread_(std::this_thread::get_id()),
      message_proxy_(message_proxy),
      has_pending_messages_(false) {
  SetRootNode(this);
  SetConnected(true);
//    SetEatTextNodeFeature(!CRConfig::enableGinTextNesting());
  
//    CKRefPtr<NativeDOMBridge> bridge = dom_binding::BridgeProxy::nativeDOMBridge(bridgeChannelId);
//    if (bridge && standard_root_) {
//      bridge->onDocumentCreate(this);
//    }

  scope_.reset(new TreeScope());
  body_element_ = std::make_shared<BodyElement>(this);
  message_proxy->SendCreateViewMessage(bridgeChannelId, *body_element_);
  body_element_->SetMounted(true);
        
  MSC_RENDERER_LOG_DEBUG("constructor of Document: %p", this);
}

void Document::Close() {
  MSC_RENDERER_LOG_ERROR("Document::Close, pageId: %d", this->bridgeChannelId());
  is_closing_ = true;
  body_element_->DestroySubtree();
  body_element_ = nullptr;
  
#if DEBUG
  //For Debug only
  this->ForceGCDebug();
#endif

  this->ClearJSWrapper();
}

Element* Document::GetElementByNodeId(const int& nodeId) {
  if (nodeId == body_element_->GetNodeId()) {
    return body_element_.get();
  }
  auto it = nodes_.find(nodeId);
  if (it != nodes_.end())
    return it->second;
  return nullptr;
}

void Document::removeNode(Element *node) {
    nodes_.erase(node->GetNodeId());
    if (scope_) {
        scope_->Remove(node->idAttribute(), node);
    }
  
  //页面已关闭，且所有结点都已销毁，则触发Document & Body的销毁流程
  if (is_closing_ && nodes_.size() == 0) {
    this->DestroyJSWrapper();
  }
}

msc::NativeDOMMetrics &Document::GetMetrics() {
  return message_proxy_->GetDocumentRegistry()->GetMetrics();
}

void Document::PrepareEvents(std::unordered_map<std::string, std::string> &attrValue) {
//    if (EventTargetDataMap* data = getEventTargetData()) {
//        prepareEvent("scroll", attrValue, this);
//        prepareEvent("scrolltoupper", attrValue, this);
//        prepareEvent("scrolltolower", attrValue, this);
//    }
}

bool Document::NeedPrepareEvents() {
    return true;
}

void Document::OnListenerFromZeroToOne(const MSCString& key)
{
//    if (key == "scroll" || key == "scrolltoupper" || key == "scrolltolower")
//        onAddEvent(key);
}

void Document::OnListenerFromOneToZero(const MSCString& key)
{
//    if (key == "scroll" || key == "scrolltoupper" || key == "scrolltolower")
//        onRemoveEvent(key);
}

void Document::dispatchEvent(int elemId,
                             const std::shared_ptr<msc::native_dom::Event>& event,
                             bool isCustomEvent)
{
    MSC_RENDERER_LOG_DEBUG("Document::dispatchEvent elemId: %d, eventName: %s", elemId, event->typeName().c_str());
    Element* element = GetElementByNodeId(elemId);
    if (element && element->nodeTag() == Tag::TEXT_NODE)
        element = static_cast<Element*>(element->parentNode());
    if (element) {
        dispatchEvent(element, event, isCustomEvent);
    } else {
//        TagLogI(kTag, "%s element is nullptr:%s", __FUNCTION__, elemId.c_str());
    }
}

void Document::dispatchEvent(Element* element,
                             const std::shared_ptr<msc::native_dom::Event>& event,
                             bool isCustomEvent) {
    if (!element) {
        return;
    }
  
    if (is_closing_) {
      return;
    }

//    void *context = nullptr;
//    EventContext eventContext;
//    eventContext.SetJSContext(context);
//    Event event(eventContext);

    event->SetTarget(element);

    if (EventUtil::needBubbles(event->typeName())) {
        event->SetBubbles(true);
    }

    if (event->bubbles()) {
        dispatchEventForBubblesEvent(element, event);
    } else {
        event->SetEventPhase(Event::EventPhase::AT_TARGET);
        event->SetCurrentTarget(element);
        event->SetStage(Event::EventStage::CAPTURING);
        element->HandleLocalEvent(event);
        event->SetStage(Event::EventStage::BUBBLING);
        element->HandleLocalEvent(event);
    }
    dispatchEventPostProcess(element, event.get());

    event->ClearJSWrapper();
}

void Document::dispatchEventPostProcess(Element* target, Event* e)
{
    e->ResetStopPropagation();
    e->SetEventPhase(Event::EventPhase::NONE);
    e->SetCurrentTarget(nullptr);
    bool isClick = e->typeName() == "tap" || e->typeName() == "click";
    if (isClick) {
        target->HandleClicked(e);
    }

    if (!e->defaultHandled()) {
        // Non-bubbling events call only one default event handler, the one for the
        // target.
        target->defaultEventHandler(e);
        // For bubbling events, call default event handlers on the same targets in
        // the same order as the bubbling phase.
        if (!e->defaultHandled() && e->bubbles()) {
            auto eventPath = target->generateEventPath();
            size_t size = eventPath.size();
            for (size_t i = 1; i < size; ++i) {
                static_cast<Element*>(eventPath[i])->defaultEventHandler(e);
                if (e->defaultHandled())
                    break;
            }
        }
    }
}

void Document::ExecuteCallback(const JSCallbackInfo& callback_info, JSCallable::Setter setter) {
  Element* element = callback_info.is_bound_to_node_ ? GetElementByNodeId(callback_info.node_id_) : this;
  if (!element) {
    MSC_RENDERER_LOG_ERROR("%s: no element, node_id:%d", __PRETTY_FUNCTION__, callback_info.node_id_);
    return;
  }
  auto js_callback = element->GetProtectedJSCallback(callback_info.callback_index_);
  if (!js_callback) {
    MSC_RENDERER_LOG_DEBUG("%s: no callback, node_id:%d, callback_id:%d", __PRETTY_FUNCTION__, callback_info.node_id_,
                           callback_info.callback_index_);
    return;
  }
  if (js_callback->callable_) {
    if (setter) {
      setter(js_callback->callable_.get(), element);
    }
    js_callback->callable_->call();
  }
  if (!callback_info.is_once_) {
    element->ProtectJSCallback(std::move(js_callback), callback_info.callback_index_);
  }
}

void Document::FireEvent(const int &elemId,
                         const std::shared_ptr<msc::native_dom::Event>& event,
                         void *domChanges,
                         bool isCustomEvent) {
  dispatchEvent(elemId, event, isCustomEvent);
}

void Document::onDocumentCreate(void* context) {

}

void Document::onDocumentConstruct() {

}

Element* Document::CreateElement(const StringView& local_name) {

    Element* element = nullptr;
    if (local_name == DOM_TAG_VIEW) {
        element = new ViewComponent(this);
    } else if (local_name == DOM_TAG_SCROLL_VIEW) {
        element = new ScrollViewComponent(this);
    } else if (local_name == DOM_TAG_SWIPER) {
        element = new SwiperComponent(this);
    } else if (local_name == DOM_TAG_SWIPER_ITEM) {
        element = new SwiperItemComponent(this);
    } else if (local_name == DOM_TAG_TEXT_COMPONENT) {
        element = new TextComponent(this);
    } else if (local_name == DOM_TAG_VIRTUAL_TEXT_COMPONENT) {
        element = new VirtualTextComponent(this);
    } else if (local_name == DOM_TAG_IMAGE) {
        element = new ImageComponent(this);
    } else if (local_name == "MSCLazyLoadScrollView") {
        element = new ScrollViewComponent(this);
    } else if (local_name == "MSCLazyLoadScrollViewItem") {
        element = new ViewComponent(this);
    }

    if (!element) {
        MSC_RENDERER_LOG_DEBUG("Failed to CreateElement: %s", local_name.data());
        element = new ViewComponent(this);
    } else {
        MSC_RENDERER_LOG_DEBUG("Document.CreateElement: %s, %d", local_name.data(), element->GetNodeId());
    }
    return element;
}

TextNode *Document::CreateTextNode(const MSCString& data) {
    auto text_node = new TextNode(data, this);

//    auto message_proxy = this->GetMessageProxy();
//    auto page_id = this->bridgeChannelId();
//    message_proxy->SendCreateTextNodeMessage(page_id, *text_node);

    return text_node;
}

void Document::QueryEnhanced(blink::mt::QueryEnhancedParams&& params,
                             JSCallbackInfo callback_info) {
  if (is_closing_) {
    return;
  }

  message_proxy_->SendQueryEnhancedMessage(bridge_channel_id_,
                                           std::move(params), callback_info);
}

void Document::FrameMarker(FrameMarkerType type) {
#if 0
    static std::chrono::high_resolution_clock::time_point begin_time;
#endif

    if (type == FrameMarkerType::Begin) {
//        MSC_RENDERER_LOG_DEBUG("[native_dom] DOMImplementation::FrameMarker begin, pageId: %d", bridge_channel_id_);
#if 0
        begin_time = std::chrono::high_resolution_clock::now();
#endif
    } else if (type == FrameMarkerType::End) {
        MSC_RENDERER_LOG_DEBUG("[native_dom] DOMImplementation::FrameMarker end, pageId: %d", bridge_channel_id_);
        if (has_pending_messages_) {
            auto messageProxy = this->GetMessageProxy();
            messageProxy->SendBDCMessage(bridge_channel_id_, blink::mt::LayoutReason::BatchDidComplete);
            has_pending_messages_ = false;

#if 0
            auto end_time =  std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - begin_time);
            static std::chrono::milliseconds s_duration(0);
            s_duration += duration;
            MSC_RENDERER_LOG_INFO("DOMImplementationWrapperV8::FrameMarker duration: %lld", s_duration.count());
#endif
        }
    } else if (type == FrameMarkerType::PreEnd) {
        MSC_RENDERER_LOG_DEBUG("[native_dom] DOMImplementation::FrameMarker preEnd, pageId: %d", bridge_channel_id_);
        auto messageProxy = this->GetMessageProxy();
        messageProxy->SendBDCMessage(bridge_channel_id_, blink::mt::LayoutReason::PreBatchDidComplete);
    }
}

void Document::SendCreateViewMessage(JSIBasicElement &element) {
    if (is_closing_) {
      return;
    }
    message_proxy_->SendCreateViewMessage(bridge_channel_id_, element);
    has_pending_messages_ = true;
}

void Document::SendCreateTextNodeMessage(const TextNode &text_node) {
    if (is_closing_) {
      return;
    }
    message_proxy_->SendCreateTextNodeMessage(bridge_channel_id_, text_node);
    has_pending_messages_ = true;
}

void Document::SendUpdateViewMessage(Element &element, const MSCString &attribute_name, const blink::mt::PropValue &attribute_value) {
    if (is_closing_) {
      return;
    }
    message_proxy_->SendUpdateViewMessage(bridge_channel_id_, element, attribute_name, attribute_value);
    has_pending_messages_ = true;
}

void Document::SendRemoveViewMessage(int viewId) {
    if (is_closing_) {
      return;
    }
    message_proxy_->SendRemoveViewMessage(bridge_channel_id_, viewId);
    has_pending_messages_ = true;
}

void Document::SendAppendChildMessage(int parentId, const std::shared_ptr<const std::vector<int>>& child_tags) {
    if (is_closing_) {
      return;
    }
    message_proxy_->SendAppendChildMessage(bridge_channel_id_, parentId, child_tags);
    has_pending_messages_ = true;
}

void Document::SendManageChildrenMessage(int parentId,  const std::shared_ptr<const blink::mt::MTDocument::ChildrenChanges>& children_changes) {
    if (is_closing_) {
      return;
    }
    message_proxy_->SendManageChildrenMessage(bridge_channel_id_, parentId, children_changes);
    has_pending_messages_ = true;
}

std::shared_ptr<IntersectionObserver> Document::CreateIntersectionObserver(
    blink::mt::CreateIntersectionObserverParams&& params,
    JSCallbackInfo callback_info) {
  if (is_closing_) {
    return nullptr;
  }

  auto observer = std::make_shared<IntersectionObserver>(
      message_proxy_, bridge_channel_id_, params);

  params.intersection_observer_id_ = observer->GetId();
  message_proxy_->SendCreateIntersectionObserverMessage(
      bridge_channel_id_, std::move(params), callback_info);
  return observer;
}

void Document::IntersectionObserverObserve(int id, int target_id) {
  if (is_closing_) {
    return;
  }
  message_proxy_->SendIntersectionObserverObserveMessage(bridge_channel_id_, id,
                                                         target_id);
}

void Document::CreateKeyframesAnimationEnhanced(
    const std::shared_ptr<blink::mt::AnimationProperties>& animation_pros,
    JSCallbackInfo callback_info) {
  message_proxy_->SendCreateKeyframesAnimationEnhancedMessage(
      bridge_channel_id_, animation_pros, callback_info);
}

void Document::ClearKeyframesAnimationEnhanced(
    const std::shared_ptr<blink::mt::ClearAnimationProperties>& option,
    JSCallbackInfo callback_info) {
  message_proxy_->SendClearKeyframesAnimationEnhancedMessage(
      bridge_channel_id_, option, callback_info);
}

}  // namespace native_dom
}  // namespace msc
