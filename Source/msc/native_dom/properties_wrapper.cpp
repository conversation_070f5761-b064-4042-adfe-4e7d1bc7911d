//
//  PropertiesWrapper.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/1/17.
//

#include "properties_wrapper.h"

namespace msc {
namespace native_dom {

bool PropertiesWrapper::SetAttribute(const MSCString &key, const MSCString &value) {
  EnsureAttribute();
  auto iter = m_attributes->find(key);
  if (iter == m_attributes->end()) {
    m_attributes->insert(std::make_pair(key, value));
    return true;
  } else {
    if (iter->second != value) {
      iter->second = value;
      return true;
    }
    return false;
  }
}

bool PropertiesWrapper::SetAttribute(const MSCString& key, const blink::mt::PropValue &value) {
  if (value.isA<blink::mt::PropValueType::String>()) {
    return this->SetAttribute(key, value.stringValue());
  } else if (props_builder_.getProps()->getProp(key) != value) {
    props_builder_.setProp(key, value);
    return true;
  }
  return false;
}

bool PropertiesWrapper::GetAttribute(const MSCString &key, blink::mt::PropValue &value) {
  if (m_attributes) {
      auto it = m_attributes->find(key);
      if (it != m_attributes->end()) {
          value = it->second;
          return true;
      }
  }
  
  value = props_builder_.getProps()->getProp(key);
  return !value.isNull();
}

bool PropertiesWrapper::hasAttribute(const MSCString& key) {
  return (m_attributes && m_attributes->find(key) != m_attributes->end()) || !props_builder_.getProps()->getProp(key).isNull();
}

bool PropertiesWrapper::RemoveAttribute(const MSCString &key) {
  if (m_attributes && m_attributes->find(key) != m_attributes->end()) {
    m_attributes->erase(key);
    return true;
  } else if (!props_builder_.getProps()->getProp(key).isNull()) {
    props_builder_.removeProp(key);
    return true;
  }
  return false;
}

void PropertiesWrapper::EnsureAttribute() {
  if (!m_attributes) {
    m_attributes = new std::unordered_map<MSCString, MSCString>();
  }
}

void PropertiesWrapper::populate(blink::mt::PropsBuilder &propsBuilder) {
  if (m_attributes) {
    for (auto pair : *m_attributes) {
      propsBuilder.setProp(pair.first, pair.second);
    }
  }
  props_builder_.getProps()->forEach([&propsBuilder](const std::string &key, const blink::mt::PropValue &value) {
    propsBuilder.setProp(key, value);
  });
}

}  // namespace native_dom
}  // namespace msc


