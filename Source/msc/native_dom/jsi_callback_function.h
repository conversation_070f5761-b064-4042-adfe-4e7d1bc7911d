//
//  JSICallbackFunction.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON><PERSON> on 2025/1/13.
//

#ifndef JSICallbackFunction_h
#define JSICallbackFunction_h

#include <msc-jsi/jsi.h>

namespace msc {
namespace native_dom {

class JSICallbackFunction : public CallbackFunction {
public:
    explicit JSICallbackFunction(std::unique_ptr<jsi::Function> callback);

    JSICallbackFunction(JSICallbackFunction& right)
        : m_callback(right.m_callback.Release())
    {
    }

    ~JSICallbackFunction() override;

    virtual bool IsSameObject(JSICallbackFunction*);

    jsi::Function* callback()
    {
        return m_callback.Get();
    }

private:
    std::unique_ptr<<jsi::Function> m_callback;
};


inline jsi::Function* GetCallbackPtr(std::unique_ptr<CallbackFunction>& callback)
{
    return callback->GetCallbackPtr<JSICallbackFunction, jsi::Function>();
}

}  // namespace native_dom
}  // namespace msc
#endif /* JSICallbackFunction_h */
