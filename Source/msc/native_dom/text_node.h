//
//  text_node.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/15.
//

#ifndef TextNode_h
#define TextNode_h

#include "character_data.h"
#include "msc_string.h"
#include "event_target.h"
#include "bridge/message_proxy.h"
#include "element.h"
#include "document.h"

namespace msc {
namespace native_dom {

class TextNode : public Element {
public:
    TextNode(const MSCString &text, ContainerNode *root, Tag tag = Tag::TEXT_NODE) : Element(tag, root) {
        SetTextContent(text);
    };
  
    void SetTextContent(const MSCString &text) override {
      CharacterData::SetTextContent(text);
      if (mounted()) {
        this->documentElement()->SendUpdateViewMessage(*this, "text", text);
      }
    }
};

}  // namespace native_dom
}  // namespace msc

#endif /* TextNode_h */
