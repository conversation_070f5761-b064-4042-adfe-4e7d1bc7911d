//
//  body_element.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/2/17.
//

#ifndef BODY_ELEMENT_H
#define BODY_ELEMENT_H

#include "element.h"
//#include "element_type.h"

namespace msc {
namespace native_dom {

class BodyElement : public Element {
public:
  BodyElement(ContainerNode *root) : Element(Tag::MSC_VIEW, root) {
      SetNodeId(1);
      SetConnected(true);
  };
  
  virtual ~BodyElement() override {};
  
  void OnCreateBody() {};
};
}
}

#endif /* BODY_ELEMENT_H */
