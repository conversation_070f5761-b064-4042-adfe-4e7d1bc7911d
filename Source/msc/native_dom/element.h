//
//  element.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/10.
//

#ifndef ELEMENT_H
#define ELEMENT_H

#include "container_node.h"
#include "css_style_declaration.h"
#include "callback_function.h"
#include "native_dom_callback.h"
#include "element_rare_data.h"
#include "properties_wrapper.h"
#include "dom_token_list.h"
#include "../../Public/task_runner.h"
#include "props.h"

namespace msc {
namespace native_dom {


class BasicElement : public ContainerNode {
public:
  BasicElement(Tag type, ContainerNode *root): ContainerNode(type, root) {};
  ~BasicElement() override {};
  
  virtual int bridgeChannelId() const = 0;
  void getScrollOffset(std::unique_ptr<CallbackFunction> callback);
  void getBoundingClientRect(std::unique_ptr<CallbackFunction> callback);
//    void querySelectorEnhanced(CKSharedValue params,
//                               std::unique_ptr<CallbackFunction> callback);
  virtual void AddJSCallback(const NativeDOMCallback& callback,
                             std::unique_ptr<CallbackFunction> jsFunc);
//    void addQuerySelectorExJSCallback(const NativeDOMQuerySelectorExCallback& callback,
//                                      std::unique_ptr<CallbackFunction> jsFunc);

  DOMCallbackMap* onceCallbacks() const { return rareData() ? ((ElementRareData*)rareData())->onceCallbacks() : nullptr; }
//    void scrollTo(CKSharedValue args, std::unique_ptr<CallbackFunction> callback);
  
protected:
    const ElementRareData* rareData() const { return m_elementRareData.get(); }
    virtual ElementRareData& EnsureRareData()
    {
        if (!m_elementRareData)
            m_elementRareData.reset(new ElementRareData);
        return *m_elementRareData.get();
    }

    DOMCallbackMap* repeatCallbacks() const { return rareData() ? ((ElementRareData*)rareData())->repeatCallbacks() : nullptr; }
    DOMCallbackMap& EnsureRepeatCallbacks() { return ((ElementRareData&)EnsureRareData()).EnsureRepeatCallbacks(); }
    DOMCallbackMap& EnsureOnceCallbacks() { return ((ElementRareData&)EnsureRareData()).EnsureOnceCallbacks(); }

    std::unique_ptr<ElementRareData> m_elementRareData;
};

class JSIBasicElement : public BasicElement {
public:
 JSIBasicElement(Tag type, ContainerNode* root);
 ~JSIBasicElement() override {};

  // Override EventTargetDataDelegate
    void OnListenerFromZeroToOne(const MSCString&) override;
    void OnListenerFromOneToZero(const MSCString&) override;

    // Override EventTarget
    EventTargetDataMap* GetEventTargetData() override;
    void EnsureEventTargetData() override;

    // Override CKNativeContainerNode
    void OnEvent(DOMEventType e,
                 ContainerNode* child,
                 ContainerNode* anchor,
                 bool child_has_parent,
                 int original_index) override;

    void SetConnected(bool connected);

    virtual void SetInlineStyle(const MSCString& inlineStyle);
    virtual MSCString GetTotalInlineStyle();

    const MSCString& idAttribute() const { return rareData() ? rareData()->idAttribute() : g_EmptyString; }
    MSCString classAttribute() const { return m_class_list->ToString(); }

    const MSCString& tagNameAttribute() const { return m_tagNameAttribute; }
    const MSCString& isAttribute() const { return m_isAttribute; }
    const MSCString& classPrefixAttribute() const { return m_classPrefixAttribute; }

    std::shared_ptr<DOMTokenList> GetDOMTokenList() const {
      return m_class_list;
    }

    virtual void OnAddEvent(const MSCString& event);
    virtual void OnRemoveEvent(const MSCString& event);

    virtual const char* classPrefix() const;

    virtual bool hoverable() const { return false; }
    virtual bool IsContainerElement() const { return false; }
//    virtual void syncAttributeValue(CKSharedValue attrs) {}
    virtual bool SupportDisabledAttr() { return false; }
    virtual bool SupportCheckedAttr() { return false; }

    // Gin: HandleLocalEvent will be called two times
    // event.stage() == CAPTURING
    // event.stage() == BUBBLING
    virtual void HandleLocalEvent(std::shared_ptr<Event> event);
    virtual void defaultEventHandler(Event* event);
    virtual void HandleClicked(Event* event);

    virtual float GetClientWidthInPixels() { return 0; }
    virtual float GetClientHeightInPixels() { return 0; }

//    void observed(bool observed, IntersectionObserver* io);
//    void onObservedByIo(IntersectionObserver* io);
//    void onUnobservedByIo(IntersectionObserver* io);

//    virtual CKSharedValue getExtData() const { return nullptr; }
    virtual MSCString internalTagName() const { return ""; }

    void afterMountElement();

    void CopyPropsToPropsBuilder(blink::mt::PropsBuilder& builder);
protected:
//    virtual void transformAttribute(CKValue* attrValue);
    virtual bool NeedPrepareEvents();
    virtual void PrepareEvents(std::unordered_map<std::string, std::string>& attrValue);

    void SetInlineStyleInternal(const MSCString& value) { EnsureRareData().SetInlineStyle(value); }
    const MSCString& inlineStyleInternal() const { return rareData() ? rareData()->inlineStyle() : g_EmptyString; }
    void OnSetInlineStyle(const MSCString& inlineStyle);

//    AnimatableComponentData* animatableComponentData() const { return rareData() ? rareData()->animatableComponentData() : nullptr; }
//    AnimatableComponentData& ensureAnimatableComponentData() { return EnsureRareData().ensureAnimatableComponentData(); }
//
//    HoverableComponentData* hoverableComponentData() const { return rareData() ? rareData()->hoverableComponentData() : nullptr; }
//    HoverableComponentData& ensureHoverableComponentData() { return EnsureRareData().ensureHoverableComponentData(); }

    JSIBasicElement* rootElement() const;
   int bridgeChannelId() const override;

    void SetIdAttribute(const MSCString& idAttr) { EnsureRareData().SetIdAttribute(idAttr); }

    MSCString m_tagNameAttribute;
    MSCString m_isAttribute;
    MSCString m_classPrefixAttribute;
    std::shared_ptr<DOMTokenList> m_class_list;

  private:
    virtual bool NeedCreateAttribute();

    void OnDOMChanged(const DOMEventType& e, JSIBasicElement* jsiChild);
    void OnDOMRemoveChange(const DOMEventType& e);
    void OnDOMMove(DOMEventType event, JSIBasicElement* pElement, int original_index);
};

class Document;
class ElementHostObject;

class MessageProxy;

class Element : public JSIBasicElement {
friend class MessageProxy;
  
public:
  Element(Tag tag, ContainerNode *root);
  Element(Tag tag, ContainerNode *root, std::function<void()> deleter);
  virtual ~Element();

  bool SetAttribute(const MSCString& key, const blink::mt::PropValue &value);
  bool RemoveAttribute(const MSCString &key);

  bool GetAttribute(const MSCString& key, blink::mt::PropValue& value);
//  uint32_t attributesLength();
//  
//  JSIAttr* ensureJSIAttr(jsi::JSContext* context, const ACKString& key);
//  const ACKString& attributeNameByIndex(uint32_t index);
//  JSIAttr* getAttributeNode(jsi::JSContext* context, const ACKString& key);
//  JSIAttr* setAttributeNode(jsi::JSContext* context, JSIAttr* attr);
//  JSIAttr* detachAttribute(jsi::JSContext* context, const ACKString& key);
//  
//  virtual void onSetAttribute(jsi::JSContext* context,
//                              const MSCString& key,
//                              const MSCString& value);
//  virtual void onRemoveAttribute(jsi::JSContext* context,
//                                 const MSCString& key);
//  
//  virtual void AddEventListener(const MSCString& key,
//                                jsi::UniquePtr<jsi::Value> callback,
//                                bool capture);
//  virtual void RemoveEventListener(const MSCString& key,
//                                   jsi::UniquePtr<jsi::Value> callback,
//                                   bool capture);
//  
//  // process js callback
//  virtual void invokeJSCallback(const NativeDOMCallback& callback,
//                                CKSharedValue error,
//                                CKSharedValue data);
//  void invokeQuerySelectorExJSCallback(NativeDOMQuerySelectorExCallback callback,
//                                       CKSharedValue error,
//                                       CKSharedValue data);
//  void invokeGetComputedStyleJSCallback(NativeDOMGetComputedStyleCallback callback,
//                                        CKSharedValue error,
//                                        const CKGetComputedStyleData& computedStyleData);
//  void invokeGetComputedStyleJSCallbackInternal(jsi::Function* callback,
//                                                CKSharedValue error,
//                                                const CKGetComputedStyleData& computedStyleData);
  void getComputedStyle(const MSCString& elementId,
                        const MSCString& pseudoElt,
                        std::unique_ptr<CallbackFunction> callback);

  std::shared_ptr<CSSStyleDeclaration> GetCSSStyleDeclaration() {
    return css_style_declaration_;
  }
  
//#if AUTO_TEST
//  void getComputedStyleForAutoTest(jsi::JSContext* context,
//                                   CKComputedStyleMap& result,
//                                   CKComputedStylePropertyNameMap& propertyNamesMap);
//#endif
  Document* documentElement() const;
  
  PropertiesWrapper& properties() { return properties_; }
  const PropertiesWrapper& properties() const { return properties_; }
  
//  dom::JSINamedNodeMap& attributesForBindings(jsi::JSContext* context);

  unsigned ProtectJSCallback(std::unique_ptr<JSCallback> callback) {
    unsigned index = msc::JSCallback::GenerateId();
    protected_js_callbacks_[index] = std::move(callback);
    return index;
  }
  void ProtectJSCallback(std::unique_ptr<JSCallback> callback, unsigned index) {
    protected_js_callbacks_[index] = std::move(callback);
  }
  std::unique_ptr<JSCallback> GetProtectedJSCallback(unsigned index) {
    auto itr = protected_js_callbacks_.find(index);
    if (itr == protected_js_callbacks_.end()) return nullptr;
    auto js_callback = std::move(itr->second);
    protected_js_callbacks_.erase(itr);
    return js_callback;
  }

  void ClearJSWrapper(bool clear_jsobject = true) override;

private:
//  bool SetAttribute(const MSCString& key,
//                    const MSCString& value);
//  bool RemoveAttribute(const MSCString& key);
  
//  void detachAttrNodeFromElementWithValue(jsi::JSContext* context,
//                                          JSIAttr* attr,
//                                          const MSCString& value);
  
  PropertiesWrapper properties_;
  std::shared_ptr<CSSStyleDeclaration> css_style_declaration_;
  std::unordered_map<unsigned, std::unique_ptr<JSCallback>>
      protected_js_callbacks_;
  std::function<void()> deleter_;

public:
  static const std::string kGetAttribute;
  static const std::string kSetAttribute;
  static const std::string kRemoveAttribute;
};

}  // namespace native_dom
}  // namespace msc

#endif /* Element_h */
