//
//  document.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/10.
//

#ifndef Document_h
#define Document_h

#include "element.h"
#include "native_dom_callback.h"
#include "tree_scope.h"
#include "body_element.h"
// #include "text_node.h"
#include "intersection_observer.h"
#include "../../Public/native_dom_metrics.h"

#include "mtdocument.h"
#include <thread>


namespace msc {
namespace native_dom {

class TextNode;

class Document : public Element {
public:
  Document(Tag tag, ContainerNode *root, int pageId, std::shared_ptr<MessageProxy> message_proxy)
    : Document(root, false, false, false, pageId, message_proxy) {};
  
  Document(ContainerNode *root, bool IsScrollable, bool standardRoot, bool FrameMarker, int bridgeChannelId, std::shared_ptr<MessageProxy> message_proxy);
  
  ~Document() override {
//    if (CRConfig::enableGinNDOMDocumentUnload())
//        FireEvent(idStr(), "unload", nullptr, nullptr, false);
//
//    // TODO(Gin)
//    if (standard_root_)
//        CKASSERT(life_thread_ == std::this_thread::get_id());
//    CKUNSAFE_DELETE(head_element_);
//    CKUNSAFE_DELETE(body_element_);

    // TODO(Gin) more good way
//    while (nodes_.size()) {
//        delete nodes_.begin()->second;
//    }
    nodes_.clear();
    
    if (body_element_) {
      body_element_ = nullptr;
    }
  }
  
  void Close();
  
  virtual int bridgeChannelId() const override { return bridge_channel_id_; }

  bool IsScrollable() const { return is_scrollable_; }
  bool standardRoot() const { return standard_root_; }
  
  void AddNode(Element *node) {
    nodes_.insert(std::make_pair(node->GetNodeId(), node));
  }
  
  void removeNode(Element *node);

  void ExecuteCallback(const JSCallbackInfo &callback_info,
                       JSCallable::Setter setter);
  void FireEvent(const int &elemId,
                 const std::shared_ptr<msc::native_dom::Event>& event,
                 void *domChanges,
                 bool isCustomEvent);
  
//  void invokeCallback(NativeDOMCallback callback,
//                          CKSharedValue error,
//                          CKSharedValue data);
  
  TreeScope* treeScope() {
    return scope_.get();
  }
  
  Element* CreateElement(const StringView& local_name);
  TextNode *CreateTextNode(const MSCString& data);

  void QueryEnhanced(blink::mt::QueryEnhancedParams &&params,
                     JSCallbackInfo callback_info);

  void onDocumentCreate(void* context);
  void onDocumentConstruct();

  void PrepareEvents(std::unordered_map<std::string, std::string>& attrValue) override;

  bool NeedPrepareEvents() override;

  void OnListenerFromZeroToOne(const MSCString& key) override;
  void OnListenerFromOneToZero(const MSCString& key) override;

  std::shared_ptr<BodyElement> body() {
    return body_element_;
  }

    enum class FrameMarkerType { Begin, End, PreEnd };
    void FrameMarker(FrameMarkerType type);

    void SendCreateViewMessage(JSIBasicElement &element);

    void SendCreateTextNodeMessage(const TextNode &text_node);
    void SendUpdateViewMessage(Element &element, const MSCString &attribute_name, const blink::mt::PropValue &attribute_value);
    void SendRemoveViewMessage(int viewId);

    void SendAppendChildMessage(int parentId, const std::shared_ptr<const std::vector<int>>& child_tags);
    void SendManageChildrenMessage(int parentId,  const std::shared_ptr<const blink::mt::MTDocument::ChildrenChanges>& children_changes);

    void CreateKeyframesAnimationEnhanced(
        const std::shared_ptr<blink::mt::AnimationProperties> &animation_pros,
        JSCallbackInfo callback_info);
    void ClearKeyframesAnimationEnhanced(
        const std::shared_ptr<blink::mt::ClearAnimationProperties> &option,
        JSCallbackInfo callback_info);

    std::shared_ptr<IntersectionObserver> CreateIntersectionObserver(
        blink::mt::CreateIntersectionObserverParams &&params,
        JSCallbackInfo callback_info);
    void IntersectionObserverObserve(int id, int target_id);
    Element *GetElementByNodeId(const int &nodeId);

  msc::NativeDOMMetrics &GetMetrics();

private:

  std::shared_ptr<MessageProxy> GetMessageProxy() {
    return message_proxy_;
  }

  void dispatchEventPostProcess(Element* target, Event* e);

  void dispatchEvent(int elemId,
                     const std::shared_ptr<msc::native_dom::Event>& event,
                     bool isCustomEvent);

  void dispatchEvent(Element* element,
                     const std::shared_ptr<msc::native_dom::Event>& event,
                     bool isCustomEvent);
  
  int bridge_channel_id_;

//  Element* head_element_;
  std::shared_ptr<BodyElement> body_element_;

  std::shared_ptr<MessageProxy> message_proxy_;
//  jsi::JSEngine* m_engine;
//  size_t m_contextId;

  std::unordered_map<int, Element*> nodes_;
  std::unique_ptr<TreeScope> scope_;

  std::vector<std::unique_ptr<DOMAttr>> standalone_attrs_;

  bool has_pending_messages_: 1;
  bool create_elementInner_context_flag_ : 1;
  bool is_scrollable_ : 1;
  bool root_created_ : 1;
  bool standard_root_ : 1;
  bool frame_marker_ : 1;
  bool is_closing_ : 1;
  std::thread::id life_thread_;
  

public:
  static const std::string kCreateElement;
  static const std::string kCreateTextNode;
  static const std::string kCreateVNode;
  static const std::string kCreateKeyframesAnimationEnhanced;
  static const std::string kClearKeyframesAnimationEnhanced;
  static const std::string kQueryEnhanced;
  static const std::string kFrameMarker;
  static const std::string kFrameMarkerTypeBegin;
  static const std::string kFrameMarkerTypeEnd;
  static const std::string kFrameMarkerTypePreEnd;
};

} // namespace native_dom
} // namespace msc

#endif /* Document_h */
