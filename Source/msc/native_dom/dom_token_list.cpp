#include "dom_token_list.h"

namespace msc {
namespace native_dom {

std::string DOMTokenList::GetItem(int index) {
  if (0 <= index && index < tokens_.size()) {
    return tokens_[index];
  }

  return "null";
}

void DOMTokenList::Add(const std::vector<std::string> &tokens) {
  bool updated = false;
  for (auto &token : tokens) {
    if (!Contains(token)) {
      updated = true;
      tokens_.emplace_back(std::move(token));
    }
  }

  if (updated && on_token_changed_) {
    on_token_changed_(ToString());
  }
}

void DOMTokenList::Remove(const std::vector<std::string> &tokens) {
  bool updated = false;
  for (auto &token : tokens) {
    auto it = std::find(std::begin(tokens_), std::end(tokens_), token);
    if (it != std::end(tokens_)) {
      updated = true;
      tokens_.erase(it);
    }
  }

  if (updated && on_token_changed_) {
    on_token_changed_(ToString());
  }
}

bool DOMTokenList::Replace(const std::string &old_token,
                           const std::string &new_token) {
  bool updated = false;
  auto old_token_it =
      std::find(std::begin(tokens_), std::end(tokens_), old_token);
  if (old_token_it != std::end(tokens_)) {
    updated = true;
    auto new_token_it =
        std::find(std::begin(tokens_), std::end(tokens_), new_token);
    if (new_token_it == std::end(tokens_)) {
      *old_token_it = new_token;
    } else {
      tokens_.erase(old_token_it);
    }

    if (updated && on_token_changed_) {
      on_token_changed_(ToString());
    }
    return true;
  }
  return false;
}

bool DOMTokenList::Supports(const std::string &token) { return true; }

bool DOMTokenList::Toggle(const std::string &token, bool force,
                          bool has_force) {
  if (has_force) {
    if (force) {
      Add({token});
      return true;
    }
    Remove({token});
    return false;
  }

  if (Contains(token)) {
    Remove({token});
    return false;
  }
  Add({token});
  return true;
}

void DOMTokenList::ForEach(const ForEachCallback &callback) {
  for (auto i = 0; i < tokens_.size(); ++i) {
    callback(tokens_[i], i);
  }
}

void DOMTokenList::FromString(const std::string &s) {
  std::vector<std::string> tokens;
  std::istringstream iss(s);
  std::string token;

  while (iss >> token) {  // 自动处理连续空白
    tokens.push_back(token);
  }

  Remove(std::vector<std::string>{std::begin(tokens_), std::end(tokens_)});
  Add(tokens);
}
}  // namespace native_dom
}  // namespace msc
