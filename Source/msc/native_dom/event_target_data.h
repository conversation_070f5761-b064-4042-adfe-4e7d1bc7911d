//
//  event_target_data.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef EVENT_TARGET_DATA_H
#define EVENT_TARGET_DATA_H

#include "event.h"
#include "event_listener.h"

namespace msc {
namespace native_dom {

class EventTargetData {
public:
  EventTargetData() = default;
  EventTargetData(EventTargetData &&other) { m_listeners.swap(other.m_listeners); };
  virtual ~EventTargetData() = default;
  
  void AppendIfNotPresent(std::unique_ptr<EventListener> listener, bool& fromZeroToOne);
  void RemoveIfPresent(EventListener *listener, bool &fromOneToZero);
  size_t size() const;
  
  void FireEvent(std::shared_ptr<Event> event);
  
private:
  std::vector<std::unique_ptr<EventListener>> m_listeners;
};

} // namespace native_dom
} // namespace msc

#endif /* EVENT_TARGET_DATA_H */
