//
//  container_node.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/10.
//

#ifndef CONTAINER_NODE_H
#define CONTAINER_NODE_H

#include "node.h"
#include "msc_function.h"
#include "character_data.h"

namespace msc {
namespace native_dom {

class ContainerNode : public CharacterData {
public:
  ContainerNode(ContainerNode &) = delete;
  virtual ~ContainerNode() override;
  
  enum class DOMEventType {
         CREATE_ELEMENT = 0,
         // When anchor == nullptr will trigger APPEND_CHILD
         INSERT_BEFORE,
         INSERT_AFTER,
         APPEND_CHILD,
         REMOVE,

         // no use
     };

     void InsertBefore(ContainerNode* child,
                       ContainerNode* anchor);
     void AppendChild(ContainerNode* child);
     void RemoveChild(ContainerNode* child);
     void Remove();

     virtual void OnEvent(DOMEventType e,
                          ContainerNode* child,
                          ContainerNode* anchor,
                          bool child_has_parent,
                          int original_index) {};

     Node* firstChild() const { return first_child_; }
     Node* lastChild() const { return last_child_; }

     int nodeType() const;

     const char* nodeName() const;
     const char* tagName() const;

     void detachFromParent();
  
     void DestroySubtree();

//     void addEvent(const VString& event);
//     void removeEvent(const VString& event);

     // TODO(Gin) remove this for speed reason
     size_t GetIndex(ContainerNode*, bool skipComment = false);
     Node* getElement(int index, bool skipComment = false);

     bool IsRoot() const { return this == rootNode(); }

     using VisitCallback = std::function<void(ContainerNode*, Node*)>;
     using VisitBoolCallback = std::function<bool(ContainerNode*, Node*)>;
     void VisitAllDescendants(VisitCallback callback);
     void VisitAllDescendants_PostOrder(VisitCallback callback);
     void VisitEachChild(VisitCallback callback);
     void VisitEachChildCanStop(VisitBoolCallback callback);
     bool contains(const Node* child);

 #if DOM_TEST
     // Override EventTarget
     EventTargetDataMap* GetEventTargetData() override { return nullptr; }
     void EnsureEventTargetData() override {}

     // TODO
     // virtual ACKString toString(bool onlyAttrs = false);

     static ContainerNode* CreateElement(Tag type,
                                         ContainerNode* root);
     static ContainerNode* createElementForTest(Tag type,
                                                ContainerNode* root, int num);
     static ContainerNode* createRootForTest(Tag type, int num);

     // TODO(Gin) remove all default values
     static void dumpTree(Node* element, int tab, bool attrs = false);
 #endif

 private:
     void InsertAfterInternal(ContainerNode* child,
                              ContainerNode* anchor);
     void AppendChildInternal(ContainerNode* child);

 protected:
     ContainerNode(Tag type, ContainerNode* root);

     Node* first_child_ = nullptr;
     Node* last_child_ = nullptr;
};

}  // namespace native_dom
}  // namespace msc

#endif /* CONTAINER_NODE_H */
