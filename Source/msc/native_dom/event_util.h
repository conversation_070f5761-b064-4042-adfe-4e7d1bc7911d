//
// Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/5/19.
//

#ifndef MSC_EVENT_UTIL_H
#define MSC_EVENT_UTIL_H

#include "event_target.h"

namespace msc {
namespace native_dom {

class EventUtil {
public:
    static bool needBubbles(const MSCString& type);
    static bool isAnimationEvent(const MSCString& type);
    static bool isTouchEvent(const MSCString& type);
    static bool isTransitionEvent(const MSCString& type);
    static bool isInputEvent(const MSCString& type);

    static bool existAnimationEvent(const EventTargetDataMap& eventTarget);
    static bool existTransitionEvent(const EventTargetDataMap& eventTarget);
    
    static const MSCString &GetMappedEventName(const MSCString& event_name);
};

} // msc
} // native_dom

#endif //MSC_ANDROID_EVENT_UTIL_H
