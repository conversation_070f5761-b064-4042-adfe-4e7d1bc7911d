//
//  element_type.h
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> on 2025/1/13.
//

#ifndef ElementType_h
#define ElementType_h

namespace msc {
namespace native_dom {

enum class Tag {
    DIV = 1,
    SPAN,
    IMG,
    COMMENT,
    DOCUMENT,
    STYLE,
    HEAD,
    BODY,
    TEXT_NODE,
    S<PERSON>P<PERSON>,
    SWIPER_ITEM,
    BUTTON,
    NAVIGATOR,
    RADIO,
    RADIO_GROUP,
    CHECKBOX,
    CHECKBOX_GROUP,
    LABEL,
    ICON,
    FORM,
    SWITCH,
    INPUT,
    TEXT_AREA,
    EMBED,
    MSC_INPUT,
    MSC_VIEW,
    MSC_TEXT,
    MSC_VIRTUAL_TEXT,
    MSC_IMAGE,
    MSC_SCROLLER,
    MSC_SCROLL_VIEW,
    MSC_VIDEO,
    MSC_ROOT,
    MSC_EXTERNAL,
    EXTERNAL_TABS,
    EXTERNAL_TABS_BAR,
    EXTERNAL_TABS_BAR_ITEM,
    EXTERNAL_TABS_CONTAINER,
    EXTERNAL_TAB_ITEM,
    MSC_LIST_VIEW,
    MSC_GRID_VIEW,
    MSC_UNDEFINE,

    TAG_COUNT
};

enum NodeType {
    kElementNode = 1,
    kAttributeNode = 2,
    kTextNode = 3,
    kCdataSectionNode = 4,
    kProcessingInstructionNode = 7,
    kCommentNode = 8,
    kDocumentNode = 9,
    kDocumentTypeNode = 10,
    kDocumentFragmentNode = 11,
};

#define DOM_TAG_NAME_LIST(V)                                        \
    V(DOM_TAG_DIV, ("div"))                                         \
    V(DOM_TAG_VIEW, ("MSCView"))                                       \
    V(DOM_TAG_NAVIGATOR, ("navigator"))                             \
    V(DOM_TAG_TEXT, ("text"))                                       \
    V(DOM_TAG_TEXT_COMPONENT, ("MSCText"))                          \
    V(DOM_TAG_VIRTUAL_TEXT_COMPONENT, ("MSCVirtualText"))           \
    V(DOM_TAG_TEXT_NODE, ("MSCRawText"))                             \
    V(DOM_TAG_SPAN, ("span"))                                       \
    V(DOM_TAG_LABEL, ("label"))                                     \
    V(DOM_TAG_RICHTEXT, ("rich-text"))                              \
    V(DOM_TAG_IMAGE, ("MSCImage"))                                     \
    V(DOM_TAG_IMG, ("img"))                                         \
    V(DOM_TAG_RADIO, ("radio"))                                     \
    V(DOM_TAG_ICON, ("icon"))                                       \
    V(DOM_TAG_CHECKBOX, ("checkbox"))                               \
    V(DOM_TAG_RADIO_GROUP, ("radio-group"))                         \
    V(DOM_TAG_CHECKBOX_GROUP, ("checkbox-group"))                   \
    V(DOM_TAG_SWITCH, ("switch"))                                   \
    V(DOM_TAG_INPUT, ("input"))                                     \
    V(DOM_TAG_TEXTAREA, ("textarea"))                               \
    V(DOM_TAG_FORM, ("form"))                                       \
    V(DOM_TAG_BUTTON, ("button"))                                   \
    V(DOM_TAG_EMBED, ("embed"))                                     \
    V(DOM_TAG_EXTERNAL_TABS, ("external-tabs"))                     \
    V(DOM_TAG_EXTERNAL_TABS_BAR, ("external-tabs-bar"))             \
    V(DOM_TAG_EXTERNAL_TABS_BAR_ITEM, ("external-tabs-bar-item"))   \
    V(DOM_TAG_EXTERNAL_TABS_CONTAINER, ("external-tabs-container")) \
    V(DOM_TAG_EXTERNAL_TAB_ITEM, ("external-tab-item"))             \
    V(DOM_TAG_LIST_VIEW, ("list-view"))                             \
    V(DOM_TAG_GRID_VIEW, ("grid-view"))                             \
    V(DOM_TAG_SWIPER, ("MSCSwiper"))                                   \
    V(DOM_TAG_SWIPER_ITEM, ("MSCSwiperItem"))                         \
    V(DOM_TAG_SCROLLER, ("scroller"))                               \
    V(DOM_TAG_SCROLL_VIEW, ("MSCScrollView"))                         \
    V(DOM_TAG_VIDEO, ("video"))                                     \
    V(DOM_TAG_STYLE, ("style"))                                     \
    V(DOM_TAG_HEAD, ("head"))                                       \
    V(DOM_TAG_ROOT, ("root"))

#define DECLARE_STRING_CONSTANT_NDOM(Name, Value) \
    constexpr const char* Name = Value;

DOM_TAG_NAME_LIST(DECLARE_STRING_CONSTANT_NDOM)

}  // namespace native_dom
}  // namespace msc

#endif /* ElementType_h */
