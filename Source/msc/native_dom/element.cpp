//
//  Element.cpp
//  MSC
//
//  Created by <PERSON><PERSON><PERSON> <PERSON> on 2025/1/13.
//

#include "element.h"
#include "document.h"

#include "bridge/message_proxy.h"
#include "mtdocument.h"

namespace msc {
namespace native_dom {

using namespace std::string_literals;
const std::string Element::kGetAttribute = "getAttribute"s;
const std::string Element::kSetAttribute = "setAttribute"s;
const std::string Element::kRemoveAttribute = "removeAttribute"s;

void sendEvent(const MSCString &moduleName, const MSCString &methodName, const MSCString &eventData);

TreeScope* getTreeScope(JSIBasicElement* e)
{
    // if (e->IsQuickJSImpl()) {
    //     return static_cast<Element*>(e)->documentElement()->treeScope();
    // } else {
    return static_cast<Element*>(e)->documentElement()->treeScope();
    // }
}


void BasicElement::AddJSCallback(const NativeDOMCallback& callback,
                                 std::unique_ptr<CallbackFunction> jsFunc)
{
  if (callback.isOnce()) {
    EnsureOnceCallbacks()[callback.callbackId()] = std::move(jsFunc);
  } else {
    EnsureRepeatCallbacks()[callback.callbackId()] = std::move(jsFunc);
  }
}

void BasicElement::getScrollOffset(std::unique_ptr<CallbackFunction> callback)
{
    int bridgeChannelId = this->bridgeChannelId();
    NativeDOMCallback ndomCallback(bridgeChannelId, GetNodeId(), true);
    AddJSCallback(ndomCallback, std::move(callback));
//    TRY_BRIDGE_PROXY(bridgeChannelId)->onGetScrollOffset(idStr(), ndomCallback);
}

void BasicElement::getBoundingClientRect(std::unique_ptr<CallbackFunction> callback)
{
    int bridgeChannelId = this->bridgeChannelId();

    NativeDOMCallback ndomCallback(bridgeChannelId, GetNodeId(), true);
    AddJSCallback(ndomCallback, std::move(callback));
//    TRY_BRIDGE_PROXY(bridgeChannelId)->onGetBoundingClientRect(idStr(), ndomCallback);
}

const int getBridgeChannelId(const JSIBasicElement *e) {
  return static_cast<const Element*>(e)->documentElement()->bridgeChannelId();
}

JSIBasicElement::JSIBasicElement(Tag type, ContainerNode* root)
    : BasicElement(type, root) {
  m_class_list =
      std::make_shared<DOMTokenList>([this](auto& new_value) {
        auto doc = static_cast<Element*>(this);
        if (doc != nullptr && mounted()) {
          doc->documentElement()->SendUpdateViewMessage(*doc, "class",
                                                        new_value);
        }
      });
};

JSIBasicElement* JSIBasicElement::rootElement() const
{
    return static_cast<JSIBasicElement*>(rootNode());
}

int JSIBasicElement::bridgeChannelId() const
{
    return getBridgeChannelId(this);
}

void JSIBasicElement::CopyPropsToPropsBuilder(blink::mt::PropsBuilder &propsBuilder) {
    Element &element = *(static_cast<Element*>(this));
    element.properties().populate(propsBuilder);

    Tag element_tag = element.nodeTag();
    if (element_tag == Tag::TEXT_NODE) {
        propsBuilder.setProp("text", element.TextContent());
    } else {
        auto className = element.classAttribute();
        if (!className.empty()) {
            propsBuilder.setProp("class", className);
        }
        auto tag_name = element.tagNameAttribute();
        auto is_attr = element.isAttribute();
        auto class_prefix = element.classPrefix();
        auto id_attr = element.idAttribute();
        auto style_attr = element.GetTotalInlineStyle();
        propsBuilder.setProp("tagName", tag_name);
        propsBuilder.setProp("is", is_attr);
        propsBuilder.setProp("classPrefix", class_prefix);
        propsBuilder.setProp("id", id_attr);
        propsBuilder.setProp("style", style_attr);
    }
}

void JSIBasicElement::OnEvent(DOMEventType e,
                              ContainerNode* child,
                              ContainerNode* anchor,
                              bool child_has_parent,
                              int original_index)
{
    auto* childElement = static_cast<JSIBasicElement*>(child);
    bool eventIsAddChild = e == DOMEventType::APPEND_CHILD || e == DOMEventType::INSERT_BEFORE || e == DOMEventType::INSERT_AFTER;
    if (eventIsAddChild && childElement) {
        if (connected()) {
            childElement->SetConnected(true);
            child->VisitAllDescendants([](ContainerNode* currentParent, Node* currentChild) {
                static_cast<JSIBasicElement*>(currentChild)->SetConnected(true);
            });
        }

//        if (childElement->EatTextNodeFeature() && nodeTag() == Tag::CUBE_TEXT
//            && childElement->nodeTag() == Tag::TEXT_NODE) {
//            TextNodeSetEated(childElement, true);
//        }
    } else if (e == DOMEventType::REMOVE) {
        if (connected()) {
            SetConnected(false);
            VisitAllDescendants([](ContainerNode* currentParent, Node* currentChild) {
                static_cast<JSIBasicElement*>(currentChild)->SetConnected(false);
            });
        }

//        if (EatTextNodeFeature() && nodeTag() == Tag::TEXT_NODE)
//            TextNodeSetEated(this, false);
    }

//    bool enableStyleSheetAppend = CRConfig::enableStyleSheetAppend();
//    if (eventIsAddChild && nodeTag() == Tag::BODY && !mounted() && childElement && childElement->nodeTag() != Tag::COMMENT) {
//        JSIBasicElement* documentElement = childElement->rootElement();
//        const ACKString& bridgeChannelId = this->bridgeChannelId();
//        JSIBasicElement* head = getHeadElementFromDocumentElement(documentElement);
//        auto* body = static_cast<JSIBasicElement*>(this);
//        if (enableStyleSheetAppend && (useStandardRoot(this) || CRConfig::enableGinAppWidgetStyleSheetAppend())) {
//            if (!body->mounted()) {
//                head->VisitAllDescendants([=](ContainerNode*, Node* childElement) {
//                    auto* styleElement = static_cast<JSIBasicElement*>(childElement);
//                    styleElementAddPageStyle(styleElement, bridgeChannelId);
//                    styleElement->SetMounted(true);
//                });
//            }
//        } else {
//            head->VisitAllDescendants([=](ContainerNode*, Node* childElement) {
//                auto* styleElement = static_cast<JSIBasicElement*>(childElement);
//                styleElementAddPageStyle(styleElement, bridgeChannelId);
//            });
//        }
//
//        triggerBodyReady(this);
//    }
//
//    if (enableStyleSheetAppend && eventIsAddChild) {
//        if (useStandardRoot(this) || CRConfig::enableGinAppWidgetStyleSheetAppend()) {
//            if (nodeTag() == Tag::HEAD && !mounted() && childElement && childElement->nodeTag() == Tag::STYLE) {
//                JSIBasicElement* documentElement = childElement->rootElement();
//                JSIBasicElement* body = getBodyElementFromDocumentElement(documentElement);
//                if (body && body->mounted()) {
//                    auto* styleElement = static_cast<JSIBasicElement*>(childElement);
//                    styleElementAddPageStyle(styleElement, this->bridgeChannelId());
//                    styleElement->SetMounted(true);
//                }
//            }
//        }
//    }
//
//    if (CRConfig::enableGinFrameMarkerNDOMSubTree() && rootElement())
//        static_cast<JSIDocumentElement*>(rootElement())->frameMarkerBegin();
      if (eventIsAddChild) {
          auto* jsiChild = static_cast<JSIBasicElement*>(child);
          if (jsiChild->mounted()) {
            OnDOMMove(e, jsiChild, original_index);
          } else {
            OnDOMChanged(e, jsiChild);
          }
      } else if (e == DOMEventType::REMOVE) {
            SetMounted(false);
//            if (CRConfig::enableGinNDOMRemoveNode()) {
                VisitAllDescendants([](ContainerNode* currentParent, Node* currentChild) {
                    currentChild->SetMounted(false);
                });
//            }
            OnDOMRemoveChange(e);
      }
//    if (CRConfig::enableGinFrameMarkerNDOMSubTree() && rootElement())
//        static_cast<JSIDocumentElement*>(rootElement())->frameMarkerEnd();
}

void JSIBasicElement::OnDOMMove(ContainerNode::DOMEventType event, JSIBasicElement* jsiChild, int original_index)
{
    MSC_RENDERER_LOG_DEBUG("JSIBasicElement::OnDOMMove, tag: %d", jsiChild->GetNodeId());

    int64_t index = GetIndex(jsiChild, true);

    Document *document = static_cast<Element *>(this)->documentElement();
    auto children_changes = std::make_shared<blink::mt::MTDocument::ChildrenChanges>();
    children_changes->move_from_indices.push_back(original_index);
    children_changes->move_to_indices.push_back(index);
    document->SendManageChildrenMessage(jsiChild->GetNodeId(), children_changes);
}

void JSIBasicElement::OnDOMRemoveChange(const DOMEventType& e)
{
  static_cast<Element *>(this)->documentElement()->SendRemoveViewMessage(this->GetNodeId());
  this->DestroySubtree();
}

void JSIBasicElement::OnDOMChanged(const ContainerNode::DOMEventType& e, JSIBasicElement* jsiChild) {
    if (!jsiChild->mounted()) {
        const MSCString parentNodeId = this->idAttribute();
        int bridgeChannelId = this->bridgeChannelId();

        Document *document = static_cast<Element *>(this)->documentElement();

        document->SendCreateViewMessage(*jsiChild);
        jsiChild->SetMounted(true);

#if 1
        auto currentElement = static_cast<JSIBasicElement *>(jsiChild);
        if (currentElement->firstChild()) {
            auto childNodeIds = std::make_shared<std::vector<int>>();
            currentElement->VisitEachChild(
                    [&childNodeIds](
                            ContainerNode *, Node *cur) {
                        childNodeIds->push_back(cur->GetNodeId());
                    });
            if (!childNodeIds->empty()) {
                document->SendAppendChildMessage(currentElement->GetNodeId(),
                                                     childNodeIds);
            }
        }
#endif

#if 0
        if (jsiChild->IsContainerElement()) {
            auto childNodeIds = std::make_shared<std::vector<int>>();
            jsiChild->VisitEachChild(
                    [bridgeChannelId, &childNodeIds, &messageProxy, &jsiChild](
                            ContainerNode *currentParent, Node *currentChild) {
                        auto currentElement = static_cast<JSIBasicElement *>(currentChild);
                        jsiChild->OnDOMChanged(DOMEventType::APPEND_CHILD, currentElement, false);

                        childNodeIds->push_back(currentChild->GetNodeId());
                    });

            if (childNodeIds->size() > 0) {
                messageProxy->SendAppendChildMessage(bridgeChannelId, jsiChild->GetNodeId(),
                                                     childNodeIds);
            }
        }
#endif

        if (this->mounted()) {
            if (e == DOMEventType::APPEND_CHILD) {
                auto childNodeIds = std::make_shared<std::vector<int>>();
                childNodeIds->push_back(jsiChild->GetNodeId());
                document->SendAppendChildMessage(this->GetNodeId(), childNodeIds);
            } else if (e == DOMEventType::INSERT_BEFORE || e == DOMEventType::INSERT_AFTER) {
                int index = GetIndex(jsiChild, true);
                auto children_changes = std::make_shared<blink::mt::MTDocument::ChildrenChanges>();
                children_changes->add_at_indices.push_back(index);
                children_changes->add_child_msc_tags.push_back(jsiChild->GetNodeId());
                document->SendManageChildrenMessage(this->GetNodeId(), children_changes);
            }
        }
    }
}
  
  
//    ATRACE_CALL();

//    Tag childNodeTag = jsiChild->nodeTag();
//    if (childNodeTag == Tag::COMMENT) {
//        return;
//    } else if (childNodeTag == Tag::TEXT_NODE) {
//        // if (IsQuickJSImpl()) {
//        //  if (jsiChild->EatTextNodeFeature() && static_cast<text_node*>(jsiChild)->eated())
//        //      return;
//        // } else {
//        if (jsiChild->EatTextNodeFeature() && static_cast<JSITextNode*>(jsiChild)->textNodeFeatureEated())
//            return;
//        // }
//    }
//
//    CKSharedValue type;
//    CKRefPtr<CKDOMNodeData> data = adoptRef(new CKDOMNodeData);
//    if (childNodeTag != Tag::CUBE_EXTERNAL) {
//        const char* tag = ElementUtil::nameFromTag(childNodeTag, EatTextNodeFeature(), textNestingFeature());
//        CKASSERT(tag);
//        type = adoptRef(CKValue::createString(tag));
//
//        if (childNodeTag == Tag::EMBED)
//            data->setOtherValue("data", jsiChild->getExtData());
//    } else {
//        type = adoptRef(CKValue::createString(jsiChild->internalTagName()));
//    }
//    data->setType(type);
//
//    CKSharedValue bridgeAttribute;
//
//    if (CKValue* attrs = static_cast<JSIElement*>(jsiChild)->properties().attributes()) {
//        bridgeAttribute = adoptRef(attrs->copy());
//    }
//
//    if (jsiChild->NeedCreateAttribute() && !bridgeAttribute)
//        bridgeAttribute = adoptRef(CKValue::createMap());
//    jsiChild->transformAttribute(bridgeAttribute.get());
//
//    ACKString classPrefixString = jsiChild->classPrefix();
//    ElementUtil::fixAttributes(bridgeAttribute, classPrefixString, "");
//
//    if (childNodeTag == Tag::CUBE_TEXT && EatTextNodeFeature()) {
//        if (!bridgeAttribute)
//            bridgeAttribute = adoptRef(CKValue::createMap());
//
//        Node* first = jsiChild->firstChild();
//        if (first && first->nodeTag() == Tag::TEXT_NODE) {
//            auto* text = static_cast<JSIBasicElement*>(first);
//            text->transformAttribute(bridgeAttribute.get());
//        }
//    }
//
//    {
//        // styles
//        ACKString style = jsiChild->GetTotalInlineStyle();
//        if (!style.empty()) {
//            auto* map = new CKStylePropertyValueMap(style);
//            data->setStyle(map);
//            CKSAFE_UNREF(map);
//        }
//    }
//
//    if (bridgeAttribute)
//        data->setAttr(bridgeAttribute);
//
//    if (jsiChild->NeedPrepareEvents()) {
//        CKSharedValue events = adoptRef(CKValue::createMap());
//        jsiChild->PrepareEvents(events.get());
//        data->setEvent(events);
//    }
//
//    CKSharedValue ref = adoptRef(CKValue::createString(jsiChild->idStr()));
//    data->setRef(ref);
//    jsiChild->SetMounted(true);
//
//    int64_t index;
//    if (e == DOMEventType::APPEND_CHILD) {
//        index = -1;
//    } else {
//        index = GetIndex(jsiChild, true);
//    }
//
//    ACKString parentNodeId = idStr();
//    const ACKString& bridgeChannelId = this->bridgeChannelId();
//    TRY_BRIDGE_PROXY(bridgeChannelId)->onAddElement(parentNodeId, data.get(), index);
//
//    jsiChild->afterMountElement();
//
//    if (jsiChild->IsContainerElement()) {
//        // sync cube sdk subtree create and attach.
//        jsiChild->VisitAllDescendants([](ContainerNode* parent, Node* child) {
//            auto* jsiChild = static_cast<JSIBasicElement*>(child);
//            if (!jsiChild->mounted()) {
//                auto* jsiParent = static_cast<JSIBasicElement*>(parent);
//                jsiParent->OnDOMChanged(DOMEventType::APPEND_CHILD, jsiChild);
//            }
//        });
//    }
//}

void JSIBasicElement::OnAddEvent(const MSCString& event)
{
    if (mounted()) {
//        const MSCString& bridgeChannelId = this->bridgeChannelId();
//        TRY_BRIDGE_PROXY(bridgeChannelId)->OnAddEvent(idStr(), event, event);
    }
}

void JSIBasicElement::OnRemoveEvent(const MSCString& event)
{
    if (mounted()) {
//        const MSCString& bridgeChannelId = this->bridgeChannelId();
//        TRY_BRIDGE_PROXY(bridgeChannelId)->OnRemoveEvent(idStr(), event);
    }
}

void JSIBasicElement::SetConnected(bool connected)
{
    if (connected_ != connected) {
        if (connected) {
            TreeScope* scope = getTreeScope(this);
            if (scope && !idAttribute().size())
                scope->Add(idAttribute(), this);
        } else {
            TreeScope* scope = getTreeScope(this);
            if (scope && idAttribute().size())
                scope->Remove(idAttribute(), this);
        }
    }
    connected_ = connected;
}

void JSIBasicElement::SetInlineStyle(const MSCString& inlineStyle)
{
  MSCString tmpStyle(inlineStyle);

    // TODO(Gin): cache origin inline style string
//    if (CRConfig::enableGinNormalizeInlineStyleStr()) {
//        tmpStyle = ElementUtil::normalizeInlineStyleStr(tmpStyle);
//    } else {
//        // TODO(Gin): remove ugly codes
//        if (tmpStyle.find("borderBottomStyle") != -1)
//            CKStringUtils::replaceString(tmpStyle, "borderBottomStyle", "border-bottom-style");
//
//        if (tmpStyle.find("borderBottomColor") != -1)
//            CKStringUtils::replaceString(tmpStyle, "borderBottomColor", "border-bottom-color");
//    }

    if (inlineStyleInternal() != tmpStyle) {
        SetInlineStyleInternal(tmpStyle);
        OnSetInlineStyle(GetTotalInlineStyle());
    }
}

MSCString JSIBasicElement::GetTotalInlineStyle()
{
    return inlineStyleInternal();
}

void JSIBasicElement::OnSetInlineStyle(const MSCString& inlineStyle)
{
    if (mounted()) {
        auto element = static_cast<Element *>(this);
        element->documentElement()->SendUpdateViewMessage(*element, "style", inlineStyle);
//        const MSCString& bridgeChannelId = this->bridgeChannelId();
//        CKSharedPtr ptr = adoptRef(new CKStylePropertyValueMap(inlineStyle));
//        CKStylePropertyValueMap* map = static_cast<CKStylePropertyValueMap*>(ptr.get());
//        TRY_BRIDGE_PROXY(bridgeChannelId)->onUpdateStyle(idStr(), map);
    }
}

//void JSIBasicElement::transformAttribute(CKValue* attrValue)
//{
//}

void JSIBasicElement::OnListenerFromZeroToOne(const MSCString& eventName)
{
    if (mounted()) {
    } else {
    }
}

void JSIBasicElement::OnListenerFromOneToZero(const MSCString& eventName)
{
    if (mounted()) {
    } else {
    }
}

bool JSIBasicElement::NeedPrepareEvents()
{
    //    if (rareData()) {
    //        auto* a = rareData()->intersectionObserverData();
    //        if (a && a->size())
    //            return true;
    //    }
    //    return false;
    return false;
}

void JSIBasicElement::PrepareEvents(std::unordered_map<std::string, std::string>& attrValue) {
    //    if (m_observedByIntersectionObserver) {
    //        attrValue->mapInsert(CKEVENT_INTERSECTION_OBSERVER,
    //                             adoptRef(CKValue::createString(CKEVENT_INTERSECTION_OBSERVER)));
    //    }
}

void JSIBasicElement::afterMountElement()
{
//    if (rareData() && rareData()->intersectionObserverData()) {
//        std::unordered_map<void*, bool>& m = *rareData()->intersectionObserverData();
//        for (auto it = m.begin(); it != m.end(); ++it) {
//            if (it->second)
//                onObservedByIo((IntersectionObserver*)it->first);
//        }
//    }
}

//void JSIBasicElement::onObservedByIo(IntersectionObserver* io)
//{
//    if (mounted()) {
//        const ACKString& bridgeChannelId = this->bridgeChannelId();
//        TRY_BRIDGE_PROXY(bridgeChannelId)->onObservedByIo(idStr(), io->idStr(), io->getConfig());
//    }
//}
//
//void JSIBasicElement::onUnobservedByIo(IntersectionObserver* io)
//{
//    if (mounted()) {
//        const ACKString& bridgeChannelId = this->bridgeChannelId();
//        TRY_BRIDGE_PROXY(bridgeChannelId)->onUnobservedByIo(idStr(), io->idStr());
//    }
//}
//
//void JSIBasicElement::observed(bool observed, IntersectionObserver* io)
//{
//    auto& data = EnsureRareData().ensureIntersectionObserverData();
//    auto it = data.find(io);
//    if (it == data.end()) {
//        data[io] = observed;
//        if (observed)
//            onObservedByIo(io);
//        else
//            onUnobservedByIo(io);
//    } else {
//        if (it->second != observed) {
//            data[io] = observed;
//            if (observed)
//                onObservedByIo(io);
//            else
//                onUnobservedByIo(io);
//        }
//    }
//}

EventTargetDataMap* JSIBasicElement::GetEventTargetData()
{
    if (rareData())
        return rareData()->eventTargetData();
    return nullptr;
}

void JSIBasicElement::EnsureEventTargetData()
{
    EnsureRareData().EnsureEventTargetData();
}

void JSIBasicElement::HandleLocalEvent(std::shared_ptr<Event> event)
{
    if (EventTargetDataMap* targetData = GetEventTargetData()) {
        if (targetData->find(event->typeName()) != targetData->end()) {
            EventTargetData& data = targetData->at(event->typeName());
            data.FireEvent(event);
            // TODO(Gin): remove ugly logic
//            bool standardRoot = useStandardRoot(this);
//            if (!EventUtil::isTouchEvent(event->typeName()) || standardRoot)
//                data.FireEvent(event);
        }
    }
}

void JSIBasicElement::defaultEventHandler(Event* event)
{
}

void JSIBasicElement::HandleClicked(Event* event)
{
}

bool JSIBasicElement::NeedCreateAttribute()
{
    return false;
}


const char* JSIBasicElement::classPrefix() const
{
    return "";
}

Element::Element(Tag tag, ContainerNode *root) : JSIBasicElement(tag, root),
                                                 css_style_declaration_{
                                                         std::make_shared<CSSStyleDeclaration>(
                                                                 *this)},
                                                 deleter_(nullptr) {
    auto document = static_cast<Document *>(root);
    if (document) {
        document->AddNode(this);
    }

    MSC_RENDERER_LOG_DEBUG("constructor of Element, nodeId: %d, %p", GetNodeId(), this);
}

Element::Element(Tag tag, ContainerNode *root, std::function<void()> deleter) : Element(tag, root) {
    deleter_ = std::move(deleter);
}

Element::~Element() {
  MSC_RENDERER_LOG_DEBUG("[native_dom] Element destructed, nodeId: %d", GetNodeId());

  auto document = static_cast<Document*>(rootNode());
  if (document != this) {
      document->removeNode(this);
  }
  
  if (css_style_declaration_) {
    css_style_declaration_->DestroyJSWrapper();
  }
  
  if (m_class_list) {
    m_class_list->DestroyJSWrapper();
  }
  
  if (deleter_) {
      deleter_();
  }
}

bool Element::SetAttribute(const MSCString& key, const blink::mt::PropValue &value) {
  MSC_RENDERER_LOG_DEBUG("[native_dom] Element::SetAttribute %d %s %s", this->GetNodeId(), key.c_str(), value.stringValue().c_str());
  bool updated = true;
  if (value.isA<blink::mt::PropValueType::String>()) {
    if (key == "id") {
      SetIdAttribute(value.stringValue());
      if (connected()) {
        TreeScope *scope = documentElement()->treeScope();
        if (scope && idAttribute().size()) {
          scope->Add(idAttribute(), this);
        }
      }
    } else if (key == "is") {
      //TODO: element W3C接口Unit Test
      m_isAttribute = value.stringValue();
    } else if (key == "class") {
      //TODO: element W3C接口Unit Test
      m_class_list->FromString(value.stringValue());
    } else if (key == "classPrefix") {
      m_classPrefixAttribute = value.stringValue();
    } else if (key == "tagName") {
      m_tagNameAttribute = value.stringValue();
    } else if (key == "style") {
      SetInlineStyleInternal(value.stringValue());
    } else {
      updated = properties_.SetAttribute(key, value);
    }
  } else {
    updated = properties_.SetAttribute(key, value);
  }
  if (updated && mounted()) {
      static_cast<Element *>(this)->documentElement()->SendUpdateViewMessage(*this, key, value);
  }
  return updated;
}

bool Element::RemoveAttribute(const MSCString& key) {
    MSC_RENDERER_LOG_DEBUG("[native_dom] Element::RemoveAttribute %s", key.c_str());

    bool updated = true;
    if (key == "id") {
        SetIdAttribute("");
        if (connected()) {
            TreeScope* scope = documentElement()->treeScope();
            if (scope && idAttribute().size())
                scope->Remove(idAttribute(), this);
        }
    } else if (key == "class") {
      m_class_list->Clear();
    } else if (key == "is") {
        m_isAttribute.clear();
    } else if (key == "tagName") {
        m_tagNameAttribute.clear();
    } else if (key == "classPrefix") {
        m_classPrefixAttribute.clear();
    } else if (key == "tagName") {
        m_tagNameAttribute.clear();
    } else if (key == "style") {
        SetInlineStyleInternal("");
    } else {
        updated = properties_.RemoveAttribute(key);
    }

    if (updated && mounted()) {
        static_cast<Element *>(this)->documentElement()->SendUpdateViewMessage(*this, key,
                                            g_EmptyString);
    }
    return updated;
}

bool Element::GetAttribute(const MSCString& key, blink::mt::PropValue& value) {
    MSC_RENDERER_LOG_DEBUG("[native_dom] Element::GetAttribute %p %s: %s\n", this, key.c_str(),
                           value.stringValue().c_str());

    if (key == "id") {
        value = idAttribute();
    } else if (key == "is") {
        value = m_isAttribute;
    } else if (key == "class") {
      value = m_class_list->GetValue();
    } else if (key == "classPrefix") {
        value = m_classPrefixAttribute;
    } else if (key == "tagName") {
        value = m_tagNameAttribute;
    } else if (key == "style") {
        value = GetTotalInlineStyle();
    } else {
        return properties_.GetAttribute(key, value);
    }
    return true;
}

Document* Element::documentElement() const
{
    return static_cast<Document*>(rootNode());
}

void Element::ClearJSWrapper(bool clear_jsobject) {
    ContainerNode::ClearJSWrapper(clear_jsobject);
    
    //Element断开指向JSObject的强引用时，把关联的class_list和style也断开其对JSObject的强引用
    if (clear_jsobject) {
        if (m_class_list) {
            m_class_list->ClearJSWrapper();
        }
        if (css_style_declaration_) {
            css_style_declaration_->ClearJSWrapper();
        }
        protected_js_callbacks_.clear();
    }
};

}  // namespace native_dom
}  // namespace msc
