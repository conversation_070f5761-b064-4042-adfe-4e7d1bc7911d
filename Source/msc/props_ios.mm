//
//  props_ios.m
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/3/3.
//

#import "props.h"

#import <Foundation/Foundation.h>

namespace blink::mt {

namespace {

PropValue convertFromObjcValue(id value) {
  if ([value isKindOfClass:[NSString class]]) {
    return [(NSString*)value UTF8String];
  } else if ([value isKindOfClass:[NSNumber class]]) {
    return [(NSNumber*)value doubleValue];
  } else if ([value isKindOfClass:[NSArray class]]) {
    std::vector<PropValue> vector;
    for (id item in value) {
      vector.push_back(convertFromObjcValue(item));
    }
    return vector;
  } else if ([value isKindOfClass:[NSDictionary class]]) {
    std::unordered_map<std::string, PropValue> map;
    for (id key in value) {
      id item = value[key];
      map[[key UTF8String]] = convertFromObjcValue(item);
    }
    return map;
  } else {
    return PropValue();
  }
}

id convertToObjcValue(PropValue value) {
  id ns_value = nil;
  std::visit([&ns_value](auto&& arg) {
    using T = std::decay_t<decltype(arg)>;

    if constexpr (std::is_same_v<T, std::string>) {
      ns_value = [NSString stringWithUTF8String:arg.c_str()];
    } else if constexpr (std::is_same_v<T, int>) {
      ns_value = @(arg);
    } else if constexpr (std::is_same_v<T, double>) {
      ns_value = @(arg);
    } else if constexpr (std::is_same_v<T, bool>) {
      ns_value = @(arg);
    } else if constexpr (std::is_same_v<T, std::vector<PropValue>>) {
      NSMutableArray* array = [NSMutableArray array];
      vector<PropValue> vec = arg;
      for (auto &item : vec) {
        [array addObject:convertToObjcValue(item)];
      }
      ns_value = array;
    } else if constexpr (std::is_same_v<T, std::unordered_map<std::string, PropValue>>) {
      NSMutableDictionary* dict = [NSMutableDictionary dictionary];
      unordered_map<string, PropValue> map = arg;
      for (auto &[key, val] : map) {
        dict[@(key.c_str())] = convertToObjcValue(val);
      }
      ns_value = dict;
    } else {
      ns_value = [NSNull null];
    }
  }, value);
  return ns_value;
}

}; // namespace

const shared_ptr<const blink::mt::Props> propsFromDict(NSDictionary *dict) {
  if (!dict || ![dict isKindOfClass:[NSDictionary class]]) {
    return nullptr;
  }
  PropsBuilder builder;
  for (NSString *key in dict) {
    auto v = convertFromObjcValue(dict[key]);
    builder.setProp(key.UTF8String, v);
  }
  return builder.getProps();
}

NSDictionary* propsToDict(shared_ptr<const blink::mt::Props> props) {
  if (!props) {
    return nil;
  }

  NSMutableDictionary* dict = [NSMutableDictionary dictionary];
  props->forEach([&dict](const std::string& key, const PropValue& value) {
    NSString* ns_key = [NSString stringWithUTF8String:key.c_str()];
    id ns_value = convertToObjcValue(value);
    dict[ns_key] = ns_value;
  });
  return dict;
}

std::string propsToJson(const shared_ptr<const blink::mt::Props>& props) {
  NSDictionary* dict = propsToDict(props);
  if (!dict) {
    return "";
  }
  NSData* data = [NSJSONSerialization dataWithJSONObject:dict options:0 error:nullptr];
  NSString* string = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
  return string ? string.UTF8String : "";
}

const std::shared_ptr<const blink::mt::Props> jsonToProps(const std::string& json_string) {
  NSString *ns_json_string = [NSString stringWithUTF8String:json_string.c_str()];
  NSData *data = [ns_json_string dataUsingEncoding:NSUTF8StringEncoding];
  if (!data) {
    return nullptr;
  }
  id ns_json_object = [NSJSONSerialization JSONObjectWithData:data options:0 error:nullptr];
  return propsFromDict(ns_json_object);
}

}; // namespace blink::mt
