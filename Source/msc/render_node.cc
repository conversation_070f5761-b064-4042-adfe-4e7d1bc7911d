//
//  render_node.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/04/03.
//

#include "render_node.h"

#include "style_convertor.h"
#include "mtdocument.h"

#include "ui_command_buffer.h"
#include "third_party/blink/renderer/core/layout/layout_box.h"
#include "third_party/blink/renderer/core/paint/paint_layer.h"
#include "third_party/blink/renderer/platform/heap/member.h"
#include "third_party/blink/renderer/platform/heap/collection_support/heap_vector.h"
#include "third_party/blink/renderer/platform/wtf/hash_set.h"
#include "third_party/blink/renderer/platform/wtf/hash_map.h"
#include "third_party/blink/renderer/platform/wtf/vector.h"

namespace blink::mt {

#define ENABLE_STACKING_CHILD_PRINT (DEBUG && false)

// 剪裁判断
inline bool CanBeStripped(LayoutObject *layout_object) {
    auto elem = To<Element>(layout_object->GetNode());
    if (elem->GetMscViewWanted()) {
#if ENABLE_DBUEG_LAYOUT_ONLY_PRINT
        printf("[blink][flatten] tag:%ld, view wanted \n", elem->getMscTag());
#endif
        return false; // 如果已经在样式解析阶段判断，不拍平。
    }
    // 重要, 不能拍掉Image/Text等组件，只处理MSCView.
    if (elem->componentName().Utf8() != "mt-view") {
#if ENABLE_DBUEG_LAYOUT_ONLY_PRINT
        printf("[blink][flatten] tag:%ld, not mscview \n", elem->getMscTag());
#endif
        return false;
    }
    // 需要在布局阶段判断的拍平条件
    bool prop_id = elem->HasID();  // 有Id
    bool events = false;           // todo: 触摸事件兼容/动画
    EPosition postion = layout_object->Style()->GetPosition();
    bool position = postion == EPosition::kAbsolute || postion == EPosition::kFixed; // 相对于ViewPort
    
    bool notLayoutOnly = prop_id || events || position;
    
    if (notLayoutOnly) {
#if ENABLE_DBUEG_LAYOUT_ONLY_PRINT
        printf("[blink][flatten] tag:%ld, notLayoutOnly: prop_id:%d, events:%d position:%d \n", elem->getMscTag(), prop_id, events, position);
#endif
        return false;
    }
    return true;
}

void CollectNormalFlowChildren(LayoutObject& box, HeapVector<Member<RenderNode>>& collecting_children) {
  for (LayoutObject* child = box.SlowFirstChild(); child; child = child->NextSibling()) {
    // layout box, 规避LayoutText的场景。
    if (IsA<LayoutBox>(child)) {
      // child不能是包含Layer的。
      if (child->HasLayer()) {
        continue;
      }

      auto layout_box = To<LayoutBox>(child);

      // 从有Layer变成无Layer需要通过Diff删除孩子。
      RenderNode* render_node = layout_box->GetRenderNode();
      if (render_node && !render_node->children().empty()) {
        render_node->updateStackingContextChildren();
      }

      // 视图剪裁，只有需要节点的时候，才插入
      if (!CanBeStripped(child)) {
        collecting_children.push_back(To<LayoutBox>(child)->CreateRenderNodeIfNeeded());
      } else {
        layout_box->ClearRenderNode();
  #if ENABLE_STACKING_CHILD_PRINT
        printf("[blink] strip %d, %s \n", layout_box->GetNode()->getMscTag(), layout_box->ToString().Utf8().c_str());
  #endif
      }
    }

    // 递归孩子
    CollectNormalFlowChildren(*child, collecting_children);
  }
  return;
}

void RenderNode::updateStackingContextChildren() {
  LayoutBox& box_ref = *box_;

  HeapVector<Member<RenderNode>> new_children;

  if (box_ref.IsStacked()) {
    PaintLayer& layer_ref = *box_ref.Layer();
    if (layer_ref.StackingNode()) {
      for (PaintLayer* child_layer : layer_ref.StackingNode()->NegZOrderList()) {
        LayoutBox* child_box = child_layer->GetLayoutBox();
        if (LIKELY(child_box)) {
          new_children.push_back(child_box->CreateRenderNodeIfNeeded());
        }
      }
    }
  }

  // 中间文档流，插入到 PaintLayer 对应的节点。一个辅助函数，获取NormalFlowChildren
  if (box_ref.IsStacked()) {
    CollectNormalFlowChildren(box_ref, new_children);
  }

#if ENABLE_STACKING_CHILD_PRINT
  printf("[blink] child neg: ");
  PaintLayer& layer_ref = *box_ref.Layer();
  if (layer_ref.StackingNode()) {
    for (PaintLayer* child_layer : layer_ref.StackingNode()->PosZOrderList()) {
      PaintLayer& child_layer_ref = *child_layer;
      if (UNLIKELY(child_layer_ref.GetLayoutObject().IsInline())) {
        continue;
      }
      printf("%d ", child_layer->GetLayoutBox()->GetNode()->getMscTag());
    }
  }
  printf("\n");
#endif

  if (box_ref.IsStacked()) {
    PaintLayer& layer_ref = *box_ref.Layer();
    if (layer_ref.StackingNode()) {
      for (PaintLayer* child_layer : layer_ref.StackingNode()->PosZOrderList()) {
        LayoutBox* child_box = child_layer->GetLayoutBox();
        if (LIKELY(child_box)) {
          new_children.push_back(child_box->CreateRenderNodeIfNeeded());
        }
      }
    }
  }
#if ENABLE_STACKING_CHILD_PRINT
    printf("[blink] %d new child: ", box_ref.GetNode()->getMscTag());
    for (auto renderNode : new_children) {
      printf("%d ", renderNode->getLayoutBox().GetNode()->getMscTag());
    }
    printf("\n");
    
    printf("[blink] %d old child: ", box_ref.GetNode()->getMscTag());
    for (auto renderNode : children_) {
      printf("%d ", renderNode->getLayoutBox().GetNode()->getMscTag());
    }
    printf("\n");
#endif
  
  HashSet<Tag> old_tags;
  HashSet<Tag> new_tags;
  HashMap<Tag, int> old_tag2origin_index; // tag 在 old_tags 中原始的 index
  HashMap<Tag, int> old_tag2index; // tag 在 old_tags 中删除元素后的 index

  for (auto& item_ref : children_) {
    item_ref->parent_.Clear();
    old_tags.insert(item_ref->box_->GetNode()->getMscTag());
  }
  for (auto& item_ref : new_children) {
    item_ref->parent_ = this;
    new_tags.insert(item_ref->box_->GetNode()->getMscTag());
  }

  std::vector<RemoveChildViewsCommand::RemoveOp> delete_ops;
  std::vector<InsertChildViewsCommand::InsertOp> insert_ops;

  int remove_count = 0; // 移除的节点数量
  for (auto i = 0; i < children_.size(); i++) {
    Tag tag = children_[i]->box_->GetNode()->getMscTag();
    old_tag2origin_index.insert(tag, i);
    if (!new_tags.Contains(tag)) {
      delete_ops.emplace_back(i);
      remove_count++;
    } else {
      old_tag2index.insert(tag, i - remove_count);
    }
  }

  int real_add_count = 0; // 新增（非移动）的节点数量
  for (auto i = 0; i < new_children.size(); i++) {
    Tag tag = new_children[i]->box_->GetNode()->getMscTag();
    if (!old_tags.Contains(tag)) {
      insert_ops.emplace_back(tag, i);
      real_add_count++;
    } else {
      int old_index = old_tag2index.at(tag);
      if (old_index != i - real_add_count) {
        // 移动节点的场景
        auto original_i = old_tag2origin_index.at(tag);
        delete_ops.emplace_back(original_i);
        insert_ops.emplace_back(tag, i);
      }
    }
  }
  
#if ENABLE_STACKING_CHILD_PRINT
  printf("[blink] %d delete: ", box_ref.GetNode()->getMscTag());
  for (auto op : delete_ops) {
    printf("%d ", op);
  }
  printf("\n");
#endif
  
#if ENABLE_STACKING_CHILD_PRINT
  printf("[blink] %d insert: ", box_ref.GetNode()->getMscTag());
  for (auto op : insert_ops) {
    printf("%d(%d) ", op.index, op.tag);
  }
  printf("\n");
#endif

  std::sort(delete_ops.begin(), delete_ops.end());

//  printf("[op] %d old tags:", box_->GetNode()->getMscTag());
//  for (auto& item : children_) {
//    printf("%d, ", item->box_->GetNode()->getMscTag());
//  }
//  printf("\n");
//
//  printf("[op] %d new tags:", box_->GetNode()->getMscTag());
//  for (auto& item : new_children) {
//    printf("%d, ", item->box_->GetNode()->getMscTag());
//  }
//  printf("\n");
//
//  printf("[op] %d del:", box_->GetNode()->getMscTag());
//  for (auto op : delete_ops) {
//    printf("%d at %d, ", children_.at(op)->box_->GetNode()->getMscTag(), op);
//  }
//  printf("\n");
//
//  printf("[op] %d ins:", box_->GetNode()->getMscTag());
//  for (auto op : insert_ops) {
//    printf("%d at %d, ", op.tag, op.index);
//  }
//  printf("\n");

  children_.clear();
  children_.reserve(new_children.size());
  for (auto& item_ref : new_children) {
    children_.push_back(item_ref);
  }
  Tag my_tag = box_ref.GetNode()->getMscTag();

  Element* element = DynamicTo<Element>(box_->GetNode());
  if (element) {
    auto document_ptr = element->mtDocument().lock();
    if (document_ptr) {
      auto& ui_command_buffer = document_ptr->uiCommandBuffer();
      if (insert_ops.size() > 0) {
        ui_command_buffer.ScheduleCommand(ui_command_buffer.insertChildViewsCommand(my_tag, std::move(insert_ops)));
      }
      if (delete_ops.size() > 0) {
        ui_command_buffer.ScheduleCommand(ui_command_buffer.removeChildViewsCommand(my_tag, std::move(delete_ops)));
      }
    }
  }

  // 因为父节点变化了，子节点坐标可能需要重新计算
  // 标脏新增子节点的坐标（移除的子节点不需要在这里标脏）
  for (auto& item_ref : new_children) {
    if (!old_tag2origin_index.Contains(item_ref->box_->GetNode()->getMscTag())) {
      item_ref->box_->SetGeometryChanged(true, false);
    }
  }
}

void RenderNode::didCreate() {
  auto* element = DynamicTo<Element>(box_->GetNode());
  if (element) {
    auto document_ptr = element->mtDocument().lock();
    if (document_ptr) {
      auto& ui_command_buffer = document_ptr->uiCommandBuffer();
      ui_command_buffer.ScheduleCommand(ui_command_buffer.createViewCommand(element->getMscTag(),
                                                                            element->getPlatformViewName().Utf8(),
                                                                            element->getProps()));
    }
  }
}

void RenderNode::willDestroy() {
  auto* element = DynamicTo<Element>(box_->GetNode());
  if (element) {
    auto document_ptr = element->mtDocument().lock();
    if (document_ptr) {
      auto& ui_command_buffer = document_ptr->uiCommandBuffer();
      std::vector<DeleteViewsCommamnd::DeleteOp> ops;
      ops.push_back(element->getMscTag());
      ui_command_buffer.ScheduleCommand(ui_command_buffer.deleteViewsCommand(std::move(ops)));
    }
  }
}

void RenderNode::update() {
  auto* element = DynamicTo<Element>(box_->GetNode());
  if (UNLIKELY(!element)) {
    return;
  }

  if (getPropsChanged()) {
    auto document_ptr = element->mtDocument().lock();
    if (document_ptr) {
      auto& ui_command_buffer = document_ptr->uiCommandBuffer();
      ui_command_buffer.ScheduleCommand(ui_command_buffer.updateViewCommand(element->getMscTag(),
                                                                            element->getPlatformViewName().Utf8(),
                                                                            element->getProps()));
    }
    setPropsChanged(false);
  }

  if (box_->StyleChanged()) {
    auto document_ptr = element->mtDocument().lock();
    if (document_ptr) {
      auto& ui_command_buffer = document_ptr->uiCommandBuffer();
      std::shared_ptr<const NativeStyle> native_style = ConvertStyle(box_);
      auto command = document_ptr->uiCommandBuffer().updateViewStyleCommand(element->getMscTag(), native_style);
      ui_command_buffer.ScheduleCommand(ui_command_buffer.updateViewStyleCommand(element->getMscTag(), native_style));
    }
    if (element->isText()) {
      box_->SetTextChanged(true);
    }
    box_->SetStyleChanged(false);
  }
}

}; // blink::mt
