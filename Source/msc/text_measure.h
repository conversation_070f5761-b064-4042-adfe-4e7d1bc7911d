//
//  text_measure.h
//  MSCRenderer
//
//  Created by Admin on 2024/10/12.
//

#ifndef text_measure_h
#define text_measure_h

#include "types_def.h"

#include "third_party/blink/renderer/platform/heap/garbage_collected.h"
#include "third_party/blink/renderer/platform/heap/member.h"
#include "third_party/blink/renderer/platform/geometry/layout_unit.h"

#include <unordered_map>

namespace blink::mt {

const void *CreateTextData(LayoutBox *box, LayoutUnit constraint_width);

const void* RetainTextData(const void *text_data);

void ReleaseTextData(const void *text_data);

void CalculateTextSize(LayoutBox *box,
                       LayoutUnit constraint_width,
                       const void *text_data,
                       LayoutUnit *out_width,
                       LayoutUnit *out_height);

void UpdateTextFinalSize(const void* text_data, float width, float height);

void CalculateTextAttachmentFrame(const void* string_data,
                                  Tag attachment_tag,
                                  float* out_x,
                                  float* out_y,
                                  float* out_width,
                                  float* out_height,
                                  bool* out_hidden);

class TextMeasureResult {
public:
  TextMeasureResult(const LayoutUnit& width, const LayoutUnit& height, const void* string_data);

  ~TextMeasureResult();

  const LayoutUnit& getWidth() const;

  const LayoutUnit& getHeight() const;

  const void* getStringData() const;

private:
  LayoutUnit width_;
  LayoutUnit height_;
  const void* string_data_;
};

class TextMeasurer {
public:
  const TextMeasureResult* measure(Tag tag, LayoutBox *text_box, const LayoutUnit& constraint_width);

  void UpdateInlineBoxPosition(Tag tag, LayoutBox* text_box, const LogicalSize& size);

  const TextMeasureResult* getResult(Tag tag) const;

  void clearResults();

private:
  const TextMeasureResult* updateResult(Tag tag, LayoutUnit& width, LayoutUnit& height, const void* data);

  std::unordered_map<int, std::shared_ptr<TextMeasureResult>> cache_;
};

}; // namespace blink::mt

#endif /* text_measure_h */
