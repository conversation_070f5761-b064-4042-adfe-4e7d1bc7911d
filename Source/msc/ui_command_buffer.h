//
//  ui_command_buffer.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/6/23.
//

#include "types_def.h"

#include <string>
#include <vector>
#include <memory>
#include <functional>

#include "logger.h"

namespace blink::mt {

using UICommandCollection = std::vector<std::shared_ptr<UICommand>>;

class UICommandBuffer {
public:
  enum class Opportunity {
    BeforeLayout,
    AfterLayout,
    AfterBatch
  };

 auto createKeyframesAnimationCommand(const PropValueType::Array& tags,
                                      const PropValueType::Array& keyframes,
                                      int duration,
                                      const std::function<void()>& callback) {
   return std::make_shared<CreateKeyframesAnimationCommand>(
       cmd_id_base_++, tags, keyframes, duration, callback);
 }

 auto clearKeyframesAnimationCommand(const PropValueType::Array& tags,
                                     const PropValue& options,
                                     const std::function<void()>& callback) {
   return std::make_shared<ClearKeyframesAnimationCommand>(cmd_id_base_++, tags,
                                                           options, callback);
 }

  UICommandBuffer() : cmd_id_base_(0), in_batch_(false), has_flush_ui_tasks_command_(false), callback_(nullptr) {}

  std::shared_ptr<CreateViewCommamnd> createViewCommand(Tag tag,
                                                        const std::string& view_name,
                                                        const std::shared_ptr<const Props>& props) {
    return std::make_shared<CreateViewCommamnd>(cmd_id_base_++,
                                                UICommandType::CreateView,
                                                tag,
                                                view_name,
                                                props);
  }

  std::shared_ptr<UpdateViewCommamnd> updateViewCommand(Tag tag,
                                                        const std::string& view_name,
                                                        const std::shared_ptr<const Props>& props) {
    return std::make_shared<UpdateViewCommamnd>(cmd_id_base_++,
                                                UICommandType::UpdateView,
                                                tag,
                                                view_name,
                                                props);
  }

  std::shared_ptr<DeleteViewsCommamnd> deleteViewsCommand(std::vector<DeleteViewsCommamnd::DeleteOp>&& delete_ops) {
    return std::make_shared<DeleteViewsCommamnd>(cmd_id_base_++, UICommandType::DeleteView, std::move(delete_ops));
  }

  std::shared_ptr<InsertChildViewsCommand>
  insertChildViewsCommand(Tag tag,
                        std::vector<InsertChildViewsCommand::InsertOp>&& insert_ops) {
    return std::make_shared<InsertChildViewsCommand>(cmd_id_base_++,
                                                     UICommandType::InsertChildViews,
                                                     tag,
                                                     std::move(insert_ops));
  }

  std::shared_ptr<RemoveChildViewsCommand>
  removeChildViewsCommand(Tag tag,
                        std::vector<RemoveChildViewsCommand::RemoveOp>&& remove_ops) {
    return std::make_shared<RemoveChildViewsCommand>(cmd_id_base_++,
                                                     UICommandType::RemoveChildViews,
                                                     tag,
                                                     std::move(remove_ops));
  }

  std::shared_ptr<UpdateViewStyleCommamnd>
  updateViewStyleCommand(Tag tag, const shared_ptr<const NativeStyle>& style) {
    return std::make_shared<UpdateViewStyleCommamnd>(cmd_id_base_++,
                                                     UICommandType::UpdateViewStyle,
                                                     tag, style);
  }

  std::shared_ptr<UpdateViewTransformCommamnd> updateViewTransformCommand(Tag tag, const NativeTransforms& transforms) {
    MSC_RENDERER_LOG_DEBUG("create command updateViewTransform");
    return std::make_shared<UpdateViewTransformCommamnd>(cmd_id_base_++,
                                                         UICommandType::UpdateViewTransform,
                                                         tag,
                                                         transforms);
  }

  std::shared_ptr<UpdateViewFrameCommamnd> updateViewFrameCommand(Tag tag,
                                                                  float x,
                                                                  float y,
                                                                  float width,
                                                                  float height,
                                                                  bool hidden) {
    return std::make_shared<UpdateViewFrameCommamnd>(cmd_id_base_++,
                                                     UICommandType::UpdateViewFrame,
                                                     tag,
                                                     x,
                                                     y,
                                                     width,
                                                     height,
                                                     hidden);
  }

  std::shared_ptr<UpdateTextCommamnd> updateTextCommand(Tag tag,
                                                        float x,
                                                        float y,
                                                        float width,
                                                        float height,
                                                        float top_inset,
                                                        float left_inset,
                                                        float bottom_inset,
                                                        float right_inset,
                                                        const void* string_data) {
    return std::make_shared<UpdateTextCommamnd>(cmd_id_base_++,
                                                UICommandType::UpdateText,
                                                tag,
                                                x,
                                                y,
                                                width,
                                                height,
                                                top_inset,
                                                left_inset,
                                                bottom_inset,
                                                right_inset,
                                                string_data);
  }

  std::shared_ptr<BatchDidFinishCommand> batchDidFinishCommand(LayoutReason reason) {
    return std::make_shared<BatchDidFinishCommand>(cmd_id_base_++, UICommandType::BatchDidFinish, reason);
  }

  std::shared_ptr<FlushUITasksCommand> CreateFlushUITasksCommand() {
    return std::make_shared<FlushUITasksCommand>(cmd_id_base_++, UICommandType::FlushUITasks);
  }

  void SetInBatch(bool in_batch) {
    in_batch_ = in_batch;
  }

  void ScheduleCommand(std::shared_ptr<UICommand> command) {
    switch (command->type) {
    case UICommandType::CreateView:
      create_view_commands_.push_back(command);
      break;
    case UICommandType::UpdateView:
      update_view_commands_.push_back(command);
      break;
    case UICommandType::DeleteView:
      delete_views_commands_.push_back(command);
      break;
    case UICommandType::InsertChildViews:
      insert_child_views_commands_.push_back(command);
      break;
    case UICommandType::RemoveChildViews:
      remove_child_views_commands_.push_back(command);
      break;
    case UICommandType::UpdateViewStyle:
      update_view_style_commands_.push_back(command);
      break;
    case UICommandType::UpdateViewTransform:
      update_view_transform_commands_.push_back(command);
      break;
    case UICommandType::UpdateViewFrame:
      update_view_frame_commands_.push_back(command);
      break;
    case UICommandType::UpdateText:
      update_text_commands_.push_back(command);
      break;
    default:
      if (command->type == UICommandType::FlushUITasks) {
        if (has_flush_ui_tasks_command_) {
          break;
        }
        has_flush_ui_tasks_command_ = true;
      }
      after_batch_commands_.push_back(command);
      if (!in_batch_) {
        SendCommands(Opportunity::AfterBatch);
      }
      break;
    }
  }

  void SendCommands(Opportunity opportunity) {
    auto commands = std::make_shared<UICommandCollection>();
    if (opportunity == Opportunity::BeforeLayout) {
      AddCommands(create_view_commands_, commands);
      AddCommands(update_view_commands_, commands);
      AddCommands(update_view_style_commands_, commands);
      AddCommands(remove_child_views_commands_, commands);
      AddCommands(insert_child_views_commands_, commands);
      AddCommands(delete_views_commands_, commands);
    } else if (opportunity == Opportunity::AfterLayout) {
      AddCommands(update_view_frame_commands_, commands);
      AddCommands(update_text_commands_, commands);
      AddCommands(update_view_transform_commands_, commands);
      AddCommands(batch_did_finish_commands_, commands);
    } else {
      has_flush_ui_tasks_command_ = false;
      AddCommands(after_batch_commands_, commands);
    }

    if (commands->size() > 0) {
      callback_(commands);
    }
  }

  void setCallback(UICommandBufferCallback callback) {
    callback_ = callback;
  }

private:
  void AddCommands(UICommandCollection& commands, const UICommands& target) {
    for (const std::shared_ptr<UICommand>& command : commands) {
      target->push_back(command);
    }
    commands.clear();
  }

  UICommandID cmd_id_base_;
  bool in_batch_;
  bool has_flush_ui_tasks_command_;
  UICommandCollection create_view_commands_;
  UICommandCollection update_view_commands_;
  UICommandCollection update_view_style_commands_;
  UICommandCollection update_view_frame_commands_;
  UICommandCollection update_text_commands_;
  UICommandCollection update_view_transform_commands_;
  UICommandCollection remove_child_views_commands_;
  UICommandCollection insert_child_views_commands_;
  UICommandCollection delete_views_commands_;
  UICommandCollection after_batch_commands_;
  UICommandCollection batch_did_finish_commands_;
  UICommandBufferCallback callback_;
};

}; // blink::mt
