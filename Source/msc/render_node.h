//
//  render_node.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/04/03.
//

#ifndef render_node_h
#define render_node_h

#include "types_def.h"

namespace blink::mt {

class UICommandBuffer;

class RenderNode : public GarbageCollected<RenderNode> {
public:
  struct Rect {
    LayoutUnit x;
    LayoutUnit y;
    LayoutUnit width;
    LayoutUnit height;

    Rect(): x(0), y(0), width(0), height(0) {}

    bool operator==(const Rect& other) const {
      return x == other.x &&
             y == other.y &&
             width == other.width &&
             height == other.height;
    }
  };

  explicit RenderNode(LayoutBox* box):
    rect_(Rect()),
    props_changed_(false),
    has_transform_(false),
    box_(box),
    parent_(nullptr),
    children_(::blink::HeapVector<Member<RenderNode>>()) {
  }

  LayoutBox& getLayoutBox() const { return *box_; }

  RenderNode* parent() {
    return parent_;
  }

  const ::blink::HeapVector<Member<RenderNode>>& children() const {
    return children_;
  }

  void updateStackingContextChildren();

  const Rect& getRect() const {
    return rect_;
  }

  void setRect(Rect&& rect) {
    rect_ = std::move(rect);
  }

  bool getPropsChanged() const {
    return props_changed_;
  }

  void setPropsChanged(bool props_changed) {
    props_changed_ = props_changed;
  }

  bool GetHasTransform() const {
    return has_transform_;
  }

  void SetHasTransform(bool has_transform) {
    has_transform_ = has_transform;
  }

  void didCreate();

  void willDestroy();

  void update();

  void Trace(blink::Visitor* visitor) const {
    visitor->Trace(box_);
    visitor->Trace(parent_);
    visitor->Trace(children_);
  }

private:
  Rect rect_;
  bool props_changed_;
  bool has_transform_;
  Member<LayoutBox> box_;
  Member<RenderNode> parent_;
  ::blink::HeapVector<Member<RenderNode>> children_;
};

}; // namespace blink::mt

#endif
