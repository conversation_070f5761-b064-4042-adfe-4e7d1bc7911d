//
//  thread.cc
//  MSCRenderer
//
//  Created by Admin on 2025/4/15.
//

#include "blink.h"
#include "worker_thread.h"

namespace blink::mt {

bool isWorkerThread() {
  return WorkerThread::isWorkerThread();
}

void runInWorkerThread(std::function<void()> block) {
  WorkerThread::thread().addTask(block);
}

void forceGarbageCollection(std::function<void()> callback) {
  WorkerThread::thread().gc(callback);
}

}; // namespace blink::mt
