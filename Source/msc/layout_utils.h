//
//  layout_utils.h
//  MSCRenderer
//
//  Created by Admin on 2024/10/14.
//

#ifndef layout_utils_h
#define layout_utils_h

#include "types_def.h"

#include <memory>
#include <vector>
#include <functional>

namespace blink::mt {

Tag GenerateMscTag(void);

struct LayoutTreeItem {
public:
  LayoutObject* node;
  int depth;
};

void TraverseLayoutTree(LayoutObject *node,
                        std::function<void(const LayoutTreeItem& item, bool* skip_children)> callback);

struct RenderTreeItem {
public:
  RenderNode* node;
  int depth;
};

void TraverseRenderTree(RenderNode *node,
                        std::function<void(const RenderTreeItem& item, bool* skip_children)> callback);

}; // namespace blink::mt

#endif
