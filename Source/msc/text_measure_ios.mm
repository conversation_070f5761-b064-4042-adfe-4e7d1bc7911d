//
//  text_measure_helper.m
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/2/12.
//

#ifdef __APPLE__

#include "device.h"
#include "style_convertor_helper.h"
#include "layout_utils.h"

#include "third_party/blink/renderer/core/html/html_element.h"
#include "third_party/blink/renderer/core/layout/layout_text.h"
#include "third_party/blink/renderer/core/layout/layout_inline.h"
#include "third_party/blink/renderer/core/layout/layout_box.h"
#include "third_party/blink/renderer/core/layout/constraint_space_builder.h"
#include "third_party/blink/renderer/core/layout/block_node.h"

#import <Foundation/Foundation.h>

@interface MSCTextAttachment : NSTextAttachment

@property (nonatomic, assign) blink::mt::Tag mscTag;

@end

@implementation MSCTextAttachment
@end

namespace blink::mt {

namespace {

UIFontWeight WeightOfFont(UIFont *font)
{
  auto traits = (__bridge_transfer NSDictionary *)CTFontCopyTraits((CTFontRef)font);
  return [traits[UIFontWeightTrait] doubleValue];
}

BOOL IsItalicFont(UIFont *font)
{
  return (CTFontGetSymbolicTraits((CTFontRef)font) & kCTFontTraitItalic) != 0;
}

//BOOL IsCondensedFont(UIFont *font)
//{
//  return (CTFontGetSymbolicTraits((CTFontRef)font) & kCTFontTraitCondensed) != 0;
//}

NSArray<NSString *> *FontNamesForFamilyName(NSString *family_name)
{
  static NSCache<NSString *, NSArray<NSString *> *> *cache;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    cache = [NSCache new];
    [NSNotificationCenter.defaultCenter
     addObserverForName:(NSNotificationName)kCTFontManagerRegisteredFontsChangedNotification
     object:nil
     queue:nil
     usingBlock:^(NSNotification *) {
      [cache removeAllObjects];
    }];
  });
  
  auto names = [cache objectForKey:family_name];
  if (!names) {
    names = [UIFont fontNamesForFamilyName:family_name] ?: [NSArray new];
    [cache setObject:names forKey:family_name];
  }
  return names;
}

UIFont *GetCachedFont(NSString* ns_family_name, int font_size, UIFontWeight ns_font_weight, bool is_italic) {
  static NSCache<NSValue *, UIFont *> *font_cache;
  static dispatch_once_t onceToken;
  dispatch_once(&onceToken, ^{
    font_cache = [NSCache new];
    [NSNotificationCenter.defaultCenter
     addObserverForName:(NSNotificationName)kCTFontManagerRegisteredFontsChangedNotification
     object:nil
     queue:nil
     usingBlock:^(NSNotification *) {
      [font_cache removeAllObjects];
    }];
  });

  struct CacheKey {
    NSString* family_name;
    UIFontWeight weight;
    int size;
    bool is_italic;
  };

  CacheKey key{ns_family_name, ns_font_weight, font_size, is_italic};
  NSValue *cache_key = [[NSValue alloc] initWithBytes:&key objCType:@encode(CacheKey)];
  UIFont *font = [font_cache objectForKey:cache_key];
  if (font) {
    return font;
  }

  NSArray<NSString *> *names = FontNamesForFamilyName(ns_family_name);

  // Gracefully handle being given a font name rather than font family, for
  // example: "Helvetica Light Oblique" rather than just "Helvetica".
  if (names.count == 0) {
    font = [UIFont fontWithName:ns_family_name size:font_size];
    if (font) {
      names = FontNamesForFamilyName(font.familyName);
    }
  }

  // Get the closest font that matches the given weight for the fontFamily
  CGFloat closest_weight = INFINITY;
  for (NSString *name in names) {
    UIFont *match = [UIFont fontWithName:name size:font_size];
    if (is_italic == IsItalicFont(match)) {
      CGFloat test_weight = WeightOfFont(match);
      if (ABS(test_weight - ns_font_weight) < ABS(closest_weight - ns_font_weight)) {
        font = match;
        closest_weight = test_weight;
      }
    }
  }

  // If we still don't have a match at least return the first font in the fontFamily
  // This is to support built-in font Zapfino and other custom single font families like Impact
  if (UNLIKELY(!font && names.count > 0)) {
    font = [UIFont fontWithName:names[0] size:font_size];
  }
  if (UNLIKELY(!font)) {
    font = [UIFont systemFontOfSize:font_size weight:ns_font_weight];
  }

  [font_cache setObject:font forKey:cache_key];

  return font;
}

UIFont *CreateFont(const AtomicString &family_name,
                   int font_size,
                   FontSelectionValue font_weight,
                   FontSelectionValue font_style) {
  // Defaults
  static NSString *default_font_family;
  static dispatch_once_t token;
  dispatch_once(&token, ^{
    default_font_family = [UIFont systemFontOfSize:14].familyName;
  });
  
  // Get font attributes
  NSString *ns_family_name = [NSString stringWithUTF8String:family_name.Utf8().c_str()];
  if (ns_family_name.length == 0) {
    ns_family_name = @"PingFang SC";
  }
  auto ns_font_weight = mt::StyleConvertor::ConvertFontWeight(font_weight);
  auto is_italic = font_style != kNormalSlopeValue;

  return GetCachedFont(ns_family_name, font_size, ns_font_weight, is_italic);
}

NSMutableDictionary *CreateTextAttributes(const ComputedStyle &style) {
  NSMutableDictionary *attributes = [NSMutableDictionary dictionary];
  
  UIFont *font = CreateFont(style.GetFontDescription().Family().FamilyName(),
                            style.FontSize(),
                            style.GetFontDescription().Weight(),
                            style.GetFontDescription().Style());
  attributes[NSFontAttributeName] = font;
  attributes[NSForegroundColorAttributeName] = mt::StyleConvertor::Convert(style.GetCurrentColor());
  
  auto letter_spacing = style.LetterSpacing();
  if (!isnan(letter_spacing)) {
    attributes[NSKernAttributeName] = @(letter_spacing);
  }
  
  auto decoration = style.GetTextDecorationLine();
  if (static_cast<unsigned>(decoration) & static_cast<unsigned>(TextDecorationLine::kUnderline)) {
    attributes[NSUnderlineStyleAttributeName] = @(NSUnderlineStyleSingle);
  }
  if (static_cast<unsigned>(decoration) & static_cast<unsigned>(TextDecorationLine::kLineThrough)) {
    attributes[NSStrikethroughStyleAttributeName] = @(NSUnderlineStyleSingle);
  }
  
  NSMutableParagraphStyle *ps = [NSMutableParagraphStyle new];
  ps.alignment = mt::StyleConvertor::Convert(style.GetTextAlign());
  
  auto writing_dir = style.GetWritingDirection();
  if (writing_dir.IsLtr()) {
    ps.baseWritingDirection = NSWritingDirectionLeftToRight;
  } else {
    ps.baseWritingDirection = NSWritingDirectionRightToLeft;
  }
  
  auto line_height = style.ComputedLineHeight();
  if (!isnan(line_height)) {
    ps.minimumLineHeight = line_height;
    ps.maximumLineHeight = line_height;
  }
  
  attributes[NSParagraphStyleAttributeName] = ps;
  
  return attributes;
}

void PostProcessAttributedText(NSMutableAttributedString *attributed_text)
{
  __block CGFloat maximum_line_height = 0;
  
  [attributed_text enumerateAttribute:NSParagraphStyleAttributeName
                              inRange:NSMakeRange(0, attributed_text.length)
                              options:NSAttributedStringEnumerationLongestEffectiveRangeNotRequired
                           usingBlock:
       ^(NSParagraphStyle *paragraphStyle, __unused NSRange range, __unused BOOL *stop) {
    if (!paragraphStyle) {
        return;
    }
    
    maximum_line_height = MAX(paragraphStyle.maximumLineHeight, maximum_line_height);
  }
  ];
  
  if (maximum_line_height == 0) {
    // `lineHeight` was not specified, nothing to do.
    return;
  }
  
  __block CGFloat maximum_font_line_height = 0;
  
  [attributed_text enumerateAttribute:NSFontAttributeName
                              inRange:NSMakeRange(0, attributed_text.length)
                              options:NSAttributedStringEnumerationLongestEffectiveRangeNotRequired
                           usingBlock:
     ^(UIFont *font, NSRange range, __unused BOOL *stop) {
    if (!font) {
        return;
    }
    
    if (maximum_font_line_height <= font.lineHeight) {
        maximum_font_line_height = font.lineHeight;
    }
  }
  ];
  
  if (maximum_line_height < maximum_font_line_height) {
    return;
  }
  
  CGFloat base_line_offset = maximum_line_height / 2.0 - maximum_font_line_height / 2.0;
  [attributed_text addAttribute:NSBaselineOffsetAttributeName
                          value:@(base_line_offset)
                          range:NSMakeRange(0, attributed_text.length)];
}

};

const void *CreateTextData(LayoutBox *box, LayoutUnit constraint_width) {
  NSTextStorage *attributed_string = [NSTextStorage new];

  TraverseLayoutTree(box, ^(const mt::LayoutTreeItem& item, bool* skip_children) {
    if (IsA<LayoutView>(item.node)) {
      return;
    }

    LayoutObject *layout_object = item.node;

    if (IsA<LayoutInline>(layout_object) || IsA<LayoutText>(layout_object)) {
      layout_object->ClearNeedsLayout();
    }

    if (IsA<LayoutText>(layout_object)) {
      auto layout_text = To<LayoutText>(layout_object);
      auto &text = layout_text->TransformedText();
      auto *ns_text = [NSString stringWithUTF8String:text.Utf8().c_str()];
      auto parent_layout_object = layout_object->Parent();
      auto *attrs = CreateTextAttributes(parent_layout_object->StyleRef());
      //TODO: jz - key暂时写死字符串，等后续迁到MSCNativeRenderer后再改为变量名
      attrs[@"MSCTextAttributesTagAttributeName"] = @(parent_layout_object->GetNode()->getMscTag());
      [attributed_string appendAttributedString:[[NSAttributedString alloc] initWithString:ns_text attributes:attrs]];
    } else if (UNLIKELY(To<Element>(layout_object->GetNode())->componentName() == "-mt-break")) {
      auto *ns_text = @"\n";
      auto *attrs = CreateTextAttributes(layout_object->StyleRef());
      //TODO: jz - key暂时写死字符串，等后续迁到MSCNativeRenderer后再改为变量名
      attrs[@"MSCTextAttributesTagAttributeName"] = @(layout_object->GetNode()->getMscTag());
      [attributed_string appendAttributedString:[[NSAttributedString alloc] initWithString:ns_text attributes:attrs]];
    } else if (layout_object != box && IsA<LayoutBlock>(layout_object)) {
      *skip_children = true;

      auto layout_box = To<LayoutBox>(layout_object);
      auto &style = layout_box->StyleRef();

      layout_box->SetGeometryChanged(true, true);

      bool width_is_fixed = false;
      auto width = -1;
      if (style.Width().GetType() == Length::Type::kFixed) {
        width_is_fixed = true;
        width = style.Width().Value();
      }
      bool height_is_fixed = false;
      auto height = -1;
      if (style.Height().GetType() == Length::Type::kFixed) {
        height_is_fixed = true;
        height = style.Height().Value();
      }
      if (constraint_width != kIndefiniteSize && (!width_is_fixed || constraint_width.ToFloat() < width)) {
        width = constraint_width.ToFloat();
      }

      ConstraintSpaceBuilder builder(
          style.GetWritingMode(), style.GetWritingDirection(),
          /* is_new_fc */ true, /* adjust_inline_size_if_needed */ false);
      builder.SetAvailableSize(LogicalSize(LayoutUnit(width >= 0 ? width : FLT_MAX),
                                           LayoutUnit(height >= 0 ? height : FLT_MAX)));
      if (width_is_fixed) {
        builder.SetIsFixedInlineSize(true);
      }
      if (height_is_fixed) {
        builder.SetIsFixedBlockSize(true);
      }
      BlockNode(layout_box).Layout(builder.ToConstraintSpace());

      MSCTextAttachment *attachment = [MSCTextAttachment new];
      attachment.bounds = (CGRect){CGPointZero, CGSizeMake(layout_box->Size().width.ToFloat(),
                                                           layout_box->Size().height.ToFloat())};
      attachment.image = [UIImage new];
      attachment.mscTag = layout_object->GetNode()->getMscTag();
      [attributed_string appendAttributedString:[NSAttributedString attributedStringWithAttachment:attachment]];
    }
  });

  PostProcessAttributedText(attributed_string);

  return CFAutorelease((__bridge_retained void *)attributed_string);
}

const void* RetainTextData(const void *text_data) {
  if (text_data) {
    return CFRetain(text_data);
  }
  return nullptr;
}

void ReleaseTextData(const void *text_data) {
  if (text_data) {
    CFRelease(text_data);
  }
}

void CalculateTextSize(LayoutBox *box, LayoutUnit constraint_width, const void *text_data, LayoutUnit *out_width, LayoutUnit *out_height) {
  if (!text_data) {
    *out_width = LayoutUnit();
    *out_height = LayoutUnit();
    return;
  }
  NSTextStorage *text_storage = (__bridge NSTextStorage *)text_data;
  NSLayoutManager *layout_manager;
  NSTextContainer *text_container;
  CGSize new_size = CGSizeMake(constraint_width != kIndefiniteSize ? constraint_width.ToFloat() : FLT_MAX, FLT_MAX);

  if (!text_storage.layoutManagers.firstObject) {
    layout_manager = [NSLayoutManager new];
    layout_manager.usesFontLeading = NO;
    text_container = [[NSTextContainer alloc] initWithSize:new_size];
    text_container.lineFragmentPadding = 0.0; // Note, the default value is 5.
    text_container.maximumNumberOfLines = /*_maximumNumberOfLines*/0;
    [layout_manager addTextContainer:text_container];
    [text_storage addLayoutManager:layout_manager];

    auto renderStyle = box->Style();
    // 此处与浏览器行为不一致，默认显示省略号，浏览器行为中默认显示截断
    // MSC历史原因导致默认显示省略号，后续这块改动需要周知所有业务进行回归
    // 浏览器行为中，word-break=normal显示截断，其他值为省略号
    NSLineBreakMode lineBreakMode = NSLineBreakByTruncatingTail;
    if (renderStyle->TextOverflow() == ETextOverflow::kClip) {
      lineBreakMode = NSLineBreakByWordWrapping;
    } else if (renderStyle->TextOverflow() == ETextOverflow::kEllipsis) {
      lineBreakMode = NSLineBreakByTruncatingTail;
    }
    if (renderStyle->BoxOrient() == EBoxOrient::kVertical
        && renderStyle->OverflowX() == EOverflow::kHidden
        && renderStyle->OverflowY() == EOverflow::kHidden) {
      int line = renderStyle->WebkitLineClamp();
      text_container.maximumNumberOfLines = line;
      text_container.lineBreakMode = lineBreakMode;
    } else {
      text_container.maximumNumberOfLines = 0;
      text_container.lineBreakMode = lineBreakMode;
    }
    if (renderStyle->WhiteSpace() == EWhiteSpace::kNowrap) {
      text_container.maximumNumberOfLines = 1;
    }
  } else {
    layout_manager = text_storage.layoutManagers.firstObject;
    text_container = layout_manager.textContainers.firstObject;
    if (!CGSizeEqualToSize(text_container.size, new_size)) {
      text_container.size = new_size;
    }
  }

  [layout_manager ensureLayoutForTextContainer:text_container];
  auto size = [layout_manager usedRectForTextContainer:text_container].size;
  *out_width = LayoutUnit::FromFloatCeil(size.width);
  *out_height = LayoutUnit::FromFloatCeil(size.height);
}

void UpdateTextFinalSize(const void* text_data, float width, float height) {
  NSTextStorage *text_storage = (__bridge NSTextStorage *)text_data;
  NSLayoutManager* layout_manager = text_storage.layoutManagers.firstObject;
  NSTextContainer* text_container = layout_manager.textContainers.firstObject;
  text_container.size = CGSizeMake(width, height);
}

void CalculateTextAttachmentFrame(const void* string_data,
                                  Tag attachment_tag,
                                  float* out_x,
                                  float* out_y,
                                  float* out_width,
                                  float* out_height,
                                  bool* out_hidden) {
  if (!string_data) {
    return;
  }

  NSTextStorage* string = (__bridge NSTextStorage *)string_data;

  NSLayoutManager* layout_manager = string.layoutManagers.firstObject;
  NSTextContainer* text_container = layout_manager.textContainers.firstObject;
  __block NSRange attachment_range;
  __block MSCTextAttachment *attachment;
  [string enumerateAttribute:NSAttachmentAttributeName
                     inRange:NSMakeRange(0, string.length)
                     options:0
                  usingBlock:^(id value, NSRange range, BOOL *stop) {
    if (((MSCTextAttachment *)value).mscTag == attachment_tag) {
      *stop = YES;
      attachment_range = range;
      attachment = value;
    }
  }];

  if (LIKELY(attachment)) {
    CGRect frame = [layout_manager boundingRectForGlyphRange:attachment_range inTextContainer:text_container];
    if (!CGRectEqualToRect(frame, CGRectZero)) {
      CGSize size = attachment.bounds.size;
      UIFont *font = [string attribute:NSFontAttributeName atIndex:attachment_range.location effectiveRange:nil];
      frame = {{
        frame.origin.x,
        frame.origin.y + frame.size.height - size.height + font.descender
      }, {
        size.width,
        size.height
      }};

      NSRange truncated_range = [layout_manager truncatedGlyphRangeInLineFragmentForGlyphAtIndex:attachment_range.location];
      BOOL view_is_truncated = NSIntersectionRange(attachment_range, truncated_range).length != 0;

      *out_x = frame.origin.x;
      *out_y = frame.origin.y;
      *out_width = frame.size.width;
      *out_height = frame.size.height;
      *out_hidden = view_is_truncated;
      return;
    }
  }

  *out_x = 0;
  *out_y = 0;
  *out_width = 0;
  *out_height = 0;
  *out_hidden = true;
}

}; // namespace blink

#endif
