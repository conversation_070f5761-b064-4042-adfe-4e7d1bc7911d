//
//  MSCTextMeasure.m
//  MSCRenderer
//
//  Created by Admin on 2024/10/12.
//

#include "text_measure.h"

#include "types_def.h"
#include "layout_utils.h"

namespace blink::mt {

TextMeasureResult::TextMeasureResult(const LayoutUnit& width, const LayoutUnit& height, const void* string_data):
  width_(width), height_(height), string_data_(RetainTextData(string_data)) {
}

TextMeasureResult::~TextMeasureResult() {
  ReleaseTextData(string_data_);
}

const LayoutUnit& TextMeasureResult::getWidth() const {
  return width_;
}

const LayoutUnit& TextMeasureResult::getHeight() const {
  return height_;
}

const void* TextMeasureResult::getStringData() const {
  return string_data_;
}

const TextMeasureResult* TextMeasurer::measure(Tag tag, LayoutBox *text_box, const LayoutUnit& constraint_width) {
  const TextMeasureResult* result_ptr = getResult(tag);
  const void* data = nullptr;
  if (result_ptr) {
    data = result_ptr->getStringData();
    if (constraint_width != kIndefiniteSize && constraint_width < result_ptr->getWidth()) {
      result_ptr = nullptr;
      data = nullptr;
    }
  }

  if (!result_ptr) {
    data = CreateTextData(text_box, constraint_width);
    text_box->SetTextChanged(true);
    LayoutUnit width, height;
    CalculateTextSize(text_box, constraint_width, data, &width, &height);
    result_ptr = updateResult(tag, width, height, data);
    data = result_ptr->getStringData();
  } else if (text_box->TextChangeState() == LayoutBox::TextChangeState::Init) {
    text_box->SetTextChanged(true);
    data = result_ptr->getStringData();
  }

  return result_ptr;
}

void TextMeasurer::UpdateInlineBoxPosition(Tag tag, LayoutBox* text_box, const LogicalSize& size) {
  auto* result = getResult(tag);

  auto* style = text_box->Style();
  auto top_space = style->BorderTopWidth() + style->PaddingTop().Value();
  auto left_space = style->BorderLeftWidth() + style->PaddingLeft().Value();
  auto bottom_space = style->BorderBottomWidth() + style->PaddingBottom().Value();
  auto right_space = style->BorderRightWidth() + style->PaddingRight().Value();

  float width = size.inline_size.ToFloat() - left_space - right_space;
  float height = size.block_size.ToFloat() - top_space - bottom_space;
  UpdateTextFinalSize(result->getStringData(), width, height);

  TraverseLayoutTree(text_box, [result, &left_space, &top_space, text_box]
                     (const mt::LayoutTreeItem& item, bool* skip_children) {
    LayoutBox* box = DynamicTo<LayoutBox>(item.node);
    if (!box || box == text_box) {
      return;
    }

    float x, y, width, height;
    bool hidden;
    CalculateTextAttachmentFrame(result->getStringData(), box->GetNode()->getMscTag(), &x, &y, &width, &height, &hidden);
    box->SetLocation(LayoutPoint(LayoutUnit(left_space + x), LayoutUnit(top_space + y)));
    box->SetHidden(hidden);

    *skip_children = true;
  });
}

const TextMeasureResult* TextMeasurer::getResult(Tag tag) const {
  auto iter = cache_.find(tag);
  if (iter != cache_.end()) {
    return iter->second.get();
  }
  return nullptr;
}

const TextMeasureResult* TextMeasurer::updateResult(Tag tag, LayoutUnit& width, LayoutUnit& height, const void* data) {
  auto result = std::make_shared<TextMeasureResult>(width, height, data);
  return cache_.emplace(tag, std::move(result)).first->second.get();
}

void TextMeasurer::clearResults() {
  cache_.clear();
}

}; // namespace blink::mt
