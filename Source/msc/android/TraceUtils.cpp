//
// Created by <PERSON><PERSON><PERSON> on 2025/3/17.
//

#include "TraceUtils.h"
#include <iostream>
#include <chrono>
#include <ctime>
#include <sys/syscall.h>
#include <unistd.h>
#include <cstdint>
#include <sstream>
#include "system_trace.h"



#ifndef __APPLE__
#include "NativeLog.h"

int64_t getTime() {
    struct timespec ts;
#ifdef __APPLE__
    clock_gettime(CLOCK_MONOTONIC, &ts);
#else
    clock_gettime(CLOCK_BOOTTIME, &ts);
#endif
    return ts.tv_sec * 1000000000LL + ts.tv_nsec;
}

long getUnixTime() {
    // 获取当前时间点
    auto now = std::chrono::system_clock::now();
    // 将时间点转换为 Unix 时间戳（毫秒）
    auto unix_timestamp_ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()).count();

    return unix_timestamp_ms;
}

// Keep legacy event collection for backward compatibility
//std::vector<TraceEvent> events;


void traceBegin(const char *sectionName) {
    // Use system_trace.h for actual tracing
    blink::atrace::BeginTrace(sectionName);

    // Keep legacy event collection for backward compatibility
//    TraceEvent event{};
//    event.type = 1;
//    event.name = sectionName;
//    event.time = getTime();
//    event.unixTs = getUnixTime();
//    events.push_back(event);
}

void traceEnd(const char *sectionName) {
    // Use system_trace.h for actual tracing
    blink::atrace::EndTrace();

    // Keep legacy event collection for backward compatibility
//    TraceEvent event{};
//    event.type = 2;
//    event.name = sectionName;
//    event.time = getTime();
//    event.unixTs = getUnixTime();
//    events.push_back(event);
}

void instance(const char *className) {
    // For instance events, we can use a begin/end pair with system_trace.h
//    blink::atrace::BeginTrace(className);
//    blink::atrace::EndTrace();

//    // Keep legacy event collection for backward compatibility
//    TraceEvent event{};
//    event.type = 3;
//    event.name = className;
//    event.time = getTime();
//    events.push_back(event);
}

void duration(const char *sectionName, long duration) {
//    // For duration events, we can use a begin/end pair with system_trace.h
//    // Note: system_trace.h doesn't support custom durations, so we just mark the event
//    blink::atrace::BeginTrace(sectionName);
//    blink::atrace::EndTrace();
//
//    // Keep legacy event collection for backward compatibility
//    TraceEvent event{};
//    event.type = 4;
//    event.name = sectionName;
//    event.time = getTime();
//    event.unixTs = getUnixTime();
//    event.duration = duration;
//    events.push_back(event);
}

void getAllEvents(std::vector<std::string> &result) {
//    for (auto event : events) {
//        std::stringstream ss;
//        ss << "{ \"type\": " << event.type << ", \"name\": \"" << event.name << "\", \"time\": " << event.time << ", \"duration\": " << event.duration << ", \"unixTs\": " << event.unixTs << "}";
//        result.push_back(ss.str());
//    }
//    events.clear();
}

#endif


