#include "TraceRecorder.h"
#include "NativeLog.h"
#include <cstring>
#include <bit>

namespace trace {

TraceRecorder::TraceRecorder() : enabled_levels_(TRACE_LEVEL_NONE) {
    // 构造函数实现
}

void TraceRecorder::setEnabledLevels(uint8_t enabled_mask) {
    enabled_levels_.store(enabled_mask);
    LOGI("TraceRecorder: Set enabled levels mask - 0x%02X (P0:%d, P1:%d, P2:%d)",
         enabled_mask,
         (enabled_mask & TRACE_LEVEL_P0) != 0,
         (enabled_mask & TRACE_LEVEL_P1) != 0,
         (enabled_mask & TRACE_LEVEL_P2) != 0);
}

void TraceRecorder::disableAll() {
    enabled_levels_.store(TRACE_LEVEL_NONE);
    LOGI("TraceRecorder: All trace levels disabled");
}

bool TraceRecorder::isLevelEnabled(TraceLevel level) {
    uint8_t current_mask = enabled_levels_.load();
    switch (level) {
        case TraceLevel::P0:
            return (current_mask & TRACE_LEVEL_P0) != 0;
        case TraceLevel::P1:
            return (current_mask & TRACE_LEVEL_P1) != 0;
        case TraceLevel::P2:
            return (current_mask & TRACE_LEVEL_P2) != 0;
        default:
            return false;
    }
}

bool TraceRecorder::isTraceEnabled() {
    return enabled_levels_.load() != TRACE_LEVEL_NONE;
}

uint8_t TraceRecorder::getEnabledLevels() {
    return enabled_levels_.load();
}

void TraceRecorder::beginTrace(const std::string& key, TraceLevel level) {
    if (!isLevelEnabled(level)) {
        return;
    }

    std::lock_guard<std::mutex> lock(mutex_);

    // 获取当前批次
    int32_t current_batch = getCurrentBatch();
    TraceBatchKey batch_key(key, current_batch);

    // 记录开始时间
    start_times_[batch_key] = std::chrono::high_resolution_clock::now();

    // 如果是第一次记录这个key-batch组合，创建TraceData
    if (trace_data_.find(batch_key) == trace_data_.end()) {
        trace_data_[batch_key] = TraceData(key, level, current_batch);
    }
}

void TraceRecorder::endTrace(const std::string& key) {
    // 如果trace完全禁用，直接返回
    if (!isTraceEnabled()) {
        return;
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    std::lock_guard<std::mutex> lock(mutex_);

    // 获取当前批次
    int32_t current_batch = getCurrentBatch();
    TraceBatchKey batch_key(key, current_batch);

    auto start_it = start_times_.find(batch_key);
    if (start_it == start_times_.end()) {
        LOGW("TraceRecorder: No start time found for key: %s, batch: %d", key.c_str(), current_batch);
        return;
    }

    auto data_it = trace_data_.find(batch_key);
    if (data_it == trace_data_.end()) {
        return;
    }

    // 计算耗时
    auto duration = std::chrono::duration_cast<std::chrono::nanoseconds>(
        end_time - start_it->second).count();

    // 更新数据
    data_it->second.total_time_ns += duration;
    data_it->second.count++;

    // 移除开始时间记录
    start_times_.erase(start_it);

    LOGD("TraceRecorder: %s (batch: %d) completed in %llu ns (total: %u times, %llu ns)",
         key.c_str(), current_batch, duration, data_it->second.count, data_it->second.total_time_ns);
}

std::vector<uint8_t> TraceRecorder::exportBinaryData() {
    // 如果trace完全禁用，返回空的二进制数据
    if (!isTraceEnabled()) {
        LOGI("TraceRecorder: Export skipped - trace is disabled");
        return std::vector<uint8_t>();
    }

    std::lock_guard<std::mutex> lock(mutex_);

    std::vector<uint8_t> binary_data;

    // 写入数据条目数量
    uint32_t count = static_cast<uint32_t>(trace_data_.size());
    // 确保使用小端序
    if constexpr (std::endian::native != std::endian::little) {
        count = __builtin_bswap32(count);
    }
    binary_data.insert(binary_data.end(),
                      reinterpret_cast<uint8_t*>(&count),
                      reinterpret_cast<uint8_t*>(&count) + sizeof(count));

    // 写入每个埋点数据
    for (const auto& pair : trace_data_) {
        const TraceData& data = pair.second;

        // 写入key长度
        uint32_t key_length = static_cast<uint32_t>(data.key.length());
        if constexpr (std::endian::native != std::endian::little) {
            key_length = __builtin_bswap32(key_length);
        }
        binary_data.insert(binary_data.end(),
                          reinterpret_cast<uint8_t*>(&key_length),
                          reinterpret_cast<uint8_t*>(&key_length) + sizeof(key_length));

        // 写入key内容（字符串不需要字节序转换）
        binary_data.insert(binary_data.end(), data.key.begin(), data.key.end());

        // 写入等级
        uint32_t level = static_cast<uint32_t>(data.level);
        if constexpr (std::endian::native != std::endian::little) {
            level = __builtin_bswap32(level);
        }
        binary_data.insert(binary_data.end(),
                          reinterpret_cast<uint8_t*>(&level),
                          reinterpret_cast<uint8_t*>(&level) + sizeof(level));

        // 写入总耗时
        uint64_t total_time = data.total_time_ns;
        if constexpr (std::endian::native != std::endian::little) {
            total_time = __builtin_bswap64(total_time);
        }
        binary_data.insert(binary_data.end(),
                          reinterpret_cast<uint8_t*>(&total_time),
                          reinterpret_cast<uint8_t*>(&total_time) + sizeof(total_time));

        // 写入执行次数
        uint32_t count_val = data.count;
        if constexpr (std::endian::native != std::endian::little) {
            count_val = __builtin_bswap32(count_val);
        }
        binary_data.insert(binary_data.end(),
                          reinterpret_cast<uint8_t*>(&count_val),
                          reinterpret_cast<uint8_t*>(&count_val) + sizeof(count_val));

        // 写入批次字段
        int32_t batch_val = data.batch;
        if constexpr (std::endian::native != std::endian::little) {
            batch_val = __builtin_bswap32(batch_val);
        }
        binary_data.insert(binary_data.end(),
                          reinterpret_cast<uint8_t*>(&batch_val),
                          reinterpret_cast<uint8_t*>(&batch_val) + sizeof(batch_val));
    }

    LOGI("TraceRecorder: Exported %zu trace records, binary size: %zu bytes",
         trace_data_.size(), binary_data.size());

    return binary_data;
}

void TraceRecorder::clear() {
    // 即使trace被禁用，clear操作仍然有意义，可以清理之前的数据
    std::lock_guard<std::mutex> lock(mutex_);
    trace_data_.clear();
    start_times_.clear();

    LOGI("TraceRecorder: All trace data cleared");
}

std::vector<TraceData> TraceRecorder::getTraceData(const std::string& key) {
    // 如果trace完全禁用，返回空数据
    if (!isTraceEnabled()) {
        return std::vector<TraceData>(); // 返回空数据
    }

    std::lock_guard<std::mutex> lock(mutex_);

    std::vector<TraceData> result;

    // 遍历所有数据，找到匹配key的所有批次数据
    for (const auto& pair : trace_data_) {
        if (pair.first.key == key) {
            result.push_back(pair.second);
        }
    }

    return result;
}

TraceData TraceRecorder::getTraceData(const std::string& key, int32_t batch) {
    // 如果trace完全禁用，返回空数据
    if (!isTraceEnabled()) {
        return TraceData(); // 返回空数据
    }

    std::lock_guard<std::mutex> lock(mutex_);

    TraceBatchKey batch_key(key, batch);
    auto it = trace_data_.find(batch_key);
    if (it != trace_data_.end()) {
        return it->second;
    }

    return TraceData(); // 返回空数据
}

std::vector<TraceData> TraceRecorder::getAllTraceData() {
    // 如果trace完全禁用，返回空列表
    if (!isTraceEnabled()) {
        return std::vector<TraceData>();
    }

    std::lock_guard<std::mutex> lock(mutex_);

    std::vector<TraceData> result;
    result.reserve(trace_data_.size());

    for (const auto& pair : trace_data_) {
        result.push_back(pair.second);
    }

    return result;
}

// 批次管理方法实现
void TraceRecorder::addBatch(int32_t batch) {
    // 如果trace完全禁用，跳过批次管理操作
    if (!isTraceEnabled()) {
        LOGD("TraceRecorder: Add batch skipped - trace is disabled");
        return;
    }

    std::lock_guard<std::mutex> lock(batch_mutex_);

    // 检查队头是否已经是相同的batch，如果是则跳过插入
    if (!batch_deque_.empty() && batch_deque_.front() == batch) {
        LOGD("TraceRecorder: Batch %d already at front, skipping insertion", batch);
        return;
    }

    batch_deque_.push_front(batch);  // 添加到队列头部
    LOGI("TraceRecorder: Added batch %d to deque front, deque size: %zu", batch, batch_deque_.size());
}

void TraceRecorder::clearTailBatch() {
    // 如果trace完全禁用，跳过批次管理操作
    if (!isTraceEnabled()) {
        LOGD("TraceRecorder: Clear tail batch skipped - trace is disabled");
        return;
    }

    std::lock_guard<std::mutex> lock(batch_mutex_);
    if (!batch_deque_.empty()) {
        int32_t removed_batch = batch_deque_.back();
        batch_deque_.pop_back();  // 直接删除尾部元素，O(1)操作
        LOGI("TraceRecorder: Cleared tail batch %d, deque size: %zu", removed_batch, batch_deque_.size());
    } else {
        LOGW("TraceRecorder: Attempted to clear tail batch but deque is empty");
    }
}

int32_t TraceRecorder::getCurrentBatch() {
    // 如果trace完全禁用，返回默认批次
    if (!isTraceEnabled()) {
        return 0; // 默认批次
    }

    std::lock_guard<std::mutex> lock(batch_mutex_);
    if (batch_deque_.empty()) {
        return 0; // 默认批次
    }
    return batch_deque_.back(); // 返回队列尾部的批次
}

} // namespace trace