//
//  wxs_timer.cc
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/6/17.
//

#include "wxs_timer_ios.h"

namespace blink::mt {

WXSTimer::WXSTimer() {
  dispatch_queue_t target_queue = dispatch_get_global_queue(QOS_CLASS_USER_INITIATED, 0);
  timer_queue_ = dispatch_queue_create_with_target("com.msc.wxstimer", DISPATCH_QUEUE_SERIAL, target_queue);
}

WXSTimer::~WXSTimer() {
  for (auto &[timer_id, timer] : timers_) {
    dispatch_source_cancel(timer);
  }
  timers_.clear();
}

void WXSTimer::CreateTimer(int timer_id, double start_time, double duration, bool repeats) {
  auto iter = timers_.find(timer_id);
  if (iter != timers_.end()) {
    return;
  }

  dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, timer_queue_);
  std::weak_ptr w_this = shared_from_this();
  dispatch_source_set_event_handler(timer, ^{
    std::shared_ptr<WXSTimer> s_this = w_this.lock();
    if (!s_this) {
      return;
    }
    if (s_this->callback_) {
      s_this->callback_(timer_id); // TODO: blink thread
    }
    if (!repeats) {
      s_this->DestroyTimer(timer_id);
    }
  });

  int64_t real_start_time = (start_time - [NSDate date].timeIntervalSince1970) * NSEC_PER_SEC + duration * NSEC_PER_SEC;
  dispatch_time_t start = dispatch_time(DISPATCH_TIME_NOW, real_start_time);
  dispatch_source_set_timer(timer, start, duration * NSEC_PER_SEC, 0);
  dispatch_resume(timer);

  timers_.emplace(timer_id, timer);
}

void WXSTimer::DestroyTimer(int timer_id) {
  auto iter = timers_.find(timer_id);
  if (iter == timers_.end()) {
    return;
  }

  dispatch_source_t timer = iter->second;
  dispatch_source_cancel(timer);
  timers_.erase(iter);
}

void WXSTimer::SetTimerCallback(std::function<void(int timer_id)> callback) {
  callback_ = std::move(callback);
}

}; // blink::mt
