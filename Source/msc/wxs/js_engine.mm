//
//  js_engine.mm
//  MSCRenderer
//
//  Created by Admin on 2025/6/6.
//

#include "js_engine.h"

#import <JavaScriptCore/JavaScriptCore.h>
#import <dlfcn.h>

namespace blink::mt {

void JSEngine::SupportDebugger() {
  JSGlobalContextRef ctx = (JSGlobalContextRef)runtime_->jsContext();
  if (ctx) {
    if (@available(iOS 16.4, *)) {
      // hpx 升级 xocde 14.3 后可改为直接调用 JSGlobalContextSetInspectable 函数
      void *handle = dlopen(0, RTLD_GLOBAL | RTLD_NOW);
      void (*JSGlobalContextSetInspectablePtr)(JSGlobalContextRef, bool)
        = (void (*)(JSGlobalContextRef, bool))dlsym(handle, "JSGlobalContextSetInspectable");
      if (JSGlobalContextSetInspectablePtr != NULL) {
        JSGlobalContextSetInspectablePtr(ctx, YES);
      }
      dlclose(handle);
    }
  }
}

}; // namespace blink::mt
