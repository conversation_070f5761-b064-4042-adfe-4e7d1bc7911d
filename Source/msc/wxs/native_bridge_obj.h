//
//  native_beidge_obj.h
//  MSCRenderer
//
//  Created by 司雨寒 on 2025/6/4.
//

#pragma once

#include "js_engine.h"

#include <vector>
#include <string>
#include <unordered_map>

#include <msc-jsi/jsi.h>

namespace blink::mt {

class MTDocument;

class NativeBridgeObject : public msc::jsi::HostObject {
public:
  using Runtime = JSEngine::Runtime;
  using Value = JSEngine::Value;
  using String = JSEngine::String;
  using Array = JSEngine::Array;
  using Object = JSEngine::Object;
  using PropNameID = JSEngine::PropNameID;
  using Function = JSEngine::Function;
  using HostFunctionType = JSEngine::HostFunctionType;

  NativeBridgeObject(const std::weak_ptr<MTDocument>& mt_document, std::weak_ptr<JSEngine> engine_ptr);

  Value get(Runtime& runtime, const PropNameID& name) override;
  
  std::vector<PropNameID> getPropertyNames(Runtime& runtime) override;
  
  static void Install(const std::weak_ptr<MTDocument>& mt_document, std::weak_ptr<JSEngine> engine_ptr);

private:
  Value Invoke(Runtime& runtime,
               const std::string& module_name,
               const std::string& method_name,
               const std::string& args);
  Value NativeCallSyncHook(Runtime& runtime,
                           const std::string& module_name,
                           const std::string& method_name,
                           const std::string& args,
                           const std::string& inner);

  Value Transport(Runtime& runtime, const std::shared_ptr<const Props>& args);
  Value SelectComponent(Runtime& runtime, const std::shared_ptr<const Props>& args);
  Value SetStyle(Runtime& runtime, const std::shared_ptr<const Props>& args);
  Value GetDataset(Runtime& runtime, const std::shared_ptr<const Props>& args);
  Value TriggerEvent(Runtime& runtime, const std::shared_ptr<const Props>& args);
  Value GetBoundingClientRect(Runtime& runtime, const std::shared_ptr<const Props>& args);

  std::weak_ptr<MTDocument> mt_document_;
  std::weak_ptr<JSEngine> engine_;
  std::unordered_map<std::string, Function> prop_map_;
};

} // namespace blink::mt
