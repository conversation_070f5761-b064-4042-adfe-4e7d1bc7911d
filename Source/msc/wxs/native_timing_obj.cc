//
//  native_timing_obj.cc
//  MSCRenderer
//
//  Created by 司雨寒 on 2025/6/4.
//

#include "native_timing_obj.h"

#include "wxs_timer_ios.h"
#include "props.h"
#include <iostream>

#ifdef __APPLE__

namespace blink::mt {

using namespace msc;

NativeTimingObject::NativeTimingObject(std::weak_ptr<JSEngine> engine_ptr) {
  wxs_timer_ = std::make_shared<WXSTimer>();
  wxs_timer_->SetTimerCallback([engine_ptr](int timer_id) {
    std::shared_ptr<JSEngine> s_engine_ptr = engine_ptr.lock();
    if (!s_engine_ptr) {
      return;
    }

    PropValueType::Array args;
    PropValueType::Array timer_ids;
    timer_ids.push_back((PropValueType::Number)timer_id);
    args.push_back(timer_ids);
    s_engine_ptr->InvokeJSCall("JSTimers", "callTimers", args);
  });

  std::shared_ptr<JSEngine> s_engine_ptr = engine_ptr.lock();
  if (!s_engine_ptr) {
    return;
  }

  HostFunctionType create_timer_wrapper = [this] (Runtime& runtime, const Value& value, const Value* args, size_t count) -> Value {
    constexpr int args_required = 4;
    if (count < args_required) {
      return Value::undefined();
    }
    for (int i = 0; i < 3; i++) {
      if (!args[i].isNumber()) {
        return Value::undefined();
      }
    }
    if (!args[3].isBool()) {
      return Value::undefined();
    }

    int timer_id = static_cast<int>(args[0].asNumber());
    int js_duration = static_cast<int>(args[1].asNumber());
    int js_scheduling_time = static_cast<int>(args[2].asNumber());
    int repeats = static_cast<int>(args[3].getBool());
    this->CreateTimer(timer_id, js_duration, js_scheduling_time, repeats);

    return Value::undefined();
  };
  prop_map_.emplace("createTimer", Function::createFromHostFunction(*s_engine_ptr->runtime_,
                                                                    PropNameID::forAscii(*s_engine_ptr->runtime_, "createTimer"),
                                                                    0,
                                                                    std::move(create_timer_wrapper)));

  HostFunctionType delete_timer_wrapper = [this] (Runtime& runtime, const Value& value, const Value* args, size_t count) -> Value {
    constexpr int args_required = 1;
    if (count < args_required) {
      return Value::undefined();
    }
    if (!args[0].isNumber()) {
      return Value::undefined();
    }

    int timer_id = static_cast<int>(args[0].asNumber());
    this->DeleteTimer(timer_id);

    return Value::undefined();
  };
  prop_map_.emplace("deleteTimer", Function::createFromHostFunction(*s_engine_ptr->runtime_,
                                                                    PropNameID::forAscii(*s_engine_ptr->runtime_,
                                                                                         "deleteTimer"),
                                                                    0,
                                                                    std::move(delete_timer_wrapper)));
}

NativeTimingObject::Value NativeTimingObject::get(Runtime &runtime, const PropNameID &name) {
  std::string prop = name.utf8(runtime);
  auto iter = prop_map_.find(prop);
  if (iter == prop_map_.end()) {
    return Value::undefined();
  }
  return Value(runtime, iter->second);
}

std::vector<NativeTimingObject::PropNameID> NativeTimingObject::getPropertyNames(Runtime& runtime) {
  std::vector<PropNameID> ret;
  ret.push_back(PropNameID::forAscii(runtime, "createTimer"));
  ret.push_back(PropNameID::forAscii(runtime, "deleteTimer"));
  return ret;
}

void NativeTimingObject::Install(std::weak_ptr<JSEngine> engine_ptr) {
  auto native_timing = std::make_shared<NativeTimingObject>(engine_ptr);
  std::shared_ptr<JSEngine> s_engine_ptr = engine_ptr.lock();
  if (s_engine_ptr) {
    jsi::Object js_obj = jsi::Object::createFromHostObject(*s_engine_ptr->runtime_, native_timing);
    s_engine_ptr->runtime_->global().setProperty(*s_engine_ptr->runtime_, "NativeTiming", std::move(js_obj));
  }
}

void NativeTimingObject::CreateTimer(int timer_id, int js_duration, int js_scheduling_time, int repeats) {
  wxs_timer_->CreateTimer(timer_id, js_scheduling_time / 1000.0, js_duration / 1000.0, repeats);
}

void NativeTimingObject::DeleteTimer(int timer_id) {
  wxs_timer_->DestroyTimer(timer_id);
}

} // namespace blink::mt

#endif
