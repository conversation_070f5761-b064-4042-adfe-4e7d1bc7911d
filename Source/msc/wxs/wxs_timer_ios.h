//
//  wxs_timer.h
//  MSCRenderer
//
//  Created by Admin on 2025/6/17.
//

#ifdef __APPLE__

#include <unordered_map>
#include <functional>
#include <memory>
#include <dispatch/dispatch.h>

namespace blink::mt {

class WXSTimer : public std::enable_shared_from_this<WXSTimer> {
public:
  WXSTimer();

  ~WXSTimer();

  void Destroy();

  void CreateTimer(int timer_id, double start_time, double duration, bool repeats);

  void DestroyTimer(int timer_id);

  void SetTimerCallback(std::function<void(int timer_id)> callback);

private:
  dispatch_queue_t timer_queue_;
  std::unordered_map<int, dispatch_source_t> timers_;
  std::function<void(int timer_id)> callback_;
};

}; // namespace blink::mt 

#endif
