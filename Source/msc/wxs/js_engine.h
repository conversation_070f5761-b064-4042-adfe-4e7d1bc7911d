//
//  js_engine.h
//  MSCRenderer
//
//  Created by 司雨寒 on 2025/6/3.
//
#pragma once

#include <memory>
#include <optional>

#ifdef ANDROID
#include <msc-jsi/mtv8runtime/MTV8RuntimeFactory.h>
#else
#include <msc-jsi/JSCRuntime.h>
#endif

namespace blink::mt {

class MTDocument;

class JSEngine : public std::enable_shared_from_this<JSEngine> {
public:
  JSEngine(const std::weak_ptr<MTDocument>& mt_document);

  // 因为构造函数中不能调用shared_from_this()，所以需要手动调用Init来初始化JSEngine
  void Init();

  ~JSEngine() = default;

  using Runtime = msc::jsi::Runtime;
  using Value = msc::jsi::Value;
  using String = msc::jsi::String;
  using Array = msc::jsi::Array;
  using Object = msc::jsi::Object;
  using Buffer = msc::jsi::Buffer;
  using PropNameID = msc::jsi::PropNameID;
  using Function = msc::jsi::Function;
  using HostFunctionType = msc::jsi::HostFunctionType;

  Value ValueToJSValue(const PropValue& value);

  std::optional<Value> EvaluateJavaScript(const std::shared_ptr<Buffer>& buffer, const std::string& source_url);

  // JS call native methods
  void NativeLoggingHook(const std::string& message, int level);

  // native all JS methods
  const PropValue InvokeJSCall(const std::string& module, const std::string& method, const PropValueType::Array& args);

  std::weak_ptr<MTDocument> mt_document_;
  std::unique_ptr<Runtime> runtime_;

private:
  void SupportDebugger();
  void InstallFunctions();
  void InstallWxsBridgeObj();
  void InstallNativeLoggingHook();
};

} // namespace blink::mt
