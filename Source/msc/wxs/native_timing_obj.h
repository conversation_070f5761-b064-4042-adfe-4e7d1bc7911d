//
//  native_timing_obj.h
//  MSCRenderer
//
//  Created by 司雨寒 on 2025/6/4.
//

#pragma once

#ifdef __APPLE__

#include "js_engine.h"

#include <vector>
#include <string>
#include <unordered_map>
#include <memory>

#include <msc-jsi/jsi.h>

namespace blink::mt {

class WXSTimer;

class NativeTimingObject : public msc::jsi::HostObject {
public:
  using Value = JSEngine::Value;
  using Runtime = JSEngine::Runtime;
  using PropNameID = JSEngine::PropNameID;
  using Function = JSEngine::Function;
  using HostFunctionType = JSEngine::HostFunctionType;

  NativeTimingObject(std::weak_ptr<JSEngine> engine_ptr);
  
  Value get(Runtime& runtime, const PropNameID& name) override;
  
  std::vector<PropNameID> getPropertyNames(Runtime& runtime) override;
  
  static void Install(std::weak_ptr<JSEngine> engine_ptr);

private:
  void CreateTimer(int timer_id, int js_duration, int js_scheduling_time, int repeats);
  void DeleteTimer(int timer_id);

  std::unordered_map<std::string, Function> prop_map_;
  std::shared_ptr<WXSTimer> wxs_timer_;
};

} // namespace blink::mt

#endif
