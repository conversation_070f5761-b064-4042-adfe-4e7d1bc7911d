//
//  js_engine.cc
//  MSCRenderer
//
//  Created by 司雨寒 on 2025/6/4.
//

#include "js_engine.h"

#ifdef ANDROID
#include "worker_thread.h"
#endif

#include "mtdocument.h"
#include "native_bridge_obj.h"
#include "native_timing_obj.h"

#include <iostream>

namespace blink::mt {

using namespace msc;

JSEngine::JSEngine(const std::weak_ptr<MTDocument> &mt_document)
  : mt_document_(mt_document),
#ifdef ANDROID
  runtime_(mtv8::createMTV8Runtime("wxs_engine_debug",
                                   [](std::function<void()> func){
                                     WorkerThread::thread().addTask(std::move(func));
                                   }))
#else
  runtime_(jsc::makeJSCRuntime())
#endif
{}

void JSEngine::Init() {
  SupportDebugger();
  InstallFunctions();
}

void JSEngine::InstallFunctions() {
  InstallNativeLoggingHook();
  NativeBridgeObject::Install(mt_document_.lock(), weak_from_this());
//#ifdef __APPLE__
//  NativeTimingObject::Install(weak_from_this());
//#endif
}

JSEngine::Value JSEngine::ValueToJSValue(const PropValue& value) {
  if (value.isA<PropValueType::Number>()) {
    return JSEngine::Value(value.numberValue());
  } else if (value.isA<PropValueType::String>()) {
    return JSEngine::String::createFromUtf8(*runtime_, value.stringValue().c_str());
  } else if (value.isA<PropValueType::Array>()) {
    const PropValueType::Array& arr = value.arrayValue();
    JSEngine::Array array(*runtime_, arr.size());
    for (int i = 0; i < arr.size(); i++) {
      array.setValueAtIndex(*runtime_, i, ValueToJSValue(arr[i]));
    }
    return array;
  } else if (value.isA<PropValueType::Dictionary>()) {
    const PropValueType::Dictionary& dict = value.dictionaryValue();
    JSEngine::Object obj(*runtime_);
    for (auto& [key, value] : dict) {
      obj.setProperty(*runtime_, key.c_str(), ValueToJSValue(value));
    }
    return obj;
  } else {
    return NativeBridgeObject::Value::undefined();
  }
}

std::optional<JSEngine::Value> JSEngine::EvaluateJavaScript(const std::shared_ptr<Buffer>& buffer, const std::string& source_url) {
  try {
    return runtime_->evaluateJavaScript(buffer, source_url);
  } catch (const jsi::JSError& err) {
    return std::nullopt;
  }
}

void JSEngine::InstallNativeLoggingHook() {
  auto native_logging_hook = [this](Runtime& runtime, const Value& this_val, const Value* args, size_t count) -> Value {
    if (count < 2) {
      return Value::undefined();
    }
    if (!args[0].isString() || !args[1].isNumber()) {
      return Value::undefined();
    }
    std::string message = args[0].getString(runtime).utf8(runtime);
    int level = static_cast<int>(args[1].getNumber());
    this->NativeLoggingHook(message, level);
    return Value::undefined();
  };
  runtime_->global().setProperty(*runtime_, "nativeLoggingHook",
                                 jsi::Function::createFromHostFunction(*runtime_, jsi::PropNameID::forAscii(*runtime_, "nativeLoggingHook"), 2, std::move(native_logging_hook)));
}

void JSEngine::NativeLoggingHook(const std::string& message, int level) {
}

const PropValue JSEngine::InvokeJSCall(const std::string& module, const std::string& method, const PropValueType::Array& args) {
  Value module_val = runtime_->global().getProperty(*runtime_, module.c_str());
  if (!module_val.isObject()) {
    return PropValue::null();
  }

  Value method_val = module_val.getObject(*runtime_).getProperty(*runtime_, method.c_str());
  if (!method_val.isObject() || !method_val.getObject(*runtime_).isFunction(*runtime_)) {
    return PropValue::null();
  }

  jsi::Function invoke = method_val.getObject(*runtime_).getFunction(*runtime_);
  Value* args_val = new Value[args.size()];
  int idx = 0;
  for (auto& arg : args) {
    args_val[idx++] = ValueToJSValue(arg);
  }
  Value ret_val = Value::undefined();
  try {
    ret_val = invoke.call(*runtime_, (const Value*)args_val, args.size());
  } catch (const jsi::JSError& err) {
  }
  delete[] args_val;

  if (ret_val.isBool()) {
    return (PropValueType::Number)ret_val.getBool();
  } else if (ret_val.isNumber()) {
    return ret_val.getNumber();
  } else if (ret_val.isString()) {
    return ret_val.getString(*runtime_).utf8(*runtime_);
  }

  return PropValue::null();
}

#ifdef ANDROID
void JSEngine::SupportDebugger() {
}
#endif

} // namespace blink::mt
