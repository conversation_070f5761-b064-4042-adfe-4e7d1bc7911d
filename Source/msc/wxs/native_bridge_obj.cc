//
//  native_bridge_obj.cc
//  MSCRenderer
//
//  Created by 司雨寒 on 2025/6/4.
//

#include "native_bridge_obj.h"

#include "mtdocument.h"
#include <iostream>

namespace blink::mt {

using namespace msc;

NativeBridgeObject::NativeBridgeObject(const std::weak_ptr<MTDocument>& mt_document,
                                       std::weak_ptr<JSEngine> engine_ptr) : engine_(engine_ptr) {
  mt_document_ = mt_document;
  const std::shared_ptr<JSEngine> s_engine_ptr = engine_ptr.lock();
  if (!s_engine_ptr) {
    return;
  }

  HostFunctionType invoke_wrapper = [this] (Runtime& runtime, const Value& value, const Value* args, size_t count) -> Value {
    constexpr int args_required = 3;
    if (count < args_required) {
      return Value::undefined();
    }
    for (int i = 0; i < args_required; i++) {
      if (!args[i].isString()) {
        return Value::undefined();
      }
    }

    std::string module_name = args[0].asString(runtime).utf8(runtime);
    std::string method_name = args[1].asString(runtime).utf8(runtime);
    std::string args_str = args[2].asString(runtime).utf8(runtime);
    return this->Invoke(runtime, module_name, method_name, args_str);
  };
  prop_map_.emplace("invoke",
                    Function::createFromHostFunction(*s_engine_ptr->runtime_,
                                                     PropNameID::forAscii(*s_engine_ptr->runtime_, "invoke"),
                                                     0,
                                                     std::move(invoke_wrapper)));

  HostFunctionType native_call_sync_hook_wrapper = [this] (Runtime& runtime, const Value& value, const Value* args, size_t count) -> Value {
    constexpr int args_required = 3;
    if (count < args_required) {
      return Value::undefined();
    }
    for (int i = 0; i < args_required; i++) {
      if (!args[i].isString()) {
        return Value::undefined();
      }
    }

    std::string module_name = args[0].asString(runtime).utf8(runtime);
    std::string method_name = args[1].asString(runtime).utf8(runtime);
    std::string args_str = args[2].asString(runtime).utf8(runtime);
    std::string inner;
    if (count > 3) {
      inner = args[3].isString() ? args[3].asString(runtime).utf8(runtime) : "";
    }
    return this->NativeCallSyncHook(runtime, module_name, method_name, args_str, inner);
  };
  prop_map_.emplace("nativeCallSyncHook",
                    Function::createFromHostFunction(*s_engine_ptr->runtime_,
                                                     PropNameID::forAscii(*s_engine_ptr->runtime_, "nativeCallSyncHook"),
                                                     0,
                                                     std::move(native_call_sync_hook_wrapper)));
}

NativeBridgeObject::Value NativeBridgeObject::get(Runtime& runtime, const PropNameID &name) {
  std::string prop = name.utf8(runtime);
  auto iter = prop_map_.find(prop);
  if (iter == prop_map_.end()) {
    return Value::undefined();
  }
  return Value(runtime, iter->second);
}

std::vector<NativeBridgeObject::PropNameID> NativeBridgeObject::getPropertyNames(Runtime &runtime) {
  std::vector<PropNameID> ret;
  ret.push_back(PropNameID::forAscii(runtime, "invoke"));
  ret.push_back(PropNameID::forAscii(runtime, "nativeCallSyncHook"));
  return ret;
}

void NativeBridgeObject::Install(const std::weak_ptr<MTDocument>& mt_document, std::weak_ptr<JSEngine> engine_ptr) {
  auto native_bridge = std::make_shared<NativeBridgeObject>(mt_document, engine_ptr);
  const std::shared_ptr<JSEngine> s_engine_ptr = engine_ptr.lock();
  if (s_engine_ptr) {
    jsi::Object js_obj = jsi::Object::createFromHostObject(*s_engine_ptr->runtime_, native_bridge);
    s_engine_ptr->runtime_->global().setProperty(*s_engine_ptr->runtime_, "NativeBridge", std::move(js_obj));
  }
}

NativeBridgeObject::Value NativeBridgeObject::Invoke(Runtime& runtime,
                                                     const std::string& module_name,
                                                     const std::string& method_name,
                                                     const std::string& args) {
  auto args_props = jsonToProps(args);
  if (!args_props) {
    return Value::undefined();
  }
  if (module_name == "WXS") {
    if (method_name == "transport") {
      return Transport(runtime, args_props);
    }
  }
  return Value::undefined();
}

NativeBridgeObject::Value NativeBridgeObject::NativeCallSyncHook(Runtime& runtime,
                                                                 const std::string& module_name,
                                                                 const std::string& method_name,
                                                                 const std::string& args,
                                                                 const std::string& inner) {
  auto args_props = jsonToProps(args);
  if (!args_props) {
    return Value::undefined();
  }

  if (module_name == "WXS") {
    if (method_name == "selectComponent") {
      return SelectComponent(runtime, args_props);
    } else if (method_name == "setStyle") {
      return SetStyle(runtime, args_props);
    } else if (method_name == "getDataset") {
      return GetDataset(runtime, args_props);
    } else if (method_name == "triggerEvent") {
      return TriggerEvent(runtime, args_props);
    } else if (method_name == "getBoundingClientRect") {
//      return GetBoundingClientRect(runtime, args_props);
    }
  }
  return Value::undefined();
}

NativeBridgeObject::Value NativeBridgeObject::Transport(Runtime& runtime, const std::shared_ptr<const Props>& args) {
  auto args_string = propsToJson(args);
  auto mt_document = mt_document_.lock();
  if (mt_document) {
    mt_document->WXSTrasport(args_string);
  }
  return Value::undefined();
}

NativeBridgeObject::Value NativeBridgeObject::SelectComponent(Runtime& runtime,
                                                              const std::shared_ptr<const Props>& args) {
  NativeBridgeObject::Object ret(runtime);
  ret.setProperty(runtime, "viewId", -1);
  ret.setProperty(runtime, "ownerViewId", -1);

  auto& owner_msc_tag = args->getProp("ownerViewId");
  auto& selctor = args->getProp("selector");
  if (!owner_msc_tag.isA<PropValueType::Number>()) {
    return ret;
  }
  if (!selctor.isA<PropValueType::String>()) {
    return ret;
  }

  Tag tag = -1;
  Tag owner_tag = -1;
  auto mt_document = mt_document_.lock();
  if (mt_document) {
    auto& selector_string = selctor.stringValue();
    Element* result_element = mt_document->queryElementSync(owner_msc_tag.numberValue(),
                                                            selector_string);
    if (result_element) {
      tag = result_element->getMscTag();
      auto owner_tag_val = result_element->getProp("ownerMscTag");
      if (owner_tag_val.isA<PropValueType::Number>()) {
        owner_tag = owner_tag_val.numberValue();
      }
    }
  }

  ret.setProperty(runtime, "viewId", tag);
  ret.setProperty(runtime, "ownerViewId", owner_tag);
  return ret;
}

NativeBridgeObject::Value NativeBridgeObject::SetStyle(Runtime& runtime, const std::shared_ptr<const Props>& args) {
  auto& msc_tag = args->getProp("viewId");
  auto& style = args->getProp("style");
  if (!msc_tag.isA<PropValueType::Number>()) {
    return Value::undefined();
  }
  if (!style.isA<PropValueType::String>()) {
    return Value::undefined();
  }

  auto mt_document = mt_document_.lock();
  if (mt_document) {
    PropsBuilder props_builder;
    props_builder.setProp("wxsStyle", style.stringValue());
    mt_document->updateNode(msc_tag.numberValue(), "", props_builder.getProps());
  }
  return Value::undefined();
}

NativeBridgeObject::Value NativeBridgeObject::GetDataset(Runtime& runtime, const std::shared_ptr<const Props>& args) {
  auto& msc_tag = args->getProp("viewId");
  if (!msc_tag.isA<PropValueType::Number>()) {
    return Value::undefined();
  }

  auto mt_document = mt_document_.lock();
  if (mt_document) {
    Element* element = mt_document->getElementSync(msc_tag.numberValue());
    if (element) {
      auto dataset = element->getProp("dataset");
      if (dataset.isA<PropValueType::Dictionary>()) {
        const std::shared_ptr<JSEngine> s_engine_ptr = engine_.lock();
        return s_engine_ptr->ValueToJSValue(dataset);
      }
    }
  }

  return NativeBridgeObject::Object(runtime);
}

NativeBridgeObject::Value NativeBridgeObject::TriggerEvent(Runtime& runtime, const std::shared_ptr<const Props>& args) {
  auto& msc_tag = args->getProp("viewId");
  if (!msc_tag.isA<PropValueType::Number>()) {
    return Value::undefined();
  }

  {
    PropsBuilder props_builder;
    props_builder.setProp("target", msc_tag.numberValue());

    std::string event_name;
    auto& inner_args = args->getProp("args");
    if (inner_args.isA<PropValueType::Dictionary>()) {
      const PropValueType::Dictionary& inner_args_dict = inner_args.dictionaryValue();

      {
        auto iter = inner_args_dict.find("eventName");
        if (iter != inner_args_dict.end() && iter->second.isA<PropValueType::String>()) {
          event_name = iter->second.stringValue();
        }
      }

      {
        auto iter = inner_args_dict.find("detail");
        if (iter != inner_args_dict.end() && iter->second.isA<PropValueType::Dictionary>()) {
          props_builder.setProp("eventData", iter->second.dictionaryValue());
        }
      }

      {
        auto iter = inner_args_dict.find("options");
        if (iter != inner_args_dict.end() && iter->second.isA<PropValueType::Dictionary>()) {
          const PropValueType::Dictionary& options_dict = iter->second.dictionaryValue();
          {
            auto iter = options_dict.find("bubbles");
            if (iter != options_dict.end()) {
              props_builder.setProp("isBubbleEvent", iter->second.boolValue());
            }
          }
          {
            auto iter = options_dict.find("composed");
            if (iter != options_dict.end()) {
              props_builder.setProp("isComposed", iter->second.boolValue());
            }
          }
        }
      }
    }

    if (!props_builder.hasProp("eventData")) {
      props_builder.setProp("eventData", PropValueType::Dictionary());
    }

    auto mt_document = mt_document_.lock();
    if (mt_document) {
      mt_document->WXSTriggerEvent(event_name, msc_tag.numberValue(), props_builder.getProps());
    }
  }

  {
    PropsBuilder props_builder;
    props_builder.setProp("type", "triggerEvent");
    props_builder.setProp("data", args->asDict());

    auto mt_document = mt_document_.lock();
    if (mt_document) {
      std::string args_json = propsToJson(props_builder.getProps());
      mt_document->WXSTrasport(args_json);
    }
  }

  return Value::undefined();
}

NativeBridgeObject::Value NativeBridgeObject::GetBoundingClientRect(Runtime& runtime,
                                                                    const std::shared_ptr<const Props>& args) {
  auto& msc_tag = args->getProp("viewId");
  if (!msc_tag.isA<PropValueType::Number>()) {
    return Value::undefined();
  }

  auto& config = args->getProp("config");
  bool need_rect_bool = false;
  bool need_size_bool = false;
  if (config.isA<PropValueType::Dictionary>()) {
    const PropValueType::Dictionary& config_dict = config.dictionaryValue();
    {
      auto iter = config_dict.find("rect");
      if (iter != config_dict.end() && iter->second.isA<PropValueType::Number>()) {
        need_rect_bool = iter->second.numberValue();
      }
    }

    {
      auto iter = config_dict.find("size");
      if (iter != config_dict.end() && iter->second.isA<PropValueType::Number>()) {
        need_size_bool = iter->second.numberValue();
      }
    }
  }

  Rect rect;
  auto mt_document = mt_document_.lock();
  if (mt_document) {
    rect = mt_document->getClientRectSync(msc_tag.numberValue());
  }

  Object object(runtime);
  if (need_rect_bool) {
    object.setProperty(runtime, "left", rect.location.x);
    object.setProperty(runtime, "top", rect.location.y);
    object.setProperty(runtime, "right", rect.location.x + rect.size.width);
    object.setProperty(runtime, "bottom", rect.location.y + rect.size.height);
  }
  if (need_size_bool) {
    object.setProperty(runtime, "width", rect.size.width);
    object.setProperty(runtime, "height", rect.size.height);
  }

  return object;
}

} // namespace blink::mt
