//
//  worker_thread.hpp
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2024/11/8.
//

#ifndef worker_thread_hpp
#define worker_thread_hpp

#include "third_party/blink/renderer/platform/heap/thread_state.h"
#include "third_party/blink/renderer/platform/wtf/text/string_statics.h"
#include "third_party/blink/renderer/platform/instrumentation/instance_counters.h"

#include <functional>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <pthread.h>
#ifndef __APPLE__
#include <sys/resource.h>
#endif

namespace blink::mt {

class WorkerThread {
public:
  WorkerThread() : is_running_(false) {}

  static WorkerThread& thread() {
    static WorkerThread* instance;
    if (instance == nullptr) {
      std::lock_guard<std::mutex> lock(mtx_);
      if (instance == nullptr) {
        instance = new WorkerThread();
        instance->start();
      }
    }
    return *instance;
  }

  static bool isWorkerThread() {
    return pthread_equal(pthread_self(), WorkerThread::thread().thread_);
  }

  void start() {
    if (is_running_) return;

    is_running_ = true;

    pthread_attr_t attr;
    pthread_attr_init(&attr);

#ifdef __APPLE__
    pthread_attr_set_qos_class_np(&attr, QOS_CLASS_USER_INITIATED, 0);
#endif
    pthread_create(&thread_, &attr, [](void *thread) {
#ifdef __APPLE__
      pthread_setname_np("com.msc.renderer");
#else
      setpriority(PRIO_PROCESS, gettid(), -20); // 设置较高优先级
#endif
      ((WorkerThread *)thread)->workerFunction();
      return (void *)nullptr;
    }, this);
  }

  void stop() {
    if (!is_running_) return;
    {
      std::lock_guard<std::mutex> lock(mutex_);
      is_running_ = false;
      cond_var_.notify_all();
    }
    pthread_join(thread_, nullptr);
  }

  template <typename Func>
  void gc(Func&& func) {
    addTask([func]() {
      ThreadState::Current()->cpp_heap().ForceGarbageCollectionSlow("msc", "force GC");
      if (func) {
        func();
      }
    });
  }

  template <typename Func>
  void addTask(Func&& func) {
    if (pthread_equal(pthread_self(), thread_)) {
      func();
    } else {
      std::lock_guard<std::mutex> lock(mutex_);
      task_queue_.push(std::forward<Func>(func));
      cond_var_.notify_all();
    }
  }

private:
  
  void workerFunction() {
    while (is_running_ || !task_queue_.empty()) {
      std::function<void()> task;
      {
        std::unique_lock<std::mutex> lock(mutex_);
        cond_var_.wait(lock, [this]() { return !task_queue_.empty() || !is_running_; });

        if (!task_queue_.empty()) {
          task = std::move(task_queue_.front());
          task_queue_.pop();
        }
      }

#ifdef __APPLE__
      runTask(task);
#else
      task();
#endif
    }
  }

  void runTask(const std::function<void()>& task);

  static inline std::mutex mtx_;

  pthread_t thread_;
  std::queue<std::function<void()>> task_queue_;
  std::mutex mutex_;
  std::condition_variable cond_var_;
  std::atomic<bool> is_running_;
};

}; // namespace blink::mt

#endif /* worker_thread_hpp */
