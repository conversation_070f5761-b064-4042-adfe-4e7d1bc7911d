//
//  native_dom_metrics.cc
//  MSCRenderer
//
//  Created by qinqiwei02 on 2025/6/10.
//

#include "native_dom_metrics.h"

namespace msc {

using namespace std::string_literals;

const std::string NativeDOMMetrics::API::kNone = ""s;
const std::string NativeDOMMetrics::API::kSetupJSContext = "SetupJSContext"s;
const std::string NativeDOMMetrics::API::kRegisterDOMDocument = "RegisterDOMDocument"s;
const std::string NativeDOMMetrics::API::kDocumentQueryEnhanced = "DocumentQueryEnhanced"s;
const std::string NativeDOMMetrics::API::kGetDocument = "GetDocument"s;
const std::string NativeDOMMetrics::API::kMTDocumentQueryEnhanced = "MTDocumentQueryEnhanced"s;
const std::string NativeDOMMetrics::API::kMTDocumentIntersectionObserverObserve = "MTDocumentIntersectionObserverObserve"s;

const std::string NativeDOMMetrics::Message::kNone = ""s;
const std::string NativeDOMMetrics::Message::kCreateJSObject = "CreateJSObject null"s;
const std::string NativeDOMMetrics::Message::kInvalidPageId = "InvalidPageId"s;

}  // namespace msc
