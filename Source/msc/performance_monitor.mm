//
//  performance_monitor.m
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2024/12/30.
//

#include "performance_monitor.h"

namespace blink {

namespace mt {

#if defined(ENABLE_PERF_MONITOR) && defined(__APPLE__)

os_log_t PerformanceMonitor::logger_ = os_log_create("com.meituan.msc", "performance");
std::mutex PerformanceMonitor::mtx_;
int PerformanceMonitor::perf_id_base_(0);
unordered_map<int, shared_ptr<PerformanceMonitor>> PerformanceMonitor::monitors_;
std::atomic<int> PerformanceMonitor::counter_(0);

void PerformanceMonitor::printAndClearEvents(int perf_id) {
    struct Key {
        int token_;
        string event_name_;
        int event_group_id_;

        Key(int token, string &event_name, int event_group_id)
            : token_(token), event_name_(event_name), event_group_id_(event_group_id) {}
        
        Key(const Key &other)
            : token_(other.token_), event_name_(other.event_name_), event_group_id_(other.event_group_id_) {};

        bool operator==(const Key &other) const {
            return token_ == other.token_
                && event_name_ == other.event_name_
                && event_group_id_ == other.event_group_id_;
        }
    };
    struct HashFunc {
        std::size_t operator()(const Key &key) const
        {
            using std::size_t;
            using std::hash;
            return hash<int>()(key.token_)
                ^ (hash<string>()(key.event_name_) << 1)
                ^ (hash<int>()(key.event_group_id_) << 2);
        }
    };

    vector<Event> events;
    {
        std::lock_guard<std::mutex> lock(mtx_);
        auto iter = monitors_.find(perf_id);
        if (iter == monitors_.end()) {
            return;
        }
        events = iter->second->events_;
        iter->second->events_.clear();
    }

    unordered_map<Key, Event, HashFunc> map;
    for (Event &event : events) {
        if (event.event_type_ == EventType::Start) {
            auto iter = map.find(Key(event.token_, event.event_name_, event.event_group_id_));
            if (iter != map.end()) {
                continue;
            }
            map.insert(make_pair(Key(event.token_, event.event_name_, event.event_group_id_), event));
        }
    }
    
    for (Event &event : events) {
        if (event.event_type_ == EventType::End) {
            int cost = 0;
            do {
                auto iter = map.find(Key(event.token_, event.event_name_, event.event_group_id_));
                if (iter == map.end()) {
                    cost = -1;
                    break; // err1
                }
                
                auto duration
                    = std::chrono::duration_cast<std::chrono::microseconds>(event.time_ - iter->second.time_).count();
                if (duration < 0) {
                    cost = -2;
                    break; // err2
                }
                
                cost = (int)duration;
            } while(0);
            
            NSDictionary *log_info_dic = @{
                @"perf_id": @(perf_id),
                @"name": [NSString stringWithUTF8String:event.event_name_.c_str()],
                @"group": @(event.event_group_id_),
                @"cost": @(cost)
            };
            NSData *log_info_data = [NSJSONSerialization dataWithJSONObject:log_info_dic options:0 error:NULL];
            string log_info = "";
            if (log_info_data) {
                log_info = [[NSString alloc] initWithData:log_info_data encoding:NSUTF8StringEncoding].UTF8String;
            }

#if TEST || DEBUG
            printf("[MscRenderer] %s\n", log_info.c_str());
#else
            os_log(PerformanceMonitor::logger_, "[MscRenderer] %{public}s", log_info.c_str());
#endif
        }
    }
}

#endif

}; // namespace mt

}; // namespace blink
