//
//  debug.h
//  MSCRenderer
//
//  Created by Admin on 2025/4/18.
//

#ifndef debug_h
#define debug_h

#if DEBUG

namespace blink::mt {

__attribute__((unused)) void PrintDomTree(Node* node, int depth = 0);

__attribute__((unused)) void PrintLayoutTree(LayoutObject *layout_object);

__attribute__((unused)) void PrintLayerTree(const PaintLayer& layer, int depth = 0);

__attribute__((unused)) void PrintStackingTree(const PaintLayer& layer, int depth = 0);

__attribute__((unused)) void PrintRenderTree(RenderNode& root, int depth = 0);

}; // namespace blink::mt

#endif

#endif
