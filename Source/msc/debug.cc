//
//  debug.cc
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/4/18.
//

#include "debug.h"

#if DEBUG

#ifdef ANDROID
#include <android/log.h>

#define printf(...) __android_log_print(ANDROID_LOG_DEBUG, "BLINK", ##__VA_ARGS__)
#endif

#include "device.h"
#include "layout_utils.h"
#include "render_node.h"

#include "third_party/blink/renderer/core/dom/element.h"
#include "third_party/blink/renderer/core/paint/paint_layer.h"

namespace blink::mt {

__attribute__((unused)) void PrintDomTree(Node* node, int depth) {
  if (depth == 0) {
    printf("\n[blink] DOM Tree:\n");
  }

  printf("[blink] ");
  for (int i = 0; i < depth; i++) {
    printf("  ");
  }

  string msc_tag_name = "mt-text";
  if (IsA<blink::Element>(node)) {
    auto html_node = To<blink::Element>(node);
    msc_tag_name = html_node->componentName().Utf8();
  }

  if (IsA<Element>(node)) {
    Element* element = To<Element>(node);
    printf("%d <%s> %s props=%s\n", node->getMscTag(), msc_tag_name.c_str(), node->ToString().Utf8().c_str(),
           propsToJson(element->getProps()).c_str());
  } else {
    printf("%d <%s> %s\n", node->getMscTag(), msc_tag_name.c_str(), node->ToString().Utf8().c_str());
  }

  if (node->hasChildren()) {
    auto *child = node->firstChild();
    while (child) {
      PrintDomTree(child, depth + 1);
      child = child->nextSibling();
    }
  }
}

__attribute__((unused)) void PrintLayoutTree(LayoutObject *layout_object) {
  TraverseLayoutTree(layout_object, [&](const mt::LayoutTreeItem& item, bool* skip_children) {
    if (item.depth == 0) {
      printf("\n[blink] Layout Tree:\n");
    }

    if (IsA<LayoutView>(item.node)) {
      return;
    }

    printf("[blink] ");
    for (int i = 0; i < item.depth; i++) {
      printf("  ");
    }

    auto node = item.node->GetNode();
    string msc_tag_name = "mt-text";
    if (IsA<blink::Element>(node)) {
      auto html_node = To<blink::Element>(node);
      msc_tag_name = html_node->componentName().Utf8();
    }

    if (IsA<LayoutBox>(item.node)) {
      auto* layout_box = To<LayoutBox>(item.node);
      Tag container_tag = -1;
      if (layout_box->ContainingBlock()) {
        container_tag = layout_box->ContainingBlock()->GetNode()->getMscTag();
      }
      printf("%d (container is %d) rect=(%.2f, %.2f, %.2f, %.2f) %s\n",
             node->getMscTag(),
             container_tag,
             Device::gridAlignedValue(layout_box->Location().X().ToFloat()),
             Device::gridAlignedValue(layout_box->Location().Y().ToFloat()),
             Device::gridAlignedValue(layout_box->Size().width.ToFloat()),
             Device::gridAlignedValue(layout_box->Size().height.ToFloat()),
             item.node->ToString().Utf8().c_str());
    } else {
      printf("%d %s\n",
             node->getMscTag(),
             item.node->ToString().Utf8().c_str());
    }
  });
}

__attribute__((unused)) void PrintLayerTree(const PaintLayer& layer, int depth) {
  if (depth == 0) {
    printf("\n[blink] Layer tree:\n");
  }

  auto* layout_box = layer.GetLayoutBox();
  printf("[blink] ");
  for (int i = 0; i < depth; i++) {
    printf("  ");
  }

  printf("%d %s\n", layout_box->GetNode()->getMscTag(), layout_box->ToString().Utf8().c_str());

  for (auto* sub = layer.FirstChild(); sub; sub = sub->NextSibling()) {
    PrintLayerTree(*sub, depth + 1);
  }
}

__attribute__((unused)) void PrintStackingTree(const PaintLayer& layer, int depth) {
  if (depth == 0) {
    printf("\n[blink] Stacking tree:\n");
  }

  auto* layout_box = layer.GetLayoutBox();
  printf("[blink] ");
  for (int i = 0; i < depth; i++) {
    printf("  ");
  }

  printf("%s isContext=%d\n", layout_box->ToString().Utf8().c_str(),
         layout_box->IsStackingContext());

  if (layer.StackingNode()) {
    if (layer.StackingNode()->NegZOrderList().size() > 0) {
      for (auto sub : layer.StackingNode()->NegZOrderList()) {
        PrintStackingTree(*sub, depth + 1);
      }
    }
    if (layer.StackingNode()->PosZOrderList().size() > 0) {
      for (auto sub : layer.StackingNode()->PosZOrderList()) {
        PrintStackingTree(*sub, depth + 1);
      }
    }
  }
}

void PrintRenderTree(RenderNode& root, int depth) {
  if (depth == 0) {
    printf("\n[blink] Render Tree:\n");
  }

  printf("[blink] ");
  for (int i = 0; i < depth; i++) {
    printf("  ");
  }

  auto& box = root.getLayoutBox();
  auto& node = *box.GetNode();
  Tag container_tag = -1;
  if (box.ContainingBlock()) {
    container_tag = box.ContainingBlock()->GetNode()->getMscTag();
  }

  printf("%d (container is %d) rect=(%.2f, %.2f, %.2f, %.2f) %s\n",
         node.getMscTag(),
         container_tag,
         Device::gridAlignedValue(root.getRect().x.ToFloat()),
         Device::gridAlignedValue(root.getRect().y.ToFloat()),
         Device::gridAlignedValue(root.getRect().width.ToFloat()),
         Device::gridAlignedValue(root.getRect().height.ToFloat()),
         box.ToString().Utf8().c_str());

  for (auto& child : root.children()) {
    PrintRenderTree(*child, depth + 1);
  }
}

}; // blink::mt

#endif
