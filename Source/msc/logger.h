//
//  logger.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/7.
//

#ifndef logger_h
#define logger_h

#ifndef __APPLE__
#include "NativeLog.h"
#define MSC_RENDERER_LOG_INFO(format, ...)  LOGE("[MSCRenderer] " format"\n", ##__VA_ARGS__);
#define MSC_RENDERER_LOG_ERROR(format, ...) LOGE("[MSCRenderer] " format"\n", ##__VA_ARGS__);
#else
#define MSC_RENDERER_LOG_INFO(format, ...)  fprintf(stderr, "[MSCRenderer] " format"\n", ##__VA_ARGS__);
#define MSC_RENDERER_LOG_ERROR(format, ...) fprintf(stderr, "[MSCRenderer] " format"\n", ##__VA_ARGS__);
#endif

#if DEBUG
#ifndef __APPLE__
#define MSC_RENDERER_LOG_DEBUG(format, ...) LOGE("[MSCRenderer] " format"\n", ##__VA_ARGS__);
#else
#define MSC_RENDERER_LOG_DEBUG(format, ...) fprintf(stderr, "[MSCRenderer] " format"\n", ##__VA_ARGS__);
#endif
#else
#define MSC_RENDERER_LOG_DEBUG(format, ...)
#endif

#endif /* logger_h */
