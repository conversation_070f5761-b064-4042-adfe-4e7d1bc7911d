//
//  transform_convertor.h
//  MSCRenderer
//
//  Created by Admin on 2025/4/16.
//

#ifndef transform_convertor_h
#define transform_convertor_h

#include "types_def.h"
#include "render_node.h"

namespace blink {

class TransformOperations;

}; // namespace blink

namespace blink::mt {

NativeTransforms ConvertTransform(const TransformOperations& transform_ops, const RenderNode::Rect& rect);

}; // namespace blink::mt

#endif
