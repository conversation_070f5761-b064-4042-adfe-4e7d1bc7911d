//
//  native_style_utils_ios.mm
//  MSCRenderer
//
//  Created by Admin on 2025/4/8.
//

#include "style_convertor.h"
#include "device.h"
#include "style_convertor_helper.h"

#include "third_party/blink/renderer/core/css/properties/longhands.h"
#include "third_party/blink/renderer/core/html/html_element.h"

namespace blink::mt {

std::shared_ptr<const NativeStyle> ConvertStyle(const LayoutBox* box) {
  auto* style = box->Style();
  auto native_style = std::make_shared<NativeStyle>();

  native_style->hidden = style->Visibility() == EVisibility::kHidden;

  native_style->overflow_x = StyleConvertor::Convert(style->OverflowX());
  native_style->overflow_y = StyleConvertor::Convert(style->OverflowY());

  auto bg_color = style->VisitedDependentColor(GetCSSPropertyBackgroundColor());
  native_style->background_color = StyleConvertor::Convert(bg_color);

  auto color = style->GetCurrentColor();
  native_style->color = StyleConvertor::Convert(color);

  native_style->background_image = style->BackgroundImage().Utf8();
  native_style->background_gradient = StyleConvertor::Convert(style->BackgroundData().GetGradient());
  native_style->background_size = StyleConvertor::Convert(style->BackgroundData().Size());

  auto backgroundRepeat = style->BackgroundData().GetRepeat();
  native_style->background_repeat_x = StyleConvertor::Convert(backgroundRepeat.x);
  native_style->background_repeat_y = StyleConvertor::Convert(backgroundRepeat.y);

  native_style->border_top_style = StyleConvertor::Convert(style->BorderTopStyle());
  native_style->border_bottom_style = StyleConvertor::Convert(style->BorderBottomStyle());
  native_style->border_left_style = StyleConvertor::Convert(style->BorderLeftStyle());
  native_style->border_right_style = StyleConvertor::Convert(style->BorderRightStyle());

  native_style->border_top_color = StyleConvertor::Convert(style->BorderTopColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme()));
  native_style->border_bottom_color = StyleConvertor::Convert(style->BorderBottomColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme()));
  native_style->border_left_color = StyleConvertor::Convert(style->BorderLeftColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme()));
  native_style->border_right_color = StyleConvertor::Convert(style->BorderRightColor().Resolve(style->GetCurrentColor(), style->UsedColorScheme()));

  native_style->border_top_width = Device::gridAlignedValue(style->BorderTopWidth(), true);
  native_style->border_bottom_width = Device::gridAlignedValue(style->BorderBottomWidth(), true);
  native_style->border_left_width = Device::gridAlignedValue(style->BorderLeftWidth(), true);
  native_style->border_right_width = Device::gridAlignedValue(style->BorderRightWidth(), true);

  native_style->border_top_left_radius = StyleConvertor::ConvertBorderRadius(style->BorderTopLeftRadius());
  native_style->border_top_right_radius = StyleConvertor::ConvertBorderRadius(style->BorderTopRightRadius());
  native_style->border_bottom_left_radius = StyleConvertor::ConvertBorderRadius(style->BorderBottomLeftRadius());
  native_style->border_bottom_right_radius = StyleConvertor::ConvertBorderRadius(style->BorderBottomRightRadius());

  native_style->opacity = style->Opacity();

  native_style->font_size = style->FontSize();

  auto is_italic = style->GetFontDescription().Style() != kNormalSlopeValue;
  native_style->font_style = is_italic ? "italic" : "normal";

  native_style->text_align = StyleConvertor::Convert(style->GetTextAlign());

//  native_style->transform = StyleConvertor::ConvertTransform(style->Transform(), rect);
  native_style->box_shadow = StyleConvertor::Convert(style->BoxShadow(), style);
  native_style->pointer_events = StyleConvertor::Convert(style->UsedPointerEvents());

  return native_style;
}

}; // namespace blink::mt
