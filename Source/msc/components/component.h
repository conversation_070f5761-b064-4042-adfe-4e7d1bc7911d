//
//  component.h
//  MSCRenderer
//
//  Created by Admin on 2025/2/25.
//

#ifndef component_h
#define component_h

#include "third_party/blink/renderer/core/dom/document.h"
#include "third_party/blink/renderer/core/html/html_element.h"
#include "third_party/blink/renderer/platform/wtf/hash_map.h"

#include "props.h"

namespace blink::mt {

#define REGISTER_COMPONENT(ELEMENT_NAME, CLASS) \
  static bool CLASS##_registered = []() { \
    blink::mt::ComponentRegistry::registerComponent(ELEMENT_NAME, \
      [](Document &document, const char* tag_name) -> Component* { \
      return MakeGarbageCollected<CLASS>(document, AtomicString::FromUTF8(tag_name), AtomicString::FromUTF8(ELEMENT_NAME)); \
    }); \
    return true; \
  }();

class MTDocument;

/**
 本类作为 msc 组件的基类使用
 可以重载本类定义的 protected 方法，其它继承自基类的方法禁止重载！
 */
class Component : public HTMLElement {
public:
  Component(Document &document, AtomicString html_tag_name, AtomicString component_name):
    HTMLElement(blink::html_names::kDivTag, document, kCreateHTMLElement),
    html_tag_name_(html_tag_name),
    component_name_(component_name) {}

  const AtomicString& htmlTagName() const override {
    return html_tag_name_;
  }

  const AtomicString& componentName() const override {
    return component_name_;
  }

  const AtomicString& getPlatformViewName() const override {
    return platform_view_name_;
  }

  void setPlatformViewName(const AtomicString& platform_view_name) override {
    platform_view_name_ = platform_view_name;
  }

  std::weak_ptr<mt::MTDocument> mtDocument() const override {
    return document_;
  }

  void setMtDocument(std::weak_ptr<mt::MTDocument> document) override {
    document_ = document;
  }

  const std::shared_ptr<const mt::Props>& getProps() const override {
    return props_;
  }

  const mt::PropValue& getProp(const std::string &name) const override {
    if (!props_) {
      return mt::PropValue::null();
    }
    return props_->getProp(name);
  }

  void setProps(std::shared_ptr<const mt::Props> props) override {
    static auto empty_props = mt::PropsBuilder().getProps();
    if (!props_ && props) {
      propsWillChange(*empty_props, *props);
    } else if (props_ && !props) {
      propsWillChange(*props_, *empty_props);
    } else if (props_ && props) {
      propsWillChange(*props_, *props);
    } else {
      propsWillChange(*empty_props, *empty_props);
    }
    props_ = props;
  }

  bool isText() override {
    return is_text_;
  }

  void setIsText(bool is_text) override {
    is_text_ = is_text;
  }

// 内部使用，请勿调用
public:
  const AtomicString& localName() const override {
    return html_tag_name_;
  }

  void Trace(Visitor *visitor) const override {
    Element::Trace(visitor);
  }

protected:
  virtual void propsWillChange(const mt::Props& old_props,
                               const mt::Props& new_props) {}

private:
  std::weak_ptr<mt::MTDocument> document_;
  AtomicString html_tag_name_;
  AtomicString component_name_;
  AtomicString platform_view_name_;
  std::shared_ptr<const mt::Props> props_;
  bool is_text_;
};

class ComponentRegistry {
public:
  using Creator = std::function<HTMLElement* (Document &document, const char* name)>;

  static void registerComponent(const char *name, Creator creator) {
    auto &registry = getRegistry();
    auto key = AtomicString::FromUTF8(name);
    if (registry.find(key) == registry.end()) {
      registry.Set(key, creator);
    }
  }

  static HTMLElement* createElement(Document &document, const char* tag_name, const char* name) {
    auto &registry = getRegistry();
    auto wtf_name = AtomicString::FromUTF8(name);
    auto it = registry.find(wtf_name);
    if (it != registry.end()) {
      return it->value(document, tag_name);
    } else {
      return MakeGarbageCollected<Component>(document, AtomicString::FromUTF8(tag_name), wtf_name);
    }
    return nullptr;
  }

private:
  static HashMap<AtomicString, Creator> &getRegistry() {
    static HashMap<AtomicString, Creator> registry;
    return registry;
  }
};

REGISTER_COMPONENT("mt-view", Component);

}; // namespace blink::mt

#endif /* msc_element_h */
