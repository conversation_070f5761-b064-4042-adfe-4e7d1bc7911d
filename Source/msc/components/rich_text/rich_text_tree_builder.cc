//
//  rich_text_tree_builder.cpp
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/2/28.
//

#include "rich_text_tree_builder.h"

#include "layout_utils.h"
#include "tinyxml2.h"

#include <sstream>

namespace blink::mt {

namespace {

static unordered_map<string, unordered_map<string, string>>& getNodeNameMap() {
  static unordered_map<string, unordered_map<string, string>> map;
  static std::once_flag onceFlag;
  std::call_once(onceFlag, []() {
    map = {
      {"abbr", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"address", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"article", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"aside", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"b", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"big", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"br", {{"tag", "-mt-break"}, {"view", "MSCText"}}},
      {"center", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"cite", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"del", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"div", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"em", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"footer", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"h1", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"h2", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"h3", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"h4", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"h5", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"h6", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"header", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"img", {{"tag", "mt-image"}, {"view", "MSCImage"}}},
      {"ins", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"mark", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"p", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"s", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"section", {{"tag", "mt-view"}, {"view", "MSCView"}}},
      {"small", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"span", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"strong", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"u", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"cdata", {{"tag", "mt-text"}, {"view", "MSCRawText"}}},
      {"i", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"strike", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"font", {{"tag", "mt-text"}, {"view", "MSCText"}}},
      {"image", {{"tag", "mt-image"}, {"view", "MSCImage"}}}
    };
  });
  return map;
}

string replaceString(string str, const string& from, const string& to) {
  size_t start_pos = 0;
  while((start_pos = str.find(from, start_pos)) != string::npos) {
    str.replace(start_pos, from.length(), to);
    start_pos += to.length();
  }
  return str;
}

string replaceSpaceNameToUnicode(string text, RichTextSpaceType space_type) {
  text = replaceString(text, "&ensp;", "\u2002");
  text = replaceString(text, "&emsp;", "\u2003");
  text = replaceString(text, "&nbsp;", "\u00a0");
  return text;
}

std::string preprocessHTML(std::string html) {
  size_t length = html.length();
  for (size_t i = 0; i < length; i++) {
    char currentChar = html[i];
    if (currentChar == '&') {
      if (i + 4 < length && html.substr(i, 5) == "&amp;") {
        i += 4;
        continue;
      } else if (i + 5 < length && html.substr(i, 6) == "&apos;") {
        i += 5;
        continue;
      } else if (i + 5 < length && html.substr(i, 6) == "&quot;") {
        i += 5;
        continue;
      } else if (i + 3 < length && html.substr(i, 4) == "&lt;") {
        i += 3;
        continue;
      } else if (i + 3 < length && html.substr(i, 4) == "&gt;") {
        i += 3;
        continue;
      } else {
        html.replace(i, 1, "&amp;");
        i += 4;
        length += 4;
      }
    }
  }
  return html;
}

string formattingSpace(string text, RichTextSpaceType space_type) {
  // 1.空格替换成对应的space
  // 2.处理&nbsp;等编码
  if (space_type == RichTextSpaceType::Ensp) {
    text = replaceString(text, " ", "\u2002");
  } else if (space_type == RichTextSpaceType::Emsp) {
    text = replaceString(text, " ", "\u2003");
  } else if (space_type == RichTextSpaceType::Nbsp) {
    text = replaceString(text, " ", "\u00a0");
  }
  text = replaceSpaceNameToUnicode(text, space_type);
  return text;
}

inline PropValue parseXmlNode(const tinyxml2::XMLNode* node) {
  if (!node) return PropValue{};

  const tinyxml2::XMLElement* element = node->ToElement();
  if (!element) return PropValue{};

  PropValueType::Dictionary dict;
  dict["name"] = element->Name();

  // Parse attributes
  PropValueType::Dictionary attrs;
  const tinyxml2::XMLAttribute* attr = element->FirstAttribute();
  while (attr) {
    const char* name = attr->Name();
    const char* value = attr->Value();
    attrs[name] = PropValue{std::string(value)};
    attr = attr->Next();
  }
  if (!attrs.empty()) {
    dict["attrs"] = attrs;
  }

  // Parse child nodes
  PropValueType::Array children;
  const tinyxml2::XMLNode* child = element->FirstChild();
  while (child) {
    if (child->ToElement()) {
      children.push_back(parseXmlNode(child));
    } else if (child->ToText()) {
      PropValueType::Dictionary item;
      item["type"] = "text";
      item["text"] = child->Value();
      children.push_back(item);
    }
    child = child->NextSibling();
  }

  if (!children.empty()) {
    dict["children"] = PropValue{children};
  }

  return PropValue{dict};
}

shared_ptr<RichTextTreeNode> build(const PropValueType::Dictionary& dictionary) {
  auto node = make_shared<RichTextTreeNode>();

  auto iter = dictionary.find("name");
  if (iter != dictionary.end()) {
    node->html_tag = iter->second.stringValue();
  }

  iter = dictionary.find("type");
  if (iter != dictionary.end()) {
    node->type = iter->second.stringValue();
  }

  iter = dictionary.find("text");
  if (iter != dictionary.end()) {
    node->text = iter->second.stringValue();
  }

  iter = dictionary.find("attrs");
  if (iter != dictionary.end()) {
    auto& attrs = iter->second.dictionaryValue();
    for (auto& [key, value] : attrs) {
      node->attrs[key] = value.stringValue();
    }
  }

  iter = dictionary.find("children");
  if (iter != dictionary.end()) {
    auto& children = iter->second.arrayValue();
    for (auto& child : children) {
      auto sub_node = build(child.dictionaryValue());
      if (sub_node) {
        node->children.push_back(sub_node);
      }
    }
  }

  return node;
}

shared_ptr<RichTextTreeNode> build(const PropValueType::String& html, RichTextTreeBuilder::Context& context) {
  auto filtered_html = replaceSpaceNameToUnicode(html, context.space_type);
  filtered_html = preprocessHTML(html);
  tinyxml2::XMLDocument doc;
  if (doc.Parse(filtered_html.c_str()) != tinyxml2::XML_SUCCESS) {
    return nullptr;
  }
  auto value = parseXmlNode(doc.RootElement());
  return build(value.dictionaryValue());
}

shared_ptr<RichTextTreeNode> postProcess(
  shared_ptr<RichTextTreeNode> node, shared_ptr<RichTextTreeNode> parent_node, RichTextTreeBuilder::Context &context) {

  static auto create_wrapper_node = []() {
    auto wrapper_node = make_shared<RichTextTreeNode>();
    wrapper_node->msc_tag = GenerateMscTag();
    wrapper_node->html_tag = "span";
    wrapper_node->tag = "mt-text";
    wrapper_node->view = "MSCText";
    wrapper_node->attrs["htmlTagName"] = wrapper_node->html_tag;
    wrapper_node->attrs["tagName"] = "mt-text";
    return wrapper_node;
  };

  node->msc_tag = GenerateMscTag();

  if (node->type == "text") {
    node->tag = "mt-text";
    node->view = "MSCRawText";
    node->attrs["text"] = formattingSpace(node->text, context.space_type);
  } else {
    auto &map = getNodeNameMap();
    auto iter = map.find(node->html_tag);
    if (iter != map.end()) {
      node->tag = iter->second["tag"];
      node->view = iter->second["view"];
    } else {
      node->tag = "mt-text";
      node->view = "MSCText";
    }

    auto classname_iter = node->attrs.find("class");
    if (classname_iter != node->attrs.end()) {
      istringstream iss(classname_iter->second);
      string word;
      std::stringstream classname;
      while (iss >> word) {
        classname << context.class_prefix << word << " ";
      }
      node->attrs["class"] = classname.str();
    }

    std::stringstream style;

    if (node->html_tag == "img") {
      node->attrs["disableEvent"] = "true";
      auto style_iter = node->attrs.find("style");
      if (style_iter != node->attrs.end()) {
        style << style_iter->second;
      }
      auto iter = node->attrs.find("width");
      if (iter != node->attrs.end()) {
        style << ";width:" << iter->second;
      }
      iter = node->attrs.find("height");
      if (iter != node->attrs.end()) {
        style << ";height:" << iter->second;
      }
      node->attrs["style"] = style.str();
      style.clear();
    }

    if (node->html_tag == "font") {
      auto iter = node->attrs.find("size");
      if (iter != node->attrs.end()) {
        style << "font-size:" << iter->second;
      }
      iter = node->attrs.find("color");
      if (iter != node->attrs.end()) {
        style << ";color:" << iter->second;
      }
      iter = node->attrs.find("face");
      if (iter != node->attrs.end()) {
        style << ";font-famliy:" << iter->second;
      }
      auto style_iter = node->attrs.find("style");
      if (style_iter != node->attrs.end()) {
        style << ";" << style_iter->second;
      }
      node->attrs["style"] = style.str();
      style.clear();
    }
  }

  node->attrs["htmlTagName"] = node->html_tag;
  node->attrs["tagName"] = node->tag;

  if (node->type == "text" || node->tag == "-mt-break") {
    if (node->children.size() > 0) {
      node->children.clear();
    }

    // 1. 如果纯文本节点，上层没有父文本节点，则在它上层包一个文本节点
    if (!parent_node || parent_node->tag != "mt-text") {
      auto wrapper_node = create_wrapper_node();
      wrapper_node->children.push_back(node);
      node = wrapper_node;
    }
  } else {
    auto idx = 0;
    for (auto child_node : node->children) {
      auto new_child_node = postProcess(child_node, node, context);
      if (new_child_node != child_node) {
        node->children[idx] = new_child_node;
      }
      idx++;
    }

    // 2. 如果非文本节点下，有连续多个文本子节点，则在这些文本子节点上层包一个文本节点
    if (node->tag != "mt-text") {
      vector<shared_ptr<RichTextTreeNode>> new_children;
      vector<shared_ptr<RichTextTreeNode>> tmp;
      auto flush_tmp = [&new_children, &tmp](){
        if (tmp.size() > 1) {
          auto wrapper_node = create_wrapper_node();
          wrapper_node->children = tmp;
          new_children.push_back(wrapper_node);
        } else if (tmp.size() == 1) {
          new_children.push_back(tmp[0]);
        }
        tmp.clear();
      };

      for (auto child_node : node->children) {
        if (child_node->tag != "mt-text") {
          flush_tmp();
          new_children.push_back(child_node);
        } else {
          tmp.push_back(child_node);
        }
      }
      flush_tmp();
      node->children = new_children;
    }
  }

  return node;
}

}; // namespace

shared_ptr<RichTextTreeNode> RichTextTreeBuilder::build(PropValue value, Context& context) {
  shared_ptr<RichTextTreeNode> root_node;
  if (value.isA<PropValueType::Array>()) {
    auto& array = value.arrayValue();
    if (array.size() > 0) {
      root_node = ::blink::mt::build(array[0].dictionaryValue());
    }
  } else if (value.isA<PropValueType::String>()) {
    root_node = ::blink::mt::build(value.stringValue(), context);
  }
  if (!root_node) {
    root_node = make_shared<RichTextTreeNode>();
  }
  return root_node;
}

shared_ptr<RichTextTreeNode> RichTextTreeBuilder::postProcess(
  shared_ptr<RichTextTreeNode> node, Context& context) {
  return ::blink::mt::postProcess(node, nullptr, context);
}

}; // namespace blink::mt
