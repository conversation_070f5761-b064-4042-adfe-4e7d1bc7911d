//
//  rich_text_node.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/2/27.
//

#ifndef rich_text_node_h
#define rich_text_node_h

#include "types_def.h"
#include "props.h"

#include <memory>
#include <string>
#include <vector>
#include <unordered_map>

using namespace std;

namespace blink::mt {

class RichTextTreeNode {
public:
  Tag msc_tag;
  string type;
  string html_tag;
  string tag;
  string view;
  string text;
  unordered_map<string, string> attrs;
  vector<shared_ptr<RichTextTreeNode>> children;
};

enum class RichTextSpaceType {
  Empty,
  Ensp,
  Emsp,
  Nbsp
};

class RichTextTreeBuilder {
public:
  struct Context {
    string class_prefix;
    RichTextSpaceType space_type;
  };

  static shared_ptr<RichTextTreeNode> build(PropValue value, Context& context);
  static shared_ptr<RichTextTreeNode> postProcess(shared_ptr<RichTextTreeNode> node, Context& context);
};

}; // namespace blink::mt

#endif /* rich_text_node_h */
