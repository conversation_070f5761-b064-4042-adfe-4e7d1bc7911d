//
//  rich_text_component.h
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/2/25.
//

#ifndef rich_text_component_h
#define rich_text_component_h

#include "component.h"
#include "rich_text_tree_builder.h"

namespace blink::mt {

class RichTextComponent : public Component {
public:
  RichTextComponent(Document &document, AtomicString tag_name, AtomicString name):
    Component(document, tag_name, name) {}

protected:
  void propsWillChange(const mt::Props& old_props,
                       const mt::Props& new_props) override;

private:
  void createDomNode(RichTextTreeNode &node);

  shared_ptr<RichTextTreeNode> root_node_;
};

REGISTER_COMPONENT("mt-rich-text", RichTextComponent);

class BreakComponent : public Component {
public:
  BreakComponent(Document &document, AtomicString tag_name, AtomicString name):
    Component(document, tag_name, name) {}
};

REGISTER_COMPONENT("-mt-break", BreakComponent);

}; // namespace blink::mt

#endif /* rich_text_element_h */

