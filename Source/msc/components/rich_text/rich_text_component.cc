//
//  rich_text_component.cpp
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2025/2/25.
//

#include "rich_text_component.h"

#include "mtdocument.h"

#include <string>
#include <unordered_map>

using namespace std;

namespace blink::mt {

namespace {

RichTextSpaceType str2spaceType(string space) {
  if (space == "ensp") {
    return RichTextSpaceType::Ensp;
  } else if (space == "emsp") {
    return RichTextSpaceType::Emsp;
  } else if (space == "nbsp") {
    return RichTextSpaceType::Nbsp;
  }
  return RichTextSpaceType::Empty;
}

}; // namespace

void RichTextComponent::propsWillChange(const mt::Props& old_props,
                                      const mt::Props& new_props) {
  auto document_ptr = mtDocument().lock();
  if (!document_ptr) {
    return;
  }

  if (old_props.getProp("nodes") != new_props.getProp("nodes")
    || old_props.getProp("space") != new_props.getProp("space")) {
    RichTextTreeBuilder::Context context;
    context.class_prefix = new_props.getProp("classPrefix").stringValue();
    context.space_type = str2spaceType(new_props.getProp("space").stringValue());

    auto node = RichTextTreeBuilder::build(new_props.getProp("nodes"), context);
    node = RichTextTreeBuilder::postProcess(node, context);
    if (root_node_) {
      document_ptr->removeNode(root_node_->msc_tag);
    }
    root_node_ = node;
    auto child_tags = make_shared<vector<int>>(vector<int>{root_node_->msc_tag});
    createDomNode(*root_node_);
    document_ptr->setChildren(getMscTag(), child_tags);
  }
}

void RichTextComponent::createDomNode(RichTextTreeNode& node) {
  auto document_ptr = mtDocument().lock();
  if (!document_ptr) {
    return;
  }

  PropsBuilder buidler;
  for (auto& [name, value] : node.attrs) {
    buidler.setProp(name, PropValue(value));
  }
  document_ptr->createNode(node.msc_tag, std::string(node.view), -1, buidler.getProps());
  shared_ptr<vector<int>> child_tags = make_shared<vector<int>>();
  for (auto& child_node : node.children) {
    child_tags->push_back(child_node->msc_tag);
    createDomNode(*child_node);
  }
  if (child_tags->size() > 0) {
    document_ptr->setChildren(node.msc_tag, child_tags);
  }
}

}; // namespace blink::mt
