//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/11.
//

#ifndef RENDERER_NATIVELOG_H
#define RENDERER_NATIVELOG_H

#ifndef __APPLE__
#include <android/log.h>

// 定义日志标签
#define LOG_TAG "BLINK"

// 定义日志宏
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)
#define LOGD(...) __android_log_print(ANDROID_LOG_DEBUG, LOG_TAG, __VA_ARGS__)
#define LOGW(...) __android_log_print(ANDROID_LOG_WARN, LOG_TAG, __VA_ARGS__)
#define LOGV(...) __android_log_print(ANDROID_LOG_VERBOSE, LOG_TAG, __VA_ARGS__)
#else
// 非 Android 平台：全部定义为空
    #define LOGI(...)
    #define LOGE(...)
    #define LOGD(...)
    #define LOGW(...)
    #define LOGV(...)
#endif

#endif //RENDERER_NATIVELOG_H
