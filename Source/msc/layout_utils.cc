//
//  layout_utils.m
//  MSCRenderer
//
//  Created by <PERSON><PERSON> on 2024/10/14.
//

#include "layout_utils.h"
#include "render_node.h"

namespace blink {

namespace mt {

static Tag msc_tag_base = INT_MAX; // 2的31次方−1

Tag GenerateMscTag(void)
{
  msc_tag_base -= 2;
  if (msc_tag_base % 10 == 1) {
    msc_tag_base -= 2;
  }
  return msc_tag_base;
}

void TraverseLayoutTreeInternal(LayoutObject *node,
                                std::function<void(const LayoutTreeItem& item, bool* skip_children)> callback,
                                int depth) {
  if (!node) {
    return;
  }

  bool skip_children = false;
  LayoutTreeItem item;
  item.node = node;
  item.depth = depth;
  callback(item, &skip_children);

  if (!skip_children) {
    if (node->VirtualChildren()) {
      if (node->VirtualChildren()->FirstChild()) {
        auto child_node = node->VirtualChildren()->FirstChild();
        while (child_node) {
          TraverseLayoutTreeInternal(child_node, callback, depth + 1);
          child_node = child_node->NextSibling();
        }
      }
    }
  }
}

void TraverseRenderTreeInternal(RenderNode *node,
                                std::function<void(const RenderTreeItem& item, bool* skip_children)> callback,
                                int depth) {
  if (!node) {
    return;
  }

  bool skip_children = false;
  RenderTreeItem item;
  item.node = node;
  item.depth = depth;
  callback(item, &skip_children);

  if (!skip_children) {
    for (auto& child : node->children()) {
      TraverseRenderTreeInternal(child.Get(), callback, depth + 1);
    }
  }
}

void TraverseLayoutTree(LayoutObject *node, std::function<void(const LayoutTreeItem& item, bool* skip_children)> callback) {
  TraverseLayoutTreeInternal(node, callback, 0);
}

void TraverseRenderTree(RenderNode *node,
                        std::function<void(const RenderTreeItem& item, bool* skip_children)> callback) {
  TraverseRenderTreeInternal(node, callback, 0);
}

}; // namespace mt

}; // namespace blink
