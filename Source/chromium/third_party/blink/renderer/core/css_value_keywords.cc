// Generated by third_party/blink/renderer/build/scripts/gperf.py
/* C++ code produced by gperf version 3.0.3 */
/* Command-line: /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/gperf --key-positions='*' -P -n -m 50 -D -Q CSSValueStringPool  */

#if !((' ' == 32) && ('!' == 33) && ('"' == 34) && ('#' == 35) \
      && ('%' == 37) && ('&' == 38) && ('\'' == 39) && ('(' == 40) \
      && (')' == 41) && ('*' == 42) && ('+' == 43) && (',' == 44) \
      && ('-' == 45) && ('.' == 46) && ('/' == 47) && ('0' == 48) \
      && ('1' == 49) && ('2' == 50) && ('3' == 51) && ('4' == 52) \
      && ('5' == 53) && ('6' == 54) && ('7' == 55) && ('8' == 56) \
      && ('9' == 57) && (':' == 58) && (';' == 59) && ('<' == 60) \
      && ('=' == 61) && ('>' == 62) && ('?' == 63) && ('A' == 65) \
      && ('B' == 66) && ('C' == 67) && ('D' == 68) && ('E' == 69) \
      && ('F' == 70) && ('G' == 71) && ('H' == 72) && ('I' == 73) \
      && ('J' == 74) && ('K' == 75) && ('L' == 76) && ('M' == 77) \
      && ('N' == 78) && ('O' == 79) && ('P' == 80) && ('Q' == 81) \
      && ('R' == 82) && ('S' == 83) && ('T' == 84) && ('U' == 85) \
      && ('V' == 86) && ('W' == 87) && ('X' == 88) && ('Y' == 89) \
      && ('Z' == 90) && ('[' == 91) && ('\\' == 92) && (']' == 93) \
      && ('^' == 94) && ('_' == 95) && ('a' == 97) && ('b' == 98) \
      && ('c' == 99) && ('d' == 100) && ('e' == 101) && ('f' == 102) \
      && ('g' == 103) && ('h' == 104) && ('i' == 105) && ('j' == 106) \
      && ('k' == 107) && ('l' == 108) && ('m' == 109) && ('n' == 110) \
      && ('o' == 111) && ('p' == 112) && ('q' == 113) && ('r' == 114) \
      && ('s' == 115) && ('t' == 116) && ('u' == 117) && ('v' == 118) \
      && ('w' == 119) && ('x' == 120) && ('y' == 121) && ('z' == 122) \
      && ('{' == 123) && ('|' == 124) && ('}' == 125) && ('~' == 126))
/* The character set is not based on ISO-646.  */
#error "gperf generated tables don't work with this execution character set. Please report a bug to <<EMAIL>>."
#endif


// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#include "third_party/blink/renderer/core/css_value_keywords.h"

#include <string.h>

#include "base/check_op.h"
#include "third_party/blink/renderer/core/css/hash_tools.h"

#ifdef _MSC_VER
// Disable the warnings from casting a 64-bit pointer to 32-bit long
// warning C4302: 'type cast': truncation from 'char (*)[28]' to 'long'
// warning C4311: 'type cast': pointer truncation from 'char (*)[18]' to 'long'
#pragma warning(disable : 4302 4311)
#endif

namespace blink {
static const char valueListStringPool[] = {
  "inherit\0"
  "initial\0"
  "none\0"
  "hidden\0"
  "inset\0"
  "groove\0"
  "outset\0"
  "ridge\0"
  "dotted\0"
  "dashed\0"
  "solid\0"
  "double\0"
  "caption\0"
  "icon\0"
  "menu\0"
  "message-box\0"
  "small-caption\0"
  "-webkit-mini-control\0"
  "-webkit-small-control\0"
  "-webkit-control\0"
  "status-bar\0"
  "italic\0"
  "oblique\0"
  "all\0"
  "common-ligatures\0"
  "no-common-ligatures\0"
  "discretionary-ligatures\0"
  "no-discretionary-ligatures\0"
  "historical-ligatures\0"
  "no-historical-ligatures\0"
  "contextual\0"
  "no-contextual\0"
  "small-caps\0"
  "all-small-caps\0"
  "petite-caps\0"
  "all-petite-caps\0"
  "unicase\0"
  "titling-caps\0"
  "jis78\0"
  "jis83\0"
  "jis90\0"
  "jis04\0"
  "simplified\0"
  "traditional\0"
  "full-width\0"
  "proportional-width\0"
  "ruby\0"
  "lining-nums\0"
  "oldstyle-nums\0"
  "proportional-nums\0"
  "tabular-nums\0"
  "diagonal-fractions\0"
  "stacked-fractions\0"
  "ordinal\0"
  "slashed-zero\0"
  "stylistic\0"
  "historical-forms\0"
  "styleset\0"
  "character-variant\0"
  "swash\0"
  "ornaments\0"
  "annotation\0"
  "normal\0"
  "bold\0"
  "bolder\0"
  "lighter\0"
  "weight\0"
  "ultra-condensed\0"
  "extra-condensed\0"
  "condensed\0"
  "semi-condensed\0"
  "semi-expanded\0"
  "expanded\0"
  "extra-expanded\0"
  "ultra-expanded\0"
  "xx-small\0"
  "x-small\0"
  "small\0"
  "medium\0"
  "large\0"
  "x-large\0"
  "xx-large\0"
  "xxx-large\0"
  "smaller\0"
  "larger\0"
  "-webkit-xxx-large\0"
  "ex-height\0"
  "cap-height\0"
  "ch-width\0"
  "ic-width\0"
  "serif\0"
  "sans-serif\0"
  "cursive\0"
  "fantasy\0"
  "monospace\0"
  "system-ui\0"
  "-webkit-body\0"
  "math\0"
  "swap\0"
  "fallback\0"
  "optional\0"
  "font-tech\0"
  "font-format\0"
  "emoji\0"
  "unicode\0"
  "palette-mix\0"
  "aqua\0"
  "black\0"
  "blue\0"
  "fuchsia\0"
  "gray\0"
  "green\0"
  "lime\0"
  "maroon\0"
  "navy\0"
  "olive\0"
  "orange\0"
  "purple\0"
  "red\0"
  "silver\0"
  "teal\0"
  "white\0"
  "yellow\0"
  "transparent\0"
  "-webkit-link\0"
  "-webkit-activelink\0"
  "accentcolor\0"
  "accentcolortext\0"
  "activeborder\0"
  "activecaption\0"
  "activetext\0"
  "appworkspace\0"
  "background\0"
  "buttonborder\0"
  "buttonface\0"
  "buttonhighlight\0"
  "buttonshadow\0"
  "buttontext\0"
  "canvas\0"
  "canvastext\0"
  "captiontext\0"
  "field\0"
  "fieldtext\0"
  "graytext\0"
  "highlight\0"
  "highlighttext\0"
  "inactiveborder\0"
  "inactivecaption\0"
  "inactivecaptiontext\0"
  "infobackground\0"
  "infotext\0"
  "linktext\0"
  "mark\0"
  "marktext\0"
  "menutext\0"
  "selecteditem\0"
  "selecteditemtext\0"
  "scrollbar\0"
  "threeddarkshadow\0"
  "threedface\0"
  "threedhighlight\0"
  "threedlightshadow\0"
  "threedshadow\0"
  "visitedtext\0"
  "window\0"
  "windowframe\0"
  "windowtext\0"
  "-internal-active-list-box-selection\0"
  "-internal-active-list-box-selection-text\0"
  "-internal-inactive-list-box-selection\0"
  "-internal-inactive-list-box-selection-text\0"
  "-webkit-focus-ring-color\0"
  "currentcolor\0"
  "grey\0"
  "-internal-quirk-inherit\0"
  "-internal-spelling-error-color\0"
  "-internal-grammar-error-color\0"
  "aliceblue\0"
  "antiquewhite\0"
  "aquamarine\0"
  "azure\0"
  "beige\0"
  "bisque\0"
  "blanchedalmond\0"
  "blueviolet\0"
  "brown\0"
  "burlywood\0"
  "cadetblue\0"
  "chartreuse\0"
  "chocolate\0"
  "coral\0"
  "cornflowerblue\0"
  "cornsilk\0"
  "crimson\0"
  "cyan\0"
  "darkblue\0"
  "darkcyan\0"
  "darkgoldenrod\0"
  "darkgray\0"
  "darkgreen\0"
  "darkgrey\0"
  "darkkhaki\0"
  "darkmagenta\0"
  "darkolivegreen\0"
  "darkorange\0"
  "darkorchid\0"
  "darkred\0"
  "darksalmon\0"
  "darkseagreen\0"
  "darkslateblue\0"
  "darkslategray\0"
  "darkslategrey\0"
  "darkturquoise\0"
  "darkviolet\0"
  "deeppink\0"
  "deepskyblue\0"
  "dimgray\0"
  "dimgrey\0"
  "dodgerblue\0"
  "firebrick\0"
  "floralwhite\0"
  "forestgreen\0"
  "gainsboro\0"
  "ghostwhite\0"
  "gold\0"
  "goldenrod\0"
  "greenyellow\0"
  "honeydew\0"
  "hotpink\0"
  "indianred\0"
  "indigo\0"
  "ivory\0"
  "khaki\0"
  "lavender\0"
  "lavenderblush\0"
  "lawngreen\0"
  "lemonchiffon\0"
  "lightblue\0"
  "lightcoral\0"
  "lightcyan\0"
  "lightgoldenrodyellow\0"
  "lightgray\0"
  "lightgreen\0"
  "lightgrey\0"
  "lightpink\0"
  "lightsalmon\0"
  "lightseagreen\0"
  "lightskyblue\0"
  "lightslategray\0"
  "lightslategrey\0"
  "lightsteelblue\0"
  "lightyellow\0"
  "limegreen\0"
  "linen\0"
  "magenta\0"
  "mediumaquamarine\0"
  "mediumblue\0"
  "mediumorchid\0"
  "mediumpurple\0"
  "mediumseagreen\0"
  "mediumslateblue\0"
  "mediumspringgreen\0"
  "mediumturquoise\0"
  "mediumvioletred\0"
  "midnightblue\0"
  "mintcream\0"
  "mistyrose\0"
  "moccasin\0"
  "navajowhite\0"
  "oldlace\0"
  "olivedrab\0"
  "orangered\0"
  "orchid\0"
  "palegoldenrod\0"
  "palegreen\0"
  "paleturquoise\0"
  "palevioletred\0"
  "papayawhip\0"
  "peachpuff\0"
  "peru\0"
  "pink\0"
  "plum\0"
  "powderblue\0"
  "rebeccapurple\0"
  "rosybrown\0"
  "royalblue\0"
  "saddlebrown\0"
  "salmon\0"
  "sandybrown\0"
  "seagreen\0"
  "seashell\0"
  "sienna\0"
  "skyblue\0"
  "slateblue\0"
  "slategray\0"
  "slategrey\0"
  "snow\0"
  "springgreen\0"
  "steelblue\0"
  "tan\0"
  "thistle\0"
  "tomato\0"
  "turquoise\0"
  "violet\0"
  "wheat\0"
  "whitesmoke\0"
  "yellowgreen\0"
  "repeat\0"
  "repeat-x\0"
  "repeat-y\0"
  "no-repeat\0"
  "clear\0"
  "copy\0"
  "source-over\0"
  "source-in\0"
  "source-out\0"
  "source-atop\0"
  "destination-over\0"
  "destination-in\0"
  "destination-out\0"
  "destination-atop\0"
  "xor\0"
  "plus-lighter\0"
  "subtract\0"
  "intersect\0"
  "exclude\0"
  "baseline\0"
  "middle\0"
  "sub\0"
  "super\0"
  "text-top\0"
  "text-bottom\0"
  "top\0"
  "bottom\0"
  "-webkit-baseline-middle\0"
  "-webkit-auto\0"
  "left\0"
  "right\0"
  "center\0"
  "justify\0"
  "-webkit-left\0"
  "-webkit-right\0"
  "-webkit-center\0"
  "-webkit-match-parent\0"
  "-internal-center\0"
  "inline-start\0"
  "inline-end\0"
  "outside\0"
  "inside\0"
  "disc\0"
  "circle\0"
  "square\0"
  "disclosure-open\0"
  "disclosure-closed\0"
  "decimal\0"
  "inline\0"
  "block\0"
  "flow-root\0"
  "flow\0"
  "table\0"
  "flex\0"
  "grid\0"
  "contents\0"
  "table-row-group\0"
  "table-header-group\0"
  "table-footer-group\0"
  "table-row\0"
  "table-column-group\0"
  "table-column\0"
  "table-cell\0"
  "table-caption\0"
  "ruby-text\0"
  "inline-block\0"
  "inline-table\0"
  "inline-flex\0"
  "inline-grid\0"
  "-webkit-box\0"
  "-webkit-inline-box\0"
  "-webkit-flex\0"
  "-webkit-inline-flex\0"
  "layout\0"
  "inline-layout\0"
  "list-item\0"
  "auto\0"
  "crosshair\0"
  "default\0"
  "pointer\0"
  "move\0"
  "vertical-text\0"
  "cell\0"
  "context-menu\0"
  "alias\0"
  "progress\0"
  "no-drop\0"
  "not-allowed\0"
  "zoom-in\0"
  "zoom-out\0"
  "e-resize\0"
  "ne-resize\0"
  "nw-resize\0"
  "n-resize\0"
  "se-resize\0"
  "sw-resize\0"
  "s-resize\0"
  "w-resize\0"
  "ew-resize\0"
  "ns-resize\0"
  "nesw-resize\0"
  "nwse-resize\0"
  "col-resize\0"
  "row-resize\0"
  "text\0"
  "wait\0"
  "help\0"
  "all-scroll\0"
  "grab\0"
  "grabbing\0"
  "-webkit-grab\0"
  "-webkit-grabbing\0"
  "-webkit-zoom-in\0"
  "-webkit-zoom-out\0"
  "ltr\0"
  "rtl\0"
  "capitalize\0"
  "uppercase\0"
  "lowercase\0"
  "math-auto\0"
  "visible\0"
  "collapse\0"
  "preserve\0"
  "preserve-breaks\0"
  "pretty\0"
  "a3\0"
  "a4\0"
  "a5\0"
  "above\0"
  "absolute\0"
  "always\0"
  "avoid\0"
  "b4\0"
  "b5\0"
  "below\0"
  "bidi-override\0"
  "blink\0"
  "both\0"
  "break-spaces\0"
  "close-quote\0"
  "embed\0"
  "fixed\0"
  "hand\0"
  "hide\0"
  "isolate\0"
  "isolate-override\0"
  "plaintext\0"
  "-webkit-isolate\0"
  "-webkit-isolate-override\0"
  "-webkit-plaintext\0"
  "jis-b5\0"
  "jis-b4\0"
  "landscape\0"
  "ledger\0"
  "legal\0"
  "letter\0"
  "line-through\0"
  "local\0"
  "no-close-quote\0"
  "no-open-quote\0"
  "nowrap\0"
  "open-quote\0"
  "overlay\0"
  "overline\0"
  "portrait\0"
  "pre\0"
  "pre-line\0"
  "pre-wrap\0"
  "relative\0"
  "scroll\0"
  "separate\0"
  "show\0"
  "static\0"
  "thick\0"
  "thin\0"
  "underline\0"
  "view\0"
  "wavy\0"
  "compact\0"
  "stretch\0"
  "start\0"
  "end\0"
  "clone\0"
  "slice\0"
  "reverse\0"
  "horizontal\0"
  "vertical\0"
  "inline-axis\0"
  "block-axis\0"
  "flex-start\0"
  "flex-end\0"
  "space-between\0"
  "space-around\0"
  "space-evenly\0"
  "unsafe\0"
  "safe\0"
  "anchor-center\0"
  "row\0"
  "row-reverse\0"
  "column\0"
  "column-reverse\0"
  "wrap\0"
  "wrap-reverse\0"
  "auto-flow\0"
  "dense\0"
  "read-only\0"
  "read-write\0"
  "read-write-plaintext-only\0"
  "element\0"
  "-webkit-min-content\0"
  "-webkit-max-content\0"
  "-webkit-fill-available\0"
  "-webkit-fit-content\0"
  "min-content\0"
  "max-content\0"
  "fit-content\0"
  "no-autospace\0"
  "cap\0"
  "ex\0"
  "leading\0"
  "clip\0"
  "ellipsis\0"
  "spelling-error\0"
  "grammar-error\0"
  "from-font\0"
  "space-all\0"
  "space-first\0"
  "trim-start\0"
  "break-all\0"
  "keep-all\0"
  "auto-phrase\0"
  "break-word\0"
  "space\0"
  "loose\0"
  "strict\0"
  "after-white-space\0"
  "anywhere\0"
  "manual\0"
  "checkbox\0"
  "radio\0"
  "button\0"
  "listbox\0"
  "-internal-media-control\0"
  "menulist\0"
  "menulist-button\0"
  "meter\0"
  "progress-bar\0"
  "searchfield\0"
  "textfield\0"
  "textarea\0"
  "inner-spin-button\0"
  "push-button\0"
  "square-button\0"
  "slider-horizontal\0"
  "searchfield-cancel-button\0"
  "slider-vertical\0"
  "round\0"
  "base-select\0"
  "-internal-appearance-auto-base-select\0"
  "border\0"
  "border-box\0"
  "content\0"
  "content-box\0"
  "padding\0"
  "padding-box\0"
  "margin-box\0"
  "no-clip\0"
  "contain\0"
  "cover\0"
  "logical\0"
  "visual\0"
  "replace\0"
  "accumulate\0"
  "alternate\0"
  "alternate-reverse\0"
  "forwards\0"
  "backwards\0"
  "infinite\0"
  "running\0"
  "paused\0"
  "flat\0"
  "preserve-3d\0"
  "fill-box\0"
  "view-box\0"
  "ease\0"
  "linear\0"
  "ease-in\0"
  "ease-out\0"
  "ease-in-out\0"
  "jump-both\0"
  "jump-end\0"
  "jump-none\0"
  "jump-start\0"
  "step-start\0"
  "step-end\0"
  "steps\0"
  "frames\0"
  "cubic-bezier\0"
  "document\0"
  "reset\0"
  "zoom\0"
  "visiblepainted\0"
  "visiblefill\0"
  "visiblestroke\0"
  "painted\0"
  "fill\0"
  "stroke\0"
  "bounding-box\0"
  "spell-out\0"
  "digits\0"
  "literal-punctuation\0"
  "no-punctuation\0"
  "antialiased\0"
  "subpixel-antialiased\0"
  "optimizespeed\0"
  "optimizelegibility\0"
  "geometricprecision\0"
  "crispedges\0"
  "economy\0"
  "exact\0"
  "lr\0"
  "rl\0"
  "tb\0"
  "lr-tb\0"
  "rl-tb\0"
  "tb-rl\0"
  "horizontal-tb\0"
  "vertical-rl\0"
  "vertical-lr\0"
  "after\0"
  "before\0"
  "over\0"
  "under\0"
  "filled\0"
  "open\0"
  "dot\0"
  "double-circle\0"
  "triangle\0"
  "sesame\0"
  "ellipse\0"
  "closest-side\0"
  "closest-corner\0"
  "farthest-side\0"
  "farthest-corner\0"
  "mixed\0"
  "sideways\0"
  "sideways-right\0"
  "upright\0"
  "vertical-right\0"
  "on\0"
  "off\0"
  "optimizequality\0"
  "pixelated\0"
  "-webkit-optimize-contrast\0"
  "from-image\0"
  "rotate-left\0"
  "rotate-right\0"
  "nonzero\0"
  "evenodd\0"
  "at\0"
  "alphabetic\0"
  "borderless\0"
  "fullscreen\0"
  "standalone\0"
  "minimal-ui\0"
  "browser\0"
  "window-controls-overlay\0"
  "tabbed\0"
  "picture-in-picture\0"
  "minimized\0"
  "maximized\0"
  "paged\0"
  "slow\0"
  "fast\0"
  "sticky\0"
  "coarse\0"
  "fine\0"
  "on-demand\0"
  "hover\0"
  "multiply\0"
  "screen\0"
  "darken\0"
  "lighten\0"
  "color-dodge\0"
  "color-burn\0"
  "hard-light\0"
  "soft-light\0"
  "difference\0"
  "exclusion\0"
  "hue\0"
  "saturation\0"
  "color\0"
  "luminosity\0"
  "scale-down\0"
  "balance\0"
  "drag\0"
  "no-drag\0"
  "span\0"
  "minmax\0"
  "subgrid\0"
  "progressive\0"
  "interlace\0"
  "markers\0"
  "alpha\0"
  "luminance\0"
  "match-source\0"
  "srgb\0"
  "linearrgb\0"
  "butt\0"
  "miter\0"
  "bevel\0"
  "before-edge\0"
  "after-edge\0"
  "central\0"
  "text-before-edge\0"
  "text-after-edge\0"
  "ideographic\0"
  "hanging\0"
  "mathematical\0"
  "use-script\0"
  "no-change\0"
  "reset-size\0"
  "dynamic\0"
  "non-scaling-stroke\0"
  "-internal-extend-to-zoom\0"
  "pan-x\0"
  "pan-y\0"
  "pan-left\0"
  "pan-right\0"
  "pan-up\0"
  "pan-down\0"
  "manipulation\0"
  "pinch-zoom\0"
  "last-baseline\0"
  "first-baseline\0"
  "first\0"
  "last\0"
  "self-start\0"
  "self-end\0"
  "legacy\0"
  "smooth\0"
  "scroll-position\0"
  "revert\0"
  "revert-layer\0"
  "unset\0"
  "linear-gradient\0"
  "radial-gradient\0"
  "conic-gradient\0"
  "repeating-linear-gradient\0"
  "repeating-radial-gradient\0"
  "repeating-conic-gradient\0"
  "paint\0"
  "cross-fade\0"
  "-webkit-cross-fade\0"
  "-webkit-gradient\0"
  "-webkit-linear-gradient\0"
  "-webkit-radial-gradient\0"
  "-webkit-repeating-linear-gradient\0"
  "-webkit-repeating-radial-gradient\0"
  "-webkit-image-set\0"
  "image-set\0"
  "type\0"
  "to\0"
  "color-stop\0"
  "radial\0"
  "attr\0"
  "counter\0"
  "counters\0"
  "rect\0"
  "polygon\0"
  "format\0"
  "collection\0"
  "embedded-opentype\0"
  "opentype\0"
  "svg\0"
  "truetype\0"
  "woff\0"
  "woff2\0"
  "tech\0"
  "features-opentype\0"
  "features-aat\0"
  "features-graphite\0"
  "color-COLRv0\0"
  "color-COLRv1\0"
  "color-SVG\0"
  "color-sbix\0"
  "color-CBDT\0"
  "variations\0"
  "palettes\0"
  "incremental\0"
  "invert\0"
  "grayscale\0"
  "sepia\0"
  "saturate\0"
  "hue-rotate\0"
  "opacity\0"
  "brightness\0"
  "contrast\0"
  "blur\0"
  "drop-shadow\0"
  "url\0"
  "rgb\0"
  "rgba\0"
  "hsl\0"
  "hsla\0"
  "hwb\0"
  "lab\0"
  "oklab\0"
  "lch\0"
  "oklch\0"
  "light-dark\0"
  "srgb-linear\0"
  "display-p3\0"
  "a98-rgb\0"
  "prophoto-rgb\0"
  "xyz\0"
  "xyz-d50\0"
  "xyz-d65\0"
  "shorter\0"
  "longer\0"
  "decreasing\0"
  "increasing\0"
  "in\0"
  "color-mix\0"
  "from\0"
  "r\0"
  "g\0"
  "b\0"
  "h\0"
  "s\0"
  "l\0"
  "w\0"
  "a\0"
  "c\0"
  "matrix\0"
  "matrix3d\0"
  "perspective\0"
  "rotate\0"
  "rotateX\0"
  "rotateY\0"
  "rotateZ\0"
  "rotate3d\0"
  "scale\0"
  "scaleX\0"
  "scaleY\0"
  "scaleZ\0"
  "scale3d\0"
  "skew\0"
  "skewX\0"
  "skewY\0"
  "translate\0"
  "translateX\0"
  "translateY\0"
  "translateZ\0"
  "translate3d\0"
  "x\0"
  "y\0"
  "z\0"
  "path\0"
  "ray\0"
  "sides\0"
  "stroke-box\0"
  "calc\0"
  "-webkit-calc\0"
  "min\0"
  "max\0"
  "clamp\0"
  "calc-size\0"
  "any\0"
  "sin\0"
  "cos\0"
  "asin\0"
  "atan\0"
  "atan2\0"
  "acos\0"
  "mod\0"
  "rem\0"
  "up\0"
  "down\0"
  "to-zero\0"
  "sign\0"
  "abs\0"
  "pow\0"
  "sqrt\0"
  "hypot\0"
  "log\0"
  "exp\0"
  "infinity\0"
  "-infinity\0"
  "nan\0"
  "pi\0"
  "e\0"
  "mandatory\0"
  "proximity\0"
  "style\0"
  "size\0"
  "block-size\0"
  "inline-size\0"
  "scroll-state\0"
  "inset-block-start\0"
  "inset-block-end\0"
  "inset-inline-start\0"
  "inset-inline-end\0"
  "auto-fill\0"
  "auto-fit\0"
  "var\0"
  "-internal-variable-value\0"
  "env\0"
  "arg\0"
  "avoid-page\0"
  "page\0"
  "recto\0"
  "verso\0"
  "avoid-column\0"
  "p3\0"
  "rec2020\0"
  "add\0"
  "auto-add\0"
  "true\0"
  "false\0"
  "no-preference\0"
  "dark\0"
  "light\0"
  "only\0"
  "reduce\0"
  "active\0"
  "preserve-parent-color\0"
  "back-button\0"
  "fabricated\0"
  "selector\0"
  "continuous\0"
  "folded\0"
  "stable\0"
  "both-edges\0"
  "more\0"
  "less\0"
  "custom\0"
  "cyclic\0"
  "symbolic\0"
  "numeric\0"
  "additive\0"
  "extends\0"
  "-internal-simp-chinese-informal\0"
  "-internal-simp-chinese-formal\0"
  "-internal-trad-chinese-informal\0"
  "-internal-trad-chinese-formal\0"
  "-internal-korean-hangul-formal\0"
  "-internal-korean-hanja-informal\0"
  "-internal-korean-hanja-formal\0"
  "-internal-hebrew\0"
  "-internal-lower-armenian\0"
  "-internal-upper-armenian\0"
  "-internal-ethiopic-numeric\0"
  "bullets\0"
  "numbers\0"
  "words\0"
  "standard\0"
  "high\0"
  "constrained-high\0"
  "dynamic-range-limit-mix\0"
  "layer\0"
  "supports\0"
  "color-contrast\0"
  "vs\0"
  "AA\0"
  "AA-large\0"
  "AAA\0"
  "AAA-large\0"
  "drop\0"
  "raise\0"
  "xywh\0"
  "anchor\0"
  "anchor-size\0"
  "width\0"
  "height\0"
  "self-block\0"
  "self-inline\0"
  "implicit\0"
  "entry\0"
  "exit\0"
  "entry-crossing\0"
  "exit-crossing\0"
  "root\0"
  "nearest\0"
  "self\0"
  "allow-discrete\0"
  "inverted\0"
  "enabled\0"
  "initial-only\0"
  "span-left\0"
  "span-right\0"
  "x-start\0"
  "x-end\0"
  "span-x-start\0"
  "span-x-end\0"
  "x-self-start\0"
  "x-self-end\0"
  "span-x-self-start\0"
  "span-x-self-end\0"
  "span-all\0"
  "span-top\0"
  "span-bottom\0"
  "y-start\0"
  "y-end\0"
  "span-y-start\0"
  "span-y-end\0"
  "y-self-start\0"
  "y-self-end\0"
  "span-y-self-start\0"
  "span-y-self-end\0"
  "block-start\0"
  "block-end\0"
  "span-block-start\0"
  "span-block-end\0"
  "self-block-start\0"
  "self-block-end\0"
  "span-self-block-start\0"
  "span-self-block-end\0"
  "span-inline-start\0"
  "span-inline-end\0"
  "self-inline-start\0"
  "self-inline-end\0"
  "span-self-inline-start\0"
  "span-self-inline-end\0"
  "span-start\0"
  "span-end\0"
  "span-self-start\0"
  "span-self-end\0"
  "inset-area\0"
  "-internal-textarea-auto\0"
  "most-width\0"
  "most-height\0"
  "most-block-size\0"
  "most-inline-size\0"
  "flip-block\0"
  "flip-inline\0"
  "flip-start\0"
  "anchors-visible\0"
  "no-overflow\0"
  "flex-visual\0"
  "flex-flow\0"
  "grid-rows\0"
  "grid-columns\0"
  "grid-order\0"
  "context-fill\0"
  "context-stroke\0"
  "media-progress\0"
  "container-progress\0"
  "of\0"
};

static const uint16_t valueListStringOffsets[] = {
  0,
  8,
  16,
  21,
  28,
  34,
  41,
  48,
  54,
  61,
  68,
  74,
  81,
  89,
  94,
  99,
  111,
  125,
  146,
  168,
  184,
  195,
  202,
  210,
  214,
  231,
  251,
  275,
  302,
  323,
  347,
  358,
  372,
  383,
  398,
  410,
  426,
  434,
  447,
  453,
  459,
  465,
  471,
  482,
  494,
  505,
  524,
  529,
  541,
  555,
  573,
  586,
  605,
  623,
  631,
  644,
  654,
  671,
  680,
  698,
  704,
  714,
  725,
  732,
  737,
  744,
  752,
  759,
  775,
  791,
  801,
  816,
  830,
  839,
  854,
  869,
  878,
  886,
  892,
  899,
  905,
  913,
  922,
  932,
  940,
  947,
  965,
  975,
  986,
  995,
  1004,
  1010,
  1021,
  1029,
  1037,
  1047,
  1057,
  1070,
  1075,
  1080,
  1089,
  1098,
  1108,
  1120,
  1126,
  1134,
  1146,
  1151,
  1157,
  1162,
  1170,
  1175,
  1181,
  1186,
  1193,
  1198,
  1204,
  1211,
  1218,
  1222,
  1229,
  1234,
  1240,
  1247,
  1259,
  1272,
  1291,
  1303,
  1319,
  1332,
  1346,
  1357,
  1370,
  1381,
  1394,
  1405,
  1421,
  1434,
  1445,
  1452,
  1463,
  1475,
  1481,
  1491,
  1500,
  1510,
  1524,
  1539,
  1555,
  1575,
  1590,
  1599,
  1608,
  1613,
  1622,
  1631,
  1644,
  1661,
  1671,
  1688,
  1699,
  1715,
  1733,
  1746,
  1758,
  1765,
  1777,
  1788,
  1824,
  1865,
  1903,
  1946,
  1971,
  1984,
  1989,
  2013,
  2044,
  2074,
  2084,
  2097,
  2108,
  2114,
  2120,
  2127,
  2142,
  2153,
  2159,
  2169,
  2179,
  2190,
  2200,
  2206,
  2221,
  2230,
  2238,
  2243,
  2252,
  2261,
  2275,
  2284,
  2294,
  2303,
  2313,
  2325,
  2340,
  2351,
  2362,
  2370,
  2381,
  2394,
  2408,
  2422,
  2436,
  2450,
  2461,
  2470,
  2482,
  2490,
  2498,
  2509,
  2519,
  2531,
  2543,
  2553,
  2564,
  2569,
  2579,
  2591,
  2600,
  2608,
  2618,
  2625,
  2631,
  2637,
  2646,
  2660,
  2670,
  2683,
  2693,
  2704,
  2714,
  2735,
  2745,
  2756,
  2766,
  2776,
  2788,
  2802,
  2815,
  2830,
  2845,
  2860,
  2872,
  2882,
  2888,
  2896,
  2913,
  2924,
  2937,
  2950,
  2965,
  2981,
  2999,
  3015,
  3031,
  3044,
  3054,
  3064,
  3073,
  3085,
  3093,
  3103,
  3113,
  3120,
  3134,
  3144,
  3158,
  3172,
  3183,
  3193,
  3198,
  3203,
  3208,
  3219,
  3233,
  3243,
  3253,
  3265,
  3272,
  3283,
  3292,
  3301,
  3308,
  3316,
  3326,
  3336,
  3346,
  3351,
  3363,
  3373,
  3377,
  3385,
  3392,
  3402,
  3409,
  3415,
  3426,
  3438,
  3445,
  3454,
  3463,
  3473,
  3479,
  3484,
  3496,
  3506,
  3517,
  3529,
  3546,
  3561,
  3577,
  3594,
  3598,
  3611,
  3620,
  3630,
  3638,
  3647,
  3654,
  3658,
  3664,
  3673,
  3685,
  3689,
  3696,
  3720,
  3733,
  3738,
  3744,
  3751,
  3759,
  3772,
  3786,
  3801,
  3822,
  3839,
  3852,
  3863,
  3871,
  3878,
  3883,
  3890,
  3897,
  3913,
  3931,
  3939,
  3946,
  3952,
  3962,
  3967,
  3973,
  3978,
  3983,
  3992,
  4008,
  4027,
  4046,
  4056,
  4075,
  4088,
  4099,
  4113,
  4123,
  4136,
  4149,
  4161,
  4173,
  4185,
  4204,
  4217,
  4237,
  4244,
  4258,
  4268,
  4273,
  4283,
  4291,
  4299,
  4304,
  4318,
  4323,
  4336,
  4342,
  4351,
  4359,
  4371,
  4379,
  4388,
  4397,
  4407,
  4417,
  4426,
  4436,
  4446,
  4455,
  4464,
  4474,
  4484,
  4496,
  4508,
  4519,
  4530,
  4535,
  4540,
  4545,
  4556,
  4561,
  4570,
  4583,
  4600,
  4616,
  4633,
  4637,
  4641,
  4652,
  4662,
  4672,
  4682,
  4690,
  4699,
  4708,
  4724,
  4731,
  4734,
  4737,
  4740,
  4746,
  4755,
  4762,
  4768,
  4771,
  4774,
  4780,
  4794,
  4800,
  4805,
  4818,
  4830,
  4836,
  4842,
  4847,
  4852,
  4860,
  4877,
  4887,
  4903,
  4928,
  4946,
  4953,
  4960,
  4970,
  4977,
  4983,
  4990,
  5003,
  5009,
  5024,
  5038,
  5045,
  5056,
  5064,
  5073,
  5082,
  5086,
  5095,
  5104,
  5113,
  5120,
  5129,
  5134,
  5141,
  5147,
  5152,
  5162,
  5167,
  5172,
  5180,
  5188,
  5194,
  5198,
  5204,
  5210,
  5218,
  5229,
  5238,
  5250,
  5261,
  5272,
  5281,
  5295,
  5308,
  5321,
  5328,
  5333,
  5347,
  5351,
  5363,
  5370,
  5385,
  5390,
  5403,
  5413,
  5419,
  5429,
  5440,
  5466,
  5474,
  5494,
  5514,
  5537,
  5557,
  5569,
  5581,
  5593,
  5606,
  5610,
  5613,
  5621,
  5626,
  5635,
  5650,
  5664,
  5674,
  5684,
  5696,
  5707,
  5717,
  5726,
  5738,
  5749,
  5755,
  5761,
  5768,
  5786,
  5795,
  5802,
  5811,
  5817,
  5824,
  5832,
  5856,
  5865,
  5881,
  5887,
  5900,
  5912,
  5922,
  5931,
  5949,
  5961,
  5975,
  5993,
  6019,
  6035,
  6041,
  6053,
  6091,
  6098,
  6109,
  6117,
  6129,
  6137,
  6149,
  6160,
  6168,
  6176,
  6182,
  6190,
  6197,
  6205,
  6216,
  6226,
  6244,
  6253,
  6263,
  6272,
  6280,
  6287,
  6292,
  6304,
  6313,
  6322,
  6327,
  6334,
  6342,
  6351,
  6363,
  6373,
  6382,
  6392,
  6403,
  6414,
  6423,
  6429,
  6436,
  6449,
  6458,
  6464,
  6469,
  6484,
  6496,
  6510,
  6518,
  6523,
  6530,
  6543,
  6553,
  6560,
  6580,
  6595,
  6607,
  6628,
  6642,
  6661,
  6680,
  6691,
  6699,
  6705,
  6708,
  6711,
  6714,
  6720,
  6726,
  6732,
  6746,
  6758,
  6770,
  6776,
  6783,
  6788,
  6794,
  6801,
  6806,
  6810,
  6824,
  6833,
  6840,
  6848,
  6861,
  6876,
  6890,
  6906,
  6912,
  6921,
  6936,
  6944,
  6959,
  6962,
  6966,
  6982,
  6992,
  7018,
  7029,
  7041,
  7054,
  7062,
  7070,
  7073,
  7084,
  7095,
  7106,
  7117,
  7128,
  7136,
  7160,
  7167,
  7186,
  7196,
  7206,
  7212,
  7217,
  7222,
  7229,
  7236,
  7241,
  7251,
  7257,
  7266,
  7273,
  7280,
  7288,
  7300,
  7311,
  7322,
  7333,
  7344,
  7354,
  7358,
  7369,
  7375,
  7386,
  7397,
  7405,
  7410,
  7418,
  7423,
  7430,
  7438,
  7450,
  7460,
  7468,
  7474,
  7484,
  7497,
  7502,
  7512,
  7517,
  7523,
  7529,
  7541,
  7552,
  7560,
  7577,
  7593,
  7605,
  7613,
  7626,
  7637,
  7647,
  7658,
  7666,
  7685,
  7710,
  7716,
  7722,
  7731,
  7741,
  7748,
  7757,
  7770,
  7781,
  7795,
  7810,
  7816,
  7821,
  7832,
  7841,
  7848,
  7855,
  7871,
  7878,
  7891,
  7897,
  7913,
  7929,
  7944,
  7970,
  7996,
  8021,
  8027,
  8038,
  8057,
  8074,
  8098,
  8122,
  8156,
  8190,
  8208,
  8218,
  8223,
  8226,
  8237,
  8244,
  8249,
  8257,
  8266,
  8271,
  8279,
  8286,
  8297,
  8315,
  8324,
  8328,
  8337,
  8342,
  8348,
  8353,
  8371,
  8384,
  8402,
  8415,
  8428,
  8438,
  8449,
  8460,
  8471,
  8480,
  8492,
  8499,
  8509,
  8515,
  8524,
  8535,
  8543,
  8554,
  8563,
  8568,
  8580,
  8584,
  8588,
  8593,
  8597,
  8602,
  8606,
  8610,
  8616,
  8620,
  8626,
  8637,
  8649,
  8660,
  8668,
  8681,
  8685,
  8693,
  8701,
  8709,
  8716,
  8727,
  8738,
  8741,
  8751,
  8756,
  8758,
  8760,
  8762,
  8764,
  8766,
  8768,
  8770,
  8772,
  8774,
  8781,
  8790,
  8802,
  8809,
  8817,
  8825,
  8833,
  8842,
  8848,
  8855,
  8862,
  8869,
  8877,
  8882,
  8888,
  8894,
  8904,
  8915,
  8926,
  8937,
  8949,
  8951,
  8953,
  8955,
  8960,
  8964,
  8970,
  8981,
  8986,
  8999,
  9003,
  9007,
  9013,
  9023,
  9027,
  9031,
  9035,
  9040,
  9045,
  9051,
  9056,
  9060,
  9064,
  9067,
  9072,
  9080,
  9085,
  9089,
  9093,
  9098,
  9104,
  9108,
  9112,
  9121,
  9131,
  9135,
  9138,
  9140,
  9150,
  9160,
  9166,
  9171,
  9182,
  9194,
  9207,
  9225,
  9241,
  9260,
  9277,
  9287,
  9296,
  9300,
  9325,
  9329,
  9333,
  9344,
  9349,
  9355,
  9361,
  9374,
  9377,
  9385,
  9389,
  9398,
  9403,
  9409,
  9423,
  9428,
  9434,
  9439,
  9446,
  9453,
  9475,
  9487,
  9498,
  9507,
  9518,
  9525,
  9532,
  9543,
  9548,
  9553,
  9560,
  9567,
  9576,
  9584,
  9593,
  9601,
  9633,
  9663,
  9695,
  9725,
  9756,
  9788,
  9818,
  9835,
  9860,
  9885,
  9912,
  9920,
  9928,
  9934,
  9943,
  9948,
  9965,
  9989,
  9995,
  10004,
  10019,
  10022,
  10025,
  10034,
  10038,
  10048,
  10053,
  10059,
  10064,
  10071,
  10083,
  10089,
  10096,
  10107,
  10119,
  10128,
  10134,
  10139,
  10154,
  10168,
  10173,
  10181,
  10186,
  10201,
  10210,
  10218,
  10231,
  10241,
  10252,
  10260,
  10266,
  10279,
  10290,
  10303,
  10314,
  10332,
  10348,
  10357,
  10366,
  10378,
  10386,
  10392,
  10405,
  10416,
  10429,
  10440,
  10458,
  10474,
  10486,
  10496,
  10513,
  10528,
  10545,
  10560,
  10582,
  10602,
  10620,
  10636,
  10654,
  10670,
  10693,
  10714,
  10725,
  10734,
  10750,
  10764,
  10775,
  10799,
  10810,
  10822,
  10838,
  10855,
  10866,
  10878,
  10889,
  10905,
  10917,
  10929,
  10939,
  10949,
  10962,
  10973,
  10986,
  11001,
  11016,
  11035,
};

/* maximum key range = 10042, duplicates = 0 */

class CSSValueKeywordsHash
{
private:
  static inline unsigned int value_hash_function (const char *str, unsigned int len);
public:
  static const struct Value *findValueImpl (const char *str, unsigned int len);
};

inline unsigned int
CSSValueKeywordsHash::value_hash_function (const char *str, unsigned int len)
{
  static const unsigned short asso_values[] =
    {
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045,   143,   161,   130,     7,     3,
          5,     7,     5,    13,     4, 10045,     4,     3,     5,     4,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045,     4,   855,   235,
         74,     3,   641,    16,  2002,     4,  1487,   931,    96,    29,
          5,    51,   290,  1779,     7,    71,     3,   267,   127,  2872,
       2322,   940,  1357,    80,     6, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045,
      10045, 10045, 10045, 10045, 10045, 10045, 10045, 10045
    };
  unsigned int hval = 0;

  switch (len)
    {
      default:
        hval += asso_values[(unsigned char)str[41]];
        [[fallthrough]];
      case 41:
        hval += asso_values[(unsigned char)str[40]];
        [[fallthrough]];
      case 40:
        hval += asso_values[(unsigned char)str[39]];
        [[fallthrough]];
      case 39:
        hval += asso_values[(unsigned char)str[38]];
        [[fallthrough]];
      case 38:
        hval += asso_values[(unsigned char)str[37]];
        [[fallthrough]];
      case 37:
        hval += asso_values[(unsigned char)str[36]];
        [[fallthrough]];
      case 36:
        hval += asso_values[(unsigned char)str[35]];
        [[fallthrough]];
      case 35:
        hval += asso_values[(unsigned char)str[34]];
        [[fallthrough]];
      case 34:
        hval += asso_values[(unsigned char)str[33]];
        [[fallthrough]];
      case 33:
        hval += asso_values[(unsigned char)str[32]];
        [[fallthrough]];
      case 32:
        hval += asso_values[(unsigned char)str[31]];
        [[fallthrough]];
      case 31:
        hval += asso_values[(unsigned char)str[30]];
        [[fallthrough]];
      case 30:
        hval += asso_values[(unsigned char)str[29]];
        [[fallthrough]];
      case 29:
        hval += asso_values[(unsigned char)str[28]];
        [[fallthrough]];
      case 28:
        hval += asso_values[(unsigned char)str[27]];
        [[fallthrough]];
      case 27:
        hval += asso_values[(unsigned char)str[26]];
        [[fallthrough]];
      case 26:
        hval += asso_values[(unsigned char)str[25]];
        [[fallthrough]];
      case 25:
        hval += asso_values[(unsigned char)str[24]];
        [[fallthrough]];
      case 24:
        hval += asso_values[(unsigned char)str[23]];
        [[fallthrough]];
      case 23:
        hval += asso_values[(unsigned char)str[22]];
        [[fallthrough]];
      case 22:
        hval += asso_values[(unsigned char)str[21]];
        [[fallthrough]];
      case 21:
        hval += asso_values[(unsigned char)str[20]];
        [[fallthrough]];
      case 20:
        hval += asso_values[(unsigned char)str[19]];
        [[fallthrough]];
      case 19:
        hval += asso_values[(unsigned char)str[18]];
        [[fallthrough]];
      case 18:
        hval += asso_values[(unsigned char)str[17]];
        [[fallthrough]];
      case 17:
        hval += asso_values[(unsigned char)str[16]];
        [[fallthrough]];
      case 16:
        hval += asso_values[(unsigned char)str[15]];
        [[fallthrough]];
      case 15:
        hval += asso_values[(unsigned char)str[14]];
        [[fallthrough]];
      case 14:
        hval += asso_values[(unsigned char)str[13]];
        [[fallthrough]];
      case 13:
        hval += asso_values[(unsigned char)str[12]];
        [[fallthrough]];
      case 12:
        hval += asso_values[(unsigned char)str[11]];
        [[fallthrough]];
      case 11:
        hval += asso_values[(unsigned char)str[10]+1];
        [[fallthrough]];
      case 10:
        hval += asso_values[(unsigned char)str[9]];
        [[fallthrough]];
      case 9:
        hval += asso_values[(unsigned char)str[8]];
        [[fallthrough]];
      case 8:
        hval += asso_values[(unsigned char)str[7]];
        [[fallthrough]];
      case 7:
        hval += asso_values[(unsigned char)str[6]];
        [[fallthrough]];
      case 6:
        hval += asso_values[(unsigned char)str[5]];
        [[fallthrough]];
      case 5:
        hval += asso_values[(unsigned char)str[4]];
        [[fallthrough]];
      case 4:
        hval += asso_values[(unsigned char)str[3]+2];
        [[fallthrough]];
      case 3:
        hval += asso_values[(unsigned char)str[2]];
        [[fallthrough]];
      case 2:
        hval += asso_values[(unsigned char)str[1]];
        [[fallthrough]];
      case 1:
        hval += asso_values[(unsigned char)str[0]+1];
        break;
    }
  return hval;
}

struct CSSValueStringPool_t
  {
    char CSSValueStringPool_str0[sizeof("s")];
    char CSSValueStringPool_str1[sizeof("h")];
    char CSSValueStringPool_str2[sizeof("sin")];
    char CSSValueStringPool_str3[sizeof("min")];
    char CSSValueStringPool_str4[sizeof("start")];
    char CSSValueStringPool_str5[sizeof("drag")];
    char CSSValueStringPool_str6[sizeof("l")];
    char CSSValueStringPool_str7[sizeof("seagreen")];
    char CSSValueStringPool_str8[sizeof("meter")];
    char CSSValueStringPool_str9[sizeof("miter")];
    char CSSValueStringPool_str10[sizeof("lr")];
    char CSSValueStringPool_str11[sizeof("ltr")];
    char CSSValueStringPool_str12[sizeof("fine")];
    char CSSValueStringPool_str13[sizeof("hanging")];
    char CSSValueStringPool_str14[sizeof("dark")];
    char CSSValueStringPool_str15[sizeof("mark")];
    char CSSValueStringPool_str16[sizeof("large")];
    char CSSValueStringPool_str17[sizeof("darken")];
    char CSSValueStringPool_str18[sizeof("magenta")];
    char CSSValueStringPool_str19[sizeof("larger")];
    char CSSValueStringPool_str20[sizeof("dot")];
    char CSSValueStringPool_str21[sizeof("linen")];
    char CSSValueStringPool_str22[sizeof("nan")];
    char CSSValueStringPool_str23[sizeof("linear")];
    char CSSValueStringPool_str24[sizeof("drop")];
    char CSSValueStringPool_str25[sizeof("r")];
    char CSSValueStringPool_str26[sizeof("c")];
    char CSSValueStringPool_str27[sizeof("darkgreen")];
    char CSSValueStringPool_str28[sizeof("lime")];
    char CSSValueStringPool_str29[sizeof("more")];
    char CSSValueStringPool_str30[sizeof("z")];
    char CSSValueStringPool_str31[sizeof("disc")];
    char CSSValueStringPool_str32[sizeof("steps")];
    char CSSValueStringPool_str33[sizeof("log")];
    char CSSValueStringPool_str34[sizeof("hide")];
    char CSSValueStringPool_str35[sizeof("longer")];
    char CSSValueStringPool_str36[sizeof("srgb")];
    char CSSValueStringPool_str37[sizeof("field")];
    char CSSValueStringPool_str38[sizeof("rem")];
    char CSSValueStringPool_str39[sizeof("slice")];
    char CSSValueStringPool_str40[sizeof("help")];
    char CSSValueStringPool_str41[sizeof("limegreen")];
    char CSSValueStringPool_str42[sizeof("ledger")];
    char CSSValueStringPool_str43[sizeof("fill")];
    char CSSValueStringPool_str44[sizeof("none")];
    char CSSValueStringPool_str45[sizeof("from")];
    char CSSValueStringPool_str46[sizeof("markers")];
    char CSSValueStringPool_str47[sizeof("darkred")];
    char CSSValueStringPool_str48[sizeof("darkorange")];
    char CSSValueStringPool_str49[sizeof("mod")];
    char CSSValueStringPool_str50[sizeof("format")];
    char CSSValueStringPool_str51[sizeof("small")];
    char CSSValueStringPool_str52[sizeof("nearest")];
    char CSSValueStringPool_str53[sizeof("svg")];
    char CSSValueStringPool_str54[sizeof("smaller")];
    char CSSValueStringPool_str55[sizeof("red")];
    char CSSValueStringPool_str56[sizeof("frames")];
    char CSSValueStringPool_str57[sizeof("ridge")];
    char CSSValueStringPool_str58[sizeof("reset")];
    char CSSValueStringPool_str59[sizeof("rl")];
    char CSSValueStringPool_str60[sizeof("sides")];
    char CSSValueStringPool_str61[sizeof("rtl")];
    char CSSValueStringPool_str62[sizeof("hsl")];
    char CSSValueStringPool_str63[sizeof("letter")];
    char CSSValueStringPool_str64[sizeof("nonzero")];
    char CSSValueStringPool_str65[sizeof("calc")];
    char CSSValueStringPool_str66[sizeof("cell")];
    char CSSValueStringPool_str67[sizeof("clip")];
    char CSSValueStringPool_str68[sizeof("stable")];
    char CSSValueStringPool_str69[sizeof("circle")];
    char CSSValueStringPool_str70[sizeof("cos")];
    char CSSValueStringPool_str71[sizeof("filled")];
    char CSSValueStringPool_str72[sizeof("move")];
    char CSSValueStringPool_str73[sizeof("hover")];
    char CSSValueStringPool_str74[sizeof("coarse")];
    char CSSValueStringPool_str75[sizeof("salmon")];
    char CSSValueStringPool_str76[sizeof("unicase")];
    char CSSValueStringPool_str77[sizeof("fast")];
    char CSSValueStringPool_str78[sizeof("center")];
    char CSSValueStringPool_str79[sizeof("unset")];
    char CSSValueStringPool_str80[sizeof("revert")];
    char CSSValueStringPool_str81[sizeof("under")];
    char CSSValueStringPool_str82[sizeof("url")];
    char CSSValueStringPool_str83[sizeof("last")];
    char CSSValueStringPool_str84[sizeof("zoom")];
    char CSSValueStringPool_str85[sizeof("b")];
    char CSSValueStringPool_str86[sizeof("b4")];
    char CSSValueStringPool_str87[sizeof("step-end")];
    char CSSValueStringPool_str88[sizeof("flat")];
    char CSSValueStringPool_str89[sizeof("forestgreen")];
    char CSSValueStringPool_str90[sizeof("step-start")];
    char CSSValueStringPool_str91[sizeof("b5")];
    char CSSValueStringPool_str92[sizeof("beige")];
    char CSSValueStringPool_str93[sizeof("scale")];
    char CSSValueStringPool_str94[sizeof("linear-gradient")];
    char CSSValueStringPool_str95[sizeof("normal")];
    char CSSValueStringPool_str96[sizeof("dotted")];
    char CSSValueStringPool_str97[sizeof("navy")];
    char CSSValueStringPool_str98[sizeof("crimson")];
    char CSSValueStringPool_str99[sizeof("lavender")];
    char CSSValueStringPool_str100[sizeof("unicode")];
    char CSSValueStringPool_str101[sizeof("content")];
    char CSSValueStringPool_str102[sizeof("screen")];
    char CSSValueStringPool_str103[sizeof("contain")];
    char CSSValueStringPool_str104[sizeof("hue")];
    char CSSValueStringPool_str105[sizeof("cover")];
    char CSSValueStringPool_str106[sizeof("tan")];
    char CSSValueStringPool_str107[sizeof("teal")];
    char CSSValueStringPool_str108[sizeof("dense")];
    char CSSValueStringPool_str109[sizeof("on")];
    char CSSValueStringPool_str110[sizeof("first")];
    char CSSValueStringPool_str111[sizeof("reverse")];
    char CSSValueStringPool_str112[sizeof("darksalmon")];
    char CSSValueStringPool_str113[sizeof("root")];
    char CSSValueStringPool_str114[sizeof("space")];
    char CSSValueStringPool_str115[sizeof("sienna")];
    char CSSValueStringPool_str116[sizeof("sign")];
    char CSSValueStringPool_str117[sizeof("central")];
    char CSSValueStringPool_str118[sizeof("to")];
    char CSSValueStringPool_str119[sizeof("from-image")];
    char CSSValueStringPool_str120[sizeof("darkviolet")];
    char CSSValueStringPool_str121[sizeof("scale3d")];
    char CSSValueStringPool_str122[sizeof("rec2020")];
    char CSSValueStringPool_str123[sizeof("underline")];
    char CSSValueStringPool_str124[sizeof("contents")];
    char CSSValueStringPool_str125[sizeof("contrast")];
    char CSSValueStringPool_str126[sizeof("sesame")];
    char CSSValueStringPool_str127[sizeof("decreasing")];
    char CSSValueStringPool_str128[sizeof("raise")];
    char CSSValueStringPool_str129[sizeof("flip-start")];
    char CSSValueStringPool_str130[sizeof("rotate")];
    char CSSValueStringPool_str131[sizeof("cap")];
    char CSSValueStringPool_str132[sizeof("less")];
    char CSSValueStringPool_str133[sizeof("static")];
    char CSSValueStringPool_str134[sizeof("moccasin")];
    char CSSValueStringPool_str135[sizeof("legal")];
    char CSSValueStringPool_str136[sizeof("zoom-in")];
    char CSSValueStringPool_str137[sizeof("false")];
    char CSSValueStringPool_str138[sizeof("repeat")];
    char CSSValueStringPool_str139[sizeof("loose")];
    char CSSValueStringPool_str140[sizeof("compact")];
    char CSSValueStringPool_str141[sizeof("hsla")];
    char CSSValueStringPool_str142[sizeof("list-item")];
    char CSSValueStringPool_str143[sizeof("selector")];
    char CSSValueStringPool_str144[sizeof("clear")];
    char CSSValueStringPool_str145[sizeof("up")];
    char CSSValueStringPool_str146[sizeof("mintcream")];
    char CSSValueStringPool_str147[sizeof("darkolivegreen")];
    char CSSValueStringPool_str148[sizeof("over")];
    char CSSValueStringPool_str149[sizeof("srgb-linear")];
    char CSSValueStringPool_str150[sizeof("baseline")];
    char CSSValueStringPool_str151[sizeof("rect")];
    char CSSValueStringPool_str152[sizeof("rotate3d")];
    char CSSValueStringPool_str153[sizeof("keep-all")];
    char CSSValueStringPool_str154[sizeof("standard")];
    char CSSValueStringPool_str155[sizeof("coral")];
    char CSSValueStringPool_str156[sizeof("only")];
    char CSSValueStringPool_str157[sizeof("hue-rotate")];
    char CSSValueStringPool_str158[sizeof("bevel")];
    char CSSValueStringPool_str159[sizeof("destination-in")];
    char CSSValueStringPool_str160[sizeof("darkgoldenrod")];
    char CSSValueStringPool_str161[sizeof("recto")];
    char CSSValueStringPool_str162[sizeof("double")];
    char CSSValueStringPool_str163[sizeof("copy")];
    char CSSValueStringPool_str164[sizeof("bottom")];
    char CSSValueStringPool_str165[sizeof("numbers")];
    char CSSValueStringPool_str166[sizeof("clone")];
    char CSSValueStringPool_str167[sizeof("clamp")];
    char CSSValueStringPool_str168[sizeof("collection")];
    char CSSValueStringPool_str169[sizeof("to-zero")];
    char CSSValueStringPool_str170[sizeof("overline")];
    char CSSValueStringPool_str171[sizeof("standalone")];
    char CSSValueStringPool_str172[sizeof("relative")];
    char CSSValueStringPool_str173[sizeof("rotate-right")];
    char CSSValueStringPool_str174[sizeof("separate")];
    char CSSValueStringPool_str175[sizeof("no-repeat")];
    char CSSValueStringPool_str176[sizeof("caption")];
    char CSSValueStringPool_str177[sizeof("true")];
    char CSSValueStringPool_str178[sizeof("trim-start")];
    char CSSValueStringPool_str179[sizeof("super")];
    char CSSValueStringPool_str180[sizeof("span")];
    char CSSValueStringPool_str181[sizeof("collapse")];
    char CSSValueStringPool_str182[sizeof("blur")];
    char CSSValueStringPool_str183[sizeof("top")];
    char CSSValueStringPool_str184[sizeof("numeric")];
    char CSSValueStringPool_str185[sizeof("orange")];
    char CSSValueStringPool_str186[sizeof("replace")];
    char CSSValueStringPool_str187[sizeof("blue")];
    char CSSValueStringPool_str188[sizeof("custom")];
    char CSSValueStringPool_str189[sizeof("butt")];
    char CSSValueStringPool_str190[sizeof("triangle")];
    char CSSValueStringPool_str191[sizeof("tomato")];
    char CSSValueStringPool_str192[sizeof("no-clip")];
    char CSSValueStringPool_str193[sizeof("e")];
    char CSSValueStringPool_str194[sizeof("space-all")];
    char CSSValueStringPool_str195[sizeof("local")];
    char CSSValueStringPool_str196[sizeof("ornaments")];
    char CSSValueStringPool_str197[sizeof("-internal-center")];
    char CSSValueStringPool_str198[sizeof("bisque")];
    char CSSValueStringPool_str199[sizeof("hand")];
    char CSSValueStringPool_str200[sizeof("destination-over")];
    char CSSValueStringPool_str201[sizeof("running")];
    char CSSValueStringPool_str202[sizeof("safe")];
    char CSSValueStringPool_str203[sizeof("bullets")];
    char CSSValueStringPool_str204[sizeof("button")];
    char CSSValueStringPool_str205[sizeof("orangered")];
    char CSSValueStringPool_str206[sizeof("counter")];
    char CSSValueStringPool_str207[sizeof("zoom-out")];
    char CSSValueStringPool_str208[sizeof("supports")];
    char CSSValueStringPool_str209[sizeof("leading")];
    char CSSValueStringPool_str210[sizeof("spelling-error")];
    char CSSValueStringPool_str211[sizeof("oldlace")];
    char CSSValueStringPool_str212[sizeof("fullscreen")];
    char CSSValueStringPool_str213[sizeof("min-content")];
    char CSSValueStringPool_str214[sizeof("source-in")];
    char CSSValueStringPool_str215[sizeof("end")];
    char CSSValueStringPool_str216[sizeof("fit-content")];
    char CSSValueStringPool_str217[sizeof("hidden")];
    char CSSValueStringPool_str218[sizeof("ease")];
    char CSSValueStringPool_str219[sizeof("translate")];
    char CSSValueStringPool_str220[sizeof("cursive")];
    char CSSValueStringPool_str221[sizeof("round")];
    char CSSValueStringPool_str222[sizeof("translate3d")];
    char CSSValueStringPool_str223[sizeof("counters")];
    char CSSValueStringPool_str224[sizeof("env")];
    char CSSValueStringPool_str225[sizeof("closest-side")];
    char CSSValueStringPool_str226[sizeof("destination-out")];
    char CSSValueStringPool_str227[sizeof("darkseagreen")];
    char CSSValueStringPool_str228[sizeof("left")];
    char CSSValueStringPool_str229[sizeof("element")];
    char CSSValueStringPool_str230[sizeof("crispedges")];
    char CSSValueStringPool_str231[sizeof("span-end")];
    char CSSValueStringPool_str232[sizeof("balance")];
    char CSSValueStringPool_str233[sizeof("destination-atop")];
    char CSSValueStringPool_str234[sizeof("span-start")];
    char CSSValueStringPool_str235[sizeof("emoji")];
    char CSSValueStringPool_str236[sizeof("middle")];
    char CSSValueStringPool_str237[sizeof("outset")];
    char CSSValueStringPool_str238[sizeof("a")];
    char CSSValueStringPool_str239[sizeof("at")];
    char CSSValueStringPool_str240[sizeof("aa")];
    char CSSValueStringPool_str241[sizeof("a4")];
    char CSSValueStringPool_str242[sizeof("spell-out")];
    char CSSValueStringPool_str243[sizeof("a3")];
    char CSSValueStringPool_str244[sizeof("aaa")];
    char CSSValueStringPool_str245[sizeof("attr")];
    char CSSValueStringPool_str246[sizeof("simplified")];
    char CSSValueStringPool_str247[sizeof("repeating-linear-gradient")];
    char CSSValueStringPool_str248[sizeof("a5")];
    char CSSValueStringPool_str249[sizeof("open")];
    char CSSValueStringPool_str250[sizeof("arg")];
    char CSSValueStringPool_str251[sizeof("small-caps")];
    char CSSValueStringPool_str252[sizeof("folded")];
    char CSSValueStringPool_str253[sizeof("ease-in")];
    char CSSValueStringPool_str254[sizeof("lab")];
    char CSSValueStringPool_str255[sizeof("enabled")];
    char CSSValueStringPool_str256[sizeof("blueviolet")];
    char CSSValueStringPool_str257[sizeof("space-around")];
    char CSSValueStringPool_str258[sizeof("outside")];
    char CSSValueStringPool_str259[sizeof("no-drag")];
    char CSSValueStringPool_str260[sizeof("continuous")];
    char CSSValueStringPool_str261[sizeof("background")];
    char CSSValueStringPool_str262[sizeof("span-all")];
    char CSSValueStringPool_str263[sizeof("condensed")];
    char CSSValueStringPool_str264[sizeof("of")];
    char CSSValueStringPool_str265[sizeof("x")];
    char CSSValueStringPool_str266[sizeof("use-script")];
    char CSSValueStringPool_str267[sizeof("rgb")];
    char CSSValueStringPool_str268[sizeof("linearrgb")];
    char CSSValueStringPool_str269[sizeof("border")];
    char CSSValueStringPool_str270[sizeof("style")];
    char CSSValueStringPool_str271[sizeof("source-over")];
    char CSSValueStringPool_str272[sizeof("darkmagenta")];
    char CSSValueStringPool_str273[sizeof("no-common-ligatures")];
    char CSSValueStringPool_str274[sizeof("from-font")];
    char CSSValueStringPool_str275[sizeof("dimgrey")];
    char CSSValueStringPool_str276[sizeof("dimgray")];
    char CSSValueStringPool_str277[sizeof("alternate")];
    char CSSValueStringPool_str278[sizeof("layer")];
    char CSSValueStringPool_str279[sizeof("xor")];
    char CSSValueStringPool_str280[sizeof("snow")];
    char CSSValueStringPool_str281[sizeof("add")];
    char CSSValueStringPool_str282[sizeof("hotpink")];
    char CSSValueStringPool_str283[sizeof("turquoise")];
    char CSSValueStringPool_str284[sizeof("darkgrey")];
    char CSSValueStringPool_str285[sizeof("darkgray")];
    char CSSValueStringPool_str286[sizeof("jis78")];
    char CSSValueStringPool_str287[sizeof("ray")];
    char CSSValueStringPool_str288[sizeof("jis04")];
    char CSSValueStringPool_str289[sizeof("jis90")];
    char CSSValueStringPool_str290[sizeof("jis83")];
    char CSSValueStringPool_str291[sizeof("traditional")];
    char CSSValueStringPool_str292[sizeof("flip-inline")];
    char CSSValueStringPool_str293[sizeof("bold")];
    char CSSValueStringPool_str294[sizeof("source-out")];
    char CSSValueStringPool_str295[sizeof("digits")];
    char CSSValueStringPool_str296[sizeof("styleset")];
    char CSSValueStringPool_str297[sizeof("bolder")];
    char CSSValueStringPool_str298[sizeof("aa-large")];
    char CSSValueStringPool_str299[sizeof("currentcolor")];
    char CSSValueStringPool_str300[sizeof("uppercase")];
    char CSSValueStringPool_str301[sizeof("all")];
    char CSSValueStringPool_str302[sizeof("double-circle")];
    char CSSValueStringPool_str303[sizeof("repeating-radial-gradient")];
    char CSSValueStringPool_str304[sizeof("span-top")];
    char CSSValueStringPool_str305[sizeof("closest-corner")];
    char CSSValueStringPool_str306[sizeof("unsafe")];
    char CSSValueStringPool_str307[sizeof("disclosure-open")];
    char CSSValueStringPool_str308[sizeof("slow")];
    char CSSValueStringPool_str309[sizeof("flow")];
    char CSSValueStringPool_str310[sizeof("selecteditem")];
    char CSSValueStringPool_str311[sizeof("aaa-large")];
    char CSSValueStringPool_str312[sizeof("tb")];
    char CSSValueStringPool_str313[sizeof("sub")];
    char CSSValueStringPool_str314[sizeof("radio")];
    char CSSValueStringPool_str315[sizeof("table")];
    char CSSValueStringPool_str316[sizeof("base-select")];
    char CSSValueStringPool_str317[sizeof("sans-serif")];
    char CSSValueStringPool_str318[sizeof("atan")];
    char CSSValueStringPool_str319[sizeof("solid")];
    char CSSValueStringPool_str320[sizeof("atan2")];
    char CSSValueStringPool_str321[sizeof("lr-tb")];
    char CSSValueStringPool_str322[sizeof("fantasy")];
    char CSSValueStringPool_str323[sizeof("forwards")];
    char CSSValueStringPool_str324[sizeof("rgba")];
    char CSSValueStringPool_str325[sizeof("radial")];
    char CSSValueStringPool_str326[sizeof("strict")];
    char CSSValueStringPool_str327[sizeof("repeating-conic-gradient")];
    char CSSValueStringPool_str328[sizeof("borderless")];
    char CSSValueStringPool_str329[sizeof("scaley")];
    char CSSValueStringPool_str330[sizeof("on-demand")];
    char CSSValueStringPool_str331[sizeof("ease-out")];
    char CSSValueStringPool_str332[sizeof("slategrey")];
    char CSSValueStringPool_str333[sizeof("slategray")];
    char CSSValueStringPool_str334[sizeof("fabricated")];
    char CSSValueStringPool_str335[sizeof("no-preference")];
    char CSSValueStringPool_str336[sizeof("subgrid")];
    char CSSValueStringPool_str337[sizeof("transparent")];
    char CSSValueStringPool_str338[sizeof("asin")];
    char CSSValueStringPool_str339[sizeof("darkcyan")];
    char CSSValueStringPool_str340[sizeof("sepia")];
    char CSSValueStringPool_str341[sizeof("no-drop")];
    char CSSValueStringPool_str342[sizeof("historical-ligatures")];
    char CSSValueStringPool_str343[sizeof("-internal-media-control")];
    char CSSValueStringPool_str344[sizeof("brown")];
    char CSSValueStringPool_str345[sizeof("no-autospace")];
    char CSSValueStringPool_str346[sizeof("deeppink")];
    char CSSValueStringPool_str347[sizeof("default")];
    char CSSValueStringPool_str348[sizeof("darkslategrey")];
    char CSSValueStringPool_str349[sizeof("darkslategray")];
    char CSSValueStringPool_str350[sizeof("evenodd")];
    char CSSValueStringPool_str351[sizeof("alias")];
    char CSSValueStringPool_str352[sizeof("darkblue")];
    char CSSValueStringPool_str353[sizeof("disclosure-closed")];
    char CSSValueStringPool_str354[sizeof("stylistic")];
    char CSSValueStringPool_str355[sizeof("black")];
    char CSSValueStringPool_str356[sizeof("ruby")];
    char CSSValueStringPool_str357[sizeof("tabbed")];
    char CSSValueStringPool_str358[sizeof("mistyrose")];
    char CSSValueStringPool_str359[sizeof("landscape")];
    char CSSValueStringPool_str360[sizeof("rl-tb")];
    char CSSValueStringPool_str361[sizeof("x-start")];
    char CSSValueStringPool_str362[sizeof("decimal")];
    char CSSValueStringPool_str363[sizeof("rotatey")];
    char CSSValueStringPool_str364[sizeof("cyan")];
    char CSSValueStringPool_str365[sizeof("medium")];
    char CSSValueStringPool_str366[sizeof("browser")];
    char CSSValueStringPool_str367[sizeof("block")];
    char CSSValueStringPool_str368[sizeof("springgreen")];
    char CSSValueStringPool_str369[sizeof("cross-fade")];
    char CSSValueStringPool_str370[sizeof("font-format")];
    char CSSValueStringPool_str371[sizeof("steelblue")];
    char CSSValueStringPool_str372[sizeof("y")];
    char CSSValueStringPool_str373[sizeof("flow-root")];
    char CSSValueStringPool_str374[sizeof("no-punctuation")];
    char CSSValueStringPool_str375[sizeof("logical")];
    char CSSValueStringPool_str376[sizeof("dodgerblue")];
    char CSSValueStringPool_str377[sizeof("tb-rl")];
    char CSSValueStringPool_str378[sizeof("radial-gradient")];
    char CSSValueStringPool_str379[sizeof("darkslateblue")];
    char CSSValueStringPool_str380[sizeof("size")];
    char CSSValueStringPool_str381[sizeof("cadetblue")];
    char CSSValueStringPool_str382[sizeof("diagonal-fractions")];
    char CSSValueStringPool_str383[sizeof("x-small")];
    char CSSValueStringPool_str384[sizeof("ordinal")];
    char CSSValueStringPool_str385[sizeof("acos")];
    char CSSValueStringPool_str386[sizeof("first-baseline")];
    char CSSValueStringPool_str387[sizeof("alternate-reverse")];
    char CSSValueStringPool_str388[sizeof("symbolic")];
    char CSSValueStringPool_str389[sizeof("space-first")];
    char CSSValueStringPool_str390[sizeof("lining-nums")];
    char CSSValueStringPool_str391[sizeof("x-large")];
    char CSSValueStringPool_str392[sizeof("jump-none")];
    char CSSValueStringPool_str393[sizeof("x-end")];
    char CSSValueStringPool_str394[sizeof("dynamic")];
    char CSSValueStringPool_str395[sizeof("slateblue")];
    char CSSValueStringPool_str396[sizeof("legacy")];
    char CSSValueStringPool_str397[sizeof("jump-end")];
    char CSSValueStringPool_str398[sizeof("overlay")];
    char CSSValueStringPool_str399[sizeof("jump-start")];
    char CSSValueStringPool_str400[sizeof("repeat-y")];
    char CSSValueStringPool_str401[sizeof("flex")];
    char CSSValueStringPool_str402[sizeof("span-left")];
    char CSSValueStringPool_str403[sizeof("-internal-upper-armenian")];
    char CSSValueStringPool_str404[sizeof("literal-punctuation")];
    char CSSValueStringPool_str405[sizeof("status-bar")];
    char CSSValueStringPool_str406[sizeof("mediumseagreen")];
    char CSSValueStringPool_str407[sizeof("minimal-ui")];
    char CSSValueStringPool_str408[sizeof("in")];
    char CSSValueStringPool_str409[sizeof("cyclic")];
    char CSSValueStringPool_str410[sizeof("math")];
    char CSSValueStringPool_str411[sizeof("subtract")];
    char CSSValueStringPool_str412[sizeof("features-aat")];
    char CSSValueStringPool_str413[sizeof("luminance")];
    char CSSValueStringPool_str414[sizeof("high")];
    char CSSValueStringPool_str415[sizeof("type")];
    char CSSValueStringPool_str416[sizeof("rotate-left")];
    char CSSValueStringPool_str417[sizeof("after")];
    char CSSValueStringPool_str418[sizeof("cornsilk")];
    char CSSValueStringPool_str419[sizeof("opacity")];
    char CSSValueStringPool_str420[sizeof("ultra-condensed")];
    char CSSValueStringPool_str421[sizeof("light")];
    char CSSValueStringPool_str422[sizeof("block-end")];
    char CSSValueStringPool_str423[sizeof("lighten")];
    char CSSValueStringPool_str424[sizeof("lighter")];
    char CSSValueStringPool_str425[sizeof("space-evenly")];
    char CSSValueStringPool_str426[sizeof("blink")];
    char CSSValueStringPool_str427[sizeof("historical-forms")];
    char CSSValueStringPool_str428[sizeof("display-p3")];
    char CSSValueStringPool_str429[sizeof("buttonface")];
    char CSSValueStringPool_str430[sizeof("off")];
    char CSSValueStringPool_str431[sizeof("lightgreen")];
    char CSSValueStringPool_str432[sizeof("right")];
    char CSSValueStringPool_str433[sizeof("inset")];
    char CSSValueStringPool_str434[sizeof("serif")];
    char CSSValueStringPool_str435[sizeof("system-ui")];
    char CSSValueStringPool_str436[sizeof("se-resize")];
    char CSSValueStringPool_str437[sizeof("entry")];
    char CSSValueStringPool_str438[sizeof("s-resize")];
    char CSSValueStringPool_str439[sizeof("scalez")];
    char CSSValueStringPool_str440[sizeof("ease-in-out")];
    char CSSValueStringPool_str441[sizeof("embed")];
    char CSSValueStringPool_str442[sizeof("isolate")];
    char CSSValueStringPool_str443[sizeof("ne-resize")];
    char CSSValueStringPool_str444[sizeof("slider-vertical")];
    char CSSValueStringPool_str445[sizeof("dashed")];
    char CSSValueStringPool_str446[sizeof("invert")];
    char CSSValueStringPool_str447[sizeof("n-resize")];
    char CSSValueStringPool_str448[sizeof("mandatory")];
    char CSSValueStringPool_str449[sizeof("optional")];
    char CSSValueStringPool_str450[sizeof("semi-condensed")];
    char CSSValueStringPool_str451[sizeof("translatey")];
    char CSSValueStringPool_str452[sizeof("flex-end")];
    char CSSValueStringPool_str453[sizeof("span-bottom")];
    char CSSValueStringPool_str454[sizeof("flex-start")];
    char CSSValueStringPool_str455[sizeof("span-self-start")];
    char CSSValueStringPool_str456[sizeof("ns-resize")];
    char CSSValueStringPool_str457[sizeof("table-cell")];
    char CSSValueStringPool_str458[sizeof("span-inline-end")];
    char CSSValueStringPool_str459[sizeof("y-start")];
    char CSSValueStringPool_str460[sizeof("span-inline-start")];
    char CSSValueStringPool_str461[sizeof("inverted")];
    char CSSValueStringPool_str462[sizeof("rotatez")];
    char CSSValueStringPool_str463[sizeof("common-ligatures")];
    char CSSValueStringPool_str464[sizeof("initial")];
    char CSSValueStringPool_str465[sizeof("all-scroll")];
    char CSSValueStringPool_str466[sizeof("italic")];
    char CSSValueStringPool_str467[sizeof("media-progress")];
    char CSSValueStringPool_str468[sizeof("reset-size")];
    char CSSValueStringPool_str469[sizeof("inset-area")];
    char CSSValueStringPool_str470[sizeof("image-set")];
    char CSSValueStringPool_str471[sizeof("break-all")];
    char CSSValueStringPool_str472[sizeof("calc-size")];
    char CSSValueStringPool_str473[sizeof("after-edge")];
    char CSSValueStringPool_str474[sizeof("both")];
    char CSSValueStringPool_str475[sizeof("abs")];
    char CSSValueStringPool_str476[sizeof("mediumspringgreen")];
    char CSSValueStringPool_str477[sizeof("pi")];
    char CSSValueStringPool_str478[sizeof("p3")];
    char CSSValueStringPool_str479[sizeof("pre")];
    char CSSValueStringPool_str480[sizeof("truetype")];
    char CSSValueStringPool_str481[sizeof("col-resize")];
    char CSSValueStringPool_str482[sizeof("any")];
    char CSSValueStringPool_str483[sizeof("incremental")];
    char CSSValueStringPool_str484[sizeof("block-start")];
    char CSSValueStringPool_str485[sizeof("page")];
    char CSSValueStringPool_str486[sizeof("pink")];
    char CSSValueStringPool_str487[sizeof("multiply")];
    char CSSValueStringPool_str488[sizeof("intersect")];
    char CSSValueStringPool_str489[sizeof("increasing")];
    char CSSValueStringPool_str490[sizeof("lightsalmon")];
    char CSSValueStringPool_str491[sizeof("maroon")];
    char CSSValueStringPool_str492[sizeof("accentcolor")];
    char CSSValueStringPool_str493[sizeof("interlace")];
    char CSSValueStringPool_str494[sizeof("y-end")];
    char CSSValueStringPool_str495[sizeof("-internal-spelling-error-color")];
    char CSSValueStringPool_str496[sizeof("a98-rgb")];
    char CSSValueStringPool_str497[sizeof("back-button")];
    char CSSValueStringPool_str498[sizeof("skew")];
    char CSSValueStringPool_str499[sizeof("sticky")];
    char CSSValueStringPool_str500[sizeof("paged")];
    char CSSValueStringPool_str501[sizeof("span-y-end")];
    char CSSValueStringPool_str502[sizeof("manipulation")];
    char CSSValueStringPool_str503[sizeof("sqrt")];
    char CSSValueStringPool_str504[sizeof("palegreen")];
    char CSSValueStringPool_str505[sizeof("lightcoral")];
    char CSSValueStringPool_str506[sizeof("table-column")];
    char CSSValueStringPool_str507[sizeof("read-only")];
    char CSSValueStringPool_str508[sizeof("span-y-start")];
    char CSSValueStringPool_str509[sizeof("math-auto")];
    char CSSValueStringPool_str510[sizeof("palettes")];
    char CSSValueStringPool_str511[sizeof("antialiased")];
    char CSSValueStringPool_str512[sizeof("portrait")];
    char CSSValueStringPool_str513[sizeof("tech")];
    char CSSValueStringPool_str514[sizeof("progress")];
    char CSSValueStringPool_str515[sizeof("jis-b4")];
    char CSSValueStringPool_str516[sizeof("g")];
    char CSSValueStringPool_str517[sizeof("jis-b5")];
    char CSSValueStringPool_str518[sizeof("color")];
    char CSSValueStringPool_str519[sizeof("height")];
    char CSSValueStringPool_str520[sizeof("pre-line")];
    char CSSValueStringPool_str521[sizeof("green")];
    char CSSValueStringPool_str522[sizeof("avoid")];
    char CSSValueStringPool_str523[sizeof("all-small-caps")];
    char CSSValueStringPool_str524[sizeof("implicit")];
    char CSSValueStringPool_str525[sizeof("revert-layer")];
    char CSSValueStringPool_str526[sizeof("icon")];
    char CSSValueStringPool_str527[sizeof("additive")];
    char CSSValueStringPool_str528[sizeof("shorter")];
    char CSSValueStringPool_str529[sizeof("firebrick")];
    char CSSValueStringPool_str530[sizeof("polygon")];
    char CSSValueStringPool_str531[sizeof("paint")];
    char CSSValueStringPool_str532[sizeof("fuchsia")];
    char CSSValueStringPool_str533[sizeof("both-edges")];
    char CSSValueStringPool_str534[sizeof("grab")];
    char CSSValueStringPool_str535[sizeof("grey")];
    char CSSValueStringPool_str536[sizeof("gray")];
    char CSSValueStringPool_str537[sizeof("orchid")];
    char CSSValueStringPool_str538[sizeof("translatez")];
    char CSSValueStringPool_str539[sizeof("self")];
    char CSSValueStringPool_str540[sizeof("opentype")];
    char CSSValueStringPool_str541[sizeof("inset-inline-end")];
    char CSSValueStringPool_str542[sizeof("flex-visual")];
    char CSSValueStringPool_str543[sizeof("inset-inline-start")];
    char CSSValueStringPool_str544[sizeof("entry-crossing")];
    char CSSValueStringPool_str545[sizeof("ellipse")];
    char CSSValueStringPool_str546[sizeof("x-self-end")];
    char CSSValueStringPool_str547[sizeof("khaki")];
    char CSSValueStringPool_str548[sizeof("pointer")];
    char CSSValueStringPool_str549[sizeof("oldstyle-nums")];
    char CSSValueStringPool_str550[sizeof("fallback")];
    char CSSValueStringPool_str551[sizeof("active")];
    char CSSValueStringPool_str552[sizeof("painted")];
    char CSSValueStringPool_str553[sizeof("purple")];
    char CSSValueStringPool_str554[sizeof("aliceblue")];
    char CSSValueStringPool_str555[sizeof("plum")];
    char CSSValueStringPool_str556[sizeof("mediumpurple")];
    char CSSValueStringPool_str557[sizeof("preserve")];
    char CSSValueStringPool_str558[sizeof("ellipsis")];
    char CSSValueStringPool_str559[sizeof("x-self-start")];
    char CSSValueStringPool_str560[sizeof("scroll")];
    char CSSValueStringPool_str561[sizeof("span-y-self-end")];
    char CSSValueStringPool_str562[sizeof("span-y-self-start")];
    char CSSValueStringPool_str563[sizeof("economy")];
    char CSSValueStringPool_str564[sizeof("e-resize")];
    char CSSValueStringPool_str565[sizeof("all-petite-caps")];
    char CSSValueStringPool_str566[sizeof("content-box")];
    char CSSValueStringPool_str567[sizeof("lch")];
    char CSSValueStringPool_str568[sizeof("stretch")];
    char CSSValueStringPool_str569[sizeof("no-change")];
    char CSSValueStringPool_str570[sizeof("chocolate")];
    char CSSValueStringPool_str571[sizeof("lightseagreen")];
    char CSSValueStringPool_str572[sizeof("square")];
    char CSSValueStringPool_str573[sizeof("skyblue")];
    char CSSValueStringPool_str574[sizeof("mediumvioletred")];
    char CSSValueStringPool_str575[sizeof("lightsteelblue")];
    char CSSValueStringPool_str576[sizeof("w")];
    char CSSValueStringPool_str577[sizeof("buttonborder")];
    char CSSValueStringPool_str578[sizeof("palevioletred")];
    char CSSValueStringPool_str579[sizeof("self-end")];
    char CSSValueStringPool_str580[sizeof("luminosity")];
    char CSSValueStringPool_str581[sizeof("max")];
    char CSSValueStringPool_str582[sizeof("self-start")];
    char CSSValueStringPool_str583[sizeof("palegoldenrod")];
    char CSSValueStringPool_str584[sizeof("wrap")];
    char CSSValueStringPool_str585[sizeof("matrix")];
    char CSSValueStringPool_str586[sizeof("preserve-3d")];
    char CSSValueStringPool_str587[sizeof("small-caption")];
    char CSSValueStringPool_str588[sizeof("message-box")];
    char CSSValueStringPool_str589[sizeof("color-svg")];
    char CSSValueStringPool_str590[sizeof("marktext")];
    char CSSValueStringPool_str591[sizeof("grammar-error")];
    char CSSValueStringPool_str592[sizeof("minmax")];
    char CSSValueStringPool_str593[sizeof("paused")];
    char CSSValueStringPool_str594[sizeof("linktext")];
    char CSSValueStringPool_str595[sizeof("span-self-end")];
    char CSSValueStringPool_str596[sizeof("brightness")];
    char CSSValueStringPool_str597[sizeof("oklab")];
    char CSSValueStringPool_str598[sizeof("minimized")];
    char CSSValueStringPool_str599[sizeof("darkorchid")];
    char CSSValueStringPool_str600[sizeof("mixed")];
    char CSSValueStringPool_str601[sizeof("matrix3d")];
    char CSSValueStringPool_str602[sizeof("container-progress")];
    char CSSValueStringPool_str603[sizeof("fixed")];
    char CSSValueStringPool_str604[sizeof("fieldtext")];
    char CSSValueStringPool_str605[sizeof("flip-block")];
    char CSSValueStringPool_str606[sizeof("silver")];
    char CSSValueStringPool_str607[sizeof("chartreuse")];
    char CSSValueStringPool_str608[sizeof("monospace")];
    char CSSValueStringPool_str609[sizeof("wait")];
    char CSSValueStringPool_str610[sizeof("seashell")];
    char CSSValueStringPool_str611[sizeof("pan-up")];
    char CSSValueStringPool_str612[sizeof("canvas")];
    char CSSValueStringPool_str613[sizeof("azure")];
    char CSSValueStringPool_str614[sizeof("crosshair")];
    char CSSValueStringPool_str615[sizeof("avoid-page")];
    char CSSValueStringPool_str616[sizeof("grayscale")];
    char CSSValueStringPool_str617[sizeof("lightgrey")];
    char CSSValueStringPool_str618[sizeof("lightgray")];
    char CSSValueStringPool_str619[sizeof("horizontal")];
    char CSSValueStringPool_str620[sizeof("padding")];
    char CSSValueStringPool_str621[sizeof("inline")];
    char CSSValueStringPool_str622[sizeof("mediumblue")];
    char CSSValueStringPool_str623[sizeof("wavy")];
    char CSSValueStringPool_str624[sizeof("titling-caps")];
    char CSSValueStringPool_str625[sizeof("source-atop")];
    char CSSValueStringPool_str626[sizeof("y-self-end")];
    char CSSValueStringPool_str627[sizeof("thin")];
    char CSSValueStringPool_str628[sizeof("indigo")];
    char CSSValueStringPool_str629[sizeof("color-stop")];
    char CSSValueStringPool_str630[sizeof("discretionary-ligatures")];
    char CSSValueStringPool_str631[sizeof("inside")];
    char CSSValueStringPool_str632[sizeof("scalex")];
    char CSSValueStringPool_str633[sizeof("no-close-quote")];
    char CSSValueStringPool_str634[sizeof("font-tech")];
    char CSSValueStringPool_str635[sizeof("indianred")];
    char CSSValueStringPool_str636[sizeof("color-contrast")];
    char CSSValueStringPool_str637[sizeof("ivory")];
    char CSSValueStringPool_str638[sizeof("royalblue")];
    char CSSValueStringPool_str639[sizeof("y-self-start")];
    char CSSValueStringPool_str640[sizeof("match-source")];
    char CSSValueStringPool_str641[sizeof("wrap-reverse")];
    char CSSValueStringPool_str642[sizeof("anchor")];
    char CSSValueStringPool_str643[sizeof("thistle")];
    char CSSValueStringPool_str644[sizeof("grid")];
    char CSSValueStringPool_str645[sizeof("no-historical-ligatures")];
    char CSSValueStringPool_str646[sizeof("pan-left")];
    char CSSValueStringPool_str647[sizeof("before")];
    char CSSValueStringPool_str648[sizeof("isolate-override")];
    char CSSValueStringPool_str649[sizeof("rotatex")];
    char CSSValueStringPool_str650[sizeof("inactiveborder")];
    char CSSValueStringPool_str651[sizeof("light-dark")];
    char CSSValueStringPool_str652[sizeof("inline-grid")];
    char CSSValueStringPool_str653[sizeof("scroll-state")];
    char CSSValueStringPool_str654[sizeof("table-column-group")];
    char CSSValueStringPool_str655[sizeof("annotation")];
    char CSSValueStringPool_str656[sizeof("olive")];
    char CSSValueStringPool_str657[sizeof("text")];
    char CSSValueStringPool_str658[sizeof("lightcyan")];
    char CSSValueStringPool_str659[sizeof("stroke")];
    char CSSValueStringPool_str660[sizeof("mathematical")];
    char CSSValueStringPool_str661[sizeof("most-inline-size")];
    char CSSValueStringPool_str662[sizeof("alpha")];
    char CSSValueStringPool_str663[sizeof("textarea")];
    char CSSValueStringPool_str664[sizeof("inline-end")];
    char CSSValueStringPool_str665[sizeof("lightblue")];
    char CSSValueStringPool_str666[sizeof("span-right")];
    char CSSValueStringPool_str667[sizeof("progressive")];
    char CSSValueStringPool_str668[sizeof("capitalize")];
    char CSSValueStringPool_str669[sizeof("lightpink")];
    char CSSValueStringPool_str670[sizeof("gold")];
    char CSSValueStringPool_str671[sizeof("skewy")];
    char CSSValueStringPool_str672[sizeof("inline-start")];
    char CSSValueStringPool_str673[sizeof("avoid-column")];
    char CSSValueStringPool_str674[sizeof("repeat-x")];
    char CSSValueStringPool_str675[sizeof("pan-y")];
    char CSSValueStringPool_str676[sizeof("pretty")];
    char CSSValueStringPool_str677[sizeof("no-contextual")];
    char CSSValueStringPool_str678[sizeof("break-spaces")];
    char CSSValueStringPool_str679[sizeof("var")];
    char CSSValueStringPool_str680[sizeof("menu")];
    char CSSValueStringPool_str681[sizeof("swap")];
    char CSSValueStringPool_str682[sizeof("block-size")];
    char CSSValueStringPool_str683[sizeof("initial-only")];
    char CSSValueStringPool_str684[sizeof("last-baseline")];
    char CSSValueStringPool_str685[sizeof("saturate")];
    char CSSValueStringPool_str686[sizeof("auto")];
    char CSSValueStringPool_str687[sizeof("difference")];
    char CSSValueStringPool_str688[sizeof("hard-light")];
    char CSSValueStringPool_str689[sizeof("farthest-corner")];
    char CSSValueStringPool_str690[sizeof("goldenrod")];
    char CSSValueStringPool_str691[sizeof("violet")];
    char CSSValueStringPool_str692[sizeof("grid-order")];
    char CSSValueStringPool_str693[sizeof("vs")];
    char CSSValueStringPool_str694[sizeof("contextual")];
    char CSSValueStringPool_str695[sizeof("saturation")];
    char CSSValueStringPool_str696[sizeof("ex")];
    char CSSValueStringPool_str697[sizeof("grabbing")];
    char CSSValueStringPool_str698[sizeof("exact")];
    char CSSValueStringPool_str699[sizeof("justify")];
    char CSSValueStringPool_str700[sizeof("manual")];
    char CSSValueStringPool_str701[sizeof("row")];
    char CSSValueStringPool_str702[sizeof("show")];
    char CSSValueStringPool_str703[sizeof("searchfield")];
    char CSSValueStringPool_str704[sizeof("self-inline")];
    char CSSValueStringPool_str705[sizeof("color-dodge")];
    char CSSValueStringPool_str706[sizeof("hypot")];
    char CSSValueStringPool_str707[sizeof("buttontext")];
    char CSSValueStringPool_str708[sizeof("palette-mix")];
    char CSSValueStringPool_str709[sizeof("layout")];
    char CSSValueStringPool_str710[sizeof("max-content")];
    char CSSValueStringPool_str711[sizeof("features-graphite")];
    char CSSValueStringPool_str712[sizeof("stacked-fractions")];
    char CSSValueStringPool_str713[sizeof("conic-gradient")];
    char CSSValueStringPool_str714[sizeof("menulist")];
    char CSSValueStringPool_str715[sizeof("translatex")];
    char CSSValueStringPool_str716[sizeof("context-menu")];
    char CSSValueStringPool_str717[sizeof("infinite")];
    char CSSValueStringPool_str718[sizeof("scrollbar")];
    char CSSValueStringPool_str719[sizeof("soft-light")];
    char CSSValueStringPool_str720[sizeof("words")];
    char CSSValueStringPool_str721[sizeof("geometricprecision")];
    char CSSValueStringPool_str722[sizeof("exit")];
    char CSSValueStringPool_str723[sizeof("open-quote")];
    char CSSValueStringPool_str724[sizeof("column")];
    char CSSValueStringPool_str725[sizeof("-infinity")];
    char CSSValueStringPool_str726[sizeof("extends")];
    char CSSValueStringPool_str727[sizeof("aqua")];
    char CSSValueStringPool_str728[sizeof("span-block-end")];
    char CSSValueStringPool_str729[sizeof("bidi-override")];
    char CSSValueStringPool_str730[sizeof("span-block-start")];
    char CSSValueStringPool_str731[sizeof("captiontext")];
    char CSSValueStringPool_str732[sizeof("blanchedalmond")];
    char CSSValueStringPool_str733[sizeof("aquamarine")];
    char CSSValueStringPool_str734[sizeof("ultra-expanded")];
    char CSSValueStringPool_str735[sizeof("auto-add")];
    char CSSValueStringPool_str736[sizeof("verso")];
    char CSSValueStringPool_str737[sizeof("document")];
    char CSSValueStringPool_str738[sizeof("text-top")];
    char CSSValueStringPool_str739[sizeof("thick")];
    char CSSValueStringPool_str740[sizeof("down")];
    char CSSValueStringPool_str741[sizeof("self-inline-end")];
    char CSSValueStringPool_str742[sizeof("lawngreen")];
    char CSSValueStringPool_str743[sizeof("self-inline-start")];
    char CSSValueStringPool_str744[sizeof("xyz")];
    char CSSValueStringPool_str745[sizeof("threedface")];
    char CSSValueStringPool_str746[sizeof("exp")];
    char CSSValueStringPool_str747[sizeof("reduce")];
    char CSSValueStringPool_str748[sizeof("nowrap")];
    char CSSValueStringPool_str749[sizeof("path")];
    char CSSValueStringPool_str750[sizeof("span-x-end")];
    char CSSValueStringPool_str751[sizeof("inset-block-end")];
    char CSSValueStringPool_str752[sizeof("color-burn")];
    char CSSValueStringPool_str753[sizeof("lowercase")];
    char CSSValueStringPool_str754[sizeof("inset-block-start")];
    char CSSValueStringPool_str755[sizeof("nesw-resize")];
    char CSSValueStringPool_str756[sizeof("anchor-center")];
    char CSSValueStringPool_str757[sizeof("color-cbdt")];
    char CSSValueStringPool_str758[sizeof("oklch")];
    char CSSValueStringPool_str759[sizeof("gainsboro")];
    char CSSValueStringPool_str760[sizeof("mediumslateblue")];
    char CSSValueStringPool_str761[sizeof("span-x-start")];
    char CSSValueStringPool_str762[sizeof("vertical")];
    char CSSValueStringPool_str763[sizeof("table-caption")];
    char CSSValueStringPool_str764[sizeof("perspective")];
    char CSSValueStringPool_str765[sizeof("not-allowed")];
    char CSSValueStringPool_str766[sizeof("close-quote")];
    char CSSValueStringPool_str767[sizeof("upright")];
    char CSSValueStringPool_str768[sizeof("inner-spin-button")];
    char CSSValueStringPool_str769[sizeof("mediumaquamarine")];
    char CSSValueStringPool_str770[sizeof("scale-down")];
    char CSSValueStringPool_str771[sizeof("petite-caps")];
    char CSSValueStringPool_str772[sizeof("margin-box")];
    char CSSValueStringPool_str773[sizeof("lavenderblush")];
    char CSSValueStringPool_str774[sizeof("proportional-nums")];
    char CSSValueStringPool_str775[sizeof("-internal-grammar-error-color")];
    char CSSValueStringPool_str776[sizeof("xx-large")];
    char CSSValueStringPool_str777[sizeof("selecteditemtext")];
    char CSSValueStringPool_str778[sizeof("table-footer-group")];
    char CSSValueStringPool_str779[sizeof("span-self-block-end")];
    char CSSValueStringPool_str780[sizeof("xyz-d65")];
    char CSSValueStringPool_str781[sizeof("listbox")];
    char CSSValueStringPool_str782[sizeof("xyz-d50")];
    char CSSValueStringPool_str783[sizeof("span-self-block-start")];
    char CSSValueStringPool_str784[sizeof("midnightblue")];
    char CSSValueStringPool_str785[sizeof("fill-box")];
    char CSSValueStringPool_str786[sizeof("context-fill")];
    char CSSValueStringPool_str787[sizeof("span-self-inline-end")];
    char CSSValueStringPool_str788[sizeof("span-self-inline-start")];
    char CSSValueStringPool_str789[sizeof("inherit")];
    char CSSValueStringPool_str790[sizeof("vertical-rl")];
    char CSSValueStringPool_str791[sizeof("horizontal-tb")];
    char CSSValueStringPool_str792[sizeof("backwards")];
    char CSSValueStringPool_str793[sizeof("textfield")];
    char CSSValueStringPool_str794[sizeof("picture-in-picture")];
    char CSSValueStringPool_str795[sizeof("before-edge")];
    char CSSValueStringPool_str796[sizeof("exclude")];
    char CSSValueStringPool_str797[sizeof("inline-table")];
    char CSSValueStringPool_str798[sizeof("grid-columns")];
    char CSSValueStringPool_str799[sizeof("exclusion")];
    char CSSValueStringPool_str800[sizeof("span-x-self-end")];
    char CSSValueStringPool_str801[sizeof("span-x-self-start")];
    char CSSValueStringPool_str802[sizeof("mediumturquoise")];
    char CSSValueStringPool_str803[sizeof("highlight")];
    char CSSValueStringPool_str804[sizeof("-internal-textarea-auto")];
    char CSSValueStringPool_str805[sizeof("expanded")];
    char CSSValueStringPool_str806[sizeof("olivedrab")];
    char CSSValueStringPool_str807[sizeof("vertical-lr")];
    char CSSValueStringPool_str808[sizeof("auto-fit")];
    char CSSValueStringPool_str809[sizeof("features-opentype")];
    char CSSValueStringPool_str810[sizeof("inactivecaption")];
    char CSSValueStringPool_str811[sizeof("-internal-lower-armenian")];
    char CSSValueStringPool_str812[sizeof("hwb")];
    char CSSValueStringPool_str813[sizeof("ruby-text")];
    char CSSValueStringPool_str814[sizeof("read-write")];
    char CSSValueStringPool_str815[sizeof("text-after-edge")];
    char CSSValueStringPool_str816[sizeof("activeborder")];
    char CSSValueStringPool_str817[sizeof("appworkspace")];
    char CSSValueStringPool_str818[sizeof("semi-expanded")];
    char CSSValueStringPool_str819[sizeof("deepskyblue")];
    char CSSValueStringPool_str820[sizeof("view")];
    char CSSValueStringPool_str821[sizeof("text-bottom")];
    char CSSValueStringPool_str822[sizeof("alphabetic")];
    char CSSValueStringPool_str823[sizeof("progress-bar")];
    char CSSValueStringPool_str824[sizeof("context-stroke")];
    char CSSValueStringPool_str825[sizeof("block-axis")];
    char CSSValueStringPool_str826[sizeof("rebeccapurple")];
    char CSSValueStringPool_str827[sizeof("non-scaling-stroke")];
    char CSSValueStringPool_str828[sizeof("smooth")];
    char CSSValueStringPool_str829[sizeof("auto-fill")];
    char CSSValueStringPool_str830[sizeof("square-button")];
    char CSSValueStringPool_str831[sizeof("xx-small")];
    char CSSValueStringPool_str832[sizeof("darkkhaki")];
    char CSSValueStringPool_str833[sizeof("w-resize")];
    char CSSValueStringPool_str834[sizeof("absolute")];
    char CSSValueStringPool_str835[sizeof("-internal-quirk-inherit")];
    char CSSValueStringPool_str836[sizeof("anchors-visible")];
    char CSSValueStringPool_str837[sizeof("pan-right")];
    char CSSValueStringPool_str838[sizeof("variations")];
    char CSSValueStringPool_str839[sizeof("honeydew")];
    char CSSValueStringPool_str840[sizeof("groove")];
    char CSSValueStringPool_str841[sizeof("darkturquoise")];
    char CSSValueStringPool_str842[sizeof("row-reverse")];
    char CSSValueStringPool_str843[sizeof("sideways")];
    char CSSValueStringPool_str844[sizeof("optimizespeed")];
    char CSSValueStringPool_str845[sizeof("farthest-side")];
    char CSSValueStringPool_str846[sizeof("infinity")];
    char CSSValueStringPool_str847[sizeof("subpixel-antialiased")];
    char CSSValueStringPool_str848[sizeof("rosybrown")];
    char CSSValueStringPool_str849[sizeof("slashed-zero")];
    char CSSValueStringPool_str850[sizeof("preserve-parent-color")];
    char CSSValueStringPool_str851[sizeof("above")];
    char CSSValueStringPool_str852[sizeof("column-reverse")];
    char CSSValueStringPool_str853[sizeof("most-block-size")];
    char CSSValueStringPool_str854[sizeof("proximity")];
    char CSSValueStringPool_str855[sizeof("accentcolortext")];
    char CSSValueStringPool_str856[sizeof("skewx")];
    char CSSValueStringPool_str857[sizeof("table-row")];
    char CSSValueStringPool_str858[sizeof("oblique")];
    char CSSValueStringPool_str859[sizeof("-internal-korean-hangul-formal")];
    char CSSValueStringPool_str860[sizeof("activecaption")];
    char CSSValueStringPool_str861[sizeof("pan-x")];
    char CSSValueStringPool_str862[sizeof("menulist-button")];
    char CSSValueStringPool_str863[sizeof("inline-layout")];
    char CSSValueStringPool_str864[sizeof("preserve-breaks")];
    char CSSValueStringPool_str865[sizeof("extra-condensed")];
    char CSSValueStringPool_str866[sizeof("jump-both")];
    char CSSValueStringPool_str867[sizeof("-internal-hebrew")];
    char CSSValueStringPool_str868[sizeof("pixelated")];
    char CSSValueStringPool_str869[sizeof("-webkit-left")];
    char CSSValueStringPool_str870[sizeof("border-box")];
    char CSSValueStringPool_str871[sizeof("no-discretionary-ligatures")];
    char CSSValueStringPool_str872[sizeof("-internal-simp-chinese-formal")];
    char CSSValueStringPool_str873[sizeof("scroll-position")];
    char CSSValueStringPool_str874[sizeof("-internal-simp-chinese-informal")];
    char CSSValueStringPool_str875[sizeof("weight")];
    char CSSValueStringPool_str876[sizeof("-internal-trad-chinese-formal")];
    char CSSValueStringPool_str877[sizeof("-internal-appearance-auto-base-select")];
    char CSSValueStringPool_str878[sizeof("inline-flex")];
    char CSSValueStringPool_str879[sizeof("-internal-trad-chinese-informal")];
    char CSSValueStringPool_str880[sizeof("yellow")];
    char CSSValueStringPool_str881[sizeof("self-block")];
    char CSSValueStringPool_str882[sizeof("graytext")];
    char CSSValueStringPool_str883[sizeof("dynamic-range-limit-mix")];
    char CSSValueStringPool_str884[sizeof("white")];
    char CSSValueStringPool_str885[sizeof("sw-resize")];
    char CSSValueStringPool_str886[sizeof("yellowgreen")];
    char CSSValueStringPool_str887[sizeof("-internal-ethiopic-numeric")];
    char CSSValueStringPool_str888[sizeof("activetext")];
    char CSSValueStringPool_str889[sizeof("-webkit-center")];
    char CSSValueStringPool_str890[sizeof("burlywood")];
    char CSSValueStringPool_str891[sizeof("color-mix")];
    char CSSValueStringPool_str892[sizeof("nw-resize")];
    char CSSValueStringPool_str893[sizeof("padding-box")];
    char CSSValueStringPool_str894[sizeof("cap-height")];
    char CSSValueStringPool_str895[sizeof("width")];
    char CSSValueStringPool_str896[sizeof("bounding-box")];
    char CSSValueStringPool_str897[sizeof("break-word")];
    char CSSValueStringPool_str898[sizeof("lightskyblue")];
    char CSSValueStringPool_str899[sizeof("wheat")];
    char CSSValueStringPool_str900[sizeof("-webkit-radial-gradient")];
    char CSSValueStringPool_str901[sizeof("row-resize")];
    char CSSValueStringPool_str902[sizeof("constrained-high")];
    char CSSValueStringPool_str903[sizeof("tabular-nums")];
    char CSSValueStringPool_str904[sizeof("accumulate")];
    char CSSValueStringPool_str905[sizeof("-webkit-linear-gradient")];
    char CSSValueStringPool_str906[sizeof("saddlebrown")];
    char CSSValueStringPool_str907[sizeof("self-block-end")];
    char CSSValueStringPool_str908[sizeof("peru")];
    char CSSValueStringPool_str909[sizeof("-webkit-isolate")];
    char CSSValueStringPool_str910[sizeof("self-block-start")];
    char CSSValueStringPool_str911[sizeof("lightslategrey")];
    char CSSValueStringPool_str912[sizeof("lightslategray")];
    char CSSValueStringPool_str913[sizeof("inline-block")];
    char CSSValueStringPool_str914[sizeof("-webkit-control")];
    char CSSValueStringPool_str915[sizeof("most-height")];
    char CSSValueStringPool_str916[sizeof("cornflowerblue")];
    char CSSValueStringPool_str917[sizeof("-webkit-calc")];
    char CSSValueStringPool_str918[sizeof("pow")];
    char CSSValueStringPool_str919[sizeof("-webkit-min-content")];
    char CSSValueStringPool_str920[sizeof("maximized")];
    char CSSValueStringPool_str921[sizeof("drop-shadow")];
    char CSSValueStringPool_str922[sizeof("inline-size")];
    char CSSValueStringPool_str923[sizeof("anywhere")];
    char CSSValueStringPool_str924[sizeof("-webkit-auto")];
    char CSSValueStringPool_str925[sizeof("table-header-group")];
    char CSSValueStringPool_str926[sizeof("no-open-quote")];
    char CSSValueStringPool_str927[sizeof("canvastext")];
    char CSSValueStringPool_str928[sizeof("space-between")];
    char CSSValueStringPool_str929[sizeof("visible")];
    char CSSValueStringPool_str930[sizeof("anchor-size")];
    char CSSValueStringPool_str931[sizeof("-webkit-mini-control")];
    char CSSValueStringPool_str932[sizeof("ic-width")];
    char CSSValueStringPool_str933[sizeof("pan-down")];
    char CSSValueStringPool_str934[sizeof("push-button")];
    char CSSValueStringPool_str935[sizeof("embedded-opentype")];
    char CSSValueStringPool_str936[sizeof("below")];
    char CSSValueStringPool_str937[sizeof("inline-axis")];
    char CSSValueStringPool_str938[sizeof("woff")];
    char CSSValueStringPool_str939[sizeof("woff2")];
    char CSSValueStringPool_str940[sizeof("-internal-variable-value")];
    char CSSValueStringPool_str941[sizeof("searchfield-cancel-button")];
    char CSSValueStringPool_str942[sizeof("lightyellow")];
    char CSSValueStringPool_str943[sizeof("always")];
    char CSSValueStringPool_str944[sizeof("-webkit-isolate-override")];
    char CSSValueStringPool_str945[sizeof("pre-wrap")];
    char CSSValueStringPool_str946[sizeof("ew-resize")];
    char CSSValueStringPool_str947[sizeof("plaintext")];
    char CSSValueStringPool_str948[sizeof("swash")];
    char CSSValueStringPool_str949[sizeof("-webkit-gradient")];
    char CSSValueStringPool_str950[sizeof("mediumorchid")];
    char CSSValueStringPool_str951[sizeof("exit-crossing")];
    char CSSValueStringPool_str952[sizeof("menutext")];
    char CSSValueStringPool_str953[sizeof("nwse-resize")];
    char CSSValueStringPool_str954[sizeof("ch-width")];
    char CSSValueStringPool_str955[sizeof("visiblepainted")];
    char CSSValueStringPool_str956[sizeof("no-overflow")];
    char CSSValueStringPool_str957[sizeof("-webkit-link")];
    char CSSValueStringPool_str958[sizeof("flex-flow")];
    char CSSValueStringPool_str959[sizeof("text-before-edge")];
    char CSSValueStringPool_str960[sizeof("-webkit-image-set")];
    char CSSValueStringPool_str961[sizeof("plus-lighter")];
    char CSSValueStringPool_str962[sizeof("-internal-korean-hanja-formal")];
    char CSSValueStringPool_str963[sizeof("-internal-korean-hanja-informal")];
    char CSSValueStringPool_str964[sizeof("most-width")];
    char CSSValueStringPool_str965[sizeof("cubic-bezier")];
    char CSSValueStringPool_str966[sizeof("character-variant")];
    char CSSValueStringPool_str967[sizeof("sandybrown")];
    char CSSValueStringPool_str968[sizeof("color-sbix")];
    char CSSValueStringPool_str969[sizeof("color-colrv1")];
    char CSSValueStringPool_str970[sizeof("color-colrv0")];
    char CSSValueStringPool_str971[sizeof("pinch-zoom")];
    char CSSValueStringPool_str972[sizeof("-internal-extend-to-zoom")];
    char CSSValueStringPool_str973[sizeof("full-width")];
    char CSSValueStringPool_str974[sizeof("greenyellow")];
    char CSSValueStringPool_str975[sizeof("whitesmoke")];
    char CSSValueStringPool_str976[sizeof("-webkit-fit-content")];
    char CSSValueStringPool_str977[sizeof("-webkit-baseline-middle")];
    char CSSValueStringPool_str978[sizeof("visiblefill")];
    char CSSValueStringPool_str979[sizeof("-webkit-max-content")];
    char CSSValueStringPool_str980[sizeof("peachpuff")];
    char CSSValueStringPool_str981[sizeof("lemonchiffon")];
    char CSSValueStringPool_str982[sizeof("-webkit-cross-fade")];
    char CSSValueStringPool_str983[sizeof("ideographic")];
    char CSSValueStringPool_str984[sizeof("floralwhite")];
    char CSSValueStringPool_str985[sizeof("grid-rows")];
    char CSSValueStringPool_str986[sizeof("paleturquoise")];
    char CSSValueStringPool_str987[sizeof("xxx-large")];
    char CSSValueStringPool_str988[sizeof("-webkit-activelink")];
    char CSSValueStringPool_str989[sizeof("window")];
    char CSSValueStringPool_str990[sizeof("visual")];
    char CSSValueStringPool_str991[sizeof("-webkit-grab")];
    char CSSValueStringPool_str992[sizeof("-webkit-small-control")];
    char CSSValueStringPool_str993[sizeof("extra-expanded")];
    char CSSValueStringPool_str994[sizeof("slider-horizontal")];
    char CSSValueStringPool_str995[sizeof("buttonshadow")];
    char CSSValueStringPool_str996[sizeof("lightgoldenrodyellow")];
    char CSSValueStringPool_str997[sizeof("-webkit-box")];
    char CSSValueStringPool_str998[sizeof("-webkit-body")];
    char CSSValueStringPool_str999[sizeof("inactivecaptiontext")];
    char CSSValueStringPool_str1000[sizeof("-webkit-focus-ring-color")];
    char CSSValueStringPool_str1001[sizeof("auto-phrase")];
    char CSSValueStringPool_str1002[sizeof("-webkit-zoom-in")];
    char CSSValueStringPool_str1003[sizeof("-internal-active-list-box-selection")];
    char CSSValueStringPool_str1004[sizeof("stroke-box")];
    char CSSValueStringPool_str1005[sizeof("visiblestroke")];
    char CSSValueStringPool_str1006[sizeof("xywh")];
    char CSSValueStringPool_str1007[sizeof("checkbox")];
    char CSSValueStringPool_str1008[sizeof("infotext")];
    char CSSValueStringPool_str1009[sizeof("-webkit-fill-available")];
    char CSSValueStringPool_str1010[sizeof("allow-discrete")];
    char CSSValueStringPool_str1011[sizeof("-webkit-zoom-out")];
    char CSSValueStringPool_str1012[sizeof("vertical-text")];
    char CSSValueStringPool_str1013[sizeof("line-through")];
    char CSSValueStringPool_str1014[sizeof("-webkit-repeating-linear-gradient")];
    char CSSValueStringPool_str1015[sizeof("visitedtext")];
    char CSSValueStringPool_str1016[sizeof("powderblue")];
    char CSSValueStringPool_str1017[sizeof("infobackground")];
    char CSSValueStringPool_str1018[sizeof("highlighttext")];
    char CSSValueStringPool_str1019[sizeof("-webkit-repeating-radial-gradient")];
    char CSSValueStringPool_str1020[sizeof("ex-height")];
    char CSSValueStringPool_str1021[sizeof("auto-flow")];
    char CSSValueStringPool_str1022[sizeof("-webkit-optimize-contrast")];
    char CSSValueStringPool_str1023[sizeof("-internal-inactive-list-box-selection")];
    char CSSValueStringPool_str1024[sizeof("buttonhighlight")];
    char CSSValueStringPool_str1025[sizeof("-webkit-grabbing")];
    char CSSValueStringPool_str1026[sizeof("optimizequality")];
    char CSSValueStringPool_str1027[sizeof("optimizelegibility")];
    char CSSValueStringPool_str1028[sizeof("prophoto-rgb")];
    char CSSValueStringPool_str1029[sizeof("table-row-group")];
    char CSSValueStringPool_str1030[sizeof("vertical-right")];
    char CSSValueStringPool_str1031[sizeof("-webkit-match-parent")];
    char CSSValueStringPool_str1032[sizeof("view-box")];
    char CSSValueStringPool_str1033[sizeof("windowframe")];
    char CSSValueStringPool_str1034[sizeof("-webkit-inline-flex")];
    char CSSValueStringPool_str1035[sizeof("navajowhite")];
    char CSSValueStringPool_str1036[sizeof("-webkit-inline-box")];
    char CSSValueStringPool_str1037[sizeof("sideways-right")];
    char CSSValueStringPool_str1038[sizeof("threedshadow")];
    char CSSValueStringPool_str1039[sizeof("-webkit-plaintext")];
    char CSSValueStringPool_str1040[sizeof("read-write-plaintext-only")];
    char CSSValueStringPool_str1041[sizeof("-webkit-flex")];
    char CSSValueStringPool_str1042[sizeof("after-white-space")];
    char CSSValueStringPool_str1043[sizeof("proportional-width")];
    char CSSValueStringPool_str1044[sizeof("window-controls-overlay")];
    char CSSValueStringPool_str1045[sizeof("-webkit-right")];
    char CSSValueStringPool_str1046[sizeof("windowtext")];
    char CSSValueStringPool_str1047[sizeof("threeddarkshadow")];
    char CSSValueStringPool_str1048[sizeof("papayawhip")];
    char CSSValueStringPool_str1049[sizeof("threedhighlight")];
    char CSSValueStringPool_str1050[sizeof("-internal-active-list-box-selection-text")];
    char CSSValueStringPool_str1051[sizeof("antiquewhite")];
    char CSSValueStringPool_str1052[sizeof("-internal-inactive-list-box-selection-text")];
    char CSSValueStringPool_str1053[sizeof("ghostwhite")];
    char CSSValueStringPool_str1054[sizeof("threedlightshadow")];
    char CSSValueStringPool_str1055[sizeof("-webkit-xxx-large")];
  };
static const struct CSSValueStringPool_t CSSValueStringPool_contents =
  {
    "s",
    "h",
    "sin",
    "min",
    "start",
    "drag",
    "l",
    "seagreen",
    "meter",
    "miter",
    "lr",
    "ltr",
    "fine",
    "hanging",
    "dark",
    "mark",
    "large",
    "darken",
    "magenta",
    "larger",
    "dot",
    "linen",
    "nan",
    "linear",
    "drop",
    "r",
    "c",
    "darkgreen",
    "lime",
    "more",
    "z",
    "disc",
    "steps",
    "log",
    "hide",
    "longer",
    "srgb",
    "field",
    "rem",
    "slice",
    "help",
    "limegreen",
    "ledger",
    "fill",
    "none",
    "from",
    "markers",
    "darkred",
    "darkorange",
    "mod",
    "format",
    "small",
    "nearest",
    "svg",
    "smaller",
    "red",
    "frames",
    "ridge",
    "reset",
    "rl",
    "sides",
    "rtl",
    "hsl",
    "letter",
    "nonzero",
    "calc",
    "cell",
    "clip",
    "stable",
    "circle",
    "cos",
    "filled",
    "move",
    "hover",
    "coarse",
    "salmon",
    "unicase",
    "fast",
    "center",
    "unset",
    "revert",
    "under",
    "url",
    "last",
    "zoom",
    "b",
    "b4",
    "step-end",
    "flat",
    "forestgreen",
    "step-start",
    "b5",
    "beige",
    "scale",
    "linear-gradient",
    "normal",
    "dotted",
    "navy",
    "crimson",
    "lavender",
    "unicode",
    "content",
    "screen",
    "contain",
    "hue",
    "cover",
    "tan",
    "teal",
    "dense",
    "on",
    "first",
    "reverse",
    "darksalmon",
    "root",
    "space",
    "sienna",
    "sign",
    "central",
    "to",
    "from-image",
    "darkviolet",
    "scale3d",
    "rec2020",
    "underline",
    "contents",
    "contrast",
    "sesame",
    "decreasing",
    "raise",
    "flip-start",
    "rotate",
    "cap",
    "less",
    "static",
    "moccasin",
    "legal",
    "zoom-in",
    "false",
    "repeat",
    "loose",
    "compact",
    "hsla",
    "list-item",
    "selector",
    "clear",
    "up",
    "mintcream",
    "darkolivegreen",
    "over",
    "srgb-linear",
    "baseline",
    "rect",
    "rotate3d",
    "keep-all",
    "standard",
    "coral",
    "only",
    "hue-rotate",
    "bevel",
    "destination-in",
    "darkgoldenrod",
    "recto",
    "double",
    "copy",
    "bottom",
    "numbers",
    "clone",
    "clamp",
    "collection",
    "to-zero",
    "overline",
    "standalone",
    "relative",
    "rotate-right",
    "separate",
    "no-repeat",
    "caption",
    "true",
    "trim-start",
    "super",
    "span",
    "collapse",
    "blur",
    "top",
    "numeric",
    "orange",
    "replace",
    "blue",
    "custom",
    "butt",
    "triangle",
    "tomato",
    "no-clip",
    "e",
    "space-all",
    "local",
    "ornaments",
    "-internal-center",
    "bisque",
    "hand",
    "destination-over",
    "running",
    "safe",
    "bullets",
    "button",
    "orangered",
    "counter",
    "zoom-out",
    "supports",
    "leading",
    "spelling-error",
    "oldlace",
    "fullscreen",
    "min-content",
    "source-in",
    "end",
    "fit-content",
    "hidden",
    "ease",
    "translate",
    "cursive",
    "round",
    "translate3d",
    "counters",
    "env",
    "closest-side",
    "destination-out",
    "darkseagreen",
    "left",
    "element",
    "crispedges",
    "span-end",
    "balance",
    "destination-atop",
    "span-start",
    "emoji",
    "middle",
    "outset",
    "a",
    "at",
    "aa",
    "a4",
    "spell-out",
    "a3",
    "aaa",
    "attr",
    "simplified",
    "repeating-linear-gradient",
    "a5",
    "open",
    "arg",
    "small-caps",
    "folded",
    "ease-in",
    "lab",
    "enabled",
    "blueviolet",
    "space-around",
    "outside",
    "no-drag",
    "continuous",
    "background",
    "span-all",
    "condensed",
    "of",
    "x",
    "use-script",
    "rgb",
    "linearrgb",
    "border",
    "style",
    "source-over",
    "darkmagenta",
    "no-common-ligatures",
    "from-font",
    "dimgrey",
    "dimgray",
    "alternate",
    "layer",
    "xor",
    "snow",
    "add",
    "hotpink",
    "turquoise",
    "darkgrey",
    "darkgray",
    "jis78",
    "ray",
    "jis04",
    "jis90",
    "jis83",
    "traditional",
    "flip-inline",
    "bold",
    "source-out",
    "digits",
    "styleset",
    "bolder",
    "aa-large",
    "currentcolor",
    "uppercase",
    "all",
    "double-circle",
    "repeating-radial-gradient",
    "span-top",
    "closest-corner",
    "unsafe",
    "disclosure-open",
    "slow",
    "flow",
    "selecteditem",
    "aaa-large",
    "tb",
    "sub",
    "radio",
    "table",
    "base-select",
    "sans-serif",
    "atan",
    "solid",
    "atan2",
    "lr-tb",
    "fantasy",
    "forwards",
    "rgba",
    "radial",
    "strict",
    "repeating-conic-gradient",
    "borderless",
    "scaley",
    "on-demand",
    "ease-out",
    "slategrey",
    "slategray",
    "fabricated",
    "no-preference",
    "subgrid",
    "transparent",
    "asin",
    "darkcyan",
    "sepia",
    "no-drop",
    "historical-ligatures",
    "-internal-media-control",
    "brown",
    "no-autospace",
    "deeppink",
    "default",
    "darkslategrey",
    "darkslategray",
    "evenodd",
    "alias",
    "darkblue",
    "disclosure-closed",
    "stylistic",
    "black",
    "ruby",
    "tabbed",
    "mistyrose",
    "landscape",
    "rl-tb",
    "x-start",
    "decimal",
    "rotatey",
    "cyan",
    "medium",
    "browser",
    "block",
    "springgreen",
    "cross-fade",
    "font-format",
    "steelblue",
    "y",
    "flow-root",
    "no-punctuation",
    "logical",
    "dodgerblue",
    "tb-rl",
    "radial-gradient",
    "darkslateblue",
    "size",
    "cadetblue",
    "diagonal-fractions",
    "x-small",
    "ordinal",
    "acos",
    "first-baseline",
    "alternate-reverse",
    "symbolic",
    "space-first",
    "lining-nums",
    "x-large",
    "jump-none",
    "x-end",
    "dynamic",
    "slateblue",
    "legacy",
    "jump-end",
    "overlay",
    "jump-start",
    "repeat-y",
    "flex",
    "span-left",
    "-internal-upper-armenian",
    "literal-punctuation",
    "status-bar",
    "mediumseagreen",
    "minimal-ui",
    "in",
    "cyclic",
    "math",
    "subtract",
    "features-aat",
    "luminance",
    "high",
    "type",
    "rotate-left",
    "after",
    "cornsilk",
    "opacity",
    "ultra-condensed",
    "light",
    "block-end",
    "lighten",
    "lighter",
    "space-evenly",
    "blink",
    "historical-forms",
    "display-p3",
    "buttonface",
    "off",
    "lightgreen",
    "right",
    "inset",
    "serif",
    "system-ui",
    "se-resize",
    "entry",
    "s-resize",
    "scalez",
    "ease-in-out",
    "embed",
    "isolate",
    "ne-resize",
    "slider-vertical",
    "dashed",
    "invert",
    "n-resize",
    "mandatory",
    "optional",
    "semi-condensed",
    "translatey",
    "flex-end",
    "span-bottom",
    "flex-start",
    "span-self-start",
    "ns-resize",
    "table-cell",
    "span-inline-end",
    "y-start",
    "span-inline-start",
    "inverted",
    "rotatez",
    "common-ligatures",
    "initial",
    "all-scroll",
    "italic",
    "media-progress",
    "reset-size",
    "inset-area",
    "image-set",
    "break-all",
    "calc-size",
    "after-edge",
    "both",
    "abs",
    "mediumspringgreen",
    "pi",
    "p3",
    "pre",
    "truetype",
    "col-resize",
    "any",
    "incremental",
    "block-start",
    "page",
    "pink",
    "multiply",
    "intersect",
    "increasing",
    "lightsalmon",
    "maroon",
    "accentcolor",
    "interlace",
    "y-end",
    "-internal-spelling-error-color",
    "a98-rgb",
    "back-button",
    "skew",
    "sticky",
    "paged",
    "span-y-end",
    "manipulation",
    "sqrt",
    "palegreen",
    "lightcoral",
    "table-column",
    "read-only",
    "span-y-start",
    "math-auto",
    "palettes",
    "antialiased",
    "portrait",
    "tech",
    "progress",
    "jis-b4",
    "g",
    "jis-b5",
    "color",
    "height",
    "pre-line",
    "green",
    "avoid",
    "all-small-caps",
    "implicit",
    "revert-layer",
    "icon",
    "additive",
    "shorter",
    "firebrick",
    "polygon",
    "paint",
    "fuchsia",
    "both-edges",
    "grab",
    "grey",
    "gray",
    "orchid",
    "translatez",
    "self",
    "opentype",
    "inset-inline-end",
    "flex-visual",
    "inset-inline-start",
    "entry-crossing",
    "ellipse",
    "x-self-end",
    "khaki",
    "pointer",
    "oldstyle-nums",
    "fallback",
    "active",
    "painted",
    "purple",
    "aliceblue",
    "plum",
    "mediumpurple",
    "preserve",
    "ellipsis",
    "x-self-start",
    "scroll",
    "span-y-self-end",
    "span-y-self-start",
    "economy",
    "e-resize",
    "all-petite-caps",
    "content-box",
    "lch",
    "stretch",
    "no-change",
    "chocolate",
    "lightseagreen",
    "square",
    "skyblue",
    "mediumvioletred",
    "lightsteelblue",
    "w",
    "buttonborder",
    "palevioletred",
    "self-end",
    "luminosity",
    "max",
    "self-start",
    "palegoldenrod",
    "wrap",
    "matrix",
    "preserve-3d",
    "small-caption",
    "message-box",
    "color-svg",
    "marktext",
    "grammar-error",
    "minmax",
    "paused",
    "linktext",
    "span-self-end",
    "brightness",
    "oklab",
    "minimized",
    "darkorchid",
    "mixed",
    "matrix3d",
    "container-progress",
    "fixed",
    "fieldtext",
    "flip-block",
    "silver",
    "chartreuse",
    "monospace",
    "wait",
    "seashell",
    "pan-up",
    "canvas",
    "azure",
    "crosshair",
    "avoid-page",
    "grayscale",
    "lightgrey",
    "lightgray",
    "horizontal",
    "padding",
    "inline",
    "mediumblue",
    "wavy",
    "titling-caps",
    "source-atop",
    "y-self-end",
    "thin",
    "indigo",
    "color-stop",
    "discretionary-ligatures",
    "inside",
    "scalex",
    "no-close-quote",
    "font-tech",
    "indianred",
    "color-contrast",
    "ivory",
    "royalblue",
    "y-self-start",
    "match-source",
    "wrap-reverse",
    "anchor",
    "thistle",
    "grid",
    "no-historical-ligatures",
    "pan-left",
    "before",
    "isolate-override",
    "rotatex",
    "inactiveborder",
    "light-dark",
    "inline-grid",
    "scroll-state",
    "table-column-group",
    "annotation",
    "olive",
    "text",
    "lightcyan",
    "stroke",
    "mathematical",
    "most-inline-size",
    "alpha",
    "textarea",
    "inline-end",
    "lightblue",
    "span-right",
    "progressive",
    "capitalize",
    "lightpink",
    "gold",
    "skewy",
    "inline-start",
    "avoid-column",
    "repeat-x",
    "pan-y",
    "pretty",
    "no-contextual",
    "break-spaces",
    "var",
    "menu",
    "swap",
    "block-size",
    "initial-only",
    "last-baseline",
    "saturate",
    "auto",
    "difference",
    "hard-light",
    "farthest-corner",
    "goldenrod",
    "violet",
    "grid-order",
    "vs",
    "contextual",
    "saturation",
    "ex",
    "grabbing",
    "exact",
    "justify",
    "manual",
    "row",
    "show",
    "searchfield",
    "self-inline",
    "color-dodge",
    "hypot",
    "buttontext",
    "palette-mix",
    "layout",
    "max-content",
    "features-graphite",
    "stacked-fractions",
    "conic-gradient",
    "menulist",
    "translatex",
    "context-menu",
    "infinite",
    "scrollbar",
    "soft-light",
    "words",
    "geometricprecision",
    "exit",
    "open-quote",
    "column",
    "-infinity",
    "extends",
    "aqua",
    "span-block-end",
    "bidi-override",
    "span-block-start",
    "captiontext",
    "blanchedalmond",
    "aquamarine",
    "ultra-expanded",
    "auto-add",
    "verso",
    "document",
    "text-top",
    "thick",
    "down",
    "self-inline-end",
    "lawngreen",
    "self-inline-start",
    "xyz",
    "threedface",
    "exp",
    "reduce",
    "nowrap",
    "path",
    "span-x-end",
    "inset-block-end",
    "color-burn",
    "lowercase",
    "inset-block-start",
    "nesw-resize",
    "anchor-center",
    "color-cbdt",
    "oklch",
    "gainsboro",
    "mediumslateblue",
    "span-x-start",
    "vertical",
    "table-caption",
    "perspective",
    "not-allowed",
    "close-quote",
    "upright",
    "inner-spin-button",
    "mediumaquamarine",
    "scale-down",
    "petite-caps",
    "margin-box",
    "lavenderblush",
    "proportional-nums",
    "-internal-grammar-error-color",
    "xx-large",
    "selecteditemtext",
    "table-footer-group",
    "span-self-block-end",
    "xyz-d65",
    "listbox",
    "xyz-d50",
    "span-self-block-start",
    "midnightblue",
    "fill-box",
    "context-fill",
    "span-self-inline-end",
    "span-self-inline-start",
    "inherit",
    "vertical-rl",
    "horizontal-tb",
    "backwards",
    "textfield",
    "picture-in-picture",
    "before-edge",
    "exclude",
    "inline-table",
    "grid-columns",
    "exclusion",
    "span-x-self-end",
    "span-x-self-start",
    "mediumturquoise",
    "highlight",
    "-internal-textarea-auto",
    "expanded",
    "olivedrab",
    "vertical-lr",
    "auto-fit",
    "features-opentype",
    "inactivecaption",
    "-internal-lower-armenian",
    "hwb",
    "ruby-text",
    "read-write",
    "text-after-edge",
    "activeborder",
    "appworkspace",
    "semi-expanded",
    "deepskyblue",
    "view",
    "text-bottom",
    "alphabetic",
    "progress-bar",
    "context-stroke",
    "block-axis",
    "rebeccapurple",
    "non-scaling-stroke",
    "smooth",
    "auto-fill",
    "square-button",
    "xx-small",
    "darkkhaki",
    "w-resize",
    "absolute",
    "-internal-quirk-inherit",
    "anchors-visible",
    "pan-right",
    "variations",
    "honeydew",
    "groove",
    "darkturquoise",
    "row-reverse",
    "sideways",
    "optimizespeed",
    "farthest-side",
    "infinity",
    "subpixel-antialiased",
    "rosybrown",
    "slashed-zero",
    "preserve-parent-color",
    "above",
    "column-reverse",
    "most-block-size",
    "proximity",
    "accentcolortext",
    "skewx",
    "table-row",
    "oblique",
    "-internal-korean-hangul-formal",
    "activecaption",
    "pan-x",
    "menulist-button",
    "inline-layout",
    "preserve-breaks",
    "extra-condensed",
    "jump-both",
    "-internal-hebrew",
    "pixelated",
    "-webkit-left",
    "border-box",
    "no-discretionary-ligatures",
    "-internal-simp-chinese-formal",
    "scroll-position",
    "-internal-simp-chinese-informal",
    "weight",
    "-internal-trad-chinese-formal",
    "-internal-appearance-auto-base-select",
    "inline-flex",
    "-internal-trad-chinese-informal",
    "yellow",
    "self-block",
    "graytext",
    "dynamic-range-limit-mix",
    "white",
    "sw-resize",
    "yellowgreen",
    "-internal-ethiopic-numeric",
    "activetext",
    "-webkit-center",
    "burlywood",
    "color-mix",
    "nw-resize",
    "padding-box",
    "cap-height",
    "width",
    "bounding-box",
    "break-word",
    "lightskyblue",
    "wheat",
    "-webkit-radial-gradient",
    "row-resize",
    "constrained-high",
    "tabular-nums",
    "accumulate",
    "-webkit-linear-gradient",
    "saddlebrown",
    "self-block-end",
    "peru",
    "-webkit-isolate",
    "self-block-start",
    "lightslategrey",
    "lightslategray",
    "inline-block",
    "-webkit-control",
    "most-height",
    "cornflowerblue",
    "-webkit-calc",
    "pow",
    "-webkit-min-content",
    "maximized",
    "drop-shadow",
    "inline-size",
    "anywhere",
    "-webkit-auto",
    "table-header-group",
    "no-open-quote",
    "canvastext",
    "space-between",
    "visible",
    "anchor-size",
    "-webkit-mini-control",
    "ic-width",
    "pan-down",
    "push-button",
    "embedded-opentype",
    "below",
    "inline-axis",
    "woff",
    "woff2",
    "-internal-variable-value",
    "searchfield-cancel-button",
    "lightyellow",
    "always",
    "-webkit-isolate-override",
    "pre-wrap",
    "ew-resize",
    "plaintext",
    "swash",
    "-webkit-gradient",
    "mediumorchid",
    "exit-crossing",
    "menutext",
    "nwse-resize",
    "ch-width",
    "visiblepainted",
    "no-overflow",
    "-webkit-link",
    "flex-flow",
    "text-before-edge",
    "-webkit-image-set",
    "plus-lighter",
    "-internal-korean-hanja-formal",
    "-internal-korean-hanja-informal",
    "most-width",
    "cubic-bezier",
    "character-variant",
    "sandybrown",
    "color-sbix",
    "color-colrv1",
    "color-colrv0",
    "pinch-zoom",
    "-internal-extend-to-zoom",
    "full-width",
    "greenyellow",
    "whitesmoke",
    "-webkit-fit-content",
    "-webkit-baseline-middle",
    "visiblefill",
    "-webkit-max-content",
    "peachpuff",
    "lemonchiffon",
    "-webkit-cross-fade",
    "ideographic",
    "floralwhite",
    "grid-rows",
    "paleturquoise",
    "xxx-large",
    "-webkit-activelink",
    "window",
    "visual",
    "-webkit-grab",
    "-webkit-small-control",
    "extra-expanded",
    "slider-horizontal",
    "buttonshadow",
    "lightgoldenrodyellow",
    "-webkit-box",
    "-webkit-body",
    "inactivecaptiontext",
    "-webkit-focus-ring-color",
    "auto-phrase",
    "-webkit-zoom-in",
    "-internal-active-list-box-selection",
    "stroke-box",
    "visiblestroke",
    "xywh",
    "checkbox",
    "infotext",
    "-webkit-fill-available",
    "allow-discrete",
    "-webkit-zoom-out",
    "vertical-text",
    "line-through",
    "-webkit-repeating-linear-gradient",
    "visitedtext",
    "powderblue",
    "infobackground",
    "highlighttext",
    "-webkit-repeating-radial-gradient",
    "ex-height",
    "auto-flow",
    "-webkit-optimize-contrast",
    "-internal-inactive-list-box-selection",
    "buttonhighlight",
    "-webkit-grabbing",
    "optimizequality",
    "optimizelegibility",
    "prophoto-rgb",
    "table-row-group",
    "vertical-right",
    "-webkit-match-parent",
    "view-box",
    "windowframe",
    "-webkit-inline-flex",
    "navajowhite",
    "-webkit-inline-box",
    "sideways-right",
    "threedshadow",
    "-webkit-plaintext",
    "read-write-plaintext-only",
    "-webkit-flex",
    "after-white-space",
    "proportional-width",
    "window-controls-overlay",
    "-webkit-right",
    "windowtext",
    "threeddarkshadow",
    "papayawhip",
    "threedhighlight",
    "-internal-active-list-box-selection-text",
    "antiquewhite",
    "-internal-inactive-list-box-selection-text",
    "ghostwhite",
    "threedlightshadow",
    "-webkit-xxx-large"
  };
#define CSSValueStringPool ((const char *) &CSSValueStringPool_contents)
const struct Value *
CSSValueKeywordsHash::findValueImpl (const char *str, unsigned int len)
{
  enum
    {
      TOTAL_KEYWORDS = 1056,
      MIN_WORD_LENGTH = 1,
      MAX_WORD_LENGTH = 42,
      MIN_HASH_VALUE = 3,
      MAX_HASH_VALUE = 10044
    };

  static const struct Value value_word_list[] =
    {
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str0), static_cast<int>(CSSValueID::kS)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1), static_cast<int>(CSSValueID::kH)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str2), static_cast<int>(CSSValueID::kSin)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str3), static_cast<int>(CSSValueID::kMin)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str4), static_cast<int>(CSSValueID::kStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str5), static_cast<int>(CSSValueID::kDrag)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str6), static_cast<int>(CSSValueID::kL)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str7), static_cast<int>(CSSValueID::kSeagreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str8), static_cast<int>(CSSValueID::kMeter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str9), static_cast<int>(CSSValueID::kMiter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str10), static_cast<int>(CSSValueID::kLr)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str11), static_cast<int>(CSSValueID::kLtr)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str12), static_cast<int>(CSSValueID::kFine)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str13), static_cast<int>(CSSValueID::kHanging)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str14), static_cast<int>(CSSValueID::kDark)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str15), static_cast<int>(CSSValueID::kMark)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str16), static_cast<int>(CSSValueID::kLarge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str17), static_cast<int>(CSSValueID::kDarken)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str18), static_cast<int>(CSSValueID::kMagenta)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str19), static_cast<int>(CSSValueID::kLarger)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str20), static_cast<int>(CSSValueID::kDot)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str21), static_cast<int>(CSSValueID::kLinen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str22), static_cast<int>(CSSValueID::kNan)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str23), static_cast<int>(CSSValueID::kLinear)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str24), static_cast<int>(CSSValueID::kDrop)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str25), static_cast<int>(CSSValueID::kR)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str26), static_cast<int>(CSSValueID::kC)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str27), static_cast<int>(CSSValueID::kDarkgreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str28), static_cast<int>(CSSValueID::kLime)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str29), static_cast<int>(CSSValueID::kMore)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str30), static_cast<int>(CSSValueID::kZ)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str31), static_cast<int>(CSSValueID::kDisc)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str32), static_cast<int>(CSSValueID::kSteps)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str33), static_cast<int>(CSSValueID::kLog)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str34), static_cast<int>(CSSValueID::kHide)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str35), static_cast<int>(CSSValueID::kLonger)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str36), static_cast<int>(CSSValueID::kSRGB)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str37), static_cast<int>(CSSValueID::kField)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str38), static_cast<int>(CSSValueID::kRem)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str39), static_cast<int>(CSSValueID::kSlice)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str40), static_cast<int>(CSSValueID::kHelp)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str41), static_cast<int>(CSSValueID::kLimegreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str42), static_cast<int>(CSSValueID::kLedger)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str43), static_cast<int>(CSSValueID::kFill)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str44), static_cast<int>(CSSValueID::kNone)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str45), static_cast<int>(CSSValueID::kFrom)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str46), static_cast<int>(CSSValueID::kMarkers)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str47), static_cast<int>(CSSValueID::kDarkred)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str48), static_cast<int>(CSSValueID::kDarkorange)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str49), static_cast<int>(CSSValueID::kMod)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str50), static_cast<int>(CSSValueID::kFormat)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str51), static_cast<int>(CSSValueID::kSmall)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str52), static_cast<int>(CSSValueID::kNearest)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str53), static_cast<int>(CSSValueID::kSVG)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str54), static_cast<int>(CSSValueID::kSmaller)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str55), static_cast<int>(CSSValueID::kRed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str56), static_cast<int>(CSSValueID::kFrames)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str57), static_cast<int>(CSSValueID::kRidge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str58), static_cast<int>(CSSValueID::kReset)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str59), static_cast<int>(CSSValueID::kRl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str60), static_cast<int>(CSSValueID::kSides)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str61), static_cast<int>(CSSValueID::kRtl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str62), static_cast<int>(CSSValueID::kHsl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str63), static_cast<int>(CSSValueID::kLetter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str64), static_cast<int>(CSSValueID::kNonzero)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str65), static_cast<int>(CSSValueID::kCalc)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str66), static_cast<int>(CSSValueID::kCell)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str67), static_cast<int>(CSSValueID::kClip)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str68), static_cast<int>(CSSValueID::kStable)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str69), static_cast<int>(CSSValueID::kCircle)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str70), static_cast<int>(CSSValueID::kCos)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str71), static_cast<int>(CSSValueID::kFilled)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str72), static_cast<int>(CSSValueID::kMove)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str73), static_cast<int>(CSSValueID::kHover)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str74), static_cast<int>(CSSValueID::kCoarse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str75), static_cast<int>(CSSValueID::kSalmon)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str76), static_cast<int>(CSSValueID::kUnicase)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str77), static_cast<int>(CSSValueID::kFast)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str78), static_cast<int>(CSSValueID::kCenter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str79), static_cast<int>(CSSValueID::kUnset)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str80), static_cast<int>(CSSValueID::kRevert)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str81), static_cast<int>(CSSValueID::kUnder)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str82), static_cast<int>(CSSValueID::kUrl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str83), static_cast<int>(CSSValueID::kLast)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str84), static_cast<int>(CSSValueID::kZoom)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str85), static_cast<int>(CSSValueID::kB)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str86), static_cast<int>(CSSValueID::kB4)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str87), static_cast<int>(CSSValueID::kStepEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str88), static_cast<int>(CSSValueID::kFlat)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str89), static_cast<int>(CSSValueID::kForestgreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str90), static_cast<int>(CSSValueID::kStepStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str91), static_cast<int>(CSSValueID::kB5)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str92), static_cast<int>(CSSValueID::kBeige)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str93), static_cast<int>(CSSValueID::kScale)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str94), static_cast<int>(CSSValueID::kLinearGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str95), static_cast<int>(CSSValueID::kNormal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str96), static_cast<int>(CSSValueID::kDotted)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str97), static_cast<int>(CSSValueID::kNavy)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str98), static_cast<int>(CSSValueID::kCrimson)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str99), static_cast<int>(CSSValueID::kLavender)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str100), static_cast<int>(CSSValueID::kUnicode)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str101), static_cast<int>(CSSValueID::kContent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str102), static_cast<int>(CSSValueID::kScreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str103), static_cast<int>(CSSValueID::kContain)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str104), static_cast<int>(CSSValueID::kHue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str105), static_cast<int>(CSSValueID::kCover)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str106), static_cast<int>(CSSValueID::kTan)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str107), static_cast<int>(CSSValueID::kTeal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str108), static_cast<int>(CSSValueID::kDense)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str109), static_cast<int>(CSSValueID::kOn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str110), static_cast<int>(CSSValueID::kFirst)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str111), static_cast<int>(CSSValueID::kReverse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str112), static_cast<int>(CSSValueID::kDarksalmon)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str113), static_cast<int>(CSSValueID::kRoot)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str114), static_cast<int>(CSSValueID::kSpace)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str115), static_cast<int>(CSSValueID::kSienna)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str116), static_cast<int>(CSSValueID::kSign)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str117), static_cast<int>(CSSValueID::kCentral)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str118), static_cast<int>(CSSValueID::kTo)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str119), static_cast<int>(CSSValueID::kFromImage)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str120), static_cast<int>(CSSValueID::kDarkviolet)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str121), static_cast<int>(CSSValueID::kScale3d)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str122), static_cast<int>(CSSValueID::kRec2020)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str123), static_cast<int>(CSSValueID::kUnderline)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str124), static_cast<int>(CSSValueID::kContents)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str125), static_cast<int>(CSSValueID::kContrast)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str126), static_cast<int>(CSSValueID::kSesame)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str127), static_cast<int>(CSSValueID::kDecreasing)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str128), static_cast<int>(CSSValueID::kRaise)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str129), static_cast<int>(CSSValueID::kFlipStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str130), static_cast<int>(CSSValueID::kRotate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str131), static_cast<int>(CSSValueID::kCap)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str132), static_cast<int>(CSSValueID::kLess)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str133), static_cast<int>(CSSValueID::kStatic)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str134), static_cast<int>(CSSValueID::kMoccasin)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str135), static_cast<int>(CSSValueID::kLegal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str136), static_cast<int>(CSSValueID::kZoomIn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str137), static_cast<int>(CSSValueID::kFalse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str138), static_cast<int>(CSSValueID::kRepeat)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str139), static_cast<int>(CSSValueID::kLoose)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str140), static_cast<int>(CSSValueID::kCompact)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str141), static_cast<int>(CSSValueID::kHsla)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str142), static_cast<int>(CSSValueID::kListItem)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str143), static_cast<int>(CSSValueID::kSelector)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str144), static_cast<int>(CSSValueID::kClear)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str145), static_cast<int>(CSSValueID::kUp)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str146), static_cast<int>(CSSValueID::kMintcream)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str147), static_cast<int>(CSSValueID::kDarkolivegreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str148), static_cast<int>(CSSValueID::kOver)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str149), static_cast<int>(CSSValueID::kSRGBLinear)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str150), static_cast<int>(CSSValueID::kBaseline)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str151), static_cast<int>(CSSValueID::kRect)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str152), static_cast<int>(CSSValueID::kRotate3d)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str153), static_cast<int>(CSSValueID::kKeepAll)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str154), static_cast<int>(CSSValueID::kStandard)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str155), static_cast<int>(CSSValueID::kCoral)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str156), static_cast<int>(CSSValueID::kOnly)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str157), static_cast<int>(CSSValueID::kHueRotate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str158), static_cast<int>(CSSValueID::kBevel)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str159), static_cast<int>(CSSValueID::kDestinationIn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str160), static_cast<int>(CSSValueID::kDarkgoldenrod)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str161), static_cast<int>(CSSValueID::kRecto)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str162), static_cast<int>(CSSValueID::kDouble)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str163), static_cast<int>(CSSValueID::kCopy)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str164), static_cast<int>(CSSValueID::kBottom)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str165), static_cast<int>(CSSValueID::kNumbers)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str166), static_cast<int>(CSSValueID::kClone)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str167), static_cast<int>(CSSValueID::kClamp)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str168), static_cast<int>(CSSValueID::kCollection)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str169), static_cast<int>(CSSValueID::kToZero)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str170), static_cast<int>(CSSValueID::kOverline)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str171), static_cast<int>(CSSValueID::kStandalone)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str172), static_cast<int>(CSSValueID::kRelative)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str173), static_cast<int>(CSSValueID::kRotateRight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str174), static_cast<int>(CSSValueID::kSeparate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str175), static_cast<int>(CSSValueID::kNoRepeat)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str176), static_cast<int>(CSSValueID::kCaption)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str177), static_cast<int>(CSSValueID::kTrue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str178), static_cast<int>(CSSValueID::kTrimStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str179), static_cast<int>(CSSValueID::kSuper)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str180), static_cast<int>(CSSValueID::kSpan)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str181), static_cast<int>(CSSValueID::kCollapse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str182), static_cast<int>(CSSValueID::kBlur)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str183), static_cast<int>(CSSValueID::kTop)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str184), static_cast<int>(CSSValueID::kNumeric)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str185), static_cast<int>(CSSValueID::kOrange)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str186), static_cast<int>(CSSValueID::kReplace)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str187), static_cast<int>(CSSValueID::kBlue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str188), static_cast<int>(CSSValueID::kCustom)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str189), static_cast<int>(CSSValueID::kButt)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str190), static_cast<int>(CSSValueID::kTriangle)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str191), static_cast<int>(CSSValueID::kTomato)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str192), static_cast<int>(CSSValueID::kNoClip)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str193), static_cast<int>(CSSValueID::kE)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str194), static_cast<int>(CSSValueID::kSpaceAll)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str195), static_cast<int>(CSSValueID::kLocal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str196), static_cast<int>(CSSValueID::kOrnaments)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str197), static_cast<int>(CSSValueID::kInternalCenter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str198), static_cast<int>(CSSValueID::kBisque)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str199), static_cast<int>(CSSValueID::kHand)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str200), static_cast<int>(CSSValueID::kDestinationOver)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str201), static_cast<int>(CSSValueID::kRunning)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str202), static_cast<int>(CSSValueID::kSafe)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str203), static_cast<int>(CSSValueID::kBullets)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str204), static_cast<int>(CSSValueID::kButton)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str205), static_cast<int>(CSSValueID::kOrangered)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str206), static_cast<int>(CSSValueID::kCounter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str207), static_cast<int>(CSSValueID::kZoomOut)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str208), static_cast<int>(CSSValueID::kSupports)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str209), static_cast<int>(CSSValueID::kLeading)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str210), static_cast<int>(CSSValueID::kSpellingError)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str211), static_cast<int>(CSSValueID::kOldlace)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str212), static_cast<int>(CSSValueID::kFullscreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str213), static_cast<int>(CSSValueID::kMinContent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str214), static_cast<int>(CSSValueID::kSourceIn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str215), static_cast<int>(CSSValueID::kEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str216), static_cast<int>(CSSValueID::kFitContent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str217), static_cast<int>(CSSValueID::kHidden)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str218), static_cast<int>(CSSValueID::kEase)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str219), static_cast<int>(CSSValueID::kTranslate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str220), static_cast<int>(CSSValueID::kCursive)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str221), static_cast<int>(CSSValueID::kRound)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str222), static_cast<int>(CSSValueID::kTranslate3d)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str223), static_cast<int>(CSSValueID::kCounters)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str224), static_cast<int>(CSSValueID::kEnv)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str225), static_cast<int>(CSSValueID::kClosestSide)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str226), static_cast<int>(CSSValueID::kDestinationOut)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str227), static_cast<int>(CSSValueID::kDarkseagreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str228), static_cast<int>(CSSValueID::kLeft)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str229), static_cast<int>(CSSValueID::kElement)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str230), static_cast<int>(CSSValueID::kCrispedges)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str231), static_cast<int>(CSSValueID::kSpanEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str232), static_cast<int>(CSSValueID::kBalance)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str233), static_cast<int>(CSSValueID::kDestinationAtop)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str234), static_cast<int>(CSSValueID::kSpanStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str235), static_cast<int>(CSSValueID::kEmoji)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str236), static_cast<int>(CSSValueID::kMiddle)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str237), static_cast<int>(CSSValueID::kOutset)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str238), static_cast<int>(CSSValueID::kA)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str239), static_cast<int>(CSSValueID::kAt)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str240), static_cast<int>(CSSValueID::kAA)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str241), static_cast<int>(CSSValueID::kA4)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str242), static_cast<int>(CSSValueID::kSpellOut)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str243), static_cast<int>(CSSValueID::kA3)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str244), static_cast<int>(CSSValueID::kAAA)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str245), static_cast<int>(CSSValueID::kAttr)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str246), static_cast<int>(CSSValueID::kSimplified)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str247), static_cast<int>(CSSValueID::kRepeatingLinearGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str248), static_cast<int>(CSSValueID::kA5)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str249), static_cast<int>(CSSValueID::kOpen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str250), static_cast<int>(CSSValueID::kArg)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str251), static_cast<int>(CSSValueID::kSmallCaps)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str252), static_cast<int>(CSSValueID::kFolded)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str253), static_cast<int>(CSSValueID::kEaseIn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str254), static_cast<int>(CSSValueID::kLab)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str255), static_cast<int>(CSSValueID::kEnabled)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str256), static_cast<int>(CSSValueID::kBlueviolet)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str257), static_cast<int>(CSSValueID::kSpaceAround)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str258), static_cast<int>(CSSValueID::kOutside)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str259), static_cast<int>(CSSValueID::kNoDrag)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str260), static_cast<int>(CSSValueID::kContinuous)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str261), static_cast<int>(CSSValueID::kBackground)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str262), static_cast<int>(CSSValueID::kSpanAll)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str263), static_cast<int>(CSSValueID::kCondensed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str264), static_cast<int>(CSSValueID::kOf)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str265), static_cast<int>(CSSValueID::kX)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str266), static_cast<int>(CSSValueID::kUseScript)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str267), static_cast<int>(CSSValueID::kRgb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str268), static_cast<int>(CSSValueID::kLinearrgb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str269), static_cast<int>(CSSValueID::kBorder)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str270), static_cast<int>(CSSValueID::kStyle)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str271), static_cast<int>(CSSValueID::kSourceOver)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str272), static_cast<int>(CSSValueID::kDarkmagenta)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str273), static_cast<int>(CSSValueID::kNoCommonLigatures)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str274), static_cast<int>(CSSValueID::kFromFont)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str275), static_cast<int>(CSSValueID::kDimgrey)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str276), static_cast<int>(CSSValueID::kDimgray)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str277), static_cast<int>(CSSValueID::kAlternate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str278), static_cast<int>(CSSValueID::kLayer)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str279), static_cast<int>(CSSValueID::kXor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str280), static_cast<int>(CSSValueID::kSnow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str281), static_cast<int>(CSSValueID::kAdd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str282), static_cast<int>(CSSValueID::kHotpink)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str283), static_cast<int>(CSSValueID::kTurquoise)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str284), static_cast<int>(CSSValueID::kDarkgrey)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str285), static_cast<int>(CSSValueID::kDarkgray)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str286), static_cast<int>(CSSValueID::kJis78)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str287), static_cast<int>(CSSValueID::kRay)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str288), static_cast<int>(CSSValueID::kJis04)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str289), static_cast<int>(CSSValueID::kJis90)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str290), static_cast<int>(CSSValueID::kJis83)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str291), static_cast<int>(CSSValueID::kTraditional)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str292), static_cast<int>(CSSValueID::kFlipInline)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str293), static_cast<int>(CSSValueID::kBold)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str294), static_cast<int>(CSSValueID::kSourceOut)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str295), static_cast<int>(CSSValueID::kDigits)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str296), static_cast<int>(CSSValueID::kStyleset)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str297), static_cast<int>(CSSValueID::kBolder)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str298), static_cast<int>(CSSValueID::kAALarge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str299), static_cast<int>(CSSValueID::kCurrentcolor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str300), static_cast<int>(CSSValueID::kUppercase)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str301), static_cast<int>(CSSValueID::kAll)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str302), static_cast<int>(CSSValueID::kDoubleCircle)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str303), static_cast<int>(CSSValueID::kRepeatingRadialGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str304), static_cast<int>(CSSValueID::kSpanTop)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str305), static_cast<int>(CSSValueID::kClosestCorner)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str306), static_cast<int>(CSSValueID::kUnsafe)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str307), static_cast<int>(CSSValueID::kDisclosureOpen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str308), static_cast<int>(CSSValueID::kSlow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str309), static_cast<int>(CSSValueID::kFlow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str310), static_cast<int>(CSSValueID::kSelecteditem)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str311), static_cast<int>(CSSValueID::kAAALarge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str312), static_cast<int>(CSSValueID::kTb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str313), static_cast<int>(CSSValueID::kSub)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str314), static_cast<int>(CSSValueID::kRadio)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str315), static_cast<int>(CSSValueID::kTable)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str316), static_cast<int>(CSSValueID::kBaseSelect)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str317), static_cast<int>(CSSValueID::kSansSerif)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str318), static_cast<int>(CSSValueID::kAtan)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str319), static_cast<int>(CSSValueID::kSolid)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str320), static_cast<int>(CSSValueID::kAtan2)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str321), static_cast<int>(CSSValueID::kLrTb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str322), static_cast<int>(CSSValueID::kFantasy)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str323), static_cast<int>(CSSValueID::kForwards)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str324), static_cast<int>(CSSValueID::kRgba)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str325), static_cast<int>(CSSValueID::kRadial)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str326), static_cast<int>(CSSValueID::kStrict)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str327), static_cast<int>(CSSValueID::kRepeatingConicGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str328), static_cast<int>(CSSValueID::kBorderless)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str329), static_cast<int>(CSSValueID::kScaleY)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str330), static_cast<int>(CSSValueID::kOnDemand)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str331), static_cast<int>(CSSValueID::kEaseOut)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str332), static_cast<int>(CSSValueID::kSlategrey)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str333), static_cast<int>(CSSValueID::kSlategray)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str334), static_cast<int>(CSSValueID::kFabricated)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str335), static_cast<int>(CSSValueID::kNoPreference)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str336), static_cast<int>(CSSValueID::kSubgrid)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str337), static_cast<int>(CSSValueID::kTransparent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str338), static_cast<int>(CSSValueID::kAsin)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str339), static_cast<int>(CSSValueID::kDarkcyan)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str340), static_cast<int>(CSSValueID::kSepia)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str341), static_cast<int>(CSSValueID::kNoDrop)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str342), static_cast<int>(CSSValueID::kHistoricalLigatures)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str343), static_cast<int>(CSSValueID::kInternalMediaControl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str344), static_cast<int>(CSSValueID::kBrown)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str345), static_cast<int>(CSSValueID::kNoAutospace)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str346), static_cast<int>(CSSValueID::kDeeppink)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str347), static_cast<int>(CSSValueID::kDefault)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str348), static_cast<int>(CSSValueID::kDarkslategrey)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str349), static_cast<int>(CSSValueID::kDarkslategray)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str350), static_cast<int>(CSSValueID::kEvenodd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str351), static_cast<int>(CSSValueID::kAlias)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str352), static_cast<int>(CSSValueID::kDarkblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str353), static_cast<int>(CSSValueID::kDisclosureClosed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str354), static_cast<int>(CSSValueID::kStylistic)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str355), static_cast<int>(CSSValueID::kBlack)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str356), static_cast<int>(CSSValueID::kRuby)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str357), static_cast<int>(CSSValueID::kTabbed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str358), static_cast<int>(CSSValueID::kMistyrose)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str359), static_cast<int>(CSSValueID::kLandscape)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str360), static_cast<int>(CSSValueID::kRlTb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str361), static_cast<int>(CSSValueID::kXStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str362), static_cast<int>(CSSValueID::kDecimal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str363), static_cast<int>(CSSValueID::kRotateY)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str364), static_cast<int>(CSSValueID::kCyan)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str365), static_cast<int>(CSSValueID::kMedium)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str366), static_cast<int>(CSSValueID::kBrowser)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str367), static_cast<int>(CSSValueID::kBlock)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str368), static_cast<int>(CSSValueID::kSpringgreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str369), static_cast<int>(CSSValueID::kCrossFade)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str370), static_cast<int>(CSSValueID::kFontFormat)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str371), static_cast<int>(CSSValueID::kSteelblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str372), static_cast<int>(CSSValueID::kY)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str373), static_cast<int>(CSSValueID::kFlowRoot)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str374), static_cast<int>(CSSValueID::kNoPunctuation)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str375), static_cast<int>(CSSValueID::kLogical)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str376), static_cast<int>(CSSValueID::kDodgerblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str377), static_cast<int>(CSSValueID::kTbRl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str378), static_cast<int>(CSSValueID::kRadialGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str379), static_cast<int>(CSSValueID::kDarkslateblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str380), static_cast<int>(CSSValueID::kSize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str381), static_cast<int>(CSSValueID::kCadetblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str382), static_cast<int>(CSSValueID::kDiagonalFractions)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str383), static_cast<int>(CSSValueID::kXSmall)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str384), static_cast<int>(CSSValueID::kOrdinal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str385), static_cast<int>(CSSValueID::kAcos)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str386), static_cast<int>(CSSValueID::kFirstBaseline)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str387), static_cast<int>(CSSValueID::kAlternateReverse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str388), static_cast<int>(CSSValueID::kSymbolic)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str389), static_cast<int>(CSSValueID::kSpaceFirst)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str390), static_cast<int>(CSSValueID::kLiningNums)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str391), static_cast<int>(CSSValueID::kXLarge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str392), static_cast<int>(CSSValueID::kJumpNone)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str393), static_cast<int>(CSSValueID::kXEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str394), static_cast<int>(CSSValueID::kDynamic)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str395), static_cast<int>(CSSValueID::kSlateblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str396), static_cast<int>(CSSValueID::kLegacy)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str397), static_cast<int>(CSSValueID::kJumpEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str398), static_cast<int>(CSSValueID::kOverlay)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str399), static_cast<int>(CSSValueID::kJumpStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str400), static_cast<int>(CSSValueID::kRepeatY)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str401), static_cast<int>(CSSValueID::kFlex)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str402), static_cast<int>(CSSValueID::kSpanLeft)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str403), static_cast<int>(CSSValueID::kInternalUpperArmenian)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str404), static_cast<int>(CSSValueID::kLiteralPunctuation)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str405), static_cast<int>(CSSValueID::kStatusBar)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str406), static_cast<int>(CSSValueID::kMediumseagreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str407), static_cast<int>(CSSValueID::kMinimalUi)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str408), static_cast<int>(CSSValueID::kIn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str409), static_cast<int>(CSSValueID::kCyclic)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str410), static_cast<int>(CSSValueID::kMath)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str411), static_cast<int>(CSSValueID::kSubtract)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str412), static_cast<int>(CSSValueID::kFeaturesAat)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str413), static_cast<int>(CSSValueID::kLuminance)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str414), static_cast<int>(CSSValueID::kHigh)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str415), static_cast<int>(CSSValueID::kType)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str416), static_cast<int>(CSSValueID::kRotateLeft)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str417), static_cast<int>(CSSValueID::kAfter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str418), static_cast<int>(CSSValueID::kCornsilk)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str419), static_cast<int>(CSSValueID::kOpacity)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str420), static_cast<int>(CSSValueID::kUltraCondensed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str421), static_cast<int>(CSSValueID::kLight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str422), static_cast<int>(CSSValueID::kBlockEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str423), static_cast<int>(CSSValueID::kLighten)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str424), static_cast<int>(CSSValueID::kLighter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str425), static_cast<int>(CSSValueID::kSpaceEvenly)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str426), static_cast<int>(CSSValueID::kBlink)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str427), static_cast<int>(CSSValueID::kHistoricalForms)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str428), static_cast<int>(CSSValueID::kDisplayP3)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str429), static_cast<int>(CSSValueID::kButtonface)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str430), static_cast<int>(CSSValueID::kOff)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str431), static_cast<int>(CSSValueID::kLightgreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str432), static_cast<int>(CSSValueID::kRight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str433), static_cast<int>(CSSValueID::kInset)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str434), static_cast<int>(CSSValueID::kSerif)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str435), static_cast<int>(CSSValueID::kSystemUi)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str436), static_cast<int>(CSSValueID::kSeResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str437), static_cast<int>(CSSValueID::kEntry)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str438), static_cast<int>(CSSValueID::kSResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str439), static_cast<int>(CSSValueID::kScaleZ)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str440), static_cast<int>(CSSValueID::kEaseInOut)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str441), static_cast<int>(CSSValueID::kEmbed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str442), static_cast<int>(CSSValueID::kIsolate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str443), static_cast<int>(CSSValueID::kNeResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str444), static_cast<int>(CSSValueID::kSliderVertical)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str445), static_cast<int>(CSSValueID::kDashed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str446), static_cast<int>(CSSValueID::kInvert)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str447), static_cast<int>(CSSValueID::kNResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str448), static_cast<int>(CSSValueID::kMandatory)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str449), static_cast<int>(CSSValueID::kOptional)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str450), static_cast<int>(CSSValueID::kSemiCondensed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str451), static_cast<int>(CSSValueID::kTranslateY)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str452), static_cast<int>(CSSValueID::kFlexEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str453), static_cast<int>(CSSValueID::kSpanBottom)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str454), static_cast<int>(CSSValueID::kFlexStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str455), static_cast<int>(CSSValueID::kSpanSelfStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str456), static_cast<int>(CSSValueID::kNsResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str457), static_cast<int>(CSSValueID::kTableCell)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str458), static_cast<int>(CSSValueID::kSpanInlineEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str459), static_cast<int>(CSSValueID::kYStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str460), static_cast<int>(CSSValueID::kSpanInlineStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str461), static_cast<int>(CSSValueID::kInverted)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str462), static_cast<int>(CSSValueID::kRotateZ)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str463), static_cast<int>(CSSValueID::kCommonLigatures)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str464), static_cast<int>(CSSValueID::kInitial)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str465), static_cast<int>(CSSValueID::kAllScroll)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str466), static_cast<int>(CSSValueID::kItalic)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str467), static_cast<int>(CSSValueID::kMediaProgress)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str468), static_cast<int>(CSSValueID::kResetSize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str469), static_cast<int>(CSSValueID::kInsetArea)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str470), static_cast<int>(CSSValueID::kImageSet)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str471), static_cast<int>(CSSValueID::kBreakAll)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str472), static_cast<int>(CSSValueID::kCalcSize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str473), static_cast<int>(CSSValueID::kAfterEdge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str474), static_cast<int>(CSSValueID::kBoth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str475), static_cast<int>(CSSValueID::kAbs)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str476), static_cast<int>(CSSValueID::kMediumspringgreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str477), static_cast<int>(CSSValueID::kPi)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str478), static_cast<int>(CSSValueID::kP3)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str479), static_cast<int>(CSSValueID::kPre)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str480), static_cast<int>(CSSValueID::kTruetype)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str481), static_cast<int>(CSSValueID::kColResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str482), static_cast<int>(CSSValueID::kAny)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str483), static_cast<int>(CSSValueID::kIncremental)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str484), static_cast<int>(CSSValueID::kBlockStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str485), static_cast<int>(CSSValueID::kPage)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str486), static_cast<int>(CSSValueID::kPink)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str487), static_cast<int>(CSSValueID::kMultiply)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str488), static_cast<int>(CSSValueID::kIntersect)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str489), static_cast<int>(CSSValueID::kIncreasing)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str490), static_cast<int>(CSSValueID::kLightsalmon)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str491), static_cast<int>(CSSValueID::kMaroon)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str492), static_cast<int>(CSSValueID::kAccentcolor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str493), static_cast<int>(CSSValueID::kInterlace)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str494), static_cast<int>(CSSValueID::kYEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str495), static_cast<int>(CSSValueID::kInternalSpellingErrorColor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str496), static_cast<int>(CSSValueID::kA98Rgb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str497), static_cast<int>(CSSValueID::kBackButton)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str498), static_cast<int>(CSSValueID::kSkew)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str499), static_cast<int>(CSSValueID::kSticky)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str500), static_cast<int>(CSSValueID::kPaged)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str501), static_cast<int>(CSSValueID::kSpanYEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str502), static_cast<int>(CSSValueID::kManipulation)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str503), static_cast<int>(CSSValueID::kSqrt)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str504), static_cast<int>(CSSValueID::kPalegreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str505), static_cast<int>(CSSValueID::kLightcoral)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str506), static_cast<int>(CSSValueID::kTableColumn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str507), static_cast<int>(CSSValueID::kReadOnly)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str508), static_cast<int>(CSSValueID::kSpanYStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str509), static_cast<int>(CSSValueID::kMathAuto)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str510), static_cast<int>(CSSValueID::kPalettes)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str511), static_cast<int>(CSSValueID::kAntialiased)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str512), static_cast<int>(CSSValueID::kPortrait)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str513), static_cast<int>(CSSValueID::kTech)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str514), static_cast<int>(CSSValueID::kProgress)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str515), static_cast<int>(CSSValueID::kJisB4)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str516), static_cast<int>(CSSValueID::kG)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str517), static_cast<int>(CSSValueID::kJisB5)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str518), static_cast<int>(CSSValueID::kColor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str519), static_cast<int>(CSSValueID::kHeight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str520), static_cast<int>(CSSValueID::kPreLine)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str521), static_cast<int>(CSSValueID::kGreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str522), static_cast<int>(CSSValueID::kAvoid)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str523), static_cast<int>(CSSValueID::kAllSmallCaps)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str524), static_cast<int>(CSSValueID::kImplicit)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str525), static_cast<int>(CSSValueID::kRevertLayer)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str526), static_cast<int>(CSSValueID::kIcon)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str527), static_cast<int>(CSSValueID::kAdditive)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str528), static_cast<int>(CSSValueID::kShorter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str529), static_cast<int>(CSSValueID::kFirebrick)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str530), static_cast<int>(CSSValueID::kPolygon)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str531), static_cast<int>(CSSValueID::kPaint)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str532), static_cast<int>(CSSValueID::kFuchsia)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str533), static_cast<int>(CSSValueID::kBothEdges)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str534), static_cast<int>(CSSValueID::kGrab)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str535), static_cast<int>(CSSValueID::kGrey)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str536), static_cast<int>(CSSValueID::kGray)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str537), static_cast<int>(CSSValueID::kOrchid)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str538), static_cast<int>(CSSValueID::kTranslateZ)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str539), static_cast<int>(CSSValueID::kSelf)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str540), static_cast<int>(CSSValueID::kOpentype)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str541), static_cast<int>(CSSValueID::kInsetInlineEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str542), static_cast<int>(CSSValueID::kFlexVisual)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str543), static_cast<int>(CSSValueID::kInsetInlineStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str544), static_cast<int>(CSSValueID::kEntryCrossing)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str545), static_cast<int>(CSSValueID::kEllipse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str546), static_cast<int>(CSSValueID::kXSelfEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str547), static_cast<int>(CSSValueID::kKhaki)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str548), static_cast<int>(CSSValueID::kPointer)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str549), static_cast<int>(CSSValueID::kOldstyleNums)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str550), static_cast<int>(CSSValueID::kFallback)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str551), static_cast<int>(CSSValueID::kActive)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str552), static_cast<int>(CSSValueID::kPainted)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str553), static_cast<int>(CSSValueID::kPurple)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str554), static_cast<int>(CSSValueID::kAliceblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str555), static_cast<int>(CSSValueID::kPlum)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str556), static_cast<int>(CSSValueID::kMediumpurple)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str557), static_cast<int>(CSSValueID::kPreserve)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str558), static_cast<int>(CSSValueID::kEllipsis)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str559), static_cast<int>(CSSValueID::kXSelfStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str560), static_cast<int>(CSSValueID::kScroll)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str561), static_cast<int>(CSSValueID::kSpanYSelfEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str562), static_cast<int>(CSSValueID::kSpanYSelfStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str563), static_cast<int>(CSSValueID::kEconomy)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str564), static_cast<int>(CSSValueID::kEResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str565), static_cast<int>(CSSValueID::kAllPetiteCaps)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str566), static_cast<int>(CSSValueID::kContentBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str567), static_cast<int>(CSSValueID::kLch)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str568), static_cast<int>(CSSValueID::kStretch)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str569), static_cast<int>(CSSValueID::kNoChange)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str570), static_cast<int>(CSSValueID::kChocolate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str571), static_cast<int>(CSSValueID::kLightseagreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str572), static_cast<int>(CSSValueID::kSquare)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str573), static_cast<int>(CSSValueID::kSkyblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str574), static_cast<int>(CSSValueID::kMediumvioletred)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str575), static_cast<int>(CSSValueID::kLightsteelblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str576), static_cast<int>(CSSValueID::kW)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str577), static_cast<int>(CSSValueID::kButtonborder)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str578), static_cast<int>(CSSValueID::kPalevioletred)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str579), static_cast<int>(CSSValueID::kSelfEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str580), static_cast<int>(CSSValueID::kLuminosity)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str581), static_cast<int>(CSSValueID::kMax)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str582), static_cast<int>(CSSValueID::kSelfStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str583), static_cast<int>(CSSValueID::kPalegoldenrod)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str584), static_cast<int>(CSSValueID::kWrap)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str585), static_cast<int>(CSSValueID::kMatrix)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str586), static_cast<int>(CSSValueID::kPreserve3d)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str587), static_cast<int>(CSSValueID::kSmallCaption)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str588), static_cast<int>(CSSValueID::kMessageBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str589), static_cast<int>(CSSValueID::kColorSVG)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str590), static_cast<int>(CSSValueID::kMarktext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str591), static_cast<int>(CSSValueID::kGrammarError)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str592), static_cast<int>(CSSValueID::kMinmax)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str593), static_cast<int>(CSSValueID::kPaused)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str594), static_cast<int>(CSSValueID::kLinktext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str595), static_cast<int>(CSSValueID::kSpanSelfEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str596), static_cast<int>(CSSValueID::kBrightness)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str597), static_cast<int>(CSSValueID::kOklab)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str598), static_cast<int>(CSSValueID::kMinimized)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str599), static_cast<int>(CSSValueID::kDarkorchid)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str600), static_cast<int>(CSSValueID::kMixed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str601), static_cast<int>(CSSValueID::kMatrix3d)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str602), static_cast<int>(CSSValueID::kContainerProgress)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str603), static_cast<int>(CSSValueID::kFixed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str604), static_cast<int>(CSSValueID::kFieldtext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str605), static_cast<int>(CSSValueID::kFlipBlock)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str606), static_cast<int>(CSSValueID::kSilver)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str607), static_cast<int>(CSSValueID::kChartreuse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str608), static_cast<int>(CSSValueID::kMonospace)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str609), static_cast<int>(CSSValueID::kWait)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str610), static_cast<int>(CSSValueID::kSeashell)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str611), static_cast<int>(CSSValueID::kPanUp)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str612), static_cast<int>(CSSValueID::kCanvas)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str613), static_cast<int>(CSSValueID::kAzure)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str614), static_cast<int>(CSSValueID::kCrosshair)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str615), static_cast<int>(CSSValueID::kAvoidPage)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str616), static_cast<int>(CSSValueID::kGrayscale)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str617), static_cast<int>(CSSValueID::kLightgrey)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str618), static_cast<int>(CSSValueID::kLightgray)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str619), static_cast<int>(CSSValueID::kHorizontal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str620), static_cast<int>(CSSValueID::kPadding)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str621), static_cast<int>(CSSValueID::kInline)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str622), static_cast<int>(CSSValueID::kMediumblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str623), static_cast<int>(CSSValueID::kWavy)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str624), static_cast<int>(CSSValueID::kTitlingCaps)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str625), static_cast<int>(CSSValueID::kSourceAtop)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str626), static_cast<int>(CSSValueID::kYSelfEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str627), static_cast<int>(CSSValueID::kThin)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str628), static_cast<int>(CSSValueID::kIndigo)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str629), static_cast<int>(CSSValueID::kColorStop)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str630), static_cast<int>(CSSValueID::kDiscretionaryLigatures)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str631), static_cast<int>(CSSValueID::kInside)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str632), static_cast<int>(CSSValueID::kScaleX)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str633), static_cast<int>(CSSValueID::kNoCloseQuote)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str634), static_cast<int>(CSSValueID::kFontTech)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str635), static_cast<int>(CSSValueID::kIndianred)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str636), static_cast<int>(CSSValueID::kColorContrast)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str637), static_cast<int>(CSSValueID::kIvory)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str638), static_cast<int>(CSSValueID::kRoyalblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str639), static_cast<int>(CSSValueID::kYSelfStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str640), static_cast<int>(CSSValueID::kMatchSource)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str641), static_cast<int>(CSSValueID::kWrapReverse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str642), static_cast<int>(CSSValueID::kAnchor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str643), static_cast<int>(CSSValueID::kThistle)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str644), static_cast<int>(CSSValueID::kGrid)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str645), static_cast<int>(CSSValueID::kNoHistoricalLigatures)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str646), static_cast<int>(CSSValueID::kPanLeft)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str647), static_cast<int>(CSSValueID::kBefore)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str648), static_cast<int>(CSSValueID::kIsolateOverride)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str649), static_cast<int>(CSSValueID::kRotateX)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str650), static_cast<int>(CSSValueID::kInactiveborder)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str651), static_cast<int>(CSSValueID::kLightDark)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str652), static_cast<int>(CSSValueID::kInlineGrid)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str653), static_cast<int>(CSSValueID::kScrollState)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str654), static_cast<int>(CSSValueID::kTableColumnGroup)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str655), static_cast<int>(CSSValueID::kAnnotation)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str656), static_cast<int>(CSSValueID::kOlive)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str657), static_cast<int>(CSSValueID::kText)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str658), static_cast<int>(CSSValueID::kLightcyan)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str659), static_cast<int>(CSSValueID::kStroke)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str660), static_cast<int>(CSSValueID::kMathematical)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str661), static_cast<int>(CSSValueID::kMostInlineSize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str662), static_cast<int>(CSSValueID::kAlpha)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str663), static_cast<int>(CSSValueID::kTextarea)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str664), static_cast<int>(CSSValueID::kInlineEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str665), static_cast<int>(CSSValueID::kLightblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str666), static_cast<int>(CSSValueID::kSpanRight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str667), static_cast<int>(CSSValueID::kProgressive)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str668), static_cast<int>(CSSValueID::kCapitalize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str669), static_cast<int>(CSSValueID::kLightpink)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str670), static_cast<int>(CSSValueID::kGold)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str671), static_cast<int>(CSSValueID::kSkewY)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str672), static_cast<int>(CSSValueID::kInlineStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str673), static_cast<int>(CSSValueID::kAvoidColumn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str674), static_cast<int>(CSSValueID::kRepeatX)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str675), static_cast<int>(CSSValueID::kPanY)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str676), static_cast<int>(CSSValueID::kPretty)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str677), static_cast<int>(CSSValueID::kNoContextual)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str678), static_cast<int>(CSSValueID::kBreakSpaces)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str679), static_cast<int>(CSSValueID::kVar)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str680), static_cast<int>(CSSValueID::kMenu)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str681), static_cast<int>(CSSValueID::kSwap)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str682), static_cast<int>(CSSValueID::kBlockSize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str683), static_cast<int>(CSSValueID::kInitialOnly)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str684), static_cast<int>(CSSValueID::kLastBaseline)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str685), static_cast<int>(CSSValueID::kSaturate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str686), static_cast<int>(CSSValueID::kAuto)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str687), static_cast<int>(CSSValueID::kDifference)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str688), static_cast<int>(CSSValueID::kHardLight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str689), static_cast<int>(CSSValueID::kFarthestCorner)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str690), static_cast<int>(CSSValueID::kGoldenrod)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str691), static_cast<int>(CSSValueID::kViolet)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str692), static_cast<int>(CSSValueID::kGridOrder)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str693), static_cast<int>(CSSValueID::kVs)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str694), static_cast<int>(CSSValueID::kContextual)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str695), static_cast<int>(CSSValueID::kSaturation)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str696), static_cast<int>(CSSValueID::kEx)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str697), static_cast<int>(CSSValueID::kGrabbing)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str698), static_cast<int>(CSSValueID::kExact)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str699), static_cast<int>(CSSValueID::kJustify)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str700), static_cast<int>(CSSValueID::kManual)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str701), static_cast<int>(CSSValueID::kRow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str702), static_cast<int>(CSSValueID::kShow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str703), static_cast<int>(CSSValueID::kSearchfield)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str704), static_cast<int>(CSSValueID::kSelfInline)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str705), static_cast<int>(CSSValueID::kColorDodge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str706), static_cast<int>(CSSValueID::kHypot)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str707), static_cast<int>(CSSValueID::kButtontext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str708), static_cast<int>(CSSValueID::kPaletteMix)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str709), static_cast<int>(CSSValueID::kLayout)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str710), static_cast<int>(CSSValueID::kMaxContent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str711), static_cast<int>(CSSValueID::kFeaturesGraphite)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str712), static_cast<int>(CSSValueID::kStackedFractions)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str713), static_cast<int>(CSSValueID::kConicGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str714), static_cast<int>(CSSValueID::kMenulist)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str715), static_cast<int>(CSSValueID::kTranslateX)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str716), static_cast<int>(CSSValueID::kContextMenu)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str717), static_cast<int>(CSSValueID::kInfinite)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str718), static_cast<int>(CSSValueID::kScrollbar)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str719), static_cast<int>(CSSValueID::kSoftLight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str720), static_cast<int>(CSSValueID::kWords)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str721), static_cast<int>(CSSValueID::kGeometricprecision)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str722), static_cast<int>(CSSValueID::kExit)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str723), static_cast<int>(CSSValueID::kOpenQuote)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str724), static_cast<int>(CSSValueID::kColumn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str725), static_cast<int>(CSSValueID::kNegativeInfinity)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str726), static_cast<int>(CSSValueID::kExtends)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str727), static_cast<int>(CSSValueID::kAqua)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str728), static_cast<int>(CSSValueID::kSpanBlockEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str729), static_cast<int>(CSSValueID::kBidiOverride)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str730), static_cast<int>(CSSValueID::kSpanBlockStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str731), static_cast<int>(CSSValueID::kCaptiontext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str732), static_cast<int>(CSSValueID::kBlanchedalmond)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str733), static_cast<int>(CSSValueID::kAquamarine)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str734), static_cast<int>(CSSValueID::kUltraExpanded)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str735), static_cast<int>(CSSValueID::kAutoAdd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str736), static_cast<int>(CSSValueID::kVerso)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str737), static_cast<int>(CSSValueID::kDocument)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str738), static_cast<int>(CSSValueID::kTextTop)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str739), static_cast<int>(CSSValueID::kThick)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str740), static_cast<int>(CSSValueID::kDown)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str741), static_cast<int>(CSSValueID::kSelfInlineEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str742), static_cast<int>(CSSValueID::kLawngreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str743), static_cast<int>(CSSValueID::kSelfInlineStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str744), static_cast<int>(CSSValueID::kXyz)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str745), static_cast<int>(CSSValueID::kThreedface)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str746), static_cast<int>(CSSValueID::kExp)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str747), static_cast<int>(CSSValueID::kReduce)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str748), static_cast<int>(CSSValueID::kNowrap)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str749), static_cast<int>(CSSValueID::kPath)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str750), static_cast<int>(CSSValueID::kSpanXEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str751), static_cast<int>(CSSValueID::kInsetBlockEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str752), static_cast<int>(CSSValueID::kColorBurn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str753), static_cast<int>(CSSValueID::kLowercase)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str754), static_cast<int>(CSSValueID::kInsetBlockStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str755), static_cast<int>(CSSValueID::kNeswResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str756), static_cast<int>(CSSValueID::kAnchorCenter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str757), static_cast<int>(CSSValueID::kColorCBDT)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str758), static_cast<int>(CSSValueID::kOklch)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str759), static_cast<int>(CSSValueID::kGainsboro)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str760), static_cast<int>(CSSValueID::kMediumslateblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str761), static_cast<int>(CSSValueID::kSpanXStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str762), static_cast<int>(CSSValueID::kVertical)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str763), static_cast<int>(CSSValueID::kTableCaption)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str764), static_cast<int>(CSSValueID::kPerspective)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str765), static_cast<int>(CSSValueID::kNotAllowed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str766), static_cast<int>(CSSValueID::kCloseQuote)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str767), static_cast<int>(CSSValueID::kUpright)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str768), static_cast<int>(CSSValueID::kInnerSpinButton)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str769), static_cast<int>(CSSValueID::kMediumaquamarine)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str770), static_cast<int>(CSSValueID::kScaleDown)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str771), static_cast<int>(CSSValueID::kPetiteCaps)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str772), static_cast<int>(CSSValueID::kMarginBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str773), static_cast<int>(CSSValueID::kLavenderblush)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str774), static_cast<int>(CSSValueID::kProportionalNums)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str775), static_cast<int>(CSSValueID::kInternalGrammarErrorColor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str776), static_cast<int>(CSSValueID::kXxLarge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str777), static_cast<int>(CSSValueID::kSelecteditemtext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str778), static_cast<int>(CSSValueID::kTableFooterGroup)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str779), static_cast<int>(CSSValueID::kSpanSelfBlockEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str780), static_cast<int>(CSSValueID::kXyzD65)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str781), static_cast<int>(CSSValueID::kListbox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str782), static_cast<int>(CSSValueID::kXyzD50)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str783), static_cast<int>(CSSValueID::kSpanSelfBlockStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str784), static_cast<int>(CSSValueID::kMidnightblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str785), static_cast<int>(CSSValueID::kFillBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str786), static_cast<int>(CSSValueID::kContextFill)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str787), static_cast<int>(CSSValueID::kSpanSelfInlineEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str788), static_cast<int>(CSSValueID::kSpanSelfInlineStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str789), static_cast<int>(CSSValueID::kInherit)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str790), static_cast<int>(CSSValueID::kVerticalRl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str791), static_cast<int>(CSSValueID::kHorizontalTb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str792), static_cast<int>(CSSValueID::kBackwards)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str793), static_cast<int>(CSSValueID::kTextfield)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str794), static_cast<int>(CSSValueID::kPictureInPicture)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str795), static_cast<int>(CSSValueID::kBeforeEdge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str796), static_cast<int>(CSSValueID::kExclude)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str797), static_cast<int>(CSSValueID::kInlineTable)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str798), static_cast<int>(CSSValueID::kGridColumns)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str799), static_cast<int>(CSSValueID::kExclusion)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str800), static_cast<int>(CSSValueID::kSpanXSelfEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str801), static_cast<int>(CSSValueID::kSpanXSelfStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str802), static_cast<int>(CSSValueID::kMediumturquoise)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str803), static_cast<int>(CSSValueID::kHighlight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str804), static_cast<int>(CSSValueID::kInternalTextareaAuto)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str805), static_cast<int>(CSSValueID::kExpanded)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str806), static_cast<int>(CSSValueID::kOlivedrab)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str807), static_cast<int>(CSSValueID::kVerticalLr)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str808), static_cast<int>(CSSValueID::kAutoFit)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str809), static_cast<int>(CSSValueID::kFeaturesOpentype)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str810), static_cast<int>(CSSValueID::kInactivecaption)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str811), static_cast<int>(CSSValueID::kInternalLowerArmenian)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str812), static_cast<int>(CSSValueID::kHwb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str813), static_cast<int>(CSSValueID::kRubyText)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str814), static_cast<int>(CSSValueID::kReadWrite)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str815), static_cast<int>(CSSValueID::kTextAfterEdge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str816), static_cast<int>(CSSValueID::kActiveborder)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str817), static_cast<int>(CSSValueID::kAppworkspace)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str818), static_cast<int>(CSSValueID::kSemiExpanded)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str819), static_cast<int>(CSSValueID::kDeepskyblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str820), static_cast<int>(CSSValueID::kView)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str821), static_cast<int>(CSSValueID::kTextBottom)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str822), static_cast<int>(CSSValueID::kAlphabetic)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str823), static_cast<int>(CSSValueID::kProgressBar)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str824), static_cast<int>(CSSValueID::kContextStroke)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str825), static_cast<int>(CSSValueID::kBlockAxis)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str826), static_cast<int>(CSSValueID::kRebeccapurple)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str827), static_cast<int>(CSSValueID::kNonScalingStroke)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str828), static_cast<int>(CSSValueID::kSmooth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str829), static_cast<int>(CSSValueID::kAutoFill)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str830), static_cast<int>(CSSValueID::kSquareButton)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str831), static_cast<int>(CSSValueID::kXxSmall)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str832), static_cast<int>(CSSValueID::kDarkkhaki)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str833), static_cast<int>(CSSValueID::kWResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str834), static_cast<int>(CSSValueID::kAbsolute)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str835), static_cast<int>(CSSValueID::kInternalQuirkInherit)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str836), static_cast<int>(CSSValueID::kAnchorsVisible)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str837), static_cast<int>(CSSValueID::kPanRight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str838), static_cast<int>(CSSValueID::kVariations)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str839), static_cast<int>(CSSValueID::kHoneydew)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str840), static_cast<int>(CSSValueID::kGroove)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str841), static_cast<int>(CSSValueID::kDarkturquoise)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str842), static_cast<int>(CSSValueID::kRowReverse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str843), static_cast<int>(CSSValueID::kSideways)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str844), static_cast<int>(CSSValueID::kOptimizespeed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str845), static_cast<int>(CSSValueID::kFarthestSide)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str846), static_cast<int>(CSSValueID::kInfinity)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str847), static_cast<int>(CSSValueID::kSubpixelAntialiased)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str848), static_cast<int>(CSSValueID::kRosybrown)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str849), static_cast<int>(CSSValueID::kSlashedZero)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str850), static_cast<int>(CSSValueID::kPreserveParentColor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str851), static_cast<int>(CSSValueID::kAbove)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str852), static_cast<int>(CSSValueID::kColumnReverse)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str853), static_cast<int>(CSSValueID::kMostBlockSize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str854), static_cast<int>(CSSValueID::kProximity)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str855), static_cast<int>(CSSValueID::kAccentcolortext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str856), static_cast<int>(CSSValueID::kSkewX)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str857), static_cast<int>(CSSValueID::kTableRow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str858), static_cast<int>(CSSValueID::kOblique)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str859), static_cast<int>(CSSValueID::kInternalKoreanHangulFormal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str860), static_cast<int>(CSSValueID::kActivecaption)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str861), static_cast<int>(CSSValueID::kPanX)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str862), static_cast<int>(CSSValueID::kMenulistButton)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str863), static_cast<int>(CSSValueID::kInlineLayout)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str864), static_cast<int>(CSSValueID::kPreserveBreaks)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str865), static_cast<int>(CSSValueID::kExtraCondensed)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str866), static_cast<int>(CSSValueID::kJumpBoth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str867), static_cast<int>(CSSValueID::kInternalHebrew)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str868), static_cast<int>(CSSValueID::kPixelated)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str869), static_cast<int>(CSSValueID::kWebkitLeft)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str870), static_cast<int>(CSSValueID::kBorderBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str871), static_cast<int>(CSSValueID::kNoDiscretionaryLigatures)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str872), static_cast<int>(CSSValueID::kInternalSimpChineseFormal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str873), static_cast<int>(CSSValueID::kScrollPosition)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str874), static_cast<int>(CSSValueID::kInternalSimpChineseInformal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str875), static_cast<int>(CSSValueID::kWeight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str876), static_cast<int>(CSSValueID::kInternalTradChineseFormal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str877), static_cast<int>(CSSValueID::kInternalAppearanceAutoBaseSelect)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str878), static_cast<int>(CSSValueID::kInlineFlex)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str879), static_cast<int>(CSSValueID::kInternalTradChineseInformal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str880), static_cast<int>(CSSValueID::kYellow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str881), static_cast<int>(CSSValueID::kSelfBlock)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str882), static_cast<int>(CSSValueID::kGraytext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str883), static_cast<int>(CSSValueID::kDynamicRangeLimitMix)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str884), static_cast<int>(CSSValueID::kWhite)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str885), static_cast<int>(CSSValueID::kSwResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str886), static_cast<int>(CSSValueID::kYellowgreen)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str887), static_cast<int>(CSSValueID::kInternalEthiopicNumeric)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str888), static_cast<int>(CSSValueID::kActivetext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str889), static_cast<int>(CSSValueID::kWebkitCenter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str890), static_cast<int>(CSSValueID::kBurlywood)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str891), static_cast<int>(CSSValueID::kColorMix)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str892), static_cast<int>(CSSValueID::kNwResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str893), static_cast<int>(CSSValueID::kPaddingBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str894), static_cast<int>(CSSValueID::kCapHeight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str895), static_cast<int>(CSSValueID::kWidth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str896), static_cast<int>(CSSValueID::kBoundingBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str897), static_cast<int>(CSSValueID::kBreakWord)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str898), static_cast<int>(CSSValueID::kLightskyblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str899), static_cast<int>(CSSValueID::kWheat)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str900), static_cast<int>(CSSValueID::kWebkitRadialGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str901), static_cast<int>(CSSValueID::kRowResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str902), static_cast<int>(CSSValueID::kConstrainedHigh)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str903), static_cast<int>(CSSValueID::kTabularNums)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str904), static_cast<int>(CSSValueID::kAccumulate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str905), static_cast<int>(CSSValueID::kWebkitLinearGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str906), static_cast<int>(CSSValueID::kSaddlebrown)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str907), static_cast<int>(CSSValueID::kSelfBlockEnd)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str908), static_cast<int>(CSSValueID::kPeru)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str909), static_cast<int>(CSSValueID::kWebkitIsolate)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str910), static_cast<int>(CSSValueID::kSelfBlockStart)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str911), static_cast<int>(CSSValueID::kLightslategrey)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str912), static_cast<int>(CSSValueID::kLightslategray)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str913), static_cast<int>(CSSValueID::kInlineBlock)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str914), static_cast<int>(CSSValueID::kWebkitControl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str915), static_cast<int>(CSSValueID::kMostHeight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str916), static_cast<int>(CSSValueID::kCornflowerblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str917), static_cast<int>(CSSValueID::kWebkitCalc)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str918), static_cast<int>(CSSValueID::kPow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str919), static_cast<int>(CSSValueID::kWebkitMinContent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str920), static_cast<int>(CSSValueID::kMaximized)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str921), static_cast<int>(CSSValueID::kDropShadow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str922), static_cast<int>(CSSValueID::kInlineSize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str923), static_cast<int>(CSSValueID::kAnywhere)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str924), static_cast<int>(CSSValueID::kWebkitAuto)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str925), static_cast<int>(CSSValueID::kTableHeaderGroup)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str926), static_cast<int>(CSSValueID::kNoOpenQuote)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str927), static_cast<int>(CSSValueID::kCanvastext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str928), static_cast<int>(CSSValueID::kSpaceBetween)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str929), static_cast<int>(CSSValueID::kVisible)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str930), static_cast<int>(CSSValueID::kAnchorSize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str931), static_cast<int>(CSSValueID::kWebkitMiniControl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str932), static_cast<int>(CSSValueID::kIcWidth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str933), static_cast<int>(CSSValueID::kPanDown)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str934), static_cast<int>(CSSValueID::kPushButton)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str935), static_cast<int>(CSSValueID::kEmbeddedOpentype)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str936), static_cast<int>(CSSValueID::kBelow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str937), static_cast<int>(CSSValueID::kInlineAxis)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str938), static_cast<int>(CSSValueID::kWoff)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str939), static_cast<int>(CSSValueID::kWoff2)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str940), static_cast<int>(CSSValueID::kInternalVariableValue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str941), static_cast<int>(CSSValueID::kSearchfieldCancelButton)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str942), static_cast<int>(CSSValueID::kLightyellow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str943), static_cast<int>(CSSValueID::kAlways)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str944), static_cast<int>(CSSValueID::kWebkitIsolateOverride)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str945), static_cast<int>(CSSValueID::kPreWrap)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str946), static_cast<int>(CSSValueID::kEwResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str947), static_cast<int>(CSSValueID::kPlaintext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str948), static_cast<int>(CSSValueID::kSwash)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str949), static_cast<int>(CSSValueID::kWebkitGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str950), static_cast<int>(CSSValueID::kMediumorchid)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str951), static_cast<int>(CSSValueID::kExitCrossing)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str952), static_cast<int>(CSSValueID::kMenutext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str953), static_cast<int>(CSSValueID::kNwseResize)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str954), static_cast<int>(CSSValueID::kChWidth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str955), static_cast<int>(CSSValueID::kVisiblepainted)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str956), static_cast<int>(CSSValueID::kNoOverflow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str957), static_cast<int>(CSSValueID::kWebkitLink)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str958), static_cast<int>(CSSValueID::kFlexFlow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str959), static_cast<int>(CSSValueID::kTextBeforeEdge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str960), static_cast<int>(CSSValueID::kWebkitImageSet)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str961), static_cast<int>(CSSValueID::kPlusLighter)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str962), static_cast<int>(CSSValueID::kInternalKoreanHanjaFormal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str963), static_cast<int>(CSSValueID::kInternalKoreanHanjaInformal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str964), static_cast<int>(CSSValueID::kMostWidth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str965), static_cast<int>(CSSValueID::kCubicBezier)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str966), static_cast<int>(CSSValueID::kCharacterVariant)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str967), static_cast<int>(CSSValueID::kSandybrown)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str968), static_cast<int>(CSSValueID::kColorSbix)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str969), static_cast<int>(CSSValueID::kColorCOLRv1)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str970), static_cast<int>(CSSValueID::kColorCOLRv0)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str971), static_cast<int>(CSSValueID::kPinchZoom)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str972), static_cast<int>(CSSValueID::kInternalExtendToZoom)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str973), static_cast<int>(CSSValueID::kFullWidth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str974), static_cast<int>(CSSValueID::kGreenyellow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str975), static_cast<int>(CSSValueID::kWhitesmoke)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str976), static_cast<int>(CSSValueID::kWebkitFitContent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str977), static_cast<int>(CSSValueID::kWebkitBaselineMiddle)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str978), static_cast<int>(CSSValueID::kVisiblefill)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str979), static_cast<int>(CSSValueID::kWebkitMaxContent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str980), static_cast<int>(CSSValueID::kPeachpuff)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str981), static_cast<int>(CSSValueID::kLemonchiffon)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str982), static_cast<int>(CSSValueID::kWebkitCrossFade)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str983), static_cast<int>(CSSValueID::kIdeographic)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str984), static_cast<int>(CSSValueID::kFloralwhite)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str985), static_cast<int>(CSSValueID::kGridRows)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str986), static_cast<int>(CSSValueID::kPaleturquoise)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str987), static_cast<int>(CSSValueID::kXxxLarge)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str988), static_cast<int>(CSSValueID::kWebkitActivelink)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str989), static_cast<int>(CSSValueID::kWindow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str990), static_cast<int>(CSSValueID::kVisual)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str991), static_cast<int>(CSSValueID::kWebkitGrab)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str992), static_cast<int>(CSSValueID::kWebkitSmallControl)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str993), static_cast<int>(CSSValueID::kExtraExpanded)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str994), static_cast<int>(CSSValueID::kSliderHorizontal)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str995), static_cast<int>(CSSValueID::kButtonshadow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str996), static_cast<int>(CSSValueID::kLightgoldenrodyellow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str997), static_cast<int>(CSSValueID::kWebkitBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str998), static_cast<int>(CSSValueID::kWebkitBody)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str999), static_cast<int>(CSSValueID::kInactivecaptiontext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1000), static_cast<int>(CSSValueID::kWebkitFocusRingColor)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1001), static_cast<int>(CSSValueID::kAutoPhrase)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1002), static_cast<int>(CSSValueID::kWebkitZoomIn)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1003), static_cast<int>(CSSValueID::kInternalActiveListBoxSelection)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1004), static_cast<int>(CSSValueID::kStrokeBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1005), static_cast<int>(CSSValueID::kVisiblestroke)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1006), static_cast<int>(CSSValueID::kXywh)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1007), static_cast<int>(CSSValueID::kCheckbox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1008), static_cast<int>(CSSValueID::kInfotext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1009), static_cast<int>(CSSValueID::kWebkitFillAvailable)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1010), static_cast<int>(CSSValueID::kAllowDiscrete)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1011), static_cast<int>(CSSValueID::kWebkitZoomOut)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1012), static_cast<int>(CSSValueID::kVerticalText)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1013), static_cast<int>(CSSValueID::kLineThrough)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1014), static_cast<int>(CSSValueID::kWebkitRepeatingLinearGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1015), static_cast<int>(CSSValueID::kVisitedtext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1016), static_cast<int>(CSSValueID::kPowderblue)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1017), static_cast<int>(CSSValueID::kInfobackground)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1018), static_cast<int>(CSSValueID::kHighlighttext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1019), static_cast<int>(CSSValueID::kWebkitRepeatingRadialGradient)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1020), static_cast<int>(CSSValueID::kExHeight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1021), static_cast<int>(CSSValueID::kAutoFlow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1022), static_cast<int>(CSSValueID::kWebkitOptimizeContrast)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1023), static_cast<int>(CSSValueID::kInternalInactiveListBoxSelection)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1024), static_cast<int>(CSSValueID::kButtonhighlight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1025), static_cast<int>(CSSValueID::kWebkitGrabbing)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1026), static_cast<int>(CSSValueID::kOptimizequality)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1027), static_cast<int>(CSSValueID::kOptimizelegibility)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1028), static_cast<int>(CSSValueID::kProphotoRgb)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1029), static_cast<int>(CSSValueID::kTableRowGroup)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1030), static_cast<int>(CSSValueID::kVerticalRight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1031), static_cast<int>(CSSValueID::kWebkitMatchParent)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1032), static_cast<int>(CSSValueID::kViewBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1033), static_cast<int>(CSSValueID::kWindowframe)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1034), static_cast<int>(CSSValueID::kWebkitInlineFlex)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1035), static_cast<int>(CSSValueID::kNavajowhite)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1036), static_cast<int>(CSSValueID::kWebkitInlineBox)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1037), static_cast<int>(CSSValueID::kSidewaysRight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1038), static_cast<int>(CSSValueID::kThreedshadow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1039), static_cast<int>(CSSValueID::kWebkitPlaintext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1040), static_cast<int>(CSSValueID::kReadWritePlaintextOnly)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1041), static_cast<int>(CSSValueID::kWebkitFlex)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1042), static_cast<int>(CSSValueID::kAfterWhiteSpace)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1043), static_cast<int>(CSSValueID::kProportionalWidth)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1044), static_cast<int>(CSSValueID::kWindowControlsOverlay)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1045), static_cast<int>(CSSValueID::kWebkitRight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1046), static_cast<int>(CSSValueID::kWindowtext)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1047), static_cast<int>(CSSValueID::kThreeddarkshadow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1048), static_cast<int>(CSSValueID::kPapayawhip)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1049), static_cast<int>(CSSValueID::kThreedhighlight)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1050), static_cast<int>(CSSValueID::kInternalActiveListBoxSelectionText)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1051), static_cast<int>(CSSValueID::kAntiquewhite)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1052), static_cast<int>(CSSValueID::kInternalInactiveListBoxSelectionText)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1053), static_cast<int>(CSSValueID::kGhostwhite)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1054), static_cast<int>(CSSValueID::kThreedlightshadow)},
      {offsetof(struct CSSValueStringPool_t, CSSValueStringPool_str1055), static_cast<int>(CSSValueID::kWebkitXxxLarge)}
    };

  static const short lookup[] =
    {
        -1,   -1,   -1,    0,    1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,    2,   -1,    3,   -1,
         4,   -1,    5,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,    6,   -1,   -1,
         7,   -1,    8,    9,   10,   -1,   -1,   11,
        -1,   12,   13,   14,   -1,   15,   -1,   16,
        -1,   -1,   -1,   17,   -1,   18,   19,   -1,
        -1,   20,   -1,   21,   22,   -1,   -1,   -1,
        -1,   23,   -1,   -1,   24,   -1,   -1,   25,
        -1,   -1,   26,   -1,   -1,   27,   28,   29,
        30,   31,   -1,   -1,   -1,   -1,   -1,   32,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        33,   -1,   34,   35,   36,   -1,   37,   38,
        -1,   -1,   -1,   -1,   -1,   39,   40,   -1,
        41,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        42,   43,   -1,   44,   -1,   45,   46,   47,
        -1,   48,   49,   -1,   50,   -1,   -1,   -1,
        -1,   51,   52,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   53,   54,   55,   -1,   -1,   -1,
        56,   -1,   -1,   -1,   57,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   58,   -1,   -1,   59,
        60,   -1,   61,   62,   63,   -1,   64,   -1,
        -1,   65,   66,   -1,   -1,   67,   -1,   68,
        -1,   -1,   -1,   69,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   70,   -1,   71,   72,
        -1,   -1,   -1,   -1,   -1,   73,   74,   -1,
        -1,   -1,   75,   -1,   -1,   -1,   -1,   -1,
        -1,   76,   77,   78,   -1,   -1,   79,   -1,
        -1,   -1,   -1,   80,   -1,   81,   82,   83,
        -1,   84,   -1,   85,   -1,   -1,   -1,   -1,
        86,   87,   -1,   88,   89,   -1,   -1,   90,
        91,   92,   93,   -1,   -1,   94,   -1,   -1,
        -1,   -1,   -1,   -1,   95,   96,   97,   98,
        -1,   99,   -1,  100,  101,  102,  103,   -1,
        -1,   -1,  104,  105,  106,   -1,   -1,  107,
        -1,  108,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  109,
        -1,  110,  111,  112,  113,   -1,   -1,  114,
        -1,   -1,   -1,   -1,   -1,  115,   -1,   -1,
        -1,  116,   -1,   -1,  117,   -1,  118,   -1,
        -1,   -1,   -1,   -1,  119,   -1,   -1,  120,
        -1,   -1,   -1,  121,   -1,  122,   -1,   -1,
        -1,  123,   -1,  124,   -1,   -1,  125,   -1,
       126,   -1,   -1,  127,   -1,  128,   -1,   -1,
        -1,   -1,  129,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  130,   -1,
       131,   -1,  132,   -1,   -1,   -1,   -1,   -1,
       133,   -1,  134,  135,   -1,   -1,   -1,   -1,
        -1,  136,  137,  138,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  139,   -1,  140,   -1,   -1,  141,   -1,
        -1,   -1,   -1,   -1,   -1,  142,  143,  144,
        -1,  145,   -1,  146,   -1,   -1,  147,  148,
        -1,   -1,  149,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  150,   -1,  151,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  152,
       153,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  154,   -1,   -1,   -1,  155,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  156,
        -1,   -1,   -1,  157,   -1,  158,  159,   -1,
        -1,   -1,   -1,   -1,  160,   -1,   -1,  161,
        -1,   -1,   -1,   -1,   -1,   -1,  162,  163,
       164,   -1,   -1,   -1,   -1,   -1,  165,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  166,  167,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  168,
       169,   -1,   -1,  170,   -1,  171,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  172,  173,
        -1,   -1,   -1,   -1,  174,   -1,   -1,  175,
        -1,   -1,   -1,  176,   -1,  177,   -1,   -1,
       178,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  179,
        -1,   -1,   -1,  180,   -1,   -1,   -1,   -1,
        -1,   -1,  181,   -1,   -1,   -1,   -1,   -1,
        -1,  182,   -1,   -1,   -1,   -1,   -1,   -1,
       183,  184,  185,  186,   -1,   -1,  187,   -1,
        -1,   -1,   -1,  188,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       189,  190,   -1,   -1,  191,   -1,  192,   -1,
        -1,  193,  194,   -1,   -1,   -1,  195,   -1,
       196,   -1,  197,  198,   -1,   -1,  199,   -1,
        -1,  200,  201,   -1,   -1,   -1,   -1,   -1,
       202,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       203,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       204,   -1,   -1,   -1,   -1,   -1,  205,  206,
        -1,  207,   -1,  208,   -1,   -1,  209,   -1,
       210,   -1,   -1,  211,  212,   -1,   -1,   -1,
        -1,  213,  214,   -1,   -1,   -1,   -1,   -1,
       215,   -1,  216,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  217,  218,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  219,   -1,   -1,   -1,  220,   -1,   -1,
        -1,  221,   -1,  222,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  223,   -1,
        -1,   -1,   -1,   -1,   -1,  224,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  225,  226,   -1,
        -1,  227,   -1,   -1,   -1,   -1,   -1,   -1,
       228,   -1,  229,   -1,   -1,   -1,   -1,   -1,
        -1,  230,   -1,   -1,  231,  232,   -1,   -1,
        -1,  233,  234,   -1,   -1,  235,   -1,  236,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  237,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  238,
        -1,   -1,  239,  240,  241,  242,  243,  244,
       245,  246,  247,   -1,  248,   -1,   -1,   -1,
        -1,  249,   -1,   -1,   -1,   -1,  250,   -1,
       251,  252,   -1,   -1,  253,   -1,   -1,   -1,
       254,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  255,  256,   -1,  257,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  258,   -1,   -1,   -1,
        -1,  259,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  260,  261,   -1,   -1,  262,  263,
        -1,   -1,   -1,  264,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  265,  266,  267,  268,
       269,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  270,   -1,   -1,  271,  272,   -1,
       273,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       274,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  275,  276,
       277,   -1,   -1,   -1,  278,   -1,  279,  280,
        -1,   -1,   -1,  281,   -1,  282,   -1,   -1,
       283,  284,  285,   -1,   -1,  286,   -1,  287,
       288,  289,  290,  291,   -1,  292,   -1,  293,
        -1,   -1,  294,   -1,  295,   -1,   -1,  296,
        -1,  297,   -1,   -1,   -1,  298,   -1,   -1,
        -1,  299,   -1,  300,   -1,   -1,   -1,  301,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       302,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  303,   -1,   -1,   -1,   -1,
        -1,   -1,  304,   -1,   -1,   -1,   -1,   -1,
       305,   -1,  306,   -1,   -1,   -1,  307,   -1,
        -1,   -1,  308,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  309,
        -1,   -1,   -1,   -1,   -1,   -1,  310,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  311,
        -1,   -1,  312,   -1,   -1,  313,   -1,   -1,
        -1,   -1,   -1,  314,   -1,   -1,  315,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       316,   -1,   -1,   -1,  317,   -1,   -1,   -1,
       318,   -1,   -1,  319,   -1,  320,   -1,   -1,
        -1,  321,   -1,   -1,   -1,   -1,   -1,  322,
        -1,   -1,  323,   -1,   -1,   -1,   -1,   -1,
        -1,  324,   -1,   -1,  325,   -1,  326,  327,
        -1,  328,   -1,   -1,   -1,   -1,  329,   -1,
        -1,   -1,  330,   -1,  331,   -1,   -1,  332,
       333,  334,   -1,   -1,   -1,  335,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  336,  337,
        -1,   -1,   -1,   -1,  338,   -1,   -1,   -1,
        -1,   -1,   -1,  339,   -1,   -1,   -1,  340,
        -1,   -1,  341,  342,  343,   -1,  344,   -1,
        -1,   -1,   -1,  345,   -1,   -1,  346,   -1,
       347,   -1,  348,  349,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  350,  351,   -1,   -1,
       352,   -1,   -1,  353,  354,  355,   -1,   -1,
        -1,  356,   -1,   -1,   -1,  357,   -1,  358,
        -1,   -1,  359,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  360,   -1,   -1,  361,
        -1,   -1,   -1,   -1,   -1,  362,   -1,   -1,
        -1,   -1,  363,   -1,  364,  365,   -1,   -1,
        -1,   -1,  366,   -1,  367,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  368,   -1,   -1,  369,
        -1,   -1,   -1,   -1,   -1,  370,  371,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  372,  373,   -1,
       374,   -1,  375,  376,  377,   -1,   -1,   -1,
       378,   -1,   -1,   -1,   -1,   -1,  379,   -1,
        -1,   -1,   -1,   -1,  380,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       381,   -1,   -1,   -1,   -1,   -1,   -1,  382,
        -1,  383,   -1,   -1,   -1,   -1,   -1,  384,
       385,   -1,   -1,   -1,   -1,   -1,  386,   -1,
        -1,   -1,   -1,   -1,  387,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       388,   -1,   -1,   -1,  389,  390,   -1,   -1,
       391,  392,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  393,  394,   -1,   -1,  395,   -1,
        -1,   -1,  396,  397,   -1,   -1,   -1,  398,
        -1,  399,   -1,   -1,   -1,   -1,  400,   -1,
       401,  402,   -1,   -1,  403,   -1,   -1,   -1,
        -1,   -1,  404,   -1,  405,  406,   -1,   -1,
       407,   -1,   -1,   -1,  408,  409,   -1,   -1,
        -1,   -1,   -1,  410,   -1,  411,   -1,  412,
        -1,   -1,   -1,   -1,  413,   -1,   -1,  414,
        -1,  415,   -1,   -1,  416,   -1,   -1,   -1,
        -1,   -1,  417,   -1,  418,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  419,  420,
        -1,   -1,   -1,  421,   -1,  422,   -1,   -1,
        -1,   -1,   -1,  423,   -1,  424,   -1,   -1,
        -1,  425,   -1,   -1,  426,   -1,   -1,   -1,
        -1,   -1,   -1,  427,   -1,  428,   -1,   -1,
        -1,   -1,   -1,  429,  430,  431,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  432,  433,   -1,
        -1,  434,   -1,  435,   -1,   -1,  436,   -1,
       437,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  438,   -1,   -1,  439,
        -1,   -1,   -1,   -1,  440,   -1,   -1,  441,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       442,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  443,   -1,
       444,   -1,  445,   -1,   -1,  446,   -1,   -1,
        -1,   -1,   -1,   -1,  447,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  448,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  449,   -1,
        -1,   -1,   -1,   -1,  450,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  451,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  452,  453,   -1,   -1,   -1,   -1,  454,
       455,   -1,  456,  457,   -1,   -1,  458,   -1,
       459,   -1,   -1,   -1,  460,   -1,   -1,   -1,
        -1,   -1,  461,  462,   -1,   -1,  463,  464,
        -1,   -1,   -1,   -1,   -1,  465,   -1,   -1,
        -1,   -1,  466,   -1,  467,   -1,  468,  469,
        -1,   -1,   -1,  470,   -1,   -1,  471,   -1,
        -1,   -1,   -1,  472,   -1,   -1,   -1,   -1,
        -1,  473,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       474,   -1,   -1,   -1,   -1,  475,  476,  477,
        -1,   -1,  478,   -1,   -1,  479,   -1,   -1,
        -1,  480,   -1,   -1,  481,   -1,   -1,   -1,
       482,   -1,   -1,   -1,   -1,   -1,  483,   -1,
        -1,   -1,   -1,  484,   -1,   -1,   -1,  485,
        -1,  486,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  487,   -1,   -1,   -1,   -1,  488,   -1,
        -1,  489,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  490,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  491,   -1,  492,   -1,   -1,
       493,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  494,   -1,  495,  496,   -1,
        -1,   -1,   -1,   -1,  497,  498,   -1,   -1,
        -1,   -1,   -1,   -1,  499,   -1,   -1,   -1,
        -1,  500,   -1,   -1,   -1,   -1,   -1,  501,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       502,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  503,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  504,   -1,   -1,  505,   -1,   -1,   -1,
       506,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  507,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  508,   -1,  509,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  510,
        -1,   -1,   -1,  511,   -1,   -1,  512,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       513,  514,   -1,   -1,  515,   -1,   -1,   -1,
        -1,   -1,  516,   -1,  517,   -1,   -1,  518,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  519,   -1,   -1,   -1,
        -1,   -1,   -1,  520,   -1,   -1,   -1,   -1,
        -1,  521,   -1,   -1,   -1,   -1,  522,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  523,
        -1,  524,  525,   -1,   -1,   -1,   -1,  526,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  527,
       528,   -1,   -1,  529,   -1,   -1,  530,   -1,
       531,   -1,   -1,   -1,  532,   -1,  533,  534,
        -1,   -1,   -1,   -1,  535,  536,   -1,   -1,
        -1,  537,   -1,   -1,   -1,   -1,  538,   -1,
       539,   -1,   -1,   -1,   -1,  540,   -1,   -1,
        -1,  541,   -1,   -1,   -1,  542,   -1,  543,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  544,
       545,   -1,   -1,   -1,  546,   -1,   -1,  547,
        -1,  548,   -1,   -1,   -1,   -1,   -1,   -1,
       549,   -1,  550,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  551,   -1,   -1,  552,   -1,  553,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  554,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  555,   -1,  556,  557,   -1,   -1,   -1,
       558,   -1,  559,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       560,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       561,   -1,   -1,   -1,   -1,   -1,  562,   -1,
        -1,   -1,   -1,   -1,   -1,  563,   -1,   -1,
        -1,   -1,  564,   -1,  565,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  566,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  567,   -1,   -1,  568,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  569,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  570,
        -1,  571,   -1,   -1,   -1,   -1,  572,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  573,  574,  575,   -1,   -1,   -1,
        -1,   -1,  576,  577,   -1,   -1,   -1,  578,
        -1,  579,  580,  581,   -1,   -1,   -1,  582,
       583,   -1,   -1,   -1,  584,  585,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  586,   -1,   -1,
        -1,   -1,   -1,  587,   -1,   -1,  588,   -1,
        -1,   -1,   -1,   -1,  589,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       590,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  591,   -1,   -1,   -1,   -1,  592,
        -1,   -1,  593,   -1,   -1,   -1,  594,   -1,
        -1,   -1,   -1,   -1,  595,  596,   -1,  597,
        -1,   -1,   -1,   -1,  598,   -1,   -1,   -1,
       599,   -1,   -1,   -1,   -1,  600,  601,   -1,
        -1,   -1,   -1,   -1,  602,   -1,   -1,   -1,
       603,  604,  605,  606,   -1,  607,   -1,   -1,
        -1,   -1,   -1,  608,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  609,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  610,  611,   -1,   -1,   -1,   -1,
       612,   -1,   -1,   -1,   -1,  613,   -1,  614,
        -1,   -1,   -1,   -1,   -1,   -1,  615,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  616,   -1,
        -1,  617,  618,   -1,   -1,  619,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  620,   -1,   -1,   -1,  621,
        -1,   -1,  622,   -1,   -1,  623,   -1,   -1,
       624,   -1,   -1,   -1,   -1,   -1,  625,   -1,
        -1,   -1,   -1,   -1,   -1,  626,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  627,  628,  629,   -1,   -1,
        -1,   -1,  630,  631,  632,   -1,   -1,  633,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  634,   -1,   -1,   -1,   -1,  635,   -1,
        -1,  636,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       637,   -1,   -1,   -1,   -1,   -1,  638,   -1,
        -1,   -1,   -1,  639,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  640,   -1,   -1,  641,   -1,   -1,   -1,
       642,   -1,  643,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  644,   -1,
        -1,   -1,   -1,   -1,  645,  646,   -1,   -1,
        -1,   -1,   -1,   -1,  647,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  648,   -1,   -1,   -1,   -1,   -1,   -1,
       649,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  650,  651,   -1,  652,   -1,   -1,   -1,
        -1,   -1,   -1,  653,   -1,   -1,  654,   -1,
        -1,   -1,  655,  656,   -1,   -1,   -1,  657,
        -1,   -1,   -1,  658,   -1,   -1,  659,   -1,
       660,   -1,  661,   -1,  662,   -1,   -1,   -1,
        -1,  663,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       664,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       665,   -1,  666,   -1,   -1,  667,  668,   -1,
        -1,  669,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  670,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  671,   -1,   -1,   -1,   -1,  672,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       673,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  674,   -1,   -1,   -1,
        -1,   -1,  675,  676,   -1,   -1,  677,   -1,
        -1,   -1,  678,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  679,   -1,  680,  681,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  682,  683,
        -1,   -1,  684,  685,   -1,   -1,   -1,   -1,
       686,   -1,  687,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       688,   -1,   -1,  689,   -1,   -1,   -1,   -1,
        -1,   -1,  690,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  691,  692,   -1,   -1,   -1,  693,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       694,   -1,   -1,   -1,  695,   -1,   -1,   -1,
        -1,   -1,   -1,  696,   -1,   -1,   -1,  697,
        -1,   -1,   -1,   -1,   -1,  698,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  699,   -1,   -1,
        -1,   -1,  700,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  701,   -1,  702,  703,   -1,   -1,
        -1,   -1,  704,   -1,   -1,   -1,  705,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       706,   -1,   -1,  707,  708,   -1,  709,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  710,   -1,
        -1,   -1,  711,   -1,  712,   -1,  713,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  714,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  715,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  716,  717,
        -1,   -1,  718,   -1,   -1,   -1,  719,   -1,
        -1,   -1,   -1,   -1,  720,  721,  722,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  723,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  724,
       725,   -1,   -1,   -1,  726,   -1,   -1,   -1,
       727,   -1,   -1,   -1,   -1,  728,   -1,   -1,
        -1,   -1,  729,  730,   -1,   -1,  731,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  732,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  733,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  734,   -1,  735,
       736,  737,   -1,   -1,   -1,   -1,  738,  739,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       740,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  741,   -1,  742,   -1,   -1,
        -1,  743,   -1,   -1,   -1,  744,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  745,  746,   -1,   -1,
        -1,   -1,  747,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  748,
        -1,  749,   -1,   -1,   -1,  750,   -1,   -1,
        -1,   -1,   -1,  751,  752,   -1,   -1,   -1,
       753,  754,   -1,  755,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  756,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  757,   -1,   -1,
        -1,   -1,  758,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  759,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  760,   -1,  761,  762,   -1,   -1,   -1,
       763,   -1,   -1,   -1,   -1,   -1,   -1,  764,
       765,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  766,
       767,   -1,  768,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  769,
        -1,   -1,   -1,  770,   -1,  771,   -1,   -1,
       772,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       773,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  774,
        -1,   -1,   -1,   -1,   -1,   -1,  775,   -1,
       776,  777,   -1,   -1,   -1,  778,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  779,  780,  781,   -1,  782,   -1,  783,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  784,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  785,   -1,   -1,   -1,
        -1,   -1,  786,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  787,   -1,   -1,
        -1,   -1,   -1,  788,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  789,   -1,   -1,  790,
       791,   -1,   -1,  792,   -1,   -1,   -1,   -1,
        -1,  793,   -1,   -1,   -1,   -1,   -1,   -1,
       794,  795,   -1,  796,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  797,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  798,   -1,   -1,
        -1,  799,   -1,   -1,   -1,   -1,  800,   -1,
        -1,   -1,   -1,   -1,  801,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  802,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       803,   -1,   -1,   -1,  804,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  805,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  806,
        -1,   -1,  807,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  808,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  809,   -1,  810,   -1,   -1,
        -1,  811,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  812,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  813,   -1,   -1,   -1,  814,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  815,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  816,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  817,   -1,
        -1,   -1,   -1,   -1,   -1,  818,   -1,   -1,
        -1,  819,   -1,  820,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  821,   -1,
       822,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  823,   -1,   -1,
        -1,   -1,  824,   -1,  825,  826,   -1,  827,
        -1,   -1,   -1,  828,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  829,  830,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  831,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  832,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  833,   -1,   -1,   -1,   -1,
        -1,  834,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  835,   -1,
        -1,   -1,  836,   -1,   -1,   -1,  837,   -1,
       838,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  839,   -1,   -1,
        -1,  840,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  841,   -1,   -1,   -1,   -1,  842,
       843,   -1,  844,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  845,   -1,   -1,   -1,   -1,
       846,   -1,  847,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  848,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  849,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  850,  851,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  852,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  853,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  854,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       855,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  856,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  857,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  858,   -1,   -1,
        -1,  859,   -1,   -1,   -1,  860,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       861,   -1,   -1,   -1,   -1,   -1,  862,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  863,   -1,
        -1,   -1,  864,  865,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       866,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  867,   -1,  868,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  869,   -1,   -1,
        -1,   -1,   -1,  870,   -1,  871,  872,   -1,
        -1,  873,   -1,   -1,   -1,   -1,   -1,  874,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  875,   -1,   -1,   -1,   -1,   -1,
       876,   -1,   -1,   -1,   -1,  877,  878,   -1,
        -1,  879,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       880,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  881,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       882,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  883,   -1,   -1,   -1,
        -1,   -1,  884,  885,   -1,   -1,   -1,   -1,
       886,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  887,   -1,
        -1,   -1,   -1,   -1,   -1,  888,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  889,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  890,   -1,
        -1,  891,   -1,  892,   -1,   -1,   -1,   -1,
       893,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       894,  895,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       896,   -1,   -1,   -1,   -1,   -1,  897,   -1,
        -1,   -1,  898,   -1,   -1,  899,   -1,   -1,
       900,  901,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  902,   -1,   -1,  903,   -1,   -1,  904,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  905,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  906,  907,   -1,   -1,  908,  909,   -1,
       910,   -1,   -1,   -1,  911,  912,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  913,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  914,   -1,   -1,
        -1,   -1,   -1,  915,   -1,  916,  917,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  918,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  919,   -1,   -1,   -1,   -1,
        -1,  920,   -1,   -1,   -1,   -1,   -1,  921,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  922,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  923,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  924,   -1,  925,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  926,
        -1,   -1,   -1,  927,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  928,   -1,   -1,   -1,
       929,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       930,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  931,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       932,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       933,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  934,   -1,   -1,   -1,   -1,   -1,  935,
        -1,  936,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  937,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       938,   -1,   -1,   -1,   -1,  939,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       940,   -1,   -1,   -1,   -1,  941,   -1,  942,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  943,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  944,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  945,   -1,   -1,   -1,
        -1,  946,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  947,   -1,  948,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  949,   -1,
        -1,   -1,   -1,   -1,   -1,  950,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  951,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       952,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  953,   -1,   -1,   -1,
        -1,   -1,  954,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  955,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  956,
        -1,  957,   -1,  958,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  959,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  960,   -1,
        -1,  961,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  962,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  963,   -1,   -1,   -1,   -1,   -1,
       964,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  965,   -1,   -1,   -1,
        -1,  966,   -1,   -1,   -1,   -1,   -1,  967,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  968,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  969,   -1,
        -1,   -1,  970,   -1,   -1,   -1,   -1,   -1,
       971,   -1,   -1,   -1,   -1,   -1,   -1,  972,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  973,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  974,   -1,  975,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  976,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  977,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  978,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  979,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  980,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  981,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,  982,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  983,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  984,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  985,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,  986,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       987,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,  988,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  989,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,  990,
        -1,   -1,   -1,   -1,  991,   -1,   -1,   -1,
        -1,  992,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  993,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       994,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,  995,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,  996,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,  997,   -1,   -1,
       998,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
       999,   -1,   -1,   -1,   -1, 1000,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1001,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1002,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1003,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1, 1004,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1, 1005,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1, 1006,
        -1, 1007,   -1, 1008,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1, 1009,   -1,   -1,   -1,   -1,   -1,   -1,
        -1, 1010,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1011,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1012,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1, 1013,   -1,   -1,   -1,   -1,
      1014,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1, 1015,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1016,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1017,   -1,   -1,   -1,
        -1, 1018,   -1,   -1,   -1,   -1, 1019,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1, 1020,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1, 1021,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1022,   -1,
        -1,   -1,   -1,   -1,   -1, 1023,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1024,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1025,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1, 1026,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1, 1027,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1028,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1, 1029,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1030,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1, 1031,
        -1,   -1,   -1,   -1,   -1,   -1, 1032,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1, 1033,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1034,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1, 1035,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1036,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1, 1037,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1, 1038,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1039,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1, 1040,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1, 1041,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1, 1042,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1043,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
      1044,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1, 1045,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1, 1046,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1, 1047,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1048,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1, 1049,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1, 1050,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1, 1051,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1, 1052,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1, 1053,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1054,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1,   -1,   -1,   -1,   -1,
        -1,   -1,   -1,   -1, 1055
    };

  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      unsigned int key = value_hash_function (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          int index = lookup[key];

          if (index >= 0)
            {
              const char *s = value_word_list[index].name_offset + CSSValueStringPool;

              if (*str == *s && !strncmp (str + 1, s + 1, len - 1) && s[len] == '\0')
                return &value_word_list[index];
            }
        }
    }
  return 0;
}


const Value* FindValue(const char* str, unsigned int len) {
  return CSSValueKeywordsHash::findValueImpl(str, len);
}

const char* getValueName(CSSValueID id) {
  DCHECK_GT(id, CSSValueID::kInvalid);
  DCHECK_LT(static_cast<int>(id), numCSSValueKeywords);
  return valueListStringPool + valueListStringOffsets[static_cast<int>(id) - 1];
}

bool isValueAllowedInMode(CSSValueID id, CSSParserMode mode) {
  switch (id) {
    case CSSValueID::kInternalActiveListBoxSelection:
    case CSSValueID::kInternalActiveListBoxSelectionText:
    case CSSValueID::kInternalInactiveListBoxSelection:
    case CSSValueID::kInternalInactiveListBoxSelectionText:
    case CSSValueID::kInternalQuirkInherit:
    case CSSValueID::kInternalSpellingErrorColor:
    case CSSValueID::kInternalGrammarErrorColor:
    case CSSValueID::kInternalCenter:
    case CSSValueID::kInternalMediaControl:
    case CSSValueID::kInternalAppearanceAutoBaseSelect:
    case CSSValueID::kInternalExtendToZoom:
    case CSSValueID::kInternalVariableValue:
    case CSSValueID::kInternalSimpChineseInformal:
    case CSSValueID::kInternalSimpChineseFormal:
    case CSSValueID::kInternalTradChineseInformal:
    case CSSValueID::kInternalTradChineseFormal:
    case CSSValueID::kInternalKoreanHangulFormal:
    case CSSValueID::kInternalKoreanHanjaInformal:
    case CSSValueID::kInternalKoreanHanjaFormal:
    case CSSValueID::kInternalHebrew:
    case CSSValueID::kInternalLowerArmenian:
    case CSSValueID::kInternalUpperArmenian:
    case CSSValueID::kInternalEthiopicNumeric:
    case CSSValueID::kInternalTextareaAuto:
      return IsUASheetBehavior(mode);
    case CSSValueID::kWebkitFocusRingColor:
      return IsUASheetBehavior(mode) || IsQuirksModeBehavior(mode);
    default:
      return true;
  }
}

} // namespace blink
