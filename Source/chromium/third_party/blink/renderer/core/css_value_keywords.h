// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#ifndef THIRD_PARTY_BLINK_RENDERER_CORE_CSS_VALUE_KEYWORDS_H_
#define THIRD_PARTY_BLINK_RENDERER_CORE_CSS_VALUE_KEYWORDS_H_

#include <string.h>
#include <stdint.h>

#include "third_party/blink/renderer/core/core_export.h"
#include "third_party/blink/renderer/core/css/parser/css_parser_mode.h"

namespace blink {

enum class CSSValueID {
  kInvalid = 0,
  kInherit = 1,
  kInitial = 2,
  kNone = 3,
  kHidden = 4,
  kInset = 5,
  kGroove = 6,
  kOutset = 7,
  kRidge = 8,
  kDotted = 9,
  kDashed = 10,
  kSolid = 11,
  kDouble = 12,
  kCaption = 13,
  kIcon = 14,
  kMenu = 15,
  kMessageBox = 16,
  kSmallCaption = 17,
  kWebkitMiniControl = 18,
  kWebkitSmallControl = 19,
  kWebkitControl = 20,
  kStatusBar = 21,
  kItalic = 22,
  kOblique = 23,
  kAll = 24,
  kCommonLigatures = 25,
  kNoCommonLigatures = 26,
  kDiscretionaryLigatures = 27,
  kNoDiscretionaryLigatures = 28,
  kHistoricalLigatures = 29,
  kNoHistoricalLigatures = 30,
  kContextual = 31,
  kNoContextual = 32,
  kSmallCaps = 33,
  kAllSmallCaps = 34,
  kPetiteCaps = 35,
  kAllPetiteCaps = 36,
  kUnicase = 37,
  kTitlingCaps = 38,
  kJis78 = 39,
  kJis83 = 40,
  kJis90 = 41,
  kJis04 = 42,
  kSimplified = 43,
  kTraditional = 44,
  kFullWidth = 45,
  kProportionalWidth = 46,
  kRuby = 47,
  kLiningNums = 48,
  kOldstyleNums = 49,
  kProportionalNums = 50,
  kTabularNums = 51,
  kDiagonalFractions = 52,
  kStackedFractions = 53,
  kOrdinal = 54,
  kSlashedZero = 55,
  kStylistic = 56,
  kHistoricalForms = 57,
  kStyleset = 58,
  kCharacterVariant = 59,
  kSwash = 60,
  kOrnaments = 61,
  kAnnotation = 62,
  kNormal = 63,
  kBold = 64,
  kBolder = 65,
  kLighter = 66,
  kWeight = 67,
  kUltraCondensed = 68,
  kExtraCondensed = 69,
  kCondensed = 70,
  kSemiCondensed = 71,
  kSemiExpanded = 72,
  kExpanded = 73,
  kExtraExpanded = 74,
  kUltraExpanded = 75,
  kXxSmall = 76,
  kXSmall = 77,
  kSmall = 78,
  kMedium = 79,
  kLarge = 80,
  kXLarge = 81,
  kXxLarge = 82,
  kXxxLarge = 83,
  kSmaller = 84,
  kLarger = 85,
  kWebkitXxxLarge = 86,
  kExHeight = 87,
  kCapHeight = 88,
  kChWidth = 89,
  kIcWidth = 90,
  kSerif = 91,
  kSansSerif = 92,
  kCursive = 93,
  kFantasy = 94,
  kMonospace = 95,
  kSystemUi = 96,
  kWebkitBody = 97,
  kMath = 98,
  kSwap = 99,
  kFallback = 100,
  kOptional = 101,
  kFontTech = 102,
  kFontFormat = 103,
  kEmoji = 104,
  kUnicode = 105,
  kPaletteMix = 106,
  kAqua = 107,
  kBlack = 108,
  kBlue = 109,
  kFuchsia = 110,
  kGray = 111,
  kGreen = 112,
  kLime = 113,
  kMaroon = 114,
  kNavy = 115,
  kOlive = 116,
  kOrange = 117,
  kPurple = 118,
  kRed = 119,
  kSilver = 120,
  kTeal = 121,
  kWhite = 122,
  kYellow = 123,
  kTransparent = 124,
  kWebkitLink = 125,
  kWebkitActivelink = 126,
  kAccentcolor = 127,
  kAccentcolortext = 128,
  kActiveborder = 129,
  kActivecaption = 130,
  kActivetext = 131,
  kAppworkspace = 132,
  kBackground = 133,
  kButtonborder = 134,
  kButtonface = 135,
  kButtonhighlight = 136,
  kButtonshadow = 137,
  kButtontext = 138,
  kCanvas = 139,
  kCanvastext = 140,
  kCaptiontext = 141,
  kField = 142,
  kFieldtext = 143,
  kGraytext = 144,
  kHighlight = 145,
  kHighlighttext = 146,
  kInactiveborder = 147,
  kInactivecaption = 148,
  kInactivecaptiontext = 149,
  kInfobackground = 150,
  kInfotext = 151,
  kLinktext = 152,
  kMark = 153,
  kMarktext = 154,
  kMenutext = 155,
  kSelecteditem = 156,
  kSelecteditemtext = 157,
  kScrollbar = 158,
  kThreeddarkshadow = 159,
  kThreedface = 160,
  kThreedhighlight = 161,
  kThreedlightshadow = 162,
  kThreedshadow = 163,
  kVisitedtext = 164,
  kWindow = 165,
  kWindowframe = 166,
  kWindowtext = 167,
  kInternalActiveListBoxSelection = 168,
  kInternalActiveListBoxSelectionText = 169,
  kInternalInactiveListBoxSelection = 170,
  kInternalInactiveListBoxSelectionText = 171,
  kWebkitFocusRingColor = 172,
  kCurrentcolor = 173,
  kGrey = 174,
  kInternalQuirkInherit = 175,
  kInternalSpellingErrorColor = 176,
  kInternalGrammarErrorColor = 177,
  kAliceblue = 178,
  kAntiquewhite = 179,
  kAquamarine = 180,
  kAzure = 181,
  kBeige = 182,
  kBisque = 183,
  kBlanchedalmond = 184,
  kBlueviolet = 185,
  kBrown = 186,
  kBurlywood = 187,
  kCadetblue = 188,
  kChartreuse = 189,
  kChocolate = 190,
  kCoral = 191,
  kCornflowerblue = 192,
  kCornsilk = 193,
  kCrimson = 194,
  kCyan = 195,
  kDarkblue = 196,
  kDarkcyan = 197,
  kDarkgoldenrod = 198,
  kDarkgray = 199,
  kDarkgreen = 200,
  kDarkgrey = 201,
  kDarkkhaki = 202,
  kDarkmagenta = 203,
  kDarkolivegreen = 204,
  kDarkorange = 205,
  kDarkorchid = 206,
  kDarkred = 207,
  kDarksalmon = 208,
  kDarkseagreen = 209,
  kDarkslateblue = 210,
  kDarkslategray = 211,
  kDarkslategrey = 212,
  kDarkturquoise = 213,
  kDarkviolet = 214,
  kDeeppink = 215,
  kDeepskyblue = 216,
  kDimgray = 217,
  kDimgrey = 218,
  kDodgerblue = 219,
  kFirebrick = 220,
  kFloralwhite = 221,
  kForestgreen = 222,
  kGainsboro = 223,
  kGhostwhite = 224,
  kGold = 225,
  kGoldenrod = 226,
  kGreenyellow = 227,
  kHoneydew = 228,
  kHotpink = 229,
  kIndianred = 230,
  kIndigo = 231,
  kIvory = 232,
  kKhaki = 233,
  kLavender = 234,
  kLavenderblush = 235,
  kLawngreen = 236,
  kLemonchiffon = 237,
  kLightblue = 238,
  kLightcoral = 239,
  kLightcyan = 240,
  kLightgoldenrodyellow = 241,
  kLightgray = 242,
  kLightgreen = 243,
  kLightgrey = 244,
  kLightpink = 245,
  kLightsalmon = 246,
  kLightseagreen = 247,
  kLightskyblue = 248,
  kLightslategray = 249,
  kLightslategrey = 250,
  kLightsteelblue = 251,
  kLightyellow = 252,
  kLimegreen = 253,
  kLinen = 254,
  kMagenta = 255,
  kMediumaquamarine = 256,
  kMediumblue = 257,
  kMediumorchid = 258,
  kMediumpurple = 259,
  kMediumseagreen = 260,
  kMediumslateblue = 261,
  kMediumspringgreen = 262,
  kMediumturquoise = 263,
  kMediumvioletred = 264,
  kMidnightblue = 265,
  kMintcream = 266,
  kMistyrose = 267,
  kMoccasin = 268,
  kNavajowhite = 269,
  kOldlace = 270,
  kOlivedrab = 271,
  kOrangered = 272,
  kOrchid = 273,
  kPalegoldenrod = 274,
  kPalegreen = 275,
  kPaleturquoise = 276,
  kPalevioletred = 277,
  kPapayawhip = 278,
  kPeachpuff = 279,
  kPeru = 280,
  kPink = 281,
  kPlum = 282,
  kPowderblue = 283,
  kRebeccapurple = 284,
  kRosybrown = 285,
  kRoyalblue = 286,
  kSaddlebrown = 287,
  kSalmon = 288,
  kSandybrown = 289,
  kSeagreen = 290,
  kSeashell = 291,
  kSienna = 292,
  kSkyblue = 293,
  kSlateblue = 294,
  kSlategray = 295,
  kSlategrey = 296,
  kSnow = 297,
  kSpringgreen = 298,
  kSteelblue = 299,
  kTan = 300,
  kThistle = 301,
  kTomato = 302,
  kTurquoise = 303,
  kViolet = 304,
  kWheat = 305,
  kWhitesmoke = 306,
  kYellowgreen = 307,
  kRepeat = 308,
  kRepeatX = 309,
  kRepeatY = 310,
  kNoRepeat = 311,
  kClear = 312,
  kCopy = 313,
  kSourceOver = 314,
  kSourceIn = 315,
  kSourceOut = 316,
  kSourceAtop = 317,
  kDestinationOver = 318,
  kDestinationIn = 319,
  kDestinationOut = 320,
  kDestinationAtop = 321,
  kXor = 322,
  kPlusLighter = 323,
  kSubtract = 324,
  kIntersect = 325,
  kExclude = 326,
  kBaseline = 327,
  kMiddle = 328,
  kSub = 329,
  kSuper = 330,
  kTextTop = 331,
  kTextBottom = 332,
  kTop = 333,
  kBottom = 334,
  kWebkitBaselineMiddle = 335,
  kWebkitAuto = 336,
  kLeft = 337,
  kRight = 338,
  kCenter = 339,
  kJustify = 340,
  kWebkitLeft = 341,
  kWebkitRight = 342,
  kWebkitCenter = 343,
  kWebkitMatchParent = 344,
  kInternalCenter = 345,
  kInlineStart = 346,
  kInlineEnd = 347,
  kOutside = 348,
  kInside = 349,
  kDisc = 350,
  kCircle = 351,
  kSquare = 352,
  kDisclosureOpen = 353,
  kDisclosureClosed = 354,
  kDecimal = 355,
  kInline = 356,
  kBlock = 357,
  kFlowRoot = 358,
  kFlow = 359,
  kTable = 360,
  kFlex = 361,
  kGrid = 362,
  kContents = 363,
  kTableRowGroup = 364,
  kTableHeaderGroup = 365,
  kTableFooterGroup = 366,
  kTableRow = 367,
  kTableColumnGroup = 368,
  kTableColumn = 369,
  kTableCell = 370,
  kTableCaption = 371,
  kRubyText = 372,
  kInlineBlock = 373,
  kInlineTable = 374,
  kInlineFlex = 375,
  kInlineGrid = 376,
  kWebkitBox = 377,
  kWebkitInlineBox = 378,
  kWebkitFlex = 379,
  kWebkitInlineFlex = 380,
  kLayout = 381,
  kInlineLayout = 382,
  kListItem = 383,
  kAuto = 384,
  kCrosshair = 385,
  kDefault = 386,
  kPointer = 387,
  kMove = 388,
  kVerticalText = 389,
  kCell = 390,
  kContextMenu = 391,
  kAlias = 392,
  kProgress = 393,
  kNoDrop = 394,
  kNotAllowed = 395,
  kZoomIn = 396,
  kZoomOut = 397,
  kEResize = 398,
  kNeResize = 399,
  kNwResize = 400,
  kNResize = 401,
  kSeResize = 402,
  kSwResize = 403,
  kSResize = 404,
  kWResize = 405,
  kEwResize = 406,
  kNsResize = 407,
  kNeswResize = 408,
  kNwseResize = 409,
  kColResize = 410,
  kRowResize = 411,
  kText = 412,
  kWait = 413,
  kHelp = 414,
  kAllScroll = 415,
  kGrab = 416,
  kGrabbing = 417,
  kWebkitGrab = 418,
  kWebkitGrabbing = 419,
  kWebkitZoomIn = 420,
  kWebkitZoomOut = 421,
  kLtr = 422,
  kRtl = 423,
  kCapitalize = 424,
  kUppercase = 425,
  kLowercase = 426,
  kMathAuto = 427,
  kVisible = 428,
  kCollapse = 429,
  kPreserve = 430,
  kPreserveBreaks = 431,
  kPretty = 432,
  kA3 = 433,
  kA4 = 434,
  kA5 = 435,
  kAbove = 436,
  kAbsolute = 437,
  kAlways = 438,
  kAvoid = 439,
  kB4 = 440,
  kB5 = 441,
  kBelow = 442,
  kBidiOverride = 443,
  kBlink = 444,
  kBoth = 445,
  kBreakSpaces = 446,
  kCloseQuote = 447,
  kEmbed = 448,
  kFixed = 449,
  kHand = 450,
  kHide = 451,
  kIsolate = 452,
  kIsolateOverride = 453,
  kPlaintext = 454,
  kWebkitIsolate = 455,
  kWebkitIsolateOverride = 456,
  kWebkitPlaintext = 457,
  kJisB5 = 458,
  kJisB4 = 459,
  kLandscape = 460,
  kLedger = 461,
  kLegal = 462,
  kLetter = 463,
  kLineThrough = 464,
  kLocal = 465,
  kNoCloseQuote = 466,
  kNoOpenQuote = 467,
  kNowrap = 468,
  kOpenQuote = 469,
  kOverlay = 470,
  kOverline = 471,
  kPortrait = 472,
  kPre = 473,
  kPreLine = 474,
  kPreWrap = 475,
  kRelative = 476,
  kScroll = 477,
  kSeparate = 478,
  kShow = 479,
  kStatic = 480,
  kThick = 481,
  kThin = 482,
  kUnderline = 483,
  kView = 484,
  kWavy = 485,
  kCompact = 486,
  kStretch = 487,
  kStart = 488,
  kEnd = 489,
  kClone = 490,
  kSlice = 491,
  kReverse = 492,
  kHorizontal = 493,
  kVertical = 494,
  kInlineAxis = 495,
  kBlockAxis = 496,
  kFlexStart = 497,
  kFlexEnd = 498,
  kSpaceBetween = 499,
  kSpaceAround = 500,
  kSpaceEvenly = 501,
  kUnsafe = 502,
  kSafe = 503,
  kAnchorCenter = 504,
  kRow = 505,
  kRowReverse = 506,
  kColumn = 507,
  kColumnReverse = 508,
  kWrap = 509,
  kWrapReverse = 510,
  kAutoFlow = 511,
  kDense = 512,
  kReadOnly = 513,
  kReadWrite = 514,
  kReadWritePlaintextOnly = 515,
  kElement = 516,
  kWebkitMinContent = 517,
  kWebkitMaxContent = 518,
  kWebkitFillAvailable = 519,
  kWebkitFitContent = 520,
  kMinContent = 521,
  kMaxContent = 522,
  kFitContent = 523,
  kNoAutospace = 524,
  kCap = 525,
  kEx = 526,
  kLeading = 527,
  kClip = 528,
  kEllipsis = 529,
  kSpellingError = 530,
  kGrammarError = 531,
  kFromFont = 532,
  kSpaceAll = 533,
  kSpaceFirst = 534,
  kTrimStart = 535,
  kBreakAll = 536,
  kKeepAll = 537,
  kAutoPhrase = 538,
  kBreakWord = 539,
  kSpace = 540,
  kLoose = 541,
  kStrict = 542,
  kAfterWhiteSpace = 543,
  kAnywhere = 544,
  kManual = 545,
  kCheckbox = 546,
  kRadio = 547,
  kButton = 548,
  kListbox = 549,
  kInternalMediaControl = 550,
  kMenulist = 551,
  kMenulistButton = 552,
  kMeter = 553,
  kProgressBar = 554,
  kSearchfield = 555,
  kTextfield = 556,
  kTextarea = 557,
  kInnerSpinButton = 558,
  kPushButton = 559,
  kSquareButton = 560,
  kSliderHorizontal = 561,
  kSearchfieldCancelButton = 562,
  kSliderVertical = 563,
  kRound = 564,
  kBaseSelect = 565,
  kInternalAppearanceAutoBaseSelect = 566,
  kBorder = 567,
  kBorderBox = 568,
  kContent = 569,
  kContentBox = 570,
  kPadding = 571,
  kPaddingBox = 572,
  kMarginBox = 573,
  kNoClip = 574,
  kContain = 575,
  kCover = 576,
  kLogical = 577,
  kVisual = 578,
  kReplace = 579,
  kAccumulate = 580,
  kAlternate = 581,
  kAlternateReverse = 582,
  kForwards = 583,
  kBackwards = 584,
  kInfinite = 585,
  kRunning = 586,
  kPaused = 587,
  kFlat = 588,
  kPreserve3d = 589,
  kFillBox = 590,
  kViewBox = 591,
  kEase = 592,
  kLinear = 593,
  kEaseIn = 594,
  kEaseOut = 595,
  kEaseInOut = 596,
  kJumpBoth = 597,
  kJumpEnd = 598,
  kJumpNone = 599,
  kJumpStart = 600,
  kStepStart = 601,
  kStepEnd = 602,
  kSteps = 603,
  kFrames = 604,
  kCubicBezier = 605,
  kDocument = 606,
  kReset = 607,
  kZoom = 608,
  kVisiblepainted = 609,
  kVisiblefill = 610,
  kVisiblestroke = 611,
  kPainted = 612,
  kFill = 613,
  kStroke = 614,
  kBoundingBox = 615,
  kSpellOut = 616,
  kDigits = 617,
  kLiteralPunctuation = 618,
  kNoPunctuation = 619,
  kAntialiased = 620,
  kSubpixelAntialiased = 621,
  kOptimizespeed = 622,
  kOptimizelegibility = 623,
  kGeometricprecision = 624,
  kCrispedges = 625,
  kEconomy = 626,
  kExact = 627,
  kLr = 628,
  kRl = 629,
  kTb = 630,
  kLrTb = 631,
  kRlTb = 632,
  kTbRl = 633,
  kHorizontalTb = 634,
  kVerticalRl = 635,
  kVerticalLr = 636,
  kAfter = 637,
  kBefore = 638,
  kOver = 639,
  kUnder = 640,
  kFilled = 641,
  kOpen = 642,
  kDot = 643,
  kDoubleCircle = 644,
  kTriangle = 645,
  kSesame = 646,
  kEllipse = 647,
  kClosestSide = 648,
  kClosestCorner = 649,
  kFarthestSide = 650,
  kFarthestCorner = 651,
  kMixed = 652,
  kSideways = 653,
  kSidewaysRight = 654,
  kUpright = 655,
  kVerticalRight = 656,
  kOn = 657,
  kOff = 658,
  kOptimizequality = 659,
  kPixelated = 660,
  kWebkitOptimizeContrast = 661,
  kFromImage = 662,
  kRotateLeft = 663,
  kRotateRight = 664,
  kNonzero = 665,
  kEvenodd = 666,
  kAt = 667,
  kAlphabetic = 668,
  kBorderless = 669,
  kFullscreen = 670,
  kStandalone = 671,
  kMinimalUi = 672,
  kBrowser = 673,
  kWindowControlsOverlay = 674,
  kTabbed = 675,
  kPictureInPicture = 676,
  kMinimized = 677,
  kMaximized = 678,
  kPaged = 679,
  kSlow = 680,
  kFast = 681,
  kSticky = 682,
  kCoarse = 683,
  kFine = 684,
  kOnDemand = 685,
  kHover = 686,
  kMultiply = 687,
  kScreen = 688,
  kDarken = 689,
  kLighten = 690,
  kColorDodge = 691,
  kColorBurn = 692,
  kHardLight = 693,
  kSoftLight = 694,
  kDifference = 695,
  kExclusion = 696,
  kHue = 697,
  kSaturation = 698,
  kColor = 699,
  kLuminosity = 700,
  kScaleDown = 701,
  kBalance = 702,
  kDrag = 703,
  kNoDrag = 704,
  kSpan = 705,
  kMinmax = 706,
  kSubgrid = 707,
  kProgressive = 708,
  kInterlace = 709,
  kMarkers = 710,
  kAlpha = 711,
  kLuminance = 712,
  kMatchSource = 713,
  kSRGB = 714,
  kLinearrgb = 715,
  kButt = 716,
  kMiter = 717,
  kBevel = 718,
  kBeforeEdge = 719,
  kAfterEdge = 720,
  kCentral = 721,
  kTextBeforeEdge = 722,
  kTextAfterEdge = 723,
  kIdeographic = 724,
  kHanging = 725,
  kMathematical = 726,
  kUseScript = 727,
  kNoChange = 728,
  kResetSize = 729,
  kDynamic = 730,
  kNonScalingStroke = 731,
  kInternalExtendToZoom = 732,
  kPanX = 733,
  kPanY = 734,
  kPanLeft = 735,
  kPanRight = 736,
  kPanUp = 737,
  kPanDown = 738,
  kManipulation = 739,
  kPinchZoom = 740,
  kLastBaseline = 741,
  kFirstBaseline = 742,
  kFirst = 743,
  kLast = 744,
  kSelfStart = 745,
  kSelfEnd = 746,
  kLegacy = 747,
  kSmooth = 748,
  kScrollPosition = 749,
  kRevert = 750,
  kRevertLayer = 751,
  kUnset = 752,
  kLinearGradient = 753,
  kRadialGradient = 754,
  kConicGradient = 755,
  kRepeatingLinearGradient = 756,
  kRepeatingRadialGradient = 757,
  kRepeatingConicGradient = 758,
  kPaint = 759,
  kCrossFade = 760,
  kWebkitCrossFade = 761,
  kWebkitGradient = 762,
  kWebkitLinearGradient = 763,
  kWebkitRadialGradient = 764,
  kWebkitRepeatingLinearGradient = 765,
  kWebkitRepeatingRadialGradient = 766,
  kWebkitImageSet = 767,
  kImageSet = 768,
  kType = 769,
  kTo = 770,
  kColorStop = 771,
  kRadial = 772,
  kAttr = 773,
  kCounter = 774,
  kCounters = 775,
  kRect = 776,
  kPolygon = 777,
  kFormat = 778,
  kCollection = 779,
  kEmbeddedOpentype = 780,
  kOpentype = 781,
  kSVG = 782,
  kTruetype = 783,
  kWoff = 784,
  kWoff2 = 785,
  kTech = 786,
  kFeaturesOpentype = 787,
  kFeaturesAat = 788,
  kFeaturesGraphite = 789,
  kColorCOLRv0 = 790,
  kColorCOLRv1 = 791,
  kColorSVG = 792,
  kColorSbix = 793,
  kColorCBDT = 794,
  kVariations = 795,
  kPalettes = 796,
  kIncremental = 797,
  kInvert = 798,
  kGrayscale = 799,
  kSepia = 800,
  kSaturate = 801,
  kHueRotate = 802,
  kOpacity = 803,
  kBrightness = 804,
  kContrast = 805,
  kBlur = 806,
  kDropShadow = 807,
  kUrl = 808,
  kRgb = 809,
  kRgba = 810,
  kHsl = 811,
  kHsla = 812,
  kHwb = 813,
  kLab = 814,
  kOklab = 815,
  kLch = 816,
  kOklch = 817,
  kLightDark = 818,
  kSRGBLinear = 819,
  kDisplayP3 = 820,
  kA98Rgb = 821,
  kProphotoRgb = 822,
  kXyz = 823,
  kXyzD50 = 824,
  kXyzD65 = 825,
  kShorter = 826,
  kLonger = 827,
  kDecreasing = 828,
  kIncreasing = 829,
  kIn = 830,
  kColorMix = 831,
  kFrom = 832,
  kR = 833,
  kG = 834,
  kB = 835,
  kH = 836,
  kS = 837,
  kL = 838,
  kW = 839,
  kA = 840,
  kC = 841,
  kMatrix = 842,
  kMatrix3d = 843,
  kPerspective = 844,
  kRotate = 845,
  kRotateX = 846,
  kRotateY = 847,
  kRotateZ = 848,
  kRotate3d = 849,
  kScale = 850,
  kScaleX = 851,
  kScaleY = 852,
  kScaleZ = 853,
  kScale3d = 854,
  kSkew = 855,
  kSkewX = 856,
  kSkewY = 857,
  kTranslate = 858,
  kTranslateX = 859,
  kTranslateY = 860,
  kTranslateZ = 861,
  kTranslate3d = 862,
  kX = 863,
  kY = 864,
  kZ = 865,
  kPath = 866,
  kRay = 867,
  kSides = 868,
  kStrokeBox = 869,
  kCalc = 870,
  kWebkitCalc = 871,
  kMin = 872,
  kMax = 873,
  kClamp = 874,
  kCalcSize = 875,
  kAny = 876,
  kSin = 877,
  kCos = 878,
  kAsin = 879,
  kAtan = 880,
  kAtan2 = 881,
  kAcos = 882,
  kMod = 883,
  kRem = 884,
  kUp = 885,
  kDown = 886,
  kToZero = 887,
  kSign = 888,
  kAbs = 889,
  kPow = 890,
  kSqrt = 891,
  kHypot = 892,
  kLog = 893,
  kExp = 894,
  kInfinity = 895,
  kNegativeInfinity = 896,
  kNan = 897,
  kPi = 898,
  kE = 899,
  kMandatory = 900,
  kProximity = 901,
  kStyle = 902,
  kSize = 903,
  kBlockSize = 904,
  kInlineSize = 905,
  kScrollState = 906,
  kInsetBlockStart = 907,
  kInsetBlockEnd = 908,
  kInsetInlineStart = 909,
  kInsetInlineEnd = 910,
  kAutoFill = 911,
  kAutoFit = 912,
  kVar = 913,
  kInternalVariableValue = 914,
  kEnv = 915,
  kArg = 916,
  kAvoidPage = 917,
  kPage = 918,
  kRecto = 919,
  kVerso = 920,
  kAvoidColumn = 921,
  kP3 = 922,
  kRec2020 = 923,
  kAdd = 924,
  kAutoAdd = 925,
  kTrue = 926,
  kFalse = 927,
  kNoPreference = 928,
  kDark = 929,
  kLight = 930,
  kOnly = 931,
  kReduce = 932,
  kActive = 933,
  kPreserveParentColor = 934,
  kBackButton = 935,
  kFabricated = 936,
  kSelector = 937,
  kContinuous = 938,
  kFolded = 939,
  kStable = 940,
  kBothEdges = 941,
  kMore = 942,
  kLess = 943,
  kCustom = 944,
  kCyclic = 945,
  kSymbolic = 946,
  kNumeric = 947,
  kAdditive = 948,
  kExtends = 949,
  kInternalSimpChineseInformal = 950,
  kInternalSimpChineseFormal = 951,
  kInternalTradChineseInformal = 952,
  kInternalTradChineseFormal = 953,
  kInternalKoreanHangulFormal = 954,
  kInternalKoreanHanjaInformal = 955,
  kInternalKoreanHanjaFormal = 956,
  kInternalHebrew = 957,
  kInternalLowerArmenian = 958,
  kInternalUpperArmenian = 959,
  kInternalEthiopicNumeric = 960,
  kBullets = 961,
  kNumbers = 962,
  kWords = 963,
  kStandard = 964,
  kHigh = 965,
  kConstrainedHigh = 966,
  kDynamicRangeLimitMix = 967,
  kLayer = 968,
  kSupports = 969,
  kColorContrast = 970,
  kVs = 971,
  kAA = 972,
  kAALarge = 973,
  kAAA = 974,
  kAAALarge = 975,
  kDrop = 976,
  kRaise = 977,
  kXywh = 978,
  kAnchor = 979,
  kAnchorSize = 980,
  kWidth = 981,
  kHeight = 982,
  kSelfBlock = 983,
  kSelfInline = 984,
  kImplicit = 985,
  kEntry = 986,
  kExit = 987,
  kEntryCrossing = 988,
  kExitCrossing = 989,
  kRoot = 990,
  kNearest = 991,
  kSelf = 992,
  kAllowDiscrete = 993,
  kInverted = 994,
  kEnabled = 995,
  kInitialOnly = 996,
  kSpanLeft = 997,
  kSpanRight = 998,
  kXStart = 999,
  kXEnd = 1000,
  kSpanXStart = 1001,
  kSpanXEnd = 1002,
  kXSelfStart = 1003,
  kXSelfEnd = 1004,
  kSpanXSelfStart = 1005,
  kSpanXSelfEnd = 1006,
  kSpanAll = 1007,
  kSpanTop = 1008,
  kSpanBottom = 1009,
  kYStart = 1010,
  kYEnd = 1011,
  kSpanYStart = 1012,
  kSpanYEnd = 1013,
  kYSelfStart = 1014,
  kYSelfEnd = 1015,
  kSpanYSelfStart = 1016,
  kSpanYSelfEnd = 1017,
  kBlockStart = 1018,
  kBlockEnd = 1019,
  kSpanBlockStart = 1020,
  kSpanBlockEnd = 1021,
  kSelfBlockStart = 1022,
  kSelfBlockEnd = 1023,
  kSpanSelfBlockStart = 1024,
  kSpanSelfBlockEnd = 1025,
  kSpanInlineStart = 1026,
  kSpanInlineEnd = 1027,
  kSelfInlineStart = 1028,
  kSelfInlineEnd = 1029,
  kSpanSelfInlineStart = 1030,
  kSpanSelfInlineEnd = 1031,
  kSpanStart = 1032,
  kSpanEnd = 1033,
  kSpanSelfStart = 1034,
  kSpanSelfEnd = 1035,
  kInsetArea = 1036,
  kInternalTextareaAuto = 1037,
  kMostWidth = 1038,
  kMostHeight = 1039,
  kMostBlockSize = 1040,
  kMostInlineSize = 1041,
  kFlipBlock = 1042,
  kFlipInline = 1043,
  kFlipStart = 1044,
  kAnchorsVisible = 1045,
  kNoOverflow = 1046,
  kFlexVisual = 1047,
  kFlexFlow = 1048,
  kGridRows = 1049,
  kGridColumns = 1050,
  kGridOrder = 1051,
  kContextFill = 1052,
  kContextStroke = 1053,
  kMediaProgress = 1054,
  kContainerProgress = 1055,
  kOf = 1056,
};

const int numCSSValueKeywords = 1057;
const size_t maxCSSValueKeywordLength = 42;

inline bool IsValidCSSValueID(CSSValueID id)
{
    return id != CSSValueID::kInvalid;
}

CORE_EXPORT const char* getValueName(CSSValueID);
bool isValueAllowedInMode(CSSValueID id, CSSParserMode mode);

}  // namespace blink

#endif  // THIRD_PARTY_BLINK_RENDERER_CORE_CSS_VALUE_KEYWORDS_H_
