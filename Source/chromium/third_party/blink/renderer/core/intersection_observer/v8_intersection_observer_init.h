// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// DO NOT EDIT: This file is auto-generated by
// //third_party/blink/renderer/bindings/scripts/generate_bindings.py
//
// Use the GN flag `blink_enable_generated_code_formatting=true` to enable
// formatting of the generated files.

#ifndef THIRD_PARTY_BLINK_RENDERER_BINDINGS_CORE_V8_V8_INTERSECTION_OBSERVER_INIT_H_
#define THIRD_PARTY_BLINK_RENDERER_BINDINGS_CORE_V8_V8_INTERSECTION_OBSERVER_INIT_H_

/**
 # 在Chromium中，IntersectionObserverInit类是自动生成的，用于V8引擎设置IntersectionObserver的初始化参数。
 TODO:  这里拷贝生成后的init类，后续改造这个类为NativeDOM设置的初始化参数
 */

#include "base/containers/span.h"
#include "third_party/blink/renderer/platform/heap/garbage_collected.h"
#include "third_party/blink/renderer/core/core_export.h"
//#include "third_party/blink/renderer/platform/bindings/dictionary_base.h"
#include "third_party/blink/renderer/platform/heap/collection_support/heap_vector.h"
#include "third_party/blink/renderer/platform/heap/member.h"
#include "third_party/blink/renderer/platform/wtf/text/wtf_string.h"

namespace blink {

class Document;
class Element;
//class ExceptionState;
//class V8UnionDocumentOrElement;
//class V8UnionDoubleOrDoubleSequence;

class CORE_EXPORT IntersectionObserverInit: public GarbageCollected<IntersectionObserverInit> {
  
  public:
static IntersectionObserverInit* Create() {
  return MakeGarbageCollected<IntersectionObserverInit>();
}
//static IntersectionObserverInit* Create(v8::Isolate* isolate) {
//  return MakeGarbageCollected<IntersectionObserverInit>(isolate);
//}
//static IntersectionObserverInit* Create(v8::Isolate* isolate, v8::Local<v8::Value> v8_value, ExceptionState& exception_state);

explicit  IntersectionObserverInit();
//explicit  IntersectionObserverInit(v8::Isolate* isolate);

bool hasDelay() const {
  return true;
}
double delay() const {
  return member_delay_;
}
void setDelay(double value) {
  member_delay_ = value;
}

bool hasRoot() const {
  return true;
}

Element* root() const {
  return member_root_.Get();
}
void setRoot(Element* value) {
  member_root_ = value;
}
    
//V8UnionDocumentOrElement* root() const {
//  return member_root_.Get();
//}
//void setRoot(V8UnionDocumentOrElement* value) {
//  member_root_ = value;
//}

bool hasRootMargin() const {
  return true;
}
const String& rootMargin() const {
  return member_root_margin_;
}
void setRootMargin(const String& value);
void setRootMargin(String&& value);

bool hasScrollMargin() const {
  return true;
}
const String& scrollMargin() const {
  return member_scroll_margin_;
}
void setScrollMargin(const String& value);
void setScrollMargin(String&& value);

bool hasThreshold() const {
  return true;
}
//V8UnionDoubleOrDoubleSequence* threshold() const {
//  return member_threshold_.Get();
//}
//void setThreshold(V8UnionDoubleOrDoubleSequence* value) {
//  member_threshold_ = value;
//DCHECK(member_threshold_);
//}

bool hasTrackVisibility() const {
  return true;
}
bool trackVisibility() const {
  return member_track_visibility_;
}
void setTrackVisibility(bool value) {
  member_track_visibility_ = value;
}




void Trace(Visitor* visitor) const;


  protected:
static constexpr size_t kBasePropertyCount = 0;
static constexpr size_t kOwnPropertyCount = 6;
static constexpr size_t kTotalPropertyCount = kBasePropertyCount + kOwnPropertyCount;
    void FillTemplateProperties(WTF::Vector<std::string_view>& properties) const ;
//void FillValuesImpl(ScriptState* script_state, base::span<v8::MaybeLocal<v8::Value>> values) const;


//void FillMembersFromV8Object(v8::Isolate* isolate, v8::Local<v8::Object> v8_dictionary, ExceptionState& exception_state);


  private:
const void* TemplateKey() const;
//v8::Local<v8::Object> FillValues(ScriptState* script_state, v8::Local<v8::DictionaryTemplate> dict_template) const override;
//static const base::span<const v8::Eternal<v8::Name>> GetV8OwnMemberNames(v8::Isolate* isolate);



double member_delay_{0};
//Member<V8UnionDocumentOrElement> member_root_;
Member<Element> member_root_;
String member_root_margin_{"0px"};
String member_scroll_margin_{"0px"};
//Member<V8UnionDoubleOrDoubleSequence> member_threshold_;
bool member_track_visibility_{false};


  
};


}  // namespace blink

#endif  // THIRD_PARTY_BLINK_RENDERER_BINDINGS_CORE_V8_V8_INTERSECTION_OBSERVER_INIT_H_
