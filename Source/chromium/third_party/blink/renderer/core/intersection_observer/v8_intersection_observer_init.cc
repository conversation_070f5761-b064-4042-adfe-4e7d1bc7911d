// Copyright 2019 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// DO NOT EDIT: This file is auto-generated by
// //third_party/blink/renderer/bindings/scripts/generate_bindings.py
//
// Use the GN flag `blink_enable_generated_code_formatting=true` to enable
// formatting of the generated files.

//#include "third_party/blink/renderer/bindings/core/v8/v8_intersection_observer_init.h"

#include "third_party/blink/renderer/core/intersection_observer/v8_intersection_observer_init.h"
//#include "third_party/blink/renderer/bindings/core/v8/generated_code_helper.h"
//#include "third_party/blink/renderer/bindings/core/v8/native_value_traits_impl.h"
//#include "third_party/blink/renderer/bindings/core/v8/to_v8_traits.h"
//#include "third_party/blink/renderer/bindings/core/v8/v8_union_document_element.h"
//#include "third_party/blink/renderer/bindings/core/v8/v8_union_double_doublesequence.h"
#include "third_party/blink/renderer/core/dom/document.h"
#include "third_party/blink/renderer/core/dom/element.h"
//#include "third_party/blink/renderer/platform/bindings/exception_messages.h"
//#include "third_party/blink/renderer/platform/bindings/exception_state.h"
//#include "third_party/blink/renderer/platform/bindings/v8_per_isolate_data.h"

namespace blink {



namespace  {

const std::string_view kOwnPropertyNames[] = {
"delay",
"root",
"rootMargin",
"scrollMargin",
"threshold",
"trackVisibility",
};


}  // namespace 

//IntersectionObserverInit* IntersectionObserverInit::Create(v8::Isolate* isolate, v8::Local<v8::Value> v8_value, ExceptionState& exception_state) {
//  IntersectionObserverInit* dictionary = MakeGarbageCollected<IntersectionObserverInit>(isolate);
//if (v8_value->IsNullOrUndefined()) {
//  return dictionary;
//}
//if (!v8_value->IsObject()) {
//  const char* const class_like_name = "IntersectionObserverInit";
//exception_state.ThrowTypeError(ExceptionMessages::ValueNotOfType(class_like_name));
//return nullptr;
//}
//dictionary->FillMembersFromV8Object(isolate, v8_value.As<v8::Object>(), exception_state);
//if (UNLIKELY(exception_state.HadException())) {
//  return nullptr;
//}
//return dictionary;
//}

 IntersectionObserverInit::IntersectionObserverInit() : member_root_(nullptr)/*, member_threshold_(MakeGarbageCollected<V8UnionDoubleOrDoubleSequence>(0))*/ {
  
}

// IntersectionObserverInit::IntersectionObserverInit(v8::Isolate* isolate) : member_root_(nullptr), member_threshold_(MakeGarbageCollected<V8UnionDoubleOrDoubleSequence>(0)) {
//  
//}













void IntersectionObserverInit::setRootMargin(const String& value) {
  member_root_margin_ = value;
}

void IntersectionObserverInit::setRootMargin(String&& value) {
  member_root_margin_ = std::move(value);
}




void IntersectionObserverInit::setScrollMargin(const String& value) {
  member_scroll_margin_ = value;
}

void IntersectionObserverInit::setScrollMargin(String&& value) {
  member_scroll_margin_ = std::move(value);
}














void IntersectionObserverInit::Trace(Visitor* visitor) const {
  TraceIfNeeded<double>::Trace(visitor, member_delay_);
//TraceIfNeeded<Member<V8UnionDocumentOrElement>>::Trace(visitor, member_root_);
TraceIfNeeded<Member<Element>>::Trace(visitor, member_root_);
TraceIfNeeded<String>::Trace(visitor, member_root_margin_);
TraceIfNeeded<String>::Trace(visitor, member_scroll_margin_);
//TraceIfNeeded<Member<V8UnionDoubleOrDoubleSequence>>::Trace(visitor, member_threshold_);
TraceIfNeeded<bool>::Trace(visitor, member_track_visibility_);
//bindings::DictionaryBase::Trace(visitor);
}

void IntersectionObserverInit::FillTemplateProperties(WTF::Vector<std::string_view>& properties) const {
  static_assert(std::size(kOwnPropertyNames) == kOwnPropertyCount);
properties.AppendRange(std::cbegin(kOwnPropertyNames), std::cend(kOwnPropertyNames));
DCHECK_EQ(properties.size(), kTotalPropertyCount);
}

//void IntersectionObserverInit::FillValuesImpl(ScriptState* script_state, base::span<v8::MaybeLocal<v8::Value>> values) const {
//  CHECK_EQ(kOwnPropertyCount, values.size());
//if (hasDelay()) {
//  values[0] = ToV8Traits<IDLDouble>::ToV8(script_state, member_delay_);
//DCHECK(!values[0].IsEmpty());
//}
//if (hasRoot()) {
//  values[1] = ToV8Traits<IDLNullable<V8UnionDocumentOrElement>>::ToV8(script_state, member_root_.Get());
//DCHECK(!values[1].IsEmpty());
//}
//if (hasRootMargin()) {
//  values[2] = ToV8Traits<IDLString>::ToV8(script_state, member_root_margin_);
//DCHECK(!values[2].IsEmpty());
//}
//if (hasScrollMargin()) {
//  values[3] = ToV8Traits<IDLString>::ToV8(script_state, member_scroll_margin_);
//DCHECK(!values[3].IsEmpty());
//}
//if (hasThreshold()) {
//  values[4] = ToV8Traits<V8UnionDoubleOrDoubleSequence>::ToV8(script_state, member_threshold_.Get());
//DCHECK(!values[4].IsEmpty());
//}
//if (hasTrackVisibility()) {
//  values[5] = ToV8Traits<IDLBoolean>::ToV8(script_state, member_track_visibility_);
//DCHECK(!values[5].IsEmpty());
//}
//}

const void* IntersectionObserverInit::TemplateKey() const {
  static const void *s_key = &s_key;
return s_key;
}

//v8::Local<v8::Object> IntersectionObserverInit::FillValues(ScriptState* script_state, v8::Local<v8::DictionaryTemplate> dict_template) const {
//  v8::MaybeLocal<v8::Value> values[kTotalPropertyCount];
//FillValuesImpl(script_state, values);
//return dict_template->NewInstance(script_state->GetContext(), values);
//}

//void IntersectionObserverInit::FillMembersFromV8Object(v8::Isolate* isolate, v8::Local<v8::Object> v8_dictionary, ExceptionState& exception_state) {
//  const char* const class_like_name = "IntersectionObserverInit";
//ExceptionState::ContextScope exception_context_scope(ExceptionContext(ExceptionContextType::kDictionaryMemberGet, class_like_name, ""), exception_state);
//exception_context_scope.ChangePropertyNameAsOptimizationHack("delay");
//constexpr bool is_optional = false;
//v8::Local<v8::Context> current_context = isolate->GetCurrentContext();
//const auto& v8_own_member_names = GetV8OwnMemberNames(isolate);
//bool fallback_presence_var;
//v8::TryCatch try_block(isolate);
//if (!bindings::GetDictionaryMemberFromV8Object<IDLDouble, is_optional>(isolate, current_context, v8_dictionary, v8_own_member_names[0].Get(isolate), fallback_presence_var, member_delay_, try_block, exception_state)) {
//  return;
//}
//exception_context_scope.ChangePropertyNameAsOptimizationHack("root");
//if (!bindings::GetDictionaryMemberFromV8Object<IDLNullable<V8UnionDocumentOrElement>, is_optional>(isolate, current_context, v8_dictionary, v8_own_member_names[1].Get(isolate), fallback_presence_var, member_root_, try_block, exception_state)) {
//  return;
//}
//exception_context_scope.ChangePropertyNameAsOptimizationHack("rootMargin");
//if (!bindings::GetDictionaryMemberFromV8Object<IDLString, is_optional>(isolate, current_context, v8_dictionary, v8_own_member_names[2].Get(isolate), fallback_presence_var, member_root_margin_, try_block, exception_state)) {
//  return;
//}
//exception_context_scope.ChangePropertyNameAsOptimizationHack("scrollMargin");
//if (!bindings::GetDictionaryMemberFromV8Object<IDLString, is_optional>(isolate, current_context, v8_dictionary, v8_own_member_names[3].Get(isolate), fallback_presence_var, member_scroll_margin_, try_block, exception_state)) {
//  return;
//}
//exception_context_scope.ChangePropertyNameAsOptimizationHack("threshold");
//if (!bindings::GetDictionaryMemberFromV8Object<V8UnionDoubleOrDoubleSequence, is_optional>(isolate, current_context, v8_dictionary, v8_own_member_names[4].Get(isolate), fallback_presence_var, member_threshold_, try_block, exception_state)) {
//  return;
//}
//exception_context_scope.ChangePropertyNameAsOptimizationHack("trackVisibility");
//if (!bindings::GetDictionaryMemberFromV8Object<IDLBoolean, is_optional>(isolate, current_context, v8_dictionary, v8_own_member_names[5].Get(isolate), fallback_presence_var, member_track_visibility_, try_block, exception_state)) {
//  return;
//}
//}

//const base::span<const v8::Eternal<v8::Name>> IntersectionObserverInit::GetV8OwnMemberNames(v8::Isolate* isolate) {
//  return V8PerIsolateData::From(isolate)->FindOrCreateEternalNameCache(kOwnPropertyNames, kOwnPropertyNames);
//}


}  // namespace blink
