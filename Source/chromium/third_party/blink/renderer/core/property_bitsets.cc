// Copyright 2022 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Generated from template:
//   core/css/properties/templates/property_bitsets.cc.tmpl
// and input files:
//   ../../third_party/blink/renderer/core/css/computed_style_field_aliases.json5
//   ../../third_party/blink/renderer/core/css/css_properties.json5
//   ../../third_party/blink/renderer/platform/runtime_enabled_features.json5


#include "third_party/blink/renderer/core/css/property_bitsets.h"

#include "third_party/blink/renderer/core/css/properties/css_bitset.h"
#include <array>

namespace blink {

const CSSBitset kLogicalGroupProperties{ {
    CSSPropertyID::kBlockSize,
    CSSPropertyID::kBorderBlockEndColor,
    CSSPropertyID::kBorderBlockEndStyle,
    CSSPropertyID::kBorderBlockEndWidth,
    CSSPropertyID::kBorderBlockStartColor,
    CSSPropertyID::kBorderBlockStartStyle,
    CSSPropertyID::kBorderBlockStartWidth,
    CSSPropertyID::kBorderEndEndRadius,
    CSSPropertyID::kBorderEndStartRadius,
    CSSPropertyID::kBorderInlineEndColor,
    CSSPropertyID::kBorderInlineEndStyle,
    CSSPropertyID::kBorderInlineEndWidth,
    CSSPropertyID::kBorderInlineStartColor,
    CSSPropertyID::kBorderInlineStartStyle,
    CSSPropertyID::kBorderInlineStartWidth,
    CSSPropertyID::kBorderStartEndRadius,
    CSSPropertyID::kBorderStartStartRadius,
    CSSPropertyID::kContainIntrinsicBlockSize,
    CSSPropertyID::kContainIntrinsicInlineSize,
    CSSPropertyID::kInlineSize,
    CSSPropertyID::kInsetBlockEnd,
    CSSPropertyID::kInsetBlockStart,
    CSSPropertyID::kInsetInlineEnd,
    CSSPropertyID::kInsetInlineStart,
    CSSPropertyID::kInternalOverflowBlock,
    CSSPropertyID::kInternalOverflowInline,
    CSSPropertyID::kInternalVisitedBorderBlockEndColor,
    CSSPropertyID::kInternalVisitedBorderBlockStartColor,
    CSSPropertyID::kInternalVisitedBorderInlineEndColor,
    CSSPropertyID::kInternalVisitedBorderInlineStartColor,
    CSSPropertyID::kMarginBlockEnd,
    CSSPropertyID::kMarginBlockStart,
    CSSPropertyID::kMarginInlineEnd,
    CSSPropertyID::kMarginInlineStart,
    CSSPropertyID::kMaxBlockSize,
    CSSPropertyID::kMaxInlineSize,
    CSSPropertyID::kMinBlockSize,
    CSSPropertyID::kMinInlineSize,
    CSSPropertyID::kOverflowBlock,
    CSSPropertyID::kOverflowInline,
    CSSPropertyID::kOverscrollBehaviorBlock,
    CSSPropertyID::kOverscrollBehaviorInline,
    CSSPropertyID::kPaddingBlockEnd,
    CSSPropertyID::kPaddingBlockStart,
    CSSPropertyID::kPaddingInlineEnd,
    CSSPropertyID::kPaddingInlineStart,
    CSSPropertyID::kScrollMarginBlockEnd,
    CSSPropertyID::kScrollMarginBlockStart,
    CSSPropertyID::kScrollMarginInlineEnd,
    CSSPropertyID::kScrollMarginInlineStart,
    CSSPropertyID::kScrollPaddingBlockEnd,
    CSSPropertyID::kScrollPaddingBlockStart,
    CSSPropertyID::kScrollPaddingInlineEnd,
    CSSPropertyID::kScrollPaddingInlineStart,
    CSSPropertyID::kScrollStartBlock,
    CSSPropertyID::kScrollStartInline,
    CSSPropertyID::kScrollStartTargetBlock,
    CSSPropertyID::kScrollStartTargetInline,
    CSSPropertyID::kBorderBlockEnd,
    CSSPropertyID::kBorderBlockStart,
    CSSPropertyID::kBorderInlineEnd,
    CSSPropertyID::kBorderInlineStart,
    CSSPropertyID::kAliasWebkitBorderEndColor,
    CSSPropertyID::kAliasWebkitBorderEndStyle,
    CSSPropertyID::kAliasWebkitBorderEndWidth,
    CSSPropertyID::kAliasWebkitBorderStartColor,
    CSSPropertyID::kAliasWebkitBorderStartStyle,
    CSSPropertyID::kAliasWebkitBorderStartWidth,
    CSSPropertyID::kAliasWebkitBorderBeforeColor,
    CSSPropertyID::kAliasWebkitBorderBeforeStyle,
    CSSPropertyID::kAliasWebkitBorderBeforeWidth,
    CSSPropertyID::kAliasWebkitBorderAfterColor,
    CSSPropertyID::kAliasWebkitBorderAfterStyle,
    CSSPropertyID::kAliasWebkitBorderAfterWidth,
    CSSPropertyID::kAliasWebkitMarginEnd,
    CSSPropertyID::kAliasWebkitMarginStart,
    CSSPropertyID::kAliasWebkitMarginBefore,
    CSSPropertyID::kAliasWebkitMarginAfter,
    CSSPropertyID::kAliasWebkitPaddingEnd,
    CSSPropertyID::kAliasWebkitPaddingStart,
    CSSPropertyID::kAliasWebkitPaddingBefore,
    CSSPropertyID::kAliasWebkitPaddingAfter,
    CSSPropertyID::kAliasWebkitLogicalWidth,
    CSSPropertyID::kAliasWebkitLogicalHeight,
    CSSPropertyID::kAliasWebkitMinLogicalWidth,
    CSSPropertyID::kAliasWebkitMinLogicalHeight,
    CSSPropertyID::kAliasWebkitMaxLogicalWidth,
    CSSPropertyID::kAliasWebkitMaxLogicalHeight,
    CSSPropertyID::kAliasWebkitBorderAfter,
    CSSPropertyID::kAliasWebkitBorderBefore,
    CSSPropertyID::kAliasWebkitBorderEnd,
    CSSPropertyID::kAliasWebkitBorderStart,
} };

const CSSBitset kKnownExposedProperties{ {
    CSSPropertyID::kColorScheme,
    CSSPropertyID::kMaskImage,
    CSSPropertyID::kMathDepth,
    CSSPropertyID::kPosition,
    CSSPropertyID::kAppearance,
    CSSPropertyID::kColor,
    CSSPropertyID::kDirection,
    CSSPropertyID::kFontFamily,
    CSSPropertyID::kFontFeatureSettings,
    CSSPropertyID::kFontKerning,
    CSSPropertyID::kFontOpticalSizing,
    CSSPropertyID::kFontPalette,
    CSSPropertyID::kFontSize,
    CSSPropertyID::kFontStretch,
    CSSPropertyID::kFontStyle,
    CSSPropertyID::kFontSynthesisSmallCaps,
    CSSPropertyID::kFontSynthesisStyle,
    CSSPropertyID::kFontSynthesisWeight,
    CSSPropertyID::kFontVariantAlternates,
    CSSPropertyID::kFontVariantCaps,
    CSSPropertyID::kFontVariantEastAsian,
    CSSPropertyID::kFontVariantLigatures,
    CSSPropertyID::kFontVariantNumeric,
    CSSPropertyID::kFontVariantPosition,
    CSSPropertyID::kFontVariationSettings,
    CSSPropertyID::kFontWeight,
    CSSPropertyID::kTextOrientation,
    CSSPropertyID::kTextRendering,
    CSSPropertyID::kTextSpacingTrim,
    CSSPropertyID::kWebkitFontSmoothing,
    CSSPropertyID::kWebkitLocale,
    CSSPropertyID::kWebkitTextOrientation,
    CSSPropertyID::kWebkitWritingMode,
    CSSPropertyID::kWritingMode,
    CSSPropertyID::kZoom,
    CSSPropertyID::kAccentColor,
    CSSPropertyID::kAdditiveSymbols,
    CSSPropertyID::kAlignContent,
    CSSPropertyID::kAlignItems,
    CSSPropertyID::kAlignSelf,
    CSSPropertyID::kAlignmentBaseline,
    CSSPropertyID::kAll,
    CSSPropertyID::kAnimationComposition,
    CSSPropertyID::kAnimationDelay,
    CSSPropertyID::kAnimationDirection,
    CSSPropertyID::kAnimationDuration,
    CSSPropertyID::kAnimationFillMode,
    CSSPropertyID::kAnimationIterationCount,
    CSSPropertyID::kAnimationName,
    CSSPropertyID::kAnimationPlayState,
    CSSPropertyID::kAnimationTimingFunction,
    CSSPropertyID::kAppRegion,
    CSSPropertyID::kAscentOverride,
    CSSPropertyID::kAspectRatio,
    CSSPropertyID::kBackdropFilter,
    CSSPropertyID::kBackfaceVisibility,
    CSSPropertyID::kBackgroundAttachment,
    CSSPropertyID::kBackgroundBlendMode,
    CSSPropertyID::kBackgroundClip,
    CSSPropertyID::kBackgroundColor,
    CSSPropertyID::kBackgroundImage,
    CSSPropertyID::kBackgroundOrigin,
    CSSPropertyID::kBackgroundPositionX,
    CSSPropertyID::kBackgroundPositionY,
    CSSPropertyID::kBackgroundRepeat,
    CSSPropertyID::kBackgroundSize,
    CSSPropertyID::kBasePalette,
    CSSPropertyID::kBaselineShift,
    CSSPropertyID::kBaselineSource,
    CSSPropertyID::kBlockSize,
    CSSPropertyID::kBorderBlockEndColor,
    CSSPropertyID::kBorderBlockEndStyle,
    CSSPropertyID::kBorderBlockEndWidth,
    CSSPropertyID::kBorderBlockStartColor,
    CSSPropertyID::kBorderBlockStartStyle,
    CSSPropertyID::kBorderBlockStartWidth,
    CSSPropertyID::kBorderBottomColor,
    CSSPropertyID::kBorderBottomLeftRadius,
    CSSPropertyID::kBorderBottomRightRadius,
    CSSPropertyID::kBorderBottomStyle,
    CSSPropertyID::kBorderBottomWidth,
    CSSPropertyID::kBorderCollapse,
    CSSPropertyID::kBorderEndEndRadius,
    CSSPropertyID::kBorderEndStartRadius,
    CSSPropertyID::kBorderImageOutset,
    CSSPropertyID::kBorderImageRepeat,
    CSSPropertyID::kBorderImageSlice,
    CSSPropertyID::kBorderImageSource,
    CSSPropertyID::kBorderImageWidth,
    CSSPropertyID::kBorderInlineEndColor,
    CSSPropertyID::kBorderInlineEndStyle,
    CSSPropertyID::kBorderInlineEndWidth,
    CSSPropertyID::kBorderInlineStartColor,
    CSSPropertyID::kBorderInlineStartStyle,
    CSSPropertyID::kBorderInlineStartWidth,
    CSSPropertyID::kBorderLeftColor,
    CSSPropertyID::kBorderLeftStyle,
    CSSPropertyID::kBorderLeftWidth,
    CSSPropertyID::kBorderRightColor,
    CSSPropertyID::kBorderRightStyle,
    CSSPropertyID::kBorderRightWidth,
    CSSPropertyID::kBorderStartEndRadius,
    CSSPropertyID::kBorderStartStartRadius,
    CSSPropertyID::kBorderTopColor,
    CSSPropertyID::kBorderTopLeftRadius,
    CSSPropertyID::kBorderTopRightRadius,
    CSSPropertyID::kBorderTopStyle,
    CSSPropertyID::kBorderTopWidth,
    CSSPropertyID::kBottom,
    CSSPropertyID::kBoxShadow,
    CSSPropertyID::kBoxSizing,
    CSSPropertyID::kBreakAfter,
    CSSPropertyID::kBreakBefore,
    CSSPropertyID::kBreakInside,
    CSSPropertyID::kBufferedRendering,
    CSSPropertyID::kCaptionSide,
    CSSPropertyID::kCaretColor,
    CSSPropertyID::kClear,
    CSSPropertyID::kClip,
    CSSPropertyID::kClipPath,
    CSSPropertyID::kClipRule,
    CSSPropertyID::kColorInterpolation,
    CSSPropertyID::kColorInterpolationFilters,
    CSSPropertyID::kColorRendering,
    CSSPropertyID::kColumnCount,
    CSSPropertyID::kColumnFill,
    CSSPropertyID::kColumnGap,
    CSSPropertyID::kColumnRuleColor,
    CSSPropertyID::kColumnRuleStyle,
    CSSPropertyID::kColumnRuleWidth,
    CSSPropertyID::kColumnSpan,
    CSSPropertyID::kColumnWidth,
    CSSPropertyID::kContain,
    CSSPropertyID::kContainIntrinsicBlockSize,
    CSSPropertyID::kContainIntrinsicHeight,
    CSSPropertyID::kContainIntrinsicInlineSize,
    CSSPropertyID::kContainIntrinsicWidth,
    CSSPropertyID::kContainerName,
    CSSPropertyID::kContainerType,
    CSSPropertyID::kContent,
    CSSPropertyID::kContentVisibility,
    CSSPropertyID::kCounterIncrement,
    CSSPropertyID::kCounterReset,
    CSSPropertyID::kCounterSet,
    CSSPropertyID::kCursor,
    CSSPropertyID::kCx,
    CSSPropertyID::kCy,
    CSSPropertyID::kD,
    CSSPropertyID::kDescentOverride,
    CSSPropertyID::kDisplay,
    CSSPropertyID::kDominantBaseline,
    CSSPropertyID::kEmptyCells,
    CSSPropertyID::kFallback,
    CSSPropertyID::kFill,
    CSSPropertyID::kFillOpacity,
    CSSPropertyID::kFillRule,
    CSSPropertyID::kFilter,
    CSSPropertyID::kFlexBasis,
    CSSPropertyID::kFlexDirection,
    CSSPropertyID::kFlexGrow,
    CSSPropertyID::kFlexShrink,
    CSSPropertyID::kFlexWrap,
    CSSPropertyID::kFloat,
    CSSPropertyID::kFloodColor,
    CSSPropertyID::kFloodOpacity,
    CSSPropertyID::kFontDisplay,
    CSSPropertyID::kGridAutoColumns,
    CSSPropertyID::kGridAutoFlow,
    CSSPropertyID::kGridAutoRows,
    CSSPropertyID::kGridColumnEnd,
    CSSPropertyID::kGridColumnStart,
    CSSPropertyID::kGridRowEnd,
    CSSPropertyID::kGridRowStart,
    CSSPropertyID::kGridTemplateAreas,
    CSSPropertyID::kGridTemplateColumns,
    CSSPropertyID::kGridTemplateRows,
    CSSPropertyID::kHeight,
    CSSPropertyID::kHyphenateCharacter,
    CSSPropertyID::kHyphenateLimitChars,
    CSSPropertyID::kHyphens,
    CSSPropertyID::kImageOrientation,
    CSSPropertyID::kImageRendering,
    CSSPropertyID::kInherits,
    CSSPropertyID::kInitialLetter,
    CSSPropertyID::kInitialValue,
    CSSPropertyID::kInlineSize,
    CSSPropertyID::kInsetBlockEnd,
    CSSPropertyID::kInsetBlockStart,
    CSSPropertyID::kInsetInlineEnd,
    CSSPropertyID::kInsetInlineStart,
    CSSPropertyID::kIsolation,
    CSSPropertyID::kJustifyContent,
    CSSPropertyID::kJustifyItems,
    CSSPropertyID::kJustifySelf,
    CSSPropertyID::kLeft,
    CSSPropertyID::kLetterSpacing,
    CSSPropertyID::kLightingColor,
    CSSPropertyID::kLineBreak,
    CSSPropertyID::kLineGapOverride,
    CSSPropertyID::kLineHeight,
    CSSPropertyID::kListStyleImage,
    CSSPropertyID::kListStylePosition,
    CSSPropertyID::kListStyleType,
    CSSPropertyID::kMarginBlockEnd,
    CSSPropertyID::kMarginBlockStart,
    CSSPropertyID::kMarginBottom,
    CSSPropertyID::kMarginInlineEnd,
    CSSPropertyID::kMarginInlineStart,
    CSSPropertyID::kMarginLeft,
    CSSPropertyID::kMarginRight,
    CSSPropertyID::kMarginTop,
    CSSPropertyID::kMarkerEnd,
    CSSPropertyID::kMarkerMid,
    CSSPropertyID::kMarkerStart,
    CSSPropertyID::kMaskClip,
    CSSPropertyID::kMaskComposite,
    CSSPropertyID::kMaskMode,
    CSSPropertyID::kMaskOrigin,
    CSSPropertyID::kMaskRepeat,
    CSSPropertyID::kMaskSize,
    CSSPropertyID::kMaskType,
    CSSPropertyID::kMathShift,
    CSSPropertyID::kMathStyle,
    CSSPropertyID::kMaxBlockSize,
    CSSPropertyID::kMaxHeight,
    CSSPropertyID::kMaxInlineSize,
    CSSPropertyID::kMaxWidth,
    CSSPropertyID::kMinBlockSize,
    CSSPropertyID::kMinHeight,
    CSSPropertyID::kMinInlineSize,
    CSSPropertyID::kMinWidth,
    CSSPropertyID::kMixBlendMode,
    CSSPropertyID::kNegative,
    CSSPropertyID::kObjectFit,
    CSSPropertyID::kObjectPosition,
    CSSPropertyID::kObjectViewBox,
    CSSPropertyID::kOffsetAnchor,
    CSSPropertyID::kOffsetDistance,
    CSSPropertyID::kOffsetPath,
    CSSPropertyID::kOffsetPosition,
    CSSPropertyID::kOffsetRotate,
    CSSPropertyID::kOpacity,
    CSSPropertyID::kOrder,
    CSSPropertyID::kOrphans,
    CSSPropertyID::kOutlineColor,
    CSSPropertyID::kOutlineOffset,
    CSSPropertyID::kOutlineStyle,
    CSSPropertyID::kOutlineWidth,
    CSSPropertyID::kOverflowAnchor,
    CSSPropertyID::kOverflowClipMargin,
    CSSPropertyID::kOverflowWrap,
    CSSPropertyID::kOverflowX,
    CSSPropertyID::kOverflowY,
    CSSPropertyID::kOverlay,
    CSSPropertyID::kOverrideColors,
    CSSPropertyID::kOverscrollBehaviorBlock,
    CSSPropertyID::kOverscrollBehaviorInline,
    CSSPropertyID::kOverscrollBehaviorX,
    CSSPropertyID::kOverscrollBehaviorY,
    CSSPropertyID::kPad,
    CSSPropertyID::kPaddingBlockEnd,
    CSSPropertyID::kPaddingBlockStart,
    CSSPropertyID::kPaddingBottom,
    CSSPropertyID::kPaddingInlineEnd,
    CSSPropertyID::kPaddingInlineStart,
    CSSPropertyID::kPaddingLeft,
    CSSPropertyID::kPaddingRight,
    CSSPropertyID::kPaddingTop,
    CSSPropertyID::kPage,
    CSSPropertyID::kPageOrientation,
    CSSPropertyID::kPaintOrder,
    CSSPropertyID::kPerspective,
    CSSPropertyID::kPerspectiveOrigin,
    CSSPropertyID::kPointerEvents,
    CSSPropertyID::kPrefix,
    CSSPropertyID::kQuotes,
    CSSPropertyID::kR,
    CSSPropertyID::kRange,
    CSSPropertyID::kResize,
    CSSPropertyID::kRight,
    CSSPropertyID::kRotate,
    CSSPropertyID::kRowGap,
    CSSPropertyID::kRubyPosition,
    CSSPropertyID::kRx,
    CSSPropertyID::kRy,
    CSSPropertyID::kScale,
    CSSPropertyID::kScrollBehavior,
    CSSPropertyID::kScrollMarginBlockEnd,
    CSSPropertyID::kScrollMarginBlockStart,
    CSSPropertyID::kScrollMarginBottom,
    CSSPropertyID::kScrollMarginInlineEnd,
    CSSPropertyID::kScrollMarginInlineStart,
    CSSPropertyID::kScrollMarginLeft,
    CSSPropertyID::kScrollMarginRight,
    CSSPropertyID::kScrollMarginTop,
    CSSPropertyID::kScrollPaddingBlockEnd,
    CSSPropertyID::kScrollPaddingBlockStart,
    CSSPropertyID::kScrollPaddingBottom,
    CSSPropertyID::kScrollPaddingInlineEnd,
    CSSPropertyID::kScrollPaddingInlineStart,
    CSSPropertyID::kScrollPaddingLeft,
    CSSPropertyID::kScrollPaddingRight,
    CSSPropertyID::kScrollPaddingTop,
    CSSPropertyID::kScrollSnapAlign,
    CSSPropertyID::kScrollSnapStop,
    CSSPropertyID::kScrollSnapType,
    CSSPropertyID::kScrollbarGutter,
    CSSPropertyID::kShapeImageThreshold,
    CSSPropertyID::kShapeMargin,
    CSSPropertyID::kShapeOutside,
    CSSPropertyID::kShapeRendering,
    CSSPropertyID::kSize,
    CSSPropertyID::kSizeAdjust,
    CSSPropertyID::kSpeak,
    CSSPropertyID::kSpeakAs,
    CSSPropertyID::kSrc,
    CSSPropertyID::kStopColor,
    CSSPropertyID::kStopOpacity,
    CSSPropertyID::kStroke,
    CSSPropertyID::kStrokeDasharray,
    CSSPropertyID::kStrokeDashoffset,
    CSSPropertyID::kStrokeLinecap,
    CSSPropertyID::kStrokeLinejoin,
    CSSPropertyID::kStrokeMiterlimit,
    CSSPropertyID::kStrokeOpacity,
    CSSPropertyID::kStrokeWidth,
    CSSPropertyID::kSuffix,
    CSSPropertyID::kSymbols,
    CSSPropertyID::kSyntax,
    CSSPropertyID::kSystem,
    CSSPropertyID::kTabSize,
    CSSPropertyID::kTableLayout,
    CSSPropertyID::kTextAlign,
    CSSPropertyID::kTextAlignLast,
    CSSPropertyID::kTextAnchor,
    CSSPropertyID::kTextCombineUpright,
    CSSPropertyID::kTextDecorationColor,
    CSSPropertyID::kTextDecorationLine,
    CSSPropertyID::kTextDecorationSkipInk,
    CSSPropertyID::kTextDecorationStyle,
    CSSPropertyID::kTextDecorationThickness,
    CSSPropertyID::kTextEmphasisColor,
    CSSPropertyID::kTextEmphasisPosition,
    CSSPropertyID::kTextEmphasisStyle,
    CSSPropertyID::kTextIndent,
    CSSPropertyID::kTextOverflow,
    CSSPropertyID::kTextShadow,
    CSSPropertyID::kTextSizeAdjust,
    CSSPropertyID::kTextTransform,
    CSSPropertyID::kTextUnderlineOffset,
    CSSPropertyID::kTextUnderlinePosition,
    CSSPropertyID::kTextWrap,
    CSSPropertyID::kTop,
    CSSPropertyID::kTouchAction,
    CSSPropertyID::kTransform,
    CSSPropertyID::kTransformBox,
    CSSPropertyID::kTransformOrigin,
    CSSPropertyID::kTransformStyle,
    CSSPropertyID::kTransitionBehavior,
    CSSPropertyID::kTransitionDelay,
    CSSPropertyID::kTransitionDuration,
    CSSPropertyID::kTransitionProperty,
    CSSPropertyID::kTransitionTimingFunction,
    CSSPropertyID::kTranslate,
    CSSPropertyID::kUnicodeBidi,
    CSSPropertyID::kUnicodeRange,
    CSSPropertyID::kUserSelect,
    CSSPropertyID::kVectorEffect,
    CSSPropertyID::kVerticalAlign,
    CSSPropertyID::kViewTransitionName,
    CSSPropertyID::kVisibility,
    CSSPropertyID::kWebkitBorderHorizontalSpacing,
    CSSPropertyID::kWebkitBorderImage,
    CSSPropertyID::kWebkitBorderVerticalSpacing,
    CSSPropertyID::kWebkitBoxAlign,
    CSSPropertyID::kWebkitBoxDecorationBreak,
    CSSPropertyID::kWebkitBoxDirection,
    CSSPropertyID::kWebkitBoxFlex,
    CSSPropertyID::kWebkitBoxOrdinalGroup,
    CSSPropertyID::kWebkitBoxOrient,
    CSSPropertyID::kWebkitBoxPack,
    CSSPropertyID::kWebkitBoxReflect,
    CSSPropertyID::kWebkitLineBreak,
    CSSPropertyID::kWebkitLineClamp,
    CSSPropertyID::kWebkitMaskBoxImageOutset,
    CSSPropertyID::kWebkitMaskBoxImageRepeat,
    CSSPropertyID::kWebkitMaskBoxImageSlice,
    CSSPropertyID::kWebkitMaskBoxImageSource,
    CSSPropertyID::kWebkitMaskBoxImageWidth,
    CSSPropertyID::kWebkitMaskPositionX,
    CSSPropertyID::kWebkitMaskPositionY,
    CSSPropertyID::kWebkitPerspectiveOriginX,
    CSSPropertyID::kWebkitPerspectiveOriginY,
    CSSPropertyID::kWebkitPrintColorAdjust,
    CSSPropertyID::kWebkitRtlOrdering,
    CSSPropertyID::kWebkitRubyPosition,
    CSSPropertyID::kWebkitTapHighlightColor,
    CSSPropertyID::kWebkitTextCombine,
    CSSPropertyID::kWebkitTextDecorationsInEffect,
    CSSPropertyID::kWebkitTextFillColor,
    CSSPropertyID::kWebkitTextSecurity,
    CSSPropertyID::kWebkitTextStrokeColor,
    CSSPropertyID::kWebkitTextStrokeWidth,
    CSSPropertyID::kWebkitTransformOriginX,
    CSSPropertyID::kWebkitTransformOriginY,
    CSSPropertyID::kWebkitTransformOriginZ,
    CSSPropertyID::kWebkitUserDrag,
    CSSPropertyID::kWebkitUserModify,
    CSSPropertyID::kWhiteSpaceCollapse,
    CSSPropertyID::kWidows,
    CSSPropertyID::kWidth,
    CSSPropertyID::kWillChange,
    CSSPropertyID::kWordBreak,
    CSSPropertyID::kWordSpacing,
    CSSPropertyID::kX,
    CSSPropertyID::kY,
    CSSPropertyID::kZIndex,
    CSSPropertyID::kBackground,
    CSSPropertyID::kBackgroundPosition,
    CSSPropertyID::kBorder,
    CSSPropertyID::kBorderBlock,
    CSSPropertyID::kBorderBlockColor,
    CSSPropertyID::kBorderBlockEnd,
    CSSPropertyID::kBorderBlockStart,
    CSSPropertyID::kBorderBlockStyle,
    CSSPropertyID::kBorderBlockWidth,
    CSSPropertyID::kBorderBottom,
    CSSPropertyID::kBorderColor,
    CSSPropertyID::kBorderImage,
    CSSPropertyID::kBorderInline,
    CSSPropertyID::kBorderInlineColor,
    CSSPropertyID::kBorderInlineEnd,
    CSSPropertyID::kBorderInlineStart,
    CSSPropertyID::kBorderInlineStyle,
    CSSPropertyID::kBorderInlineWidth,
    CSSPropertyID::kBorderLeft,
    CSSPropertyID::kBorderRadius,
    CSSPropertyID::kBorderRight,
    CSSPropertyID::kBorderSpacing,
    CSSPropertyID::kBorderStyle,
    CSSPropertyID::kBorderTop,
    CSSPropertyID::kBorderWidth,
    CSSPropertyID::kColumnRule,
    CSSPropertyID::kColumns,
    CSSPropertyID::kContainIntrinsicSize,
    CSSPropertyID::kContainer,
    CSSPropertyID::kFlex,
    CSSPropertyID::kFlexFlow,
    CSSPropertyID::kFont,
    CSSPropertyID::kFontSynthesis,
    CSSPropertyID::kFontVariant,
    CSSPropertyID::kGap,
    CSSPropertyID::kGrid,
    CSSPropertyID::kGridArea,
    CSSPropertyID::kGridColumn,
    CSSPropertyID::kGridRow,
    CSSPropertyID::kGridTemplate,
    CSSPropertyID::kInset,
    CSSPropertyID::kInsetBlock,
    CSSPropertyID::kInsetInline,
    CSSPropertyID::kListStyle,
    CSSPropertyID::kMargin,
    CSSPropertyID::kMarginBlock,
    CSSPropertyID::kMarginInline,
    CSSPropertyID::kMarker,
    CSSPropertyID::kMask,
    CSSPropertyID::kMaskPosition,
    CSSPropertyID::kOffset,
    CSSPropertyID::kOutline,
    CSSPropertyID::kOverflow,
    CSSPropertyID::kOverscrollBehavior,
    CSSPropertyID::kPadding,
    CSSPropertyID::kPaddingBlock,
    CSSPropertyID::kPaddingInline,
    CSSPropertyID::kPageBreakAfter,
    CSSPropertyID::kPageBreakBefore,
    CSSPropertyID::kPageBreakInside,
    CSSPropertyID::kPlaceContent,
    CSSPropertyID::kPlaceItems,
    CSSPropertyID::kPlaceSelf,
    CSSPropertyID::kScrollMargin,
    CSSPropertyID::kScrollMarginBlock,
    CSSPropertyID::kScrollMarginInline,
    CSSPropertyID::kScrollPadding,
    CSSPropertyID::kScrollPaddingBlock,
    CSSPropertyID::kScrollPaddingInline,
    CSSPropertyID::kTextDecoration,
    CSSPropertyID::kTextEmphasis,
    CSSPropertyID::kTransition,
    CSSPropertyID::kWebkitColumnBreakAfter,
    CSSPropertyID::kWebkitColumnBreakBefore,
    CSSPropertyID::kWebkitColumnBreakInside,
    CSSPropertyID::kWebkitMaskBoxImage,
    CSSPropertyID::kWebkitTextStroke,
    CSSPropertyID::kWhiteSpace,
    CSSPropertyID::kAliasWebkitAppearance,
    CSSPropertyID::kAliasWebkitAppRegion,
    CSSPropertyID::kAliasWebkitMaskClip,
    CSSPropertyID::kAliasWebkitMaskComposite,
    CSSPropertyID::kAliasWebkitMaskImage,
    CSSPropertyID::kAliasWebkitMaskOrigin,
    CSSPropertyID::kAliasWebkitMaskRepeat,
    CSSPropertyID::kAliasWebkitMaskSize,
    CSSPropertyID::kAliasWebkitBorderEndColor,
    CSSPropertyID::kAliasWebkitBorderEndStyle,
    CSSPropertyID::kAliasWebkitBorderEndWidth,
    CSSPropertyID::kAliasWebkitBorderStartColor,
    CSSPropertyID::kAliasWebkitBorderStartStyle,
    CSSPropertyID::kAliasWebkitBorderStartWidth,
    CSSPropertyID::kAliasWebkitBorderBeforeColor,
    CSSPropertyID::kAliasWebkitBorderBeforeStyle,
    CSSPropertyID::kAliasWebkitBorderBeforeWidth,
    CSSPropertyID::kAliasWebkitBorderAfterColor,
    CSSPropertyID::kAliasWebkitBorderAfterStyle,
    CSSPropertyID::kAliasWebkitBorderAfterWidth,
    CSSPropertyID::kAliasWebkitMarginEnd,
    CSSPropertyID::kAliasWebkitMarginStart,
    CSSPropertyID::kAliasWebkitMarginBefore,
    CSSPropertyID::kAliasWebkitMarginAfter,
    CSSPropertyID::kAliasWebkitPaddingEnd,
    CSSPropertyID::kAliasWebkitPaddingStart,
    CSSPropertyID::kAliasWebkitPaddingBefore,
    CSSPropertyID::kAliasWebkitPaddingAfter,
    CSSPropertyID::kAliasWebkitLogicalWidth,
    CSSPropertyID::kAliasWebkitLogicalHeight,
    CSSPropertyID::kAliasWebkitMinLogicalWidth,
    CSSPropertyID::kAliasWebkitMinLogicalHeight,
    CSSPropertyID::kAliasWebkitMaxLogicalWidth,
    CSSPropertyID::kAliasWebkitMaxLogicalHeight,
    CSSPropertyID::kAliasWebkitBorderAfter,
    CSSPropertyID::kAliasWebkitBorderBefore,
    CSSPropertyID::kAliasWebkitBorderEnd,
    CSSPropertyID::kAliasWebkitBorderStart,
    CSSPropertyID::kAliasWebkitMask,
    CSSPropertyID::kAliasWebkitMaskPosition,
    CSSPropertyID::kAliasEpubCaptionSide,
    CSSPropertyID::kAliasEpubTextCombine,
    CSSPropertyID::kAliasEpubTextEmphasis,
    CSSPropertyID::kAliasEpubTextEmphasisColor,
    CSSPropertyID::kAliasEpubTextEmphasisStyle,
    CSSPropertyID::kAliasEpubTextOrientation,
    CSSPropertyID::kAliasEpubTextTransform,
    CSSPropertyID::kAliasEpubWordBreak,
    CSSPropertyID::kAliasEpubWritingMode,
    CSSPropertyID::kAliasWebkitAlignContent,
    CSSPropertyID::kAliasWebkitAlignItems,
    CSSPropertyID::kAliasWebkitAlignSelf,
    CSSPropertyID::kAliasWebkitAnimationDelay,
    CSSPropertyID::kAliasWebkitAnimationDirection,
    CSSPropertyID::kAliasWebkitAnimationDuration,
    CSSPropertyID::kAliasWebkitAnimationFillMode,
    CSSPropertyID::kAliasWebkitAnimationIterationCount,
    CSSPropertyID::kAliasWebkitAnimationName,
    CSSPropertyID::kAliasWebkitAnimationPlayState,
    CSSPropertyID::kAliasWebkitAnimationTimingFunction,
    CSSPropertyID::kAliasWebkitBackfaceVisibility,
    CSSPropertyID::kAliasWebkitBackgroundClip,
    CSSPropertyID::kAliasWebkitBackgroundOrigin,
    CSSPropertyID::kAliasWebkitBackgroundSize,
    CSSPropertyID::kAliasWebkitBorderBottomLeftRadius,
    CSSPropertyID::kAliasWebkitBorderBottomRightRadius,
    CSSPropertyID::kAliasWebkitBorderRadius,
    CSSPropertyID::kAliasWebkitBorderTopLeftRadius,
    CSSPropertyID::kAliasWebkitBorderTopRightRadius,
    CSSPropertyID::kAliasWebkitBoxShadow,
    CSSPropertyID::kAliasWebkitBoxSizing,
    CSSPropertyID::kAliasWebkitClipPath,
    CSSPropertyID::kAliasWebkitColumnCount,
    CSSPropertyID::kAliasWebkitColumnGap,
    CSSPropertyID::kAliasWebkitColumnRule,
    CSSPropertyID::kAliasWebkitColumnRuleColor,
    CSSPropertyID::kAliasWebkitColumnRuleStyle,
    CSSPropertyID::kAliasWebkitColumnRuleWidth,
    CSSPropertyID::kAliasWebkitColumnSpan,
    CSSPropertyID::kAliasWebkitColumnWidth,
    CSSPropertyID::kAliasWebkitColumns,
    CSSPropertyID::kAliasWebkitFilter,
    CSSPropertyID::kAliasWebkitFlex,
    CSSPropertyID::kAliasWebkitFlexBasis,
    CSSPropertyID::kAliasWebkitFlexDirection,
    CSSPropertyID::kAliasWebkitFlexFlow,
    CSSPropertyID::kAliasWebkitFlexGrow,
    CSSPropertyID::kAliasWebkitFlexShrink,
    CSSPropertyID::kAliasWebkitFlexWrap,
    CSSPropertyID::kAliasWebkitFontFeatureSettings,
    CSSPropertyID::kAliasWebkitHyphenateCharacter,
    CSSPropertyID::kAliasWebkitJustifyContent,
    CSSPropertyID::kAliasWebkitOpacity,
    CSSPropertyID::kAliasWebkitOrder,
    CSSPropertyID::kAliasWebkitPerspective,
    CSSPropertyID::kAliasWebkitPerspectiveOrigin,
    CSSPropertyID::kAliasWebkitShapeImageThreshold,
    CSSPropertyID::kAliasWebkitShapeMargin,
    CSSPropertyID::kAliasWebkitShapeOutside,
    CSSPropertyID::kAliasWebkitTextEmphasis,
    CSSPropertyID::kAliasWebkitTextEmphasisColor,
    CSSPropertyID::kAliasWebkitTextEmphasisPosition,
    CSSPropertyID::kAliasWebkitTextEmphasisStyle,
    CSSPropertyID::kAliasWebkitTextSizeAdjust,
    CSSPropertyID::kAliasWebkitTransform,
    CSSPropertyID::kAliasWebkitTransformOrigin,
    CSSPropertyID::kAliasWebkitTransformStyle,
    CSSPropertyID::kAliasWebkitTransition,
    CSSPropertyID::kAliasWebkitTransitionDelay,
    CSSPropertyID::kAliasWebkitTransitionDuration,
    CSSPropertyID::kAliasWebkitTransitionProperty,
    CSSPropertyID::kAliasWebkitTransitionTimingFunction,
    CSSPropertyID::kAliasWebkitUserSelect,
    CSSPropertyID::kAliasWordWrap,
    CSSPropertyID::kAliasGridColumnGap,
    CSSPropertyID::kAliasGridRowGap,
    CSSPropertyID::kAliasGridGap,
} };

const CSSBitset kSurrogateProperties{ {
    CSSPropertyID::kWebkitTextOrientation,
    CSSPropertyID::kWebkitWritingMode,
    CSSPropertyID::kBlockSize,
    CSSPropertyID::kBorderBlockEndColor,
    CSSPropertyID::kBorderBlockEndStyle,
    CSSPropertyID::kBorderBlockEndWidth,
    CSSPropertyID::kBorderBlockStartColor,
    CSSPropertyID::kBorderBlockStartStyle,
    CSSPropertyID::kBorderBlockStartWidth,
    CSSPropertyID::kBorderEndEndRadius,
    CSSPropertyID::kBorderEndStartRadius,
    CSSPropertyID::kBorderInlineEndColor,
    CSSPropertyID::kBorderInlineEndStyle,
    CSSPropertyID::kBorderInlineEndWidth,
    CSSPropertyID::kBorderInlineStartColor,
    CSSPropertyID::kBorderInlineStartStyle,
    CSSPropertyID::kBorderInlineStartWidth,
    CSSPropertyID::kBorderStartEndRadius,
    CSSPropertyID::kBorderStartStartRadius,
    CSSPropertyID::kContainIntrinsicBlockSize,
    CSSPropertyID::kContainIntrinsicInlineSize,
    CSSPropertyID::kInlineSize,
    CSSPropertyID::kInsetBlockEnd,
    CSSPropertyID::kInsetBlockStart,
    CSSPropertyID::kInsetInlineEnd,
    CSSPropertyID::kInsetInlineStart,
    CSSPropertyID::kInternalOverflowBlock,
    CSSPropertyID::kInternalOverflowInline,
    CSSPropertyID::kInternalVisitedBorderBlockEndColor,
    CSSPropertyID::kInternalVisitedBorderBlockStartColor,
    CSSPropertyID::kInternalVisitedBorderInlineEndColor,
    CSSPropertyID::kInternalVisitedBorderInlineStartColor,
    CSSPropertyID::kLineBreak,
    CSSPropertyID::kMarginBlockEnd,
    CSSPropertyID::kMarginBlockStart,
    CSSPropertyID::kMarginInlineEnd,
    CSSPropertyID::kMarginInlineStart,
    CSSPropertyID::kMaxBlockSize,
    CSSPropertyID::kMaxInlineSize,
    CSSPropertyID::kMinBlockSize,
    CSSPropertyID::kMinInlineSize,
    CSSPropertyID::kOverflowBlock,
    CSSPropertyID::kOverflowInline,
    CSSPropertyID::kOverscrollBehaviorBlock,
    CSSPropertyID::kOverscrollBehaviorInline,
    CSSPropertyID::kPaddingBlockEnd,
    CSSPropertyID::kPaddingBlockStart,
    CSSPropertyID::kPaddingInlineEnd,
    CSSPropertyID::kPaddingInlineStart,
    CSSPropertyID::kScrollMarginBlockEnd,
    CSSPropertyID::kScrollMarginBlockStart,
    CSSPropertyID::kScrollMarginInlineEnd,
    CSSPropertyID::kScrollMarginInlineStart,
    CSSPropertyID::kScrollPaddingBlockEnd,
    CSSPropertyID::kScrollPaddingBlockStart,
    CSSPropertyID::kScrollPaddingInlineEnd,
    CSSPropertyID::kScrollPaddingInlineStart,
    CSSPropertyID::kScrollStartBlock,
    CSSPropertyID::kScrollStartInline,
    CSSPropertyID::kScrollStartTargetBlock,
    CSSPropertyID::kScrollStartTargetInline,
    CSSPropertyID::kWebkitRubyPosition,
    CSSPropertyID::kWebkitTextCombine,
    CSSPropertyID::kBorderBlockEnd,
    CSSPropertyID::kBorderBlockStart,
    CSSPropertyID::kBorderInlineEnd,
    CSSPropertyID::kBorderInlineStart,
    CSSPropertyID::kAliasWebkitBorderEndColor,
    CSSPropertyID::kAliasWebkitBorderEndStyle,
    CSSPropertyID::kAliasWebkitBorderEndWidth,
    CSSPropertyID::kAliasWebkitBorderStartColor,
    CSSPropertyID::kAliasWebkitBorderStartStyle,
    CSSPropertyID::kAliasWebkitBorderStartWidth,
    CSSPropertyID::kAliasWebkitBorderBeforeColor,
    CSSPropertyID::kAliasWebkitBorderBeforeStyle,
    CSSPropertyID::kAliasWebkitBorderBeforeWidth,
    CSSPropertyID::kAliasWebkitBorderAfterColor,
    CSSPropertyID::kAliasWebkitBorderAfterStyle,
    CSSPropertyID::kAliasWebkitBorderAfterWidth,
    CSSPropertyID::kAliasWebkitMarginEnd,
    CSSPropertyID::kAliasWebkitMarginStart,
    CSSPropertyID::kAliasWebkitMarginBefore,
    CSSPropertyID::kAliasWebkitMarginAfter,
    CSSPropertyID::kAliasWebkitPaddingEnd,
    CSSPropertyID::kAliasWebkitPaddingStart,
    CSSPropertyID::kAliasWebkitPaddingBefore,
    CSSPropertyID::kAliasWebkitPaddingAfter,
    CSSPropertyID::kAliasWebkitLogicalWidth,
    CSSPropertyID::kAliasWebkitLogicalHeight,
    CSSPropertyID::kAliasWebkitMinLogicalWidth,
    CSSPropertyID::kAliasWebkitMinLogicalHeight,
    CSSPropertyID::kAliasWebkitMaxLogicalWidth,
    CSSPropertyID::kAliasWebkitMaxLogicalHeight,
    CSSPropertyID::kAliasWebkitBorderAfter,
    CSSPropertyID::kAliasWebkitBorderBefore,
    CSSPropertyID::kAliasWebkitBorderEnd,
    CSSPropertyID::kAliasWebkitBorderStart,
    CSSPropertyID::kAliasEpubTextCombine,
    CSSPropertyID::kAliasEpubTextOrientation,
    CSSPropertyID::kAliasEpubWritingMode,
} };

}  // namespace blink
