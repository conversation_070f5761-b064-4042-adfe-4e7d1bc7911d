// Generated by third_party/blink/renderer/build/scripts/gperf.py
/* C++ code produced by gperf version 3.0.3 */
/* Command-line: /Applications/Xcode-15.4.0.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/gperf --key-positions='*' -D -s 2  */

#if !((' ' == 32) && ('!' == 33) && ('"' == 34) && ('#' == 35) \
      && ('%' == 37) && ('&' == 38) && ('\'' == 39) && ('(' == 40) \
      && (')' == 41) && ('*' == 42) && ('+' == 43) && (',' == 44) \
      && ('-' == 45) && ('.' == 46) && ('/' == 47) && ('0' == 48) \
      && ('1' == 49) && ('2' == 50) && ('3' == 51) && ('4' == 52) \
      && ('5' == 53) && ('6' == 54) && ('7' == 55) && ('8' == 56) \
      && ('9' == 57) && (':' == 58) && (';' == 59) && ('<' == 60) \
      && ('=' == 61) && ('>' == 62) && ('?' == 63) && ('A' == 65) \
      && ('B' == 66) && ('C' == 67) && ('D' == 68) && ('E' == 69) \
      && ('F' == 70) && ('G' == 71) && ('H' == 72) && ('I' == 73) \
      && ('J' == 74) && ('K' == 75) && ('L' == 76) && ('M' == 77) \
      && ('N' == 78) && ('O' == 79) && ('P' == 80) && ('Q' == 81) \
      && ('R' == 82) && ('S' == 83) && ('T' == 84) && ('U' == 85) \
      && ('V' == 86) && ('W' == 87) && ('X' == 88) && ('Y' == 89) \
      && ('Z' == 90) && ('[' == 91) && ('\\' == 92) && (']' == 93) \
      && ('^' == 94) && ('_' == 95) && ('a' == 97) && ('b' == 98) \
      && ('c' == 99) && ('d' == 100) && ('e' == 101) && ('f' == 102) \
      && ('g' == 103) && ('h' == 104) && ('i' == 105) && ('j' == 106) \
      && ('k' == 107) && ('l' == 108) && ('m' == 109) && ('n' == 110) \
      && ('o' == 111) && ('p' == 112) && ('q' == 113) && ('r' == 114) \
      && ('s' == 115) && ('t' == 116) && ('u' == 117) && ('v' == 118) \
      && ('w' == 119) && ('x' == 120) && ('y' == 121) && ('z' == 122) \
      && ('{' == 123) && ('|' == 124) && ('}' == 125) && ('~' == 126))
/* The character set is not based on ISO-646.  */
#error "gperf generated tables don't work with this execution character set. Please report a bug to <<EMAIL>>."
#endif


#include "third_party/blink/renderer/platform/graphics/color.h"
#include <string.h>

#pragma clang diagnostic ignored "-Wshorten-64-to-32"

namespace blink {

enum
  {
    TOTAL_KEYWORDS = 151,
    MIN_WORD_LENGTH = 3,
    MAX_WORD_LENGTH = 20,
    MIN_HASH_VALUE = 3,
    MAX_HASH_VALUE = 1055
  };

/* maximum key range = 1053, duplicates = 0 */

class ColorDataHash
{
private:
  static inline unsigned int colordata_hash_function (const char *str, unsigned int len);
public:
  static const struct NamedColor *findColorImpl (const char *str, unsigned int len);
};

inline unsigned int
ColorDataHash::colordata_hash_function (const char *str, unsigned int len)
{
  static const unsigned short asso_values[] =
    {
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056,    5,    0,  105,
         0,    0,   30,   40,   40,   10,    0,    0,   15,   60,
         0,    5,  255,   40,    0,   10,   15,  130,  300,  215,
         5,    0,    0, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056, 1056,
      1056, 1056, 1056, 1056, 1056, 1056, 1056
    };
  unsigned int hval = len;

  switch (hval)
    {
      default:
        hval += asso_values[(unsigned char)str[19]];
        [[fallthrough]];
      case 19:
        hval += asso_values[(unsigned char)str[18]];
        [[fallthrough]];
      case 18:
        hval += asso_values[(unsigned char)str[17]];
        [[fallthrough]];
      case 17:
        hval += asso_values[(unsigned char)str[16]];
        [[fallthrough]];
      case 16:
        hval += asso_values[(unsigned char)str[15]];
        [[fallthrough]];
      case 15:
        hval += asso_values[(unsigned char)str[14]];
        [[fallthrough]];
      case 14:
        hval += asso_values[(unsigned char)str[13]];
        [[fallthrough]];
      case 13:
        hval += asso_values[(unsigned char)str[12]];
        [[fallthrough]];
      case 12:
        hval += asso_values[(unsigned char)str[11]];
        [[fallthrough]];
      case 11:
        hval += asso_values[(unsigned char)str[10]+1];
        [[fallthrough]];
      case 10:
        hval += asso_values[(unsigned char)str[9]];
        [[fallthrough]];
      case 9:
        hval += asso_values[(unsigned char)str[8]];
        [[fallthrough]];
      case 8:
        hval += asso_values[(unsigned char)str[7]];
        [[fallthrough]];
      case 7:
        hval += asso_values[(unsigned char)str[6]];
        [[fallthrough]];
      case 6:
        hval += asso_values[(unsigned char)str[5]];
        [[fallthrough]];
      case 5:
        hval += asso_values[(unsigned char)str[4]];
        [[fallthrough]];
      case 4:
        hval += asso_values[(unsigned char)str[3]];
        [[fallthrough]];
      case 3:
        hval += asso_values[(unsigned char)str[2]];
        [[fallthrough]];
      case 2:
        hval += asso_values[(unsigned char)str[1]];
        [[fallthrough]];
      case 1:
        hval += asso_values[(unsigned char)str[0]];
        break;
    }
  return hval;
}

static const struct NamedColor wordlist[] =
  {
    {"red", 0xffff0000},
    {"darkred", 0xff8b0000},
    {"tan", 0xffd2b48c},
    {"linen", 0xfffaf0e6},
    {"sienna", 0xffa0522d},
    {"indianred", 0xffcd5c5c},
    {"teal", 0xff008080},
    {"grey", 0xff808080},
    {"green", 0xff008000},
    {"gray", 0xff808080},
    {"darkgrey", 0xffa9a9a9},
    {"darkgreen", 0xff006400},
    {"beige", 0xfff5f5dc},
    {"orange", 0xffffa500},
    {"darkgray", 0xffa9a9a9},
    {"orangered", 0xffff4500},
    {"khaki", 0xfff0e68c},
    {"seagreen", 0xff2e8b57},
    {"gold", 0xffffd700},
    {"darkorange", 0xffff8c00},
    {"darkkhaki", 0xffbdb76b},
    {"indigo", 0xff4b0082},
    {"goldenrod", 0xffdaa520},
    {"maroon", 0xff800000},
    {"gainsboro", 0xffdcdcdc},
    {"lime", 0xff00ff00},
    {"greenyellow", 0xffadff2f},
    {"darkgoldenrod", 0xffb8860b},
    {"slategrey", 0xff708090},
    {"slategray", 0xff708090},
    {"salmon", 0xfffa8072},
    {"darkseagreen", 0xff8fbc8f},
    {"seashell", 0xfffff5ee},
    {"darksalmon", 0xffe9967a},
    {"tomato", 0xffff6347},
    {"thistle", 0xffd8bfd8},
    {"darkslategrey", 0xff2f4f4f},
    {"cyan", 0xff00ffff},
    {"forestgreen", 0xff228b22},
    {"dimgrey", 0xff696969},
    {"darkslategray", 0xff2f4f4f},
    {"mistyrose", 0xffffe4e1},
    {"dimgray", 0xff696969},
    {"darkcyan", 0xff008b8b},
    {"black", 0xff000000},
    {"magenta", 0xffff00ff},
    {"limegreen", 0xff32cd32},
    {"coral", 0xffff7f50},
    {"darkmagenta", 0xff8b008b},
    {"azure", 0xfff0ffff},
    {"blue", 0xff0000ff},
    {"oldlace", 0xfffdf5e6},
    {"cornsilk", 0xfffff8dc},
    {"darkblue", 0xff00008b},
    {"skyblue", 0xff87ceeb},
    {"firebrick", 0xffb22222},
    {"orchid", 0xffda70d6},
    {"lightgrey", 0xffd3d3d3},
    {"lightgreen", 0xff90ee90},
    {"lightyellow", 0xffffffe0},
    {"lightgray", 0xffd3d3d3},
    {"darkorchid", 0xff9932cc},
    {"royalblue", 0xff4169e1},
    {"aqua", 0xff00ffff},
    {"steelblue", 0xff4682b4},
    {"bisque", 0xffffe4c4},
    {"crimson", 0xffdc143c},
    {"slateblue", 0xff6a5acd},
    {"dodgerblue", 0xff1e90ff},
    {"blanchedalmond", 0xffffebcd},
    {"lightseagreen", 0xff20b2aa},
    {"lightslategrey", 0xff778899},
    {"lightslategray", 0xff778899},
    {"brown", 0xffa52a2a},
    {"lightsalmon", 0xffffa07a},
    {"snow", 0xfffffafa},
    {"lightcyan", 0xffe0ffff},
    {"rosybrown", 0xffbc8f8f},
    {"sandybrown", 0xfff4a460},
    {"darkslateblue", 0xff483d8b},
    {"yellow", 0xffffff00},
    {"lightcoral", 0xfff08080},
    {"mintcream", 0xfff5fffa},
    {"aquamarine", 0xff7fffd4},
    {"saddlebrown", 0xff8b4513},
    {"honeydew", 0xfff0fff0},
    {"pink", 0xffffc0cb},
    {"lightblue", 0xffadd8e6},
    {"cadetblue", 0xff5f9ea0},
    {"wheat", 0xfff5deb3},
    {"lawngreen", 0xff7cfc00},
    {"white", 0xffffffff},
    {"aliceblue", 0xfff0f8ff},
    {"chocolate", 0xffd2691e},
    {"yellowgreen", 0xff9acd32},
    {"moccasin", 0xffffe4b5},
    {"navy", 0xff000080},
    {"chartreuse", 0xff7fff00},
    {"ivory", 0xfffffff0},
    {"palegreen", 0xff98fb98},
    {"lavender", 0xffe6e6fa},
    {"hotpink", 0xffff69b4},
    {"olive", 0xff808000},
    {"fuchsia", 0xffff00ff},
    {"mediumseagreen", 0xff3cb371},
    {"silver", 0xffc0c0c0},
    {"olivedrab", 0xff6b8e23},
    {"darkturquoise", 0xff00ced1},
    {"turquoise", 0xff40e0d0},
    {"violet", 0xffee82ee},
    {"violetred", 0xffd02090},
    {"darkviolet", 0xff9400d3},
    {"palegoldenrod", 0xffeee8aa},
    {"whitesmoke", 0xfff5f5f5},
    {"springgreen", 0xff00ff7f},
    {"burlywood", 0xffdeb887},
    {"peru", 0xffcd853f},
    {"floralwhite", 0xfffffaf0},
    {"lightpink", 0xffffb6c1},
    {"darkolivegreen", 0xff556b2f},
    {"ghostwhite", 0xfff8f8ff},
    {"mediumblue", 0xff0000cd},
    {"mediumorchid", 0xffba55d3},
    {"lightsteelblue", 0xffb0c4de},
    {"lightslateblue", 0xff8470ff},
    {"transparent", 0x00000000},
    {"deepskyblue", 0xff00bfff},
    {"lightskyblue", 0xff87cefa},
    {"lightgoldenrodyellow", 0xfffafad2},
    {"plum", 0xffdda0dd},
    {"mediumaquamarine", 0xff66cdaa},
    {"mediumslateblue", 0xff7b68ee},
    {"blueviolet", 0xff8a2be2},
    {"midnightblue", 0xff191970},
    {"deeppink", 0xffff1493},
    {"lemonchiffon", 0xfffffacd},
    {"antiquewhite", 0xfffaebd7},
    {"paleturquoise", 0xffafeeee},
    {"powderblue", 0xffb0e0e6},
    {"navajowhite", 0xffffdead},
    {"mediumspringgreen", 0xff00fa9a},
    {"cornflowerblue", 0xff6495ed},
    {"palevioletred", 0xffdb7093},
    {"mediumvioletred", 0xffc71585},
    {"purple", 0xff800080},
    {"rebeccapurple", 0xff663399},
    {"lavenderblush", 0xfffff0f5},
    {"mediumturquoise", 0xff48d1cc},
    {"peachpuff", 0xffffdab9},
    {"mediumpurple", 0xff9370db},
    {"papayawhip", 0xffffefd5}
  };

static const short lookup[] =
  {
     -1,  -1,  -1,   0,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,   1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,   2,  -1,  -1,  -1,  -1,  -1,  -1,
      3,   4,  -1,  -1,   5,  -1,  -1,  -1,  -1,   6,
     -1,  -1,  -1,  -1,   7,   8,  -1,  -1,  -1,   9,
     -1,  -1,  -1,  10,  11,  12,  13,  -1,  14,  15,
     16,  -1,  -1,  17,  18,  19,  -1,  -1,  -1,  20,
     -1,  21,  -1,  -1,  22,  -1,  -1,  -1,  -1,  -1,
     -1,  23,  -1,  -1,  24,  -1,  -1,  -1,  -1,  25,
     -1,  26,  -1,  27,  28,  -1,  -1,  -1,  -1,  29,
     -1,  30,  31,  32,  -1,  -1,  -1,  -1,  -1,  -1,
     33,  34,  35,  36,  37,  -1,  38,  39,  40,  41,
     -1,  -1,  42,  43,  -1,  -1,  -1,  -1,  -1,  -1,
     44,  -1,  45,  -1,  46,  47,  48,  -1,  -1,  -1,
     49,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  50,
     -1,  -1,  51,  52,  -1,  -1,  -1,  -1,  53,  -1,
     -1,  -1,  54,  -1,  55,  -1,  56,  -1,  -1,  57,
     58,  59,  -1,  -1,  60,  61,  -1,  -1,  -1,  62,
     -1,  -1,  -1,  -1,  63,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  64,  -1,  65,  66,  -1,  67,
     68,  -1,  -1,  -1,  69,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  70,  71,
     -1,  -1,  -1,  -1,  72,  73,  -1,  -1,  -1,  -1,
     -1,  74,  -1,  -1,  75,  -1,  -1,  -1,  -1,  76,
     -1,  -1,  -1,  -1,  77,  78,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  79,  -1,  -1,  80,  -1,  -1,  -1,
     81,  -1,  -1,  -1,  82,  83,  84,  -1,  85,  86,
     -1,  -1,  -1,  -1,  87,  -1,  -1,  -1,  -1,  88,
     89,  -1,  -1,  -1,  90,  91,  -1,  -1,  -1,  92,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  93,  -1,  94,  -1,  95,  96,
     -1,  -1,  -1,  -1,  -1,  97,  -1,  -1,  -1,  -1,
     98,  -1,  -1,  -1,  99,  -1,  -1,  -1, 100,  -1,
     -1,  -1, 101,  -1,  -1, 102,  -1, 103,  -1, 104,
     -1, 105,  -1,  -1, 106,  -1,  -1,  -1, 107, 108,
     -1, 109,  -1,  -1, 110,  -1,  -1,  -1,  -1,  -1,
    111,  -1,  -1, 112,  -1, 113,  -1,  -1,  -1,  -1,
     -1, 114,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 115,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 116,
     -1, 117,  -1,  -1, 118,  -1,  -1,  -1,  -1, 119,
    120,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1, 121,  -1,  -1,  -1,  -1,
     -1,  -1, 122,  -1, 123,  -1,  -1,  -1,  -1, 124,
     -1, 125,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1, 126,  -1,  -1,  -1,  -1,  -1, 127,  -1,  -1,
    128,  -1,  -1,  -1, 129,  -1,  -1,  -1,  -1,  -1,
     -1, 130,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1, 131,  -1,  -1,  -1,  -1,
    132,  -1, 133,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 134,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1, 135,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1, 136,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 137,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
    138,  -1,  -1,  -1,  -1,  -1, 139, 140,  -1, 141,
     -1,  -1,  -1, 142,  -1,  -1,  -1,  -1,  -1,  -1,
    143,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1, 144,  -1,  -1,  -1,  -1,  -1,  -1, 145,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 146,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1, 147,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1, 148,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1, 149,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,  -1,
     -1,  -1,  -1,  -1,  -1, 150
  };

const struct NamedColor *
ColorDataHash::findColorImpl (const char *str, unsigned int len)
{
  if (len <= MAX_WORD_LENGTH && len >= MIN_WORD_LENGTH)
    {
      unsigned int key = colordata_hash_function (str, len);

      if (key <= MAX_HASH_VALUE)
        {
          int index = lookup[key];

          if (index >= 0)
            {
              const char *s = wordlist[index].name;

              if (*str == *s && !strncmp (str + 1, s + 1, len - 1) && s[len] == '\0')
                return &wordlist[index];
            }
        }
    }
  return 0;
}


const struct NamedColor* FindColor(const char* str, unsigned len) {
  return ColorDataHash::findColorImpl(str, len);
}

} // namespace blink
