/*
 * Copyright (C) 2013 Google Inc. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are
 * met:
 *
 *     * Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above
 * copyright notice, this list of conditions and the following disclaimer
 * in the documentation and/or other materials provided with the
 * distribution.
 *     * Neither the name of Google Inc. nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 * A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 * OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, E<PERSON><PERSON><PERSON>AR<PERSON>, OR CO<PERSON>EQUENTIAL DAMAGES (INCLUDING, BUT NOT
 * LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "third_party/blink/renderer/platform/wtf/allocator/partitions.h"
#include "memory_monitor.h"
#ifdef __APPLE__
#include <malloc/malloc.h>
#endif
#include <stdlib.h>

namespace WTF {

// static
void* Partitions::BufferMalloc(size_t n, const char* type_name) {
  void *p = ::malloc(n);
#ifdef __APPLE__
  MEMORY_MALLOC(WTFMalloc, ::malloc_size(p));
#endif
  return p;
}

// static
void* Partitions::BufferTryRealloc(void* p, size_t n, const char* type_name) {
#if defined(__APPLE__) && defined(ENABLE_MEMORY_MONITOR)
  auto old_size = ::malloc_size(p);
  void *new_p = ::realloc(p, n);
  MEMORY_REALLOC(WTFMalloc, old_size, ::malloc_size(new_p));
#else
  void *new_p = ::realloc(p, n);
#endif
  return new_p;
}

// static
void Partitions::BufferFree(void* p) {
#ifdef __APPLE__
  MEMORY_FREE(WTFMalloc, ::malloc_size(p));
#endif
  ::free(p);
}

// Ideally this would be removed when PartitionAlloc is malloc(), but there are
// quite a few callers. Just forward to the C functions instead.  Most of the
// usual callers will never reach here though, as USING_FAST_MALLOC() becomes a
// no-op.
// static
void* Partitions::FastMalloc(size_t n, const char* type_name) {
  void *p = ::malloc(n);
#ifdef __APPLE__
  MEMORY_MALLOC(WTFMalloc, ::malloc_size(p));
#endif
  return p;
}

// static
void* Partitions::FastZeroedMalloc(size_t n, const char* type_name) {
  void *p = ::calloc(n, 1);
#ifdef __APPLE__
  MEMORY_MALLOC(WTFMalloc, ::malloc_size(p));
#endif
  return p;
}

// static
void Partitions::FastFree(void* p) {
#ifdef __APPLE__
  MEMORY_FREE(WTFMalloc, ::malloc_size(p));
#endif
  ::free(p);
}

}  // namespace WTF
