// Generated by build/write_buildflag_header.py
// From "//third_party/blink/renderer/platform:bindings_buildflags"

#ifndef THIRD_PARTY_BLINK_RENDERER_PLATFORM_BINDINGS_BUILDFLAGS_H_
#define THIRD_PARTY_BLINK_RENDERER_PLATFORM_BINDINGS_BUILDFLAGS_H_

#include "build/buildflag.h" // IWYU pragma: export

#define BUILDFLAG_INTERNAL_RCS_COUNT_EVERYTHING() (0)
#define BUILDFLAG_INTERNAL_BLINK_BINDINGS_TRACE_ENABLED() (0)
#define BUILDFLAG_INTERNAL_BLINK_BINDINGS_COOPERATIVE_SCHEDULING_ENABLED() (0)
#define BUILDFLAG_INTERNAL_HAS_ZSTD_COMPRESSION() (1)

#endif  // THIRD_PARTY_BLINK_RENDERER_PLATFORM_BINDINGS_BUILDFLAGS_H_
