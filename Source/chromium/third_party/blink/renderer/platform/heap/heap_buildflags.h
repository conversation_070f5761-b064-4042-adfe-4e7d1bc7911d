// Generated by build/write_buildflag_header.py
// From "//third_party/blink/renderer/platform/heap:blink_heap_buildflags"

#ifndef THIRD_PARTY_BLINK_RENDERER_PLATFORM_HEAP_HEAP_BUILDFLAGS_H_
#define THIRD_PARTY_BLINK_RENDERER_PLATFORM_HEAP_HEAP_BUILDFLAGS_H_

#include "build/buildflag.h" // IWYU pragma: export

#define BUILDFLAG_INTERNAL_VERBOSE_PERSISTENT() (0)
#define BUILDFLAG_INTERNAL_BLINK_HEAP_INSIDE_SHARED_LIBRARY() (1)

#endif  // THIRD_PARTY_BLINK_RENDERER_PLATFORM_HEAP_HEAP_BUILDFLAGS_H_
