/*
 * Copyright (C) 2011 Google, Inc.  All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY APPLE COMPUTER, INC. ``AS IS'' AND ANY
 * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF ME<PERSON>HANT<PERSON><PERSON>ITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL APPLE COMPUTER, INC. OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 * PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY
 * OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef THIRD_PARTY_BLINK_RENDERER_PLATFORM_TEXT_UNICODE_BIDI_H_
#define THIRD_PARTY_BLINK_RENDERER_PLATFORM_TEXT_UNICODE_BIDI_H_

namespace blink {

enum class UnicodeBidi : unsigned {
  kNormal,
  kEmbed,
  kBidiOverride,
  kIsolate,
  kPlaintext,
  kIsolateOverride,
};

inline bool IsIsolated(const UnicodeBidi& unicode_bidi) {
  return unicode_bidi == UnicodeBidi::kIsolate ||
         unicode_bidi == UnicodeBidi::kIsolateOverride ||
         unicode_bidi == UnicodeBidi::kPlaintext;
}

inline bool IsOverride(UnicodeBidi unicode_bidi) {
  return unicode_bidi == UnicodeBidi::kBidiOverride ||
         unicode_bidi == UnicodeBidi::kIsolateOverride;
}

}  // namespace blink

#endif  // THIRD_PARTY_BLINK_RENDERER_PLATFORM_TEXT_UNICODE_BIDI_H_
