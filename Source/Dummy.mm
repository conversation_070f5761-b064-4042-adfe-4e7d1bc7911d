
#include "atomic_string.h"

#include <iostream>
#include <memory>
#include <string>
#include <pthread.h>

#include "wtf_string.h"
#include "css_parser.h"
#include "css_parser_mode.h"
#include "security_context.h"
#include "thread_state_storage.h"
#include "heap-handle.h"
#include "allocation.h"
#include "custom_spaces.h"
#include "include/cppgc/default-platform.h"
#include "string_impl.h"
#include "string_statics.h"
#include "css_property_value_set.h"
#include "css_property_names.h"
#include "css_primitive_value.h"
#include "css_style_sheet.h"
#include "style_sheet_contents.h"
#include "style_rule.h"
#include "third_party/blink/renderer/platform/wtf/text/atomic_string.h"
#include "casting.h"
#include "document.h"
#include "html_element.h"
#include "html_names.h"
#include "style_recalc_root.h"
#include "style_engine.h"
#include "active_style_sheets.h"
#include "css_selector.h"
#include "css_parser_token_range.h"
#include "layout_block.h"
#include "constraint_space_builder.h"
#include "logical_size.h"
#include "length.h"
#import "thread_state.h"
#include "device.h"

#import "mtdocument.h"
#import "hash_traits.h"
#import "worker_thread.h"
#include <chrono>
#include <MSCRenderer/performance_monitor.h>

#include "msc/wxs/js_engine.h"

void getCurrentThreadStackSize() {
  pthread_attr_t attr;
  size_t stacksize;

  // 初始化属性对象
  pthread_attr_init(&attr);

  // 获取栈大小
  pthread_attr_getstacksize(&attr, &stacksize);

  // 使用完后销毁属性对象
  pthread_attr_destroy(&attr);
}

using namespace WTF;

using namespace blink;

void T(LayoutObject *layout, int depth=0) {
    if (depth == 0) {
        printf("print tree:\n");
    }
    for (int i=0; i<depth;i++) {
        printf("  ");
    }
    auto elem = To<Element>(layout->GetNode());
    printf("%s (addr=%p nl=%d cnl=%d)\n", elem->GetIdAttribute().Utf8().c_str(), layout, layout->SelfNeedsFullLayout(), layout->ChildNeedsFullLayout());
    if (To<LayoutBox>(layout) != nullptr) {
        if (layout->VirtualChildren()->FirstChild()) {
            auto clayout = layout->VirtualChildren()->FirstChild();
            while (clayout) {
                T(clayout, depth + 1);
                clayout = clayout->NextSibling();
            }
        }
    }
}

void TestJSEngine() {
//    mt::JSEngine engine(nullptr);
//    
//    std::string script_str = "NativeBridge.importScripts([\"111\",\"222\",\"333\"]);\
//    NativeBridge.invoke(\"1\",\"2\",\"3\");\
//    NativeBridge.invoke(\"4\",\"5\",\"6\");\
//    NativeBridge.nativeCallSyncHook(\"1\",\"2\",\"3\",\"4\");\
//    NativeTiming.createTimer(1, 2, 3, 4);\
//    NativeTiming.deleteTimer(1);\
//    nativeLoggingHook(\"hello\", 1);\
//    WxsJSBridge.invoke = (a, b) => {\
//    let ret = `args: ${a}, ${b}`;\
//    return ret;\
//    }";
//    auto script_buffer = std::make_shared<facebook::jsi::StringBuffer>(std::move(script_str));
//    auto ret = engine.EvaluateJavaScript(script_buffer, "test.js");
//    facebook::jsi::Value args[2] = {
//        facebook::jsi::Value(42),
//        facebook::jsi::String::createFromUtf8(*engine.runtime_, "lalala")
//    };
//    auto ret2 = engine.WxsJSBridge_invoke(args, 2);
//    std::cout << "WxsJSBridge.invoke return: " << ret2.asString(*engine.runtime_).utf8(*engine.runtime_) << "\n";
}

void Test()
{
  WTF::AtomicString::Init();
  WTF::StringStatics::Init();
  blink::html_names::Init();
  blink::CSSParserTokenRange::InitStaticEOFToken();
  blink::Length::Initialize();

  auto cppgc_platform = std::make_shared<cppgc::DefaultPlatform>();
  cppgc::InitializeProcess(cppgc_platform->GetPageAllocator());
  {
    blink::ThreadState::AttachCurrentThread();
    
    // inline
//    String str = String::FromUTF8("height:200px");
//    auto styles = blink::CSSParser::ParseInlineStyleDeclaration(str, blink::kHTMLStandardMode, blink::SecureContextMode::kSecureContext, nullptr);
//    auto val = styles->GetPropertyCSSValue(blink::CSSPropertyID::kHeight);
//    const blink::CSSPrimitiveValue* primitive_value = (blink::CSSPrimitiveValue*)val;
//    double value = primitive_value->GetDoubleValue(); // 获取以像素为单位的数值
//    printf("Width value in pixels: %f\n", value);
    
    // sheet
//    auto sheet = blink::CSSStyleSheet::CreateInline();
//    String str = String::FromUTF8("#mystyle {width:100px;}");
//    blink::CSSParserContext ctx(blink::kHTMLStandardMode, blink::SecureContextMode::kSecureContext);
//    blink::CSSParser::ParseSheet(&ctx, sheet->Contents(), str);
//    for (auto rule : sheet->Contents()->ChildRules()) {
//      blink::StyleRuleBase *r = rule.Get();
//      blink::StyleRule* rr = To<blink::StyleRule>(r);
//      auto &styles = rr->Properties();
//      auto val = styles.GetPropertyCSSValue(blink::CSSPropertyID::kWidth);
//      const blink::CSSPrimitiveValue* primitive_value = (blink::CSSPrimitiveValue*)val;
//      double value = primitive_value->GetDoubleValue(); // 获取以像素为单位的数值
//      printf("Width value in pixels: %f\n", value);
//    }
    
    // dom
//    auto document = blink::MakeGarbageCollected<blink::Document>();
//    auto div = blink::MakeGarbageCollected<blink::HTMLElement>(blink::html_names::kDivTag, *document);
//    AtomicString name = AtomicString::FromUTF8("thename");
//    String value = String::FromUTF8("thevalue");
//    div->setAttribute(name, value);
//    auto result = div->getAttribute(name);
//    printf("result: %s\n", result.Characters8());
//    auto div2 = blink::MakeGarbageCollected<blink::HTMLElement>(blink::html_names::kDivTag, *document);
//    String value2 = String::FromUTF8("thevalue2");
//    div2->setAttribute(name, value2);
//    div->ParserAppendChild(div2);
//    blink::NodeVector nv;
//    blink::GetChildNodes(*div, nv);
//    for (auto n : nv) {
//      auto node = n.Get();
//      auto result = ((blink::Element *)node)->getAttribute(name);
//      printf("result: %s\n", result.Characters8());
//    }
//    div->ParserRemoveChild(*div2);
//    blink::NodeVector nv2;
//    blink::GetChildNodes(*div, nv2);
//    for (auto n : nv2) {
//      printf("have children\n");
//    }

    // cascade & layout
//    auto document = blink::MakeGarbageCollected<blink::Document>();
//    document->Initialize();
//    auto &engine = document->GetStyleEngine();
//    
//    auto sheet = blink::CSSStyleSheet::CreateInline();
//    String str = String::FromUTF8("#root{display:flex} #parent {display:flex;flex-direction:column;align-items:stretch;width:200px;height:400px;background-color:yellow;border-top-width:0px;border-bottom-width:0px;border-left-width:0px;border-right-width:0px} #child1 {display:flex;flex-direction:column;flex-grow:1;background:red;border-top-width:0px;border-bottom-width:0px;border-left-width:0px;border-right-width:0px} #child2 {display:flex;flex-grow:2;background:blue;border-top-width:0px;border-bottom-width:0px;border-left-width:0px;border-right-width:0px} #child3 {display:flex;flex-grow:1;background:blue;border-top-width:0px;border-bottom-width:0px;border-left-width:0px;border-right-width:0px}");
//    blink::CSSParserContext ctx(blink::kHTMLStandardMode, blink::SecureContextMode::kSecureContext);
//    blink::CSSParser::ParseSheet(&ctx, sheet->Contents(), str);
//
//    blink::StyleEngine::RuleSetScope rule_set_scope;
//    auto ruleset = rule_set_scope.RuleSetForSheet(engine, sheet);
//    blink::ActiveStyleSheetVector sheets;
//    sheets.push_back(std::make_pair(sheet, ruleset));
//    engine.ApplyRuleSetChanges(*document, sheets);
//    
//    auto parent = blink::MakeGarbageCollected<blink::HTMLElement>(blink::html_names::kDivTag, *document);
//    {
//      {
//        AtomicString name = AtomicString::FromUTF8("id");
//        String value = String::FromUTF8("parent");
//        parent->setAttribute(name, value);
//      }
//      {
//        AtomicString name = AtomicString::FromUTF8("style");
//        String value = String::FromUTF8("width:300px");
//        parent->setAttribute(name, value);
//          parent->GetIdAttribute().Utf8().c_str();
//      }
//    }
//    document->ParserAppendChild(parent);
//    
//    auto child = blink::MakeGarbageCollected<blink::HTMLElement>(blink::html_names::kDivTag, *document);
//    {
//      AtomicString name = AtomicString::FromUTF8("id");
//      String value = String::FromUTF8("child1");
//      child->setAttribute(name, value);
//        child->GetIdAttribute().Utf8().c_str();
//    }
//    parent->ParserAppendChild(child);
//    
//    auto child2 = blink::MakeGarbageCollected<blink::HTMLElement>(blink::html_names::kDivTag, *document);
//    {
//      AtomicString name = AtomicString::FromUTF8("id");
//      String value = String::FromUTF8("child2");
//      child2->setAttribute(name, value);
//        child2->GetIdAttribute().Utf8().c_str();
//    }
//    parent->ParserAppendChild(child2);
//    
//    engine.RecalcStyle();
//    engine.RebuildLayoutTree();
//      T(parent->GetLayoutObject());
//    document->GetLayoutView()->LayoutRoot();
//
//    auto pbox = parent->GetLayoutBox();
//    auto cbox = child->GetLayoutBox();
//    auto c2box = child2->GetLayoutBox();
//    
//    auto w = pbox->Size().width.ToInt();
//    auto h = pbox->Size().height.ToInt();
//    auto w1 = cbox->Size().width.ToInt();
//    auto h1 = cbox->Size().height.ToInt();
//    auto w2 = c2box->Size().width.ToInt();
//    auto h2 = c2box->Size().height.ToInt();
//
//    auto x2 = c2box->Location().X().ToInt();
//    auto y2 = c2box->Location().Y().ToInt();
//    
//    auto child3 = blink::MakeGarbageCollected<blink::HTMLElement>(blink::html_names::kDivTag, *document);
//    {
//      AtomicString name = AtomicString::FromUTF8("id");
//      String value = String::FromUTF8("child3");
//      child3->setAttribute(name, value);
//    }
//    parent->ParserAppendChild(child3);
//
//    engine.RecalcStyle();
//    engine.RebuildLayoutTree();
//      T(parent->GetLayoutObject());
//    document->GetLayoutView()->LayoutRoot();
//
//    {
//      auto pbox = parent->GetLayoutBox();
//      auto cbox = child->GetLayoutBox();
//      auto c2box = child2->GetLayoutBox();
//      auto c3box = child3->GetLayoutBox();
//
//      auto w = pbox->Size().width.ToInt();
//      auto h = pbox->Size().height.ToInt();
//      auto w1 = cbox->Size().width.ToInt();
//      auto h1 = cbox->Size().height.ToInt();
//      auto w2 = c2box->Size().width.ToInt();
//      auto h2 = c2box->Size().height.ToInt();
//      auto w3 = c3box->Size().width.ToInt();
//      auto h3 = c3box->Size().height.ToInt();
//      auto x2 = c2box->Location().X().ToInt();
//      auto y2 = c2box->Location().Y().ToInt();
//      auto x3 = c3box->Location().X().ToInt();
//      auto y3 = c3box->Location().Y().ToInt();
//      auto f = false;
//    }
    
//    parent->ParserRemoveChild(*child);
//    
//    engine.RecalcStyle();
//    engine.RebuildLayoutTree();
//      T(parent->GetLayoutObject());
//    document->GetLayoutView()->LayoutRoot();
//    
//    {
//      auto pbox = parent->GetLayoutBox();
//      auto c2box = child2->GetLayoutBox();
//      auto c3box = child3->GetLayoutBox();
// 
//      auto w = pbox->Size().width.ToInt();
//      auto h = pbox->Size().height.ToInt();
//      auto w2 = c2box->Size().width.ToInt();
//      auto h2 = c2box->Size().height.ToInt();
//      auto w3 = c3box->Size().width.ToInt();
//      auto h3 = c3box->Size().height.ToInt();
//      auto x2 = c2box->Location().X().ToInt();
//      auto y2 = c2box->Location().Y().ToInt();
//      auto x3 = c3box->Location().X().ToInt();
//      auto y3 = c3box->Location().Y().ToInt();
//      auto f = false;
//    }

//    heap->ForceGarbageCollectionSlow("CppGC example", "Testing");
  }
}

using Tag = blink::mt::Tag;
using Props = blink::mt::Props;

shared_ptr<blink::mt::MTDocument> docptr;

void TestDocument()
{
  blink::mt::Device::initScreenData([UIScreen mainScreen].bounds.size.width,
                                    [UIScreen mainScreen].bounds.size.height,
                                    [UIScreen mainScreen].scale);

  blink::mt::Config config;
  config.enable_wxs_engine = false;
  docptr = blink::mt::MTDocument::Create(std::move(config));
  auto &doc = *docptr;

  doc.setup(nullptr, 1);

  doc.setUICommandBufferCallback([](const blink::mt::UICommands& buffer) {
  });

  doc.addCSS("");
  auto size = UIScreen.mainScreen.bounds.size;
  doc.setSize(blink::mt::Size(size.width, size.height));

  doc.createNode(1, "MSCView", 1, blink::mt::propsFromDict(@{}));
  doc.createNode(2, "MSCView", 1, blink::mt::propsFromDict(@{
    @"style": @"width: 100px; height: 100px"
  }));

  doc.setChildren(1, make_shared<std::vector<int>>(std::vector<int>({2})));
  doc.layoutRoot(blink::mt::LayoutReason::BatchDidComplete);

  doc.createNode(3, "MSCView", 1, blink::mt::propsFromDict(@{
    @"style": @"width: 100px; height: 100px"
  }));

  doc.setChildren(1, make_shared<std::vector<int>>(std::vector<int>({3})));
  doc.layoutRoot(blink::mt::LayoutReason::BatchDidComplete);
}

// 渲染树构建标脏用例
//    doc.createNode(1, "MSCView", 1, blink::mt::propsFromDict(@{}));
//
//    doc.createNode(2, "MSCView", 1, blink::mt::propsFromDict(@{
//        @"style": @""
//    }));
//    doc.createNode(3, "MSCView", 1, blink::mt::propsFromDict(@{
//        @"style": @""
//    }));
//    doc.createNode(4, "MSCView", 1, blink::mt::propsFromDict(@{
//        @"style": @""
//    }));
//    doc.createNode(5, "MSCView", 1, blink::mt::propsFromDict(@{
//        @"style": @""
//    }));
//
//    doc.setChildren(1, make_shared<std::vector<int>>(std::vector<int>({2})));
//    doc.setChildren(2, make_shared<std::vector<int>>(std::vector<int>({3})));
//    doc.setChildren(3, make_shared<std::vector<int>>(std::vector<int>({4})));
//    doc.setChildren(4, make_shared<std::vector<int>>(std::vector<int>({5})));
//
//    doc.layoutRoot([](std::shared_ptr<const blink::mt::DisplayInfo> result) {
//    });
//
//    // 插入删除普通节点
//    doc.createNode(6, "MSCView", 1, blink::mt::propsFromDict(@{
//        @"style": @""
//    }));
//    doc.setChildren(5, make_shared<std::vector<int>>(std::vector<int>({6})));
//    doc.layoutRoot([](std::shared_ptr<const blink::mt::DisplayInfo> result) {
//    });
//    auto changes = make_shared<blink::mt::MTDocument::ChildrenChanges>();
//    changes->remove_at_indices.push_back(0);
//    doc.manageChildren(5, changes);
//    doc.layoutRoot([](std::shared_ptr<const blink::mt::DisplayInfo> result) {
//    });
//
//    // 插入删除带层节点
//    doc.createNode(6, "MSCView", 1, blink::mt::propsFromDict(@{
//        @"style": @"opacity:0.5"
//    }));
//    doc.setChildren(5, make_shared<std::vector<int>>(std::vector<int>({6})));
//    doc.layoutRoot([](std::shared_ptr<const blink::mt::DisplayInfo> result) {
//    });
//    auto changes = make_shared<blink::mt::MTDocument::ChildrenChanges>();
//    changes->remove_at_indices.push_back(0);
//    doc.manageChildren(5, changes);
//    doc.layoutRoot([](std::shared_ptr<const blink::mt::DisplayInfo> result) {
//    });
//
//    // 普通节点 -> 带层节点 / 带层节点 -> 普通节点
//    doc.updateNode(5, "MSCView", blink::mt::propsFromDict(@{
//        @"style": @"opacity:0.5"
//    }));
//    doc.layoutRoot([](std::shared_ptr<const blink::mt::DisplayInfo> result) {
//    });
//    doc.updateNode(5, "MSCView", blink::mt::propsFromDict(@{
//        @"style": @""
//    }));
//    doc.layoutRoot([](std::shared_ptr<const blink::mt::DisplayInfo> result) {
//    });

