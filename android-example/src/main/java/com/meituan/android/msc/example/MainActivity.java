package com.meituan.android.msc.example;


import android.os.Bundle;
import android.os.SharedMemory;
import android.support.v7.app.AppCompatActivity;
import android.util.Log;
import android.view.View;

import com.meituan.android.msc.renderer.JniUIOperationCallback;
import com.meituan.android.msc.renderer.MTDocument;
import com.meituan.android.msc.renderer.NativeCallback;
import com.meituan.android.msc.renderer.RendererNative;
import com.meituan.android.msc.renderer.JSEngineTest;
import com.meituan.android.msc.renderer.trace.OnlineTrace;
import com.meituan.android.soloader.SoLoader;
import com.meituan.android.msc.renderer.generated.*;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class MainActivity extends AppCompatActivity {

    private MTDocument mtDocument;

    static {
        try {
            Class.forName("com.example.Person");
        } catch (ClassNotFoundException e) {
            Log.e("Preload", "Failed to preload Person class");
        }
    }

    public static class Command {
        public String type;
        public JSONArray props;

        public Command(String type, JSONArray props) {
            this.type = type;
            this.props = props;
        }
    }

    public List<Command> commandList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        SoLoader.init(this, false);
        JSEngineTest test = new JSEngineTest();
        String res = test.TestEngine();

        setContentView(R.layout.activity_main);
        try {
            commandList.add(new Command("createView", new JSONArray("[3,\"MSCView\",1,{\"is\":\"mt-view\",\"style\":\"border-width:5px; border-color:red; border-style:solid; border-radius:10px; width: 200px; height: 200px; background-color: salmon;\",\"classPrefix\":\"p-\",\"tagName\":\"mt-view\"}]")));
            commandList.add(new Command("createView", new JSONArray("[5,\"MSCRawText\",1,{\"text\":\"测试的\"}]")));
            commandList.add(new Command("createView", new JSONArray("[7,\"MSCText\",1,{\"is\":\"mt-text\",\"style\":\"display: flex;\",\"tagName\":\"mt-text\"}]")));
            commandList.add(new Command("setChildren", new JSONArray("[7,[5]]")));
            commandList.add(new Command("createView", new JSONArray("[9,\"MSCView\",1,{\"is\":\"mt-view\",\"style\":\"display: flex; flex-direction: column; width: 100%; height: 100%;\",\"classPrefix\":\"p-\",\"tagName\":\"mt-view\"}]")));
            commandList.add(new Command("setChildren", new JSONArray("[9,[3,7]]")));
            commandList.add(new Command("setChildren", new JSONArray("[1,[9]]")));
            commandList.add(new Command("batchDidCompleteWithOption", new JSONArray("[{}]")));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        findViewById(R.id.button).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {

                Log.e("MSCLOG", "onClick");
                RendererNative.initIfNeed(true);
                Log.e("MSCLOG", "onClick1");
                new Thread(new Runnable() {
                    @Override
                    public void run() {
                        final int[] fd1 = new int[1];

                        mtDocument = new MTDocument(RendererNative.jni_DocumentNewJNI(true,false,1,"index/index"), null);
                        mtDocument.setUp(123, 0);

                        mtDocument.setNativeCallBack(new NativeCallback() {
                            @Override
                            public void onNativeCallback(ByteBuffer byteBuffer) {
                                Log.e("MSCLOG", "onNativeCallback ");
//                                BlinkInfo blinkInfo = BlinkInfo.getRootAsBlinkInfo(byteBuffer);
//                                Log.e("MSCLOG", "xxx " +  blinkInfo.framesLength());
//                                int fd = fd1[0];
//                                if (fd == -1) {
//                                    Log.e("TAG", "Failed to create shared memory");
//                                    return;
//                                }
//                                // 将 fd 包装为 ParcelFileDescriptor
//                                ParcelFileDescriptor pfd = null;
//                                try {
//                                    pfd = ParcelFileDescriptor.fromFd(fd);
//                                    String fdPath = "/proc/self/fd/" + pfd.getFd();
//                                    Log.e("MSCLOG", "onNativeCallback1 " + fdPath);
//                                    if (pfd.getFileDescriptor() == null) {
//                                        Log.e("MSCLOG", "onNativeCallback1xx ");
//                                    }
//                                    if (pfd.getFileDescriptor().valid()) {
//                                        Log.e("MSCLOG", "onNativeCallback2xx ");
//                                    }
//                                    InputStream is = new FileInputStream(pfd.getFileDescriptor());
//                                    byte[] buffer1 = new byte[1024];
//                                    int bytesRead = is.read(buffer1);
//                                    Log.e("MSCLOG", "Bytes read: " + bytesRead);
//                                    is.close();
//
//                                    // 使用传统文件映射方式
//                                    FileInputStream fis = new FileInputStream(pfd.getFileDescriptor());
//                                    Log.e("MSCLOG", "onNativeCallback2 ");
//                                    FileChannel channel = fis.getChannel();
//                                    int size  = (int) channel.size();
//                                    Log.e("MSCLOG", "onNativeCallback3 " + size);
//                                    MappedByteBuffer buffer = channel.map(FileChannel.MapMode.READ_ONLY, 0, size);
//                                    Log.e("MSCLOG", "onNativeCallback4 ");
//                                    byte[] data = new byte[size];
//                                    Log.e("MSCLOG", "onNativeCallback5 ");
//                                    buffer.get(data);
//                                    Log.e("MSCLOG", "onNativeCallback6 " + data.length);
//                                    String result = new String(data);
//                                    Log.e("MSCLOG", "onNativeCallback7 " + result);
//                                } catch (Throwable e) {
//                                    Log.e("MSCLOG", "onNativeCallback error " + Log.getStackTraceString(e));
//                                    throw new RuntimeException(e);
//                                }
                            }

                            @Override
                            public float[] measureText(int tag, double width, double height, ByteBuffer attrs) {
                                MeasureTextInfo textInfo = MeasureTextInfo.getRootAsMeasureTextInfo(attrs);
                                Log.e("MSCLOG", "measureText " + textInfo.textsLength());
                                return new float[]{0, 0};
                            }
                        });

                        mtDocument.setSize(1080, 2296, 2.75f);
                        mtDocument.createNode(1, "MSCView", 1, new HashMap<>());
                        executeCommand();
                    }
                }).start();
            }
        });
    }

    public void executeCommand() {
        for (Command command : commandList) {
            String type = command.type;
            JSONArray props = command.props;
            if ("createView".equals(type)) {
                Log.e("MSCLOG", "executeCommand " + props);
                if (props.length() != 4) {
                    throw new RuntimeException("props length is not 4");
                }
                JSONObject propsObj = props.optJSONObject(3);
                String viewName = props.optString(1);
                if ("MSCText".equals(viewName)) {
                    try {
                        propsObj.put("documentKey", String.valueOf(mtDocument.getNativePointerId()));
                    } catch (JSONException e) {
                        throw new RuntimeException(e);
                    }
                }
                Map<String, Object> map = convertJSONObjectToMap(propsObj);
                // 输出转换后的 Map
                for (Map.Entry<String, Object> entry : map.entrySet()) {
                    System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
                }
                mtDocument.createNode(props.optInt(0), props.optString(1), props.optInt(2), map);
            } else if ("setChildren".equals(type)) {
                if (props.length() != 2) {
                    throw new RuntimeException("error1");
                }
                int tag = props.optInt(0);
                JSONArray array = props.optJSONArray(1);
                int[] list = new int[array.length()];
                for (int i = 0; i < array.length(); i++) {
                    list[i] = array.optInt(i);
                }
                Log.e("MSCLOG", "setChildren " + tag + " " + list);
                mtDocument.setChildren(tag, list);
            } else if ("batchDidCompleteWithOption".equals(type)) {
                Log.e("MSCLOG", "batchDidCompleteWithOption");
                mtDocument.layoutRoot(false);
            }
        }
    }

    public static Map<String, Object> convertJSONObjectToMap(JSONObject jsonObject) {
        Map<String, Object> map = new HashMap<>();
        // 获取 JSONObject 的键集合
        Iterator<String> keys = jsonObject.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            // 获取与键对应的值，并转换为字符串
            String value = jsonObject.optString(key, null);
            map.put(key, value);
        }
        return map;
    }
}