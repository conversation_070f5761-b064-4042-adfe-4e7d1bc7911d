plugins {
    id 'com.android.application'
}

android {
    namespace 'com.meituan.android.msc.example'
    compileSdkVersion 31

    defaultConfig {
        minSdkVersion 23
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['../android-module/lib/mtv8']
        }
    }

    signingConfigs {
        sankuai {
            storeFile file("keystore/meituan-debug.keystore")
            storePassword "1234567"
            keyAlias "meituan"
            keyPassword "12345678"
        }
    }
    buildTypes {
        debug {
            signingConfig signingConfigs.sankuai
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        release {
//            debuggable true
            signingConfig signingConfigs.sankuai
//            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    ndkVersion '26.0.10792818'
//    ndkVersion '26.1.10909125'
}

configurations {
    all*.exclude group: 'com.sankuai.meituan', module: 'buildconfig'
    all*.exclude group: 'com.dianping.android.sdk', module: 'mainboard'
    all*.exclude group: 'com.sankuai.meituan.kernel', module: 'image'
    // 下线netsingleton & netmodule & net-impl
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netsingleton'
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netmodule'
    all*.exclude group: 'com.sankuai.meituan.kernel', module: 'net-impl'

    all*.exclude group: 'com.squareup.okhttp', module: 'okhttp'
    all*.exclude group: 'com.meituan.android.common', module: 'babel'
    all*.exclude group: 'com.meituan.android.snare', module: 'snare'
    all*.exclude group: 'com.meituan.android.crashreporter', module: 'library'
    all*.exclude group: 'com.meituan.metrics', module: 'metrics'
    all*.exclude group: 'com.meituan.android.sniffer'
    all*.exclude group: 'com.facebook.react.modules.image.ImageLoaderModule'
    all*.exclude group: 'com.meituan.android.common', module: 'tcreporter'
    //noinspection DuplicatePlatformClasses
    all*.exclude module: 'xpp3'

    // gradle升级3.2.1
    all*.exclude group: 'com.google.android', module: 'android'
    all*.exclude group: 'org.apache.httpcomponents', module: 'httpclient'
    all*.exclude group: 'commons-logging', module: 'commons-logging'
    all*.exclude group: 'org.json', module: 'json'
    all*.exclude group: 'com.meituan.android.knb', module: 'titans-knbweb-delegate'
    // 下线netsingleton & netmodule & net-impl
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netsingleton'
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netmodule'
    all*.exclude group: 'com.sankuai.meituan.kernel', module: 'net-impl'
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'model'
    all*.exclude group: 'com.meituan.android.favorite', module: 'library'
//    all*.exclude group: 'com.meituan.android.abtest', module: 'abtestv2'
    all*.exclude group: 'com.sankuai.android.jarvis', module: 'core'
    all*.exclude group: 'com.sankuai.meituan.dev', module: 'devtools'
    all*.exclude group: 'com.meituan.android.mmp', module: 'mmp'
    all*.exclude group: 'com.meituan.android.msc', module: 'msc-render'
}

dependencies {
    implementation project(':android-module')
    implementation 'com.android.support.constraint:constraint-layout:1.1.2'
    implementation 'com.meituan.android.loader:dynloader-interface:1.0.8'
    implementation 'com.google.flatbuffers:flatbuffers-java:25.2.10'
    //implementation('com.android.support:appcompat-v7:26.0.2') {
    //    force = true
    //}
//    implementation('com.sankuai.android.jarvis:library:0.1.33') {
//        force = true
//    }

    implementation('com.android.support:support-v4:26.0.2') {
        force = true
    }
    implementation('com.android.support:appcompat-v7:26.0.2') {
        force = true
    }
    testImplementation 'junit:junit:4.13.2'
}