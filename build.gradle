/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        maven {
            url 'http://depot.sankuai.com/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.2.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        maven {
            url 'http://depot.sankuai.com/nexus/content/groups/public/'
            allowInsecureProtocol = true
        }
        maven {
            url "http://pixel.sankuai.com/repository/mtdp"
            allowInsecureProtocol = true
        }
        google()
        mavenCentral()
    }
}

//ext {
//    minSdkVersion = 14
//    targetSdkVersion = 29
//    compileSdkVersion = 29
//    buildToolsVersion = '30.0.2'
//    ndkVersion = '21.3.6528147'
//    sourceCompatibilityVersion = JavaVersion.VERSION_1_7
//    targetCompatibilityVersion = JavaVersion.VERSION_1_7
//}
//

task clean(type: Delete) {
    delete rootProject.buildDir
}
